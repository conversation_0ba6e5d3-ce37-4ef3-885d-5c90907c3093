PODS:
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - ffmpeg_kit_flutter_new (1.0.0):
    - ffmpeg_kit_flutter_new/full-gpl (= 1.0.0)
    - FlutterMacOS
  - ffmpeg_kit_flutter_new/full-gpl (1.0.0):
    - FlutterMacOS
  - file_picker (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 6.0.3)
  - FlutterMacOS (1.0.0)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 8.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (8.0.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - media_kit_libs_macos_video (1.0.4):
    - FlutterMacOS
  - media_kit_video (0.0.1):
    - FlutterMacOS
  - OrderedSet (6.0.3)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - volume_controller (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - window_manager (0.5.0):
    - FlutterMacOS

DEPENDENCIES:
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - ffmpeg_kit_flutter_new (from `Flutter/ephemeral/.symlinks/plugins/ffmpeg_kit_flutter_new/macos`)
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/darwin`)
  - google_sign_in_ios (from `Flutter/ephemeral/.symlinks/plugins/google_sign_in_ios/darwin`)
  - media_kit_libs_macos_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos`)
  - media_kit_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - photo_manager (from `Flutter/ephemeral/.symlinks/plugins/photo_manager/macos`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)
  - volume_controller (from `Flutter/ephemeral/.symlinks/plugins/volume_controller/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - webview_flutter_wkwebview (from `Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - OrderedSet
    - PromisesObjC

EXTERNAL SOURCES:
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  ffmpeg_kit_flutter_new:
    :path: Flutter/ephemeral/.symlinks/plugins/ffmpeg_kit_flutter_new/macos
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/darwin
  google_sign_in_ios:
    :path: Flutter/ephemeral/.symlinks/plugins/google_sign_in_ios/darwin
  media_kit_libs_macos_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos
  media_kit_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  photo_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/photo_manager/macos
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin
  volume_controller:
    :path: Flutter/ephemeral/.symlinks/plugins/volume_controller/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  webview_flutter_wkwebview:
    :path: Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  device_info_plus: ab67698ff77a13898b313b9280eaaea9bdadf55a
  ffmpeg_kit_flutter_new: f2693ef0662ecf167d8d0ed9706e12a556f9d61c
  file_picker: e716a70a9fe5fd9e09ebc922d7541464289443af
  file_selector_macos: cc3858c981fe6889f364731200d6232dac1d812d
  flutter_inappwebview_macos: bdf207b8f4ebd58e86ae06cd96b147de99a67c9b
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  google_sign_in_ios: 7411fab6948df90490dc4620ecbcabdc3ca04017
  GoogleSignIn: ce8c89bb9b37fb624b92e7514cc67335d1e277e4
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  media_kit_libs_macos_video: b3e2bbec2eef97c285f2b1baa7963c67c753fb82
  media_kit_video: c75b07f14d59706c775778e4dd47dd027de8d1e5
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: 12f1c5c2cfe8727ca46cbd0b26677728972d9a5b
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  photo_manager: 81954a1bf804b6e882d0453b3b6bc7fad7b47d3d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  screen_retriever_macos: 776e0fa5d42c6163d2bf772d22478df4b302b161
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  url_launcher_macos: c82c93949963e55b228a30115bd219499a6fe404
  video_player_avfoundation: 7c6c11d8470e1675df7397027218274b6d2360b3
  volume_controller: 90a5978956cf18ebb7739bf5382fc1b0cfef66d0
  wakelock_plus: 4783562c9a43d209c458cb9b30692134af456269
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188
  window_manager: e25faf20d88283a0d46e7b1a759d07261ca27575

PODFILE CHECKSUM: 58988c2788618a39541e013685842bfa312adb36

COCOAPODS: 1.16.2
