import 'package:aitoearn_app/api/account/models/account_user_info_modle.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';

/// 账号服务类
class AccountService {
  /// 根据ID获取账号
  Future<AccountUserInfoModle?> getAccountById(String id) async {
    try {
      return AccountPersistentService.to.accounts.firstWhere(
        (account) => account.id == id,
        orElse: () => throw Exception('未找到账号'),
      );
    } catch (e) {
      return null;
    }
  }
} 