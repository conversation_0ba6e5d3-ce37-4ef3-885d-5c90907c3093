import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/plat_core/models/plat_form_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_base.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/plat_douyin.dart';
import 'package:aitoearn_app/plat_core/plats/plat_kwai/plat_kwai.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/plat_wx_sph.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/plat_xhs.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';

class PlatManager {
  PlatManager();

  /// 获取平台实例，添加平台检查
  PlatBase getPlat(PlatTypeEnum platType, PlatformModel platForm) {
    // 如果是微信视频号并且不是移动平台，给出警告
    if (platType == PlatTypeEnum.wxWph) {
      PlatformChecker.checkWxSphSupport();
    }

    switch (platType) {
      case PlatTypeEnum.xhs:
        return PlatXhs(platForm);
      case PlatTypeEnum.wxWph:
        return PlatWxSph(platForm);
      case PlatTypeEnum.douyin:
        return PlatDouyin(platForm);
      case PlatTypeEnum.kwai:
        return PlatKwai(platForm);
      default:
        throw Exception('Unsupported platform type: $platType');
    }
  }

  Future<PublishResponseModel?> publishVideo() async {
    // 示例代码，实际应该实现具体逻辑
    // var plat =  getPlat(PlatTypeEnum.douyin);
    // return await plat.publishVideo(publishParamsModel);
    return null;
  }

  /// 根据平台名称获取平台实例
  static PlatBase? getPlatInstanceByName(
    String platformName,
    PlatformModel? platformModel,
  ) {
    switch (platformName) {
      case PlatformChecker.DOUYIN:
        if (platformModel == null) {
          return null;
        }
        return PlatDouyin(platformModel);
      case PlatformChecker.WX_SPH:
        if (platformModel == null) {
          return null;
        }
        return PlatWxSph(platformModel);
      case PlatformChecker.KWAI:
      case 'ks': // 增加快手平台的别名支持
        if (platformModel == null) {
          return null;
        }
        return PlatKwai(platformModel);
      case PlatformChecker.XHS: // 小红书平台
        if (platformModel == null) {
          return null;
        }
        return PlatXhs(platformModel);
      default:
        return null;
    }
  }
}
