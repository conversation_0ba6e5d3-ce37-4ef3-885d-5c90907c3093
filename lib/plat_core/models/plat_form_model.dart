import 'dart:io';

/// 平台构造器参数
class PlatformModel {
  // 平台ID
  late String? id;
  // 平台类型
  late String? platType;
  // cookie List
  late List<Cookie>? cookieList;
  // cookie字符串，用于移动端
  late String? cookie;

  PlatformModel({
    this.id,
    this.platType,
    String? type,
    this.cookieList,
    this.cookie,
  }) {
    // 兼容type和platType字段
    if (type != null && platType == null) {
      platType = type;
    }
  }
  
  // 兼容type字段
  String? get type => platType;
  set type(String? value) => platType = value;

  static List<Cookie> cookieJsonToCoolies(List<dynamic> cookieJson) {
    var cookies =
        cookieJson.map((item) {
          var cookie = Cookie(item['name'], item['value']);
          cookie.domain = item['domain'];
          cookie.path = item['path'];
          cookie.secure = item['secure'] ?? false;
          cookie.httpOnly = item['httpOnly'] ?? false;

          // Set expiration date if available
          if (item['expirationDate'] != null) {
            cookie.expires = DateTime.fromMillisecondsSinceEpoch(
              (item['expirationDate'] * 1000).toInt(),
            );
          }
          return cookie;
        }).toList();
    return cookies;
  }
  
  // 从Cookie字符串创建Cookie列表
  static List<Cookie> cookieStringToCookies(String cookieString) {
    if (cookieString.isEmpty) return [];
    
    final cookies = <Cookie>[];
    final cookieParts = cookieString.split(';');
    
    for (var part in cookieParts) {
      part = part.trim();
      if (part.isEmpty) continue;
      
      final index = part.indexOf('=');
      if (index > 0) {
        final name = part.substring(0, index).trim();
        final value = part.substring(index + 1).trim();
        if (name.isNotEmpty) {
          cookies.add(Cookie(name, value));
        }
      }
    }
    
    return cookies;
  }
}
