import 'dart:io';

import 'package:aitoearn_app/models/lable_value_modles.dart';
import 'package:aitoearn_app/plat_core/enum/pub_params_enum.dart';
import 'package:aitoearn_app/plat_core/models/location_model.dart';
import 'package:aitoearn_app/plat_core/utils/plat_common_utils.dart';

/// 基础发布参数
class PublishParamsModel {
  /// 标题
  String? title;

  /// 描述，描述中不该包含话题，如果有需要每个平台再自己做处理。
  String? desc;

  /// 话题 格式：['话题1', '话题2']，不该包含 '#'
  List<String>? topics;

  /// 位置
  LocationModel? location;

  // @好友
  List<LableValueModles>? mentionedUserInfo;

  /// 可见性,作品的查看权限
  late VisibleTypeEnum visibleType;

  /// 定时发布日期，时间戳（毫秒）
  num? timingTime;

  /// 封面
  late File cover;

  /// 视频分片
  FilePartInfo? filePartInfo;

  /// 视频
  File? video;

  // 图片
  List<File>? images;
  
  /// 进度回调函数，用于显示上传进度
  /// [progress] 进度百分比 0-100
  /// [message] 进度消息
  Function(int progress, String? message)? progressCallback;

  PublishParamsModel(
    this.cover, {
    this.title,
    this.desc,
    this.topics,
    this.images,
    this.video,
    this.location,
    this.visibleType = VisibleTypeEnum.public,
    this.timingTime,
    this.mentionedUserInfo,
    this.progressCallback,
  }) {
    if (video == null && images == null) {
      throw Exception('images 或者 video 必须要有一个');
    }
    if (video != null) {
      _initializeVideoParams();
    }
  }

  Future<void> _initializeVideoParams() async {
    if (video != null) {
      filePartInfo = await PlatCommonUtils.getFilePartInfo(video!, 5242880);
    }
  }

  @override
  String toString() {
    return 'PublishParamsModelBase{title: $title, desc: $desc, topics: $topics, cover: $cover}';
  }
}
