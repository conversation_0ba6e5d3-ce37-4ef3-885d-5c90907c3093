import 'package:aitoearn_app/plat_core/models/platform_models/platform_model_base.dart';

/// 视频号活动信息
class WxSphActivity {
  final String eventTopicId;
  final String eventName;
  final String eventCreatorNickname;

  WxSphActivity({
    required this.eventTopicId,
    required this.eventName,
    required this.eventCreatorNickname,
  });

  factory WxSphActivity.fromJson(Map<String, dynamic> json) {
    return WxSphActivity(
      eventTopicId: json['eventTopicId'] ?? '',
      eventName: json['eventName'] ?? '',
      eventCreatorNickname: json['eventCreatorNickname'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'eventTopicId': eventTopicId,
      'eventName': eventName,
      'eventCreatorNickname': eventCreatorNickname,
    };
  }

  @override
  String toString() => eventName;
}

/// 视频号用户信息
class WxSphUser {
  final String username;
  final String headImgUrl;
  final String nickName;
  final String? authImgUrl;
  final String? authCompanyName;

  WxSphUser({
    required this.username,
    required this.headImgUrl,
    required this.nickName,
    this.authImgUrl,
    this.authCompanyName,
  });

  factory WxSphUser.fromJson(Map<String, dynamic> json) {
    return WxSphUser(
      username: json['username'] ?? '',
      headImgUrl: json['headImgUrl'] ?? '',
      nickName: json['nickName'] ?? '',
      authImgUrl: json['authImgUrl'],
      authCompanyName: json['authCompanyName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'headImgUrl': headImgUrl,
      'nickName': nickName,
      'authImgUrl': authImgUrl,
      'authCompanyName': authCompanyName,
    };
  }

  @override
  String toString() => nickName;
}

/// 视频号平台特定参数模型
class WxSphPlatformModel extends PlatformModelBase {
  /// 短标题
  String shortTitle;
  
  /// 扩展链接
  String extLink;
  
  /// 是否声明原创
  bool isOriginal;
  
  /// 参与的活动
  WxSphActivity? activity;
  
  /// @的视频号用户列表
  List<WxSphUser> mentionedUsers;

  WxSphPlatformModel({
    this.shortTitle = '',
    this.extLink = '',
    this.isOriginal = false,
    this.activity,
    this.mentionedUsers = const [],
    super.isPrivate = false,
  });

  @override
  String get platform => 'wxSph';

  @override
  Map<String, dynamic> getPlatformConfig() {
    return {
      'short_title': shortTitle,
      'ext_link': extLink,
      'is_original': isOriginal,
      'activity': activity?.toJson(),
      'mentioned_users': mentionedUsers.map((user) => user.toJson()).toList(),
      'is_private': isPrivate,
    };
  }

  @override
  void updateFromConfig(Map<String, dynamic> config) {
    shortTitle = config['short_title'] ?? '';
    extLink = config['ext_link'] ?? '';
    isOriginal = config['is_original'] ?? false;
    isPrivate = config['is_private'] ?? false;
    
    if (config['activity'] != null) {
      activity = WxSphActivity.fromJson(config['activity']);
    }
    
    if (config['mentioned_users'] != null) {
      mentionedUsers = (config['mentioned_users'] as List)
          .map((user) => WxSphUser.fromJson(user))
          .toList();
    }
  }

  @override
  WxSphPlatformModel copyWith({
    String? shortTitle,
    String? extLink,
    bool? isOriginal,
    WxSphActivity? activity,
    List<WxSphUser>? mentionedUsers,
    bool? isPrivate,
  }) {
    return WxSphPlatformModel(
      shortTitle: shortTitle ?? this.shortTitle,
      extLink: extLink ?? this.extLink,
      isOriginal: isOriginal ?? this.isOriginal,
      activity: activity ?? this.activity,
      mentionedUsers: mentionedUsers ?? this.mentionedUsers,
      isPrivate: isPrivate ?? this.isPrivate,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'platform': platform,
      'short_title': shortTitle,
      'ext_link': extLink,
      'is_original': isOriginal,
      'activity': activity?.toJson(),
      'mentioned_users': mentionedUsers.map((user) => user.toJson()).toList(),
      'is_private': isPrivate,
    };
  }

  factory WxSphPlatformModel.fromJson(Map<String, dynamic> json) {
    final model = WxSphPlatformModel();
    model.updateFromConfig(json);
    return model;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WxSphPlatformModel &&
        other.shortTitle == shortTitle &&
        other.extLink == extLink &&
        other.isOriginal == isOriginal &&
        other.activity == activity &&
        other.mentionedUsers.length == mentionedUsers.length &&
        other.isPrivate == isPrivate;
  }

  @override
  int get hashCode {
    return shortTitle.hashCode ^
        extLink.hashCode ^
        isOriginal.hashCode ^
        activity.hashCode ^
        mentionedUsers.length.hashCode ^
        isPrivate.hashCode;
  }
}
