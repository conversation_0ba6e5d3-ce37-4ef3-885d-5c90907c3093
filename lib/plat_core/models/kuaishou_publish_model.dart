import 'dart:io';
import 'package:aitoearn_app/plat_core/models/base_platform_publish_model.dart';

/// 快手发布模型
class KuaishouPublishModel extends BasePlatformPublishModel {
  /// 快手特定配置
  final int visibilityType; // 可见性类型
  final bool allowComment; // 允许评论
  final bool allowDownload; // 允许下载

  KuaishouPublishModel({
    required super.account,
    super.title,
    super.description,
    super.topics,
    super.video,
    super.images,
    super.cover,
    super.isScheduled,
    super.scheduledTime,
    super.selectedLocation,
    this.visibilityType = 0,
    this.allowComment = true,
    this.allowDownload = true,
  }) : super(platform: 'kwai');

  /// 从账号配置创建快手发布模型
  factory KuaishouPublishModel.fromAccountConfig({
    required Map<String, dynamic> account,
    required Map<String, dynamic> config,
    File? video,
    List<File>? images,
    File? cover,
    String? defaultTitle,
    String? defaultDescription,
    List<String>? defaultTopics,
  }) {
    return KuaishouPublishModel(
      account: account,
      title:
          config['custom_title']?.isNotEmpty == true
              ? config['custom_title']
              : (config['title']?.isNotEmpty == true
                  ? config['title']
                  : defaultTitle),
      description:
          config['description']?.isNotEmpty == true
              ? config['description']
              : defaultDescription,
      topics:
          (config['topics'] as List<String>?)?.isNotEmpty == true
              ? config['topics'] as List<String>?
              : defaultTopics,
      video: video,
      images: images,
      cover: cover,
      isScheduled: config['is_scheduled'] as bool? ?? false,
      scheduledTime: config['scheduled_time'] as DateTime?,
      selectedLocation: config['selected_location'],
      visibilityType: config['visibility_type'] as int? ?? 0,
      allowComment: config['allow_comment'] as bool? ?? true,
      allowDownload: config['allow_download'] as bool? ?? true,
    );
  }

  @override
  Map<String, dynamic> toPublishParams() {
    return {
      'account': account,
      'platform': platform,
      'title': title,
      'description': description,
      'topics': topics,
      'video': video,
      'images': images,
      'cover': cover,
      'is_scheduled': isScheduled,
      'scheduled_time': scheduledTime,
      'selected_location': selectedLocation,
      'platform_config': getPlatformConfig(),
    };
  }

  @override
  String? validate() {
    // 检查媒体文件
    if (video == null && (images == null || images!.isEmpty)) {
      return '账号 ${account['nickname']} 缺少媒体文件';
    }

    // 检查标题
    if (title == null || title!.isEmpty) {
      return '账号 ${account['nickname']} 缺少标题';
    }

    return null;
  }

  @override
  Map<String, dynamic> getPlatformConfig() {
    return {
      'visibility_type': visibilityType,
      'allow_comment': allowComment,
      'allow_download': allowDownload,
    };
  }

  @override
  String toString() {
    return 'KuaishouPublishModel{account: ${account['nickname']}, title: $title, visibilityType: $visibilityType}';
  }
}
