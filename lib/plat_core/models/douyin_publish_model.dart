import 'dart:io';
import 'package:aitoearn_app/plat_core/models/base_platform_publish_model.dart';

/// 抖音发布模型
class DouyinPublishModel extends BasePlatformPublishModel {
  /// 抖音特定配置
  final String? selfDeclare; // 自主声明
  final List<Map<String, String>>? mentionedUsers; // @好友
  final String? activityId; // 活动参与
  final String? hotspotId; // 热点关联
  final String? mixId; // 合集选择
  final int visibilityType; // 可见性类型 0:公开 1:私密 2:好友可见

  DouyinPublishModel({
    required super.account,
    super.title,
    super.description,
    super.topics,
    super.video,
    super.images,
    super.cover,
    super.isScheduled,
    super.scheduledTime,
    super.selectedLocation,
    this.selfDeclare,
    this.mentionedUsers,
    this.activityId,
    this.hotspotId,
    this.mixId,
    this.visibilityType = 0,
  }) : super(platform: 'douyin');

  /// 从账号配置创建抖音发布模型
  factory DouyinPublishModel.fromAccountConfig({
    required Map<String, dynamic> account,
    required Map<String, dynamic> config,
    File? video,
    List<File>? images,
    File? cover,
    String? defaultTitle,
    String? defaultDescription,
    List<String>? defaultTopics,
  }) {
    return DouyinPublishModel(
      account: account,
      title:
          config['custom_title']?.isNotEmpty == true
              ? config['custom_title']
              : (config['title']?.isNotEmpty == true
                  ? config['title']
                  : defaultTitle),
      description:
          config['description']?.isNotEmpty == true
              ? config['description']
              : defaultDescription,
      topics:
          (config['topics'] as List<String>?)?.isNotEmpty == true
              ? config['topics'] as List<String>?
              : defaultTopics,
      video: video,
      images: images,
      cover: cover,
      isScheduled: config['is_scheduled'] as bool? ?? false,
      scheduledTime: config['scheduled_time'] as DateTime?,
      selectedLocation: config['selected_location'],
      selfDeclare: config['self_declare'],
      mentionedUsers: config['mentioned_users'] as List<Map<String, String>>?,
      activityId: config['activity_id'],
      hotspotId: config['hotspot_id'],
      mixId: config['mix_id'],
      visibilityType: config['visibility_type'] as int? ?? 0,
    );
  }

  @override
  Map<String, dynamic> toPublishParams() {
    return {
      'account': account,
      'platform': platform,
      'title': title,
      'description': description,
      'topics': topics,
      'video': video,
      'images': images,
      'cover': cover,
      'is_scheduled': isScheduled,
      'scheduled_time': scheduledTime,
      'selected_location': selectedLocation,
      'platform_config': getPlatformConfig(),
    };
  }

  @override
  String? validate() {
    // 检查媒体文件
    if (video == null && (images == null || images!.isEmpty)) {
      return '账号 ${account['nickname']} 缺少媒体文件';
    }

    // 检查标题
    if (title == null || title!.isEmpty) {
      return '账号 ${account['nickname']} 缺少标题';
    }

    // 抖音特定验证
    if (title != null && title!.length > 30) {
      return '抖音标题不能超过30个字符';
    }

    return null;
  }

  @override
  Map<String, dynamic> getPlatformConfig() {
    final config = <String, dynamic>{};

    if (selfDeclare != null) config['self_declare'] = selfDeclare;
    if (mentionedUsers != null) config['mentioned_users'] = mentionedUsers;
    if (activityId != null) config['activity_id'] = activityId;
    if (hotspotId != null) config['hotspot_id'] = hotspotId;
    if (mixId != null) config['mix_id'] = mixId;
    config['visibility_type'] = visibilityType;

    return config;
  }

  @override
  String toString() {
    return 'DouyinPublishModel{account: ${account['nickname']}, title: $title, visibilityType: $visibilityType}';
  }
}
