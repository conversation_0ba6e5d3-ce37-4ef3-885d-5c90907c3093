/// 发布响应结果
class PublishResponseModel {
  /// 是否成功
  final bool isSuccess;

  /// 数据ID
  final String? dataId;

  /// 作品链接
  final String? workLink;

  /// 错误消息
  final String? failMsg;
  
  /// 额外提示信息
  final String? extraMsg;

  PublishResponseModel(
    this.isSuccess, {
    this.dataId,
    this.workLink,
    this.failMsg,
    this.extraMsg,
  });
  
  /// 转换为JSON格式，用于日志记录
  Map<String, dynamic> toJson() {
    return {
      'isSuccess': isSuccess,
      'dataId': dataId,
      'workLink': workLink,
      'failMsg': failMsg,
      'extraMsg': extraMsg,
    };
  }
}
