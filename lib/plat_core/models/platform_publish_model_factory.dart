import 'dart:io';

import 'package:aitoearn_app/plat_core/models/base_platform_publish_model.dart';
import 'package:aitoearn_app/plat_core/models/douyin_publish_model.dart';
import 'package:aitoearn_app/plat_core/models/kuaishou_publish_model.dart';
import 'package:aitoearn_app/plat_core/models/wx_sph_platform_model.dart';
import 'package:aitoearn_app/plat_core/models/xiaohongshu_publish_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_types.dart';

/// 平台发布模型工厂
class PlatformPublishModelFactory {
  /// 从账号配置创建平台发布模型
  static BasePlatformPublishModel? createFromAccountConfig({
    required Map<String, dynamic> account,
    required Map<String, dynamic> config,
    File? video,
    List<File>? images,
    File? cover,
    String? defaultTitle,
    String? defaultDescription,
    List<String>? defaultTopics,
  }) {
    final platform = account['platform'] ?? '';

    switch (platform) {
      case 'douyin':
        return DouyinPublishModel.fromAccountConfig(
          account: account,
          config: config,
          video: video,
          images: images,
          cover: cover,
          defaultTitle: defaultTitle,
          defaultDescription: defaultDescription,
          defaultTopics: defaultTopics,
        );

      case 'xhs':
        return XiaohongshuPublishModel.fromAccountConfig(
          account: account,
          config: config,
          video: video,
          images: images,
          cover: cover,
          defaultTitle: defaultTitle,
          defaultDescription: defaultDescription,
          defaultTopics: defaultTopics,
        );

      case 'kwai':
      case 'ks':
        return KuaishouPublishModel.fromAccountConfig(
          account: account,
          config: config,
          video: video,
          images: images,
          cover: cover,
          defaultTitle: defaultTitle,
          defaultDescription: defaultDescription,
          defaultTopics: defaultTopics,
        );

      case 'wx-sph':
      case 'wxSph':
        return WxSphPlatformModel(
          account: account,
          title: defaultTitle,
          description: defaultDescription,
          topics: defaultTopics,
          video: video,
          images: images,
          cover: cover,
          // 视频号特定配置
          shortTitle: config['short_title'] ?? '',
          extLink: config['ext_link'] ?? '',
          isOriginal: config['is_original'] ?? false,
          activity:
              config['activity'] != null
                  ? WxSphActivity.fromJson(config['activity'])
                  : null,
          mentionedUsers:
              (config['mentioned_users'] as List?)
                  ?.map((user) => WxSphUser.fromJson(user))
                  .toList() ??
              [],
          isPrivate: config['is_private'] ?? false,
        );

      default:
        return null;
    }
  }

  /// 批量创建平台发布模型
  static List<BasePlatformPublishModel> createBatchFromAccountConfigs({
    required List<Map<String, dynamic>> selectedAccounts,
    required Map<String, Map<String, dynamic>> accountConfigs,
    File? video,
    List<File>? images,
    File? cover,
    String? defaultTitle,
    String? defaultDescription,
    List<String>? defaultTopics,
  }) {
    final List<BasePlatformPublishModel> models = [];

    for (final account in selectedAccounts) {
      final accountId = account['id'];
      final config = accountConfigs[accountId] ?? <String, dynamic>{};

      final model = createFromAccountConfig(
        account: account,
        config: config,
        video: video,
        images: images,
        cover: cover,
        defaultTitle: defaultTitle,
        defaultDescription: defaultDescription,
        defaultTopics: defaultTopics,
      );

      if (model != null) {
        models.add(model);
      }
    }

    return models;
  }

  /// 验证所有发布模型
  static String? validatePublishModels(List<BasePlatformPublishModel> models) {
    if (models.isEmpty) {
      return '没有选择发布账号';
    }

    for (final model in models) {
      final error = model.validate();
      if (error != null) {
        return error;
      }
    }

    return null; // 验证通过
  }

  /// 获取发布统计信息
  static Map<String, dynamic> getPublishStats(
    List<BasePlatformPublishModel> models,
  ) {
    final stats = <String, int>{};

    for (final model in models) {
      final platform = model.platform;
      stats[platform] = (stats[platform] ?? 0) + 1;
    }

    return {
      'total': models.length,
      'platforms': stats,
      'accounts':
          models
              .map(
                (m) => {
                  'platform': m.platform,
                  'nickname': m.account['nickname'],
                  'title': m.title,
                },
              )
              .toList(),
    };
  }

  /// 按平台分组发布模型
  static Map<String, List<BasePlatformPublishModel>> groupByPlatform(
    List<BasePlatformPublishModel> models,
  ) {
    final Map<String, List<BasePlatformPublishModel>> grouped = {};

    for (final model in models) {
      final platform = model.platform;
      if (!grouped.containsKey(platform)) {
        grouped[platform] = [];
      }
      grouped[platform]!.add(model);
    }

    return grouped;
  }

  /// 检查是否支持定时发布
  static bool supportsScheduledPublish(String platform) {
    switch (platform) {
      case 'douyin':
      case 'xhs':
      case 'kwai':
      case 'wx-sph':
        return true;
      default:
        return false;
    }
  }

  /// 检查是否支持位置功能
  static bool supportsLocation(String platform) {
    switch (platform) {
      case 'douyin':
      case 'xhs':
      case 'kwai':
      case 'wx-sph':
        return true;
      default:
        return false;
    }
  }

  /// 获取平台支持的最大标题长度
  static int getMaxTitleLength(String platform) {
    switch (platform) {
      case 'douyin':
        return 30;
      case 'xhs':
        return 20;
      case 'kwai':
        return 50;
      case 'wx-sph':
        return 100; // 视频号标题长度限制
      default:
        return 100;
    }
  }

  /// 获取平台支持的最大话题数量
  static int getMaxTopicsCount(String platform) {
    switch (platform) {
      case 'douyin':
        return 5;
      case 'xhs':
        return 10;
      case 'kwai':
        return 3;
      case 'wx-sph':
        return 5; // 视频号话题数量限制
      default:
        return 5;
    }
  }

  /// 获取平台特定的配置字段
  static List<String> getPlatformConfigFields(String platform) {
    switch (platform) {
      case 'douyin':
        return [
          'self_declare',
          'mentioned_users',
          'activity_id',
          'hotspot_id',
          'mix_id',
          'visibility_type',
        ];
      case 'xhs':
        return ['is_private', 'disable_comment'];
      case 'kwai':
        return ['visibility_type', 'allow_comment', 'allow_download'];
      case 'wx-sph':
        return [
          'short_title',
          'ext_link',
          'is_original',
          'activity',
          'mentioned_users',
          'is_private',
        ];
      default:
        return [];
    }
  }

  /// 获取平台的默认配置
  static Map<String, dynamic> getDefaultConfig(String platform) {
    switch (platform) {
      case 'douyin':
        return {
          'visibility_type': 0,
          'self_declare': null,
          'mentioned_users': <Map<String, String>>[],
          'activity_id': null,
          'hotspot_id': null,
          'mix_id': null,
        };
      case 'xhs':
        return {'is_private': false, 'disable_comment': false};
      case 'kwai':
        return {
          'visibility_type': 0,
          'allow_comment': true,
          'allow_download': true,
        };
      case 'wx-sph':
        return {
          'short_title': '',
          'ext_link': '',
          'is_original': false,
          'activity': null,
          'mentioned_users': <Map<String, dynamic>>[],
          'is_private': false,
        };
      default:
        return {};
    }
  }

  /// 合并配置（用户配置覆盖默认配置）
  static Map<String, dynamic> mergeConfig(
    String platform,
    Map<String, dynamic> userConfig,
  ) {
    final defaultConfig = getDefaultConfig(platform);
    final mergedConfig = Map<String, dynamic>.from(defaultConfig);

    // 用户配置覆盖默认配置
    userConfig.forEach((key, value) {
      if (value != null) {
        mergedConfig[key] = value;
      }
    });

    return mergedConfig;
  }

  /// 检查配置是否完整
  static bool isConfigComplete(String platform, Map<String, dynamic> config) {
    final requiredFields = _getRequiredFields(platform);

    for (final field in requiredFields) {
      if (!config.containsKey(field) || config[field] == null) {
        return false;
      }
    }

    return true;
  }

  /// 获取平台必需的配置字段
  static List<String> _getRequiredFields(String platform) {
    switch (platform) {
      case 'douyin':
        return ['visibility_type'];
      case 'xhs':
        return ['is_private'];
      case 'kwai':
        return ['visibility_type'];
      case 'wx-sph':
        return ['is_private']; // 视频号必需的配置字段
      default:
        return [];
    }
  }

  /// 获取平台显示名称
  static String getPlatformDisplayName(String platform) {
    switch (platform) {
      case 'douyin':
        return '抖音';
      case 'xhs':
        return '小红书';
      case 'kwai':
      case 'ks':
        return '快手';
      case 'wx-sph':
        return '微信视频号';
      case 'bilibili':
        return 'B站';
      default:
        return platform.toUpperCase();
    }
  }

  /// 获取平台图标
  static String getPlatformIcon(String platform) {
    switch (platform) {
      case 'douyin':
        return 'assets/icons/douyin.png';
      case 'xhs':
        return 'assets/icons/xiaohongshu.png';
      case 'kwai':
      case 'ks':
        return 'assets/icons/kuaishou.png';
      case 'wx-sph':
        return 'assets/icons/wechat.png';
      case 'bilibili':
        return 'assets/icons/bilibili.png';
      default:
        return 'assets/icons/default.png';
    }
  }

  /// 获取平台主题色
  static int getPlatformColor(String platform) {
    switch (platform) {
      case 'douyin':
        return 0xFF000000; // 黑色
      case 'xhs':
        return 0xFFFF2442; // 小红书红
      case 'kwai':
      case 'ks':
        return 0xFFFF6600; // 快手橙
      case 'wx-sph':
        return 0xFF07C160; // 微信绿
      case 'bilibili':
        return 0xFF00A1D6; // B站蓝
      default:
        return 0xFF6366F1; // 默认紫色
    }
  }
}
