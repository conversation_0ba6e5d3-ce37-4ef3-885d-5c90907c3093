import 'dart:io';

/// 基础平台发布模型
abstract class BasePlatformPublishModel {
  /// 账号信息
  final Map<String, dynamic> account;

  /// 平台类型
  final String platform;

  /// 基础内容
  final String? title;
  final String? description;
  final List<String>? topics;

  /// 媒体文件
  final File? video;
  final List<File>? images;
  final File? cover;

  /// 定时发布
  final bool isScheduled;
  final DateTime? scheduledTime;

  /// 位置信息
  final dynamic selectedLocation;

  BasePlatformPublishModel({
    required this.account,
    required this.platform,
    this.title,
    this.description,
    this.topics,
    this.video,
    this.images,
    this.cover,
    this.isScheduled = false,
    this.scheduledTime,
    this.selectedLocation,
  });

  /// 转换为发布参数Map
  Map<String, dynamic> toPublishParams();

  /// 验证参数
  String? validate();

  /// 获取平台特定配置
  Map<String, dynamic> getPlatformConfig();
}
