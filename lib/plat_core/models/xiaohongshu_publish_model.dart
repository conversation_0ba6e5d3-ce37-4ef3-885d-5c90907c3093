import 'dart:io';
import 'package:aitoearn_app/plat_core/models/base_platform_publish_model.dart';

/// 小红书发布模型
class XiaohongshuPublishModel extends BasePlatformPublishModel {
  /// 小红书特定配置
  final bool isPrivate; // 是否私密
  final bool disableComment; // 关闭评论

  XiaohongshuPublishModel({
    required super.account,
    super.title,
    super.description,
    super.topics,
    super.video,
    super.images,
    super.cover,
    super.isScheduled,
    super.scheduledTime,
    super.selectedLocation,
    this.isPrivate = false,
    this.disableComment = false,
  }) : super(platform: 'xhs');

  /// 从账号配置创建小红书发布模型
  factory XiaohongshuPublishModel.fromAccountConfig({
    required Map<String, dynamic> account,
    required Map<String, dynamic> config,
    File? video,
    List<File>? images,
    File? cover,
    String? defaultTitle,
    String? defaultDescription,
    List<String>? defaultTopics,
  }) {
    return XiaohongshuPublishModel(
      account: account,
      title:
          config['custom_title']?.isNotEmpty == true
              ? config['custom_title']
              : (config['title']?.isNotEmpty == true
                  ? config['title']
                  : defaultTitle),
      description:
          config['description']?.isNotEmpty == true
              ? config['description']
              : defaultDescription,
      topics:
          (config['topics'] as List<String>?)?.isNotEmpty == true
              ? config['topics'] as List<String>?
              : defaultTopics,
      video: video,
      images: images,
      cover: cover,
      isScheduled: config['is_scheduled'] as bool? ?? false,
      scheduledTime: config['scheduled_time'] as DateTime?,
      selectedLocation: config['selected_location'],
      isPrivate: config['is_private'] as bool? ?? false,
      disableComment: config['disable_comment'] as bool? ?? false,
    );
  }

  @override
  Map<String, dynamic> toPublishParams() {
    return {
      'account': account,
      'platform': platform,
      'title': title,
      'description': description,
      'topics': topics,
      'video': video,
      'images': images,
      'cover': cover,
      'is_scheduled': isScheduled,
      'scheduled_time': scheduledTime,
      'selected_location': selectedLocation,
      'platform_config': getPlatformConfig(),
    };
  }

  @override
  String? validate() {
    // 检查媒体文件
    if (video == null && (images == null || images!.isEmpty)) {
      return '账号 ${account['nickname']} 缺少媒体文件';
    }

    // 检查标题
    if (title == null || title!.isEmpty) {
      return '账号 ${account['nickname']} 缺少标题';
    }

    // 小红书特定验证
    if (title != null && title!.length > 20) {
      return '小红书标题不能超过20个字符';
    }

    return null;
  }

  @override
  Map<String, dynamic> getPlatformConfig() {
    return {'is_private': isPrivate, 'disable_comment': disableComment};
  }

  @override
  String toString() {
    return 'XiaohongshuPublishModel{account: ${account['nickname']}, title: $title, isPrivate: $isPrivate}';
  }
}
