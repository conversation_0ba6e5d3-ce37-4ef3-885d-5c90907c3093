import 'dart:io';
import 'package:aitoearn_app/plat_core/models/base_platform_publish_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_types.dart';

/// 视频号平台发布模型
class WxSphPlatformModel extends BasePlatformPublishModel {
  /// 短标题
  final String shortTitle;
  
  /// 扩展链接
  final String extLink;
  
  /// 是否声明原创
  final bool isOriginal;
  
  /// 参与的活动
  final WxSphActivity? activity;
  
  /// @的视频号用户列表
  final List<WxSphUser> mentionedUsers;
  
  /// 是否私密
  final bool isPrivate;

  WxSphPlatformModel({
    required super.account,
    super.title,
    super.description,
    super.topics,
    super.video,
    super.images,
    super.cover,
    super.isScheduled = false,
    super.scheduledTime,
    super.selectedLocation,
    this.shortTitle = '',
    this.extLink = '',
    this.isOriginal = false,
    this.activity,
    this.mentionedUsers = const [],
    this.isPrivate = false,
  }) : super(platform: 'wxSph');

  @override
  Map<String, dynamic> toPublishParams() {
    final params = <String, dynamic>{
      'platform': platform,
      'account': account,
      'title': title,
      'description': description,
      'topics': topics,
      'video': video?.path,
      'images': images?.map((img) => img.path).toList(),
      'cover': cover?.path,
      'isScheduled': isScheduled,
      'scheduledTime': scheduledTime?.toIso8601String(),
      'selectedLocation': selectedLocation,
    };
    
    // 添加视频号特定参数
    params.addAll(getPlatformConfig());
    
    return params;
  }

  @override
  String? validate() {
    // 基础验证
    if (video == null && (images == null || images!.isEmpty)) {
      return '请选择视频或图片';
    }
    
    // 视频号特定验证
    if (shortTitle.isNotEmpty && (shortTitle.length < 6 || shortTitle.length > 16)) {
      return '短标题长度应为6-16个字符';
    }
    
    if (mentionedUsers.length > 10) {
      return '@视频号用户不能超过10个';
    }
    
    return null;
  }

  @override
  Map<String, dynamic> getPlatformConfig() {
    return {
      'short_title': shortTitle,
      'ext_link': extLink,
      'is_original': isOriginal,
      'activity': activity?.toJson(),
      'mentioned_users': mentionedUsers.map((user) => user.toJson()).toList(),
      'is_private': isPrivate,
    };
  }

  /// 复制并修改
  WxSphPlatformModel copyWith({
    Map<String, dynamic>? account,
    String? title,
    String? description,
    List<String>? topics,
    File? video,
    List<File>? images,
    File? cover,
    bool? isScheduled,
    DateTime? scheduledTime,
    dynamic selectedLocation,
    String? shortTitle,
    String? extLink,
    bool? isOriginal,
    WxSphActivity? activity,
    List<WxSphUser>? mentionedUsers,
    bool? isPrivate,
  }) {
    return WxSphPlatformModel(
      account: account ?? this.account,
      title: title ?? this.title,
      description: description ?? this.description,
      topics: topics ?? this.topics,
      video: video ?? this.video,
      images: images ?? this.images,
      cover: cover ?? this.cover,
      isScheduled: isScheduled ?? this.isScheduled,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      selectedLocation: selectedLocation ?? this.selectedLocation,
      shortTitle: shortTitle ?? this.shortTitle,
      extLink: extLink ?? this.extLink,
      isOriginal: isOriginal ?? this.isOriginal,
      activity: activity ?? this.activity,
      mentionedUsers: mentionedUsers ?? this.mentionedUsers,
      isPrivate: isPrivate ?? this.isPrivate,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'platform': platform,
      'account': account,
      'title': title,
      'description': description,
      'topics': topics,
      'video': video?.path,
      'images': images?.map((img) => img.path).toList(),
      'cover': cover?.path,
      'isScheduled': isScheduled,
      'scheduledTime': scheduledTime?.toIso8601String(),
      'selectedLocation': selectedLocation,
      'short_title': shortTitle,
      'ext_link': extLink,
      'is_original': isOriginal,
      'activity': activity?.toJson(),
      'mentioned_users': mentionedUsers.map((user) => user.toJson()).toList(),
      'is_private': isPrivate,
    };
  }

  /// 从JSON创建实例
  factory WxSphPlatformModel.fromJson(Map<String, dynamic> json) {
    return WxSphPlatformModel(
      account: json['account'] ?? {},
      title: json['title'],
      description: json['description'],
      topics: (json['topics'] as List?)?.cast<String>(),
      video: json['video'] != null ? File(json['video']) : null,
      images: (json['images'] as List?)?.map((path) => File(path)).toList(),
      cover: json['cover'] != null ? File(json['cover']) : null,
      isScheduled: json['isScheduled'] ?? false,
      scheduledTime: json['scheduledTime'] != null 
          ? DateTime.parse(json['scheduledTime']) 
          : null,
      selectedLocation: json['selectedLocation'],
      shortTitle: json['short_title'] ?? '',
      extLink: json['ext_link'] ?? '',
      isOriginal: json['is_original'] ?? false,
      activity: json['activity'] != null 
          ? WxSphActivity.fromJson(json['activity']) 
          : null,
      mentionedUsers: (json['mentioned_users'] as List?)
          ?.map((user) => WxSphUser.fromJson(user))
          .toList() ?? [],
      isPrivate: json['is_private'] ?? false,
    );
  }

  /// 从配置创建实例（用于配置对话框）
  factory WxSphPlatformModel.fromConfig(Map<String, dynamic> config) {
    return WxSphPlatformModel(
      account: {},
      shortTitle: config['short_title'] ?? '',
      extLink: config['ext_link'] ?? '',
      isOriginal: config['is_original'] ?? false,
      activity: config['activity'] != null 
          ? WxSphActivity.fromJson(config['activity']) 
          : null,
      mentionedUsers: (config['mentioned_users'] as List?)
          ?.map((user) => WxSphUser.fromJson(user))
          .toList() ?? [],
      isPrivate: config['is_private'] ?? false,
    );
  }
}
