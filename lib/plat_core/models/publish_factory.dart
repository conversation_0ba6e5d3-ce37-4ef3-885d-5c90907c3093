import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/enum/pub_params_enum.dart';
import 'package:aitoearn_app/plat_core/models/base_platform_publish_model.dart';
import 'package:aitoearn_app/plat_core/models/douyin_publish_model.dart';
import 'package:aitoearn_app/plat_core/models/location_model.dart';
import 'package:aitoearn_app/plat_core/models/plat_form_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/models/xiaohongshu_publish_model.dart';
import 'package:aitoearn_app/plat_core/plat_manager.dart';
import 'package:aitoearn_app/plat_core/plats/plat_base.dart';
import 'package:get/get.dart';

/// 发布任务状态
enum PublishTaskStatus {
  waiting, // 等待中
  preparing, // 准备中
  uploading, // 上传中
  processing, // 处理中
  completed, // 已完成
  failed, // 失败
}

/// 发布任务状态扩展
extension PublishTaskStatusExtension on PublishTaskStatus {
  /// 获取状态的中文显示文本
  String get displayText {
    switch (this) {
      case PublishTaskStatus.waiting:
        return '等待中';
      case PublishTaskStatus.preparing:
        return '准备中';
      case PublishTaskStatus.uploading:
        return '上传中';
      case PublishTaskStatus.processing:
        return '处理中';
      case PublishTaskStatus.completed:
        return '发布成功';
      case PublishTaskStatus.failed:
        return '发布失败';
    }
  }

  /// 获取状态图标
  String get icon {
    switch (this) {
      case PublishTaskStatus.waiting:
        return '⏳';
      case PublishTaskStatus.preparing:
        return '🔄';
      case PublishTaskStatus.uploading:
        return '📤';
      case PublishTaskStatus.processing:
        return '⚙️';
      case PublishTaskStatus.completed:
        return '✅';
      case PublishTaskStatus.failed:
        return '❌';
    }
  }
}

/// 发布任务模型
class PublishTask {
  // 任务ID
  final String id;
  // 平台名称
  final String platform;
  // 平台实例
  final PlatBase platInstance;
  // 账号信息
  final Map<String, dynamic> account;
  // 发布参数
  final PublishParamsModel params;
  // 平台特定配置
  final Map<String, dynamic>? platformConfig;
  // 任务状态
  final Rx<PublishTaskStatus> status;
  // 进度 (0-100)
  final RxDouble progress;
  // 状态消息
  final RxString statusMessage;
  // 发布结果
  final Rx<PublishResponseModel?> result;
  // 错误信息
  final RxString errorMessage;
  // 开始时间
  final DateTime startTime;
  // 完成时间
  final Rx<DateTime?> completeTime;

  PublishTask({
    required this.id,
    required this.platform,
    required this.platInstance,
    required this.account,
    required this.params,
    this.platformConfig,
    PublishTaskStatus initialStatus = PublishTaskStatus.waiting,
  }) : status = Rx<PublishTaskStatus>(initialStatus),
       progress = RxDouble(0.0),
       statusMessage = RxString('等待发布'),
       result = Rx<PublishResponseModel?>(null),
       errorMessage = RxString(''),
       startTime = DateTime.now(),
       completeTime = Rx<DateTime?>(null);

  // 更新任务状态
  void updateStatus(PublishTaskStatus newStatus, {String? message}) {
    status.value = newStatus;
    if (message != null) {
      statusMessage.value = message;
    }

    // 如果任务完成或失败，设置完成时间
    if (newStatus == PublishTaskStatus.completed ||
        newStatus == PublishTaskStatus.failed) {
      completeTime.value = DateTime.now();
    }
  }

  // 更新进度
  void updateProgress(double newProgress, {String? message}) {
    progress.value = newProgress;
    if (message != null) {
      statusMessage.value = message;
    }
    LoggerUtil.i(
      'PublishTask.updateProgress: progress=${(newProgress * 100).toStringAsFixed(0)}%, message=$message',
    );
  }

  // 设置结果
  void setResult(PublishResponseModel publishResult) {
    result.value = publishResult;
    if (publishResult.isSuccess) {
      updateStatus(PublishTaskStatus.completed, message: '发布成功');
    } else {
      updateStatus(
        PublishTaskStatus.failed,
        message: publishResult.failMsg ?? '发布失败',
      );
      errorMessage.value = publishResult.failMsg ?? '未知错误';
    }
  }

  // 设置错误
  void setError(String error) {
    errorMessage.value = error;
    updateStatus(PublishTaskStatus.failed, message: error);
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'platform': platform,
      'account': account,
      'status': status.value.toString(),
      'progress': progress.value,
      'statusMessage': statusMessage.value,
      'errorMessage': errorMessage.value,
      'startTime': startTime.toIso8601String(),
      'completeTime': completeTime.value?.toIso8601String(),
      'result': result.value?.toJson(),
    };
  }
}

/// 发布工厂类
class PublishFactory extends GetxController {
  // 单例模式
  static final PublishFactory _instance = PublishFactory._internal();
  factory PublishFactory() => _instance;
  PublishFactory._internal() {
    // 确保状态正确初始化
    _initializeState();
  }

  // 发布任务列表
  final RxList<PublishTask> tasks = <PublishTask>[].obs;

  // 是否正在发布
  final RxBool isPublishing = false.obs;

  // 总进度
  final RxDouble totalProgress = 0.0.obs;

  // 当前进度回调
  Function(double progress, String message)? _currentProgressCallback;

  // 初始化状态
  void _initializeState() {
    isPublishing.value = false;
    totalProgress.value = 0.0;
    tasks.clear();
    _currentProgressCallback = null;
    LoggerUtil.i('PublishFactory 状态已初始化');
  }

  // 创建发布任务
  PublishTask createTask({
    required String platform,
    required Map<String, dynamic> account,
    required PublishParamsModel params,
  }) {
    // 从账号信息创建平台模型
    final platformModel = PlatformModel(
      id: account['id'],
      platType: platform,
      cookie: account['accessToken'],
    );

    // 获取平台实例
    final platInstance = PlatManager.getPlatInstanceByName(
      platform,
      platformModel,
    );

    if (platInstance == null) {
      throw Exception('不支持的平台: $platform');
    }

    // 创建任务ID
    final taskId =
        'task_${DateTime.now().millisecondsSinceEpoch}_${tasks.length}';

    // 创建任务
    final task = PublishTask(
      id: taskId,
      platform: platform,
      platInstance: platInstance,
      account: account,
      params: params,
    );

    // 添加到任务列表
    tasks.add(task);

    return task;
  }

  // 从通用参数创建平台特定的发布参数
  PublishParamsModel createPublishParams({
    required String title,
    required String description,
    required List<String> tags,
    required Map<String, dynamic>? locationInfo,
    required File? video,
    required List<File>? images,
    required File? cover,
    bool isScheduled = false,
    DateTime? scheduledTime,
    VisibleTypeEnum visibleType = VisibleTypeEnum.public,
  }) {
    // 处理位置信息
    LocationModel? location;
    if (locationInfo != null && locationInfo.isNotEmpty) {
      location = LocationModel();
      location.id = locationInfo['id']?.toString() ?? '';
      location.name = locationInfo['name']?.toString() ?? '';
      location.longitude =
          double.tryParse(locationInfo['longitude']?.toString() ?? '0') ?? 0;
      location.latitude =
          double.tryParse(locationInfo['latitude']?.toString() ?? '0') ?? 0;
      location.simpleAddress = locationInfo['address']?.toString() ?? '';
      location.city = locationInfo['city']?.toString() ?? '';
      // 其他可选字段
      location.poiType = null; // 小红书特有字段，默认为null
    }

    // 处理定时发布
    num? timingTime;
    if (isScheduled && scheduledTime != null) {
      timingTime = scheduledTime.millisecondsSinceEpoch;
    }

    // 确保有封面
    File actualCover;
    if (cover != null) {
      actualCover = cover;
    } else if (video != null) {
      // 如果没有提供封面但有视频，应该生成视频封面
      // 这里简单处理，实际应该调用视频缩略图生成
      actualCover = video;
    } else if (images != null && images.isNotEmpty) {
      // 如果是图文，使用第一张图作为封面
      actualCover = images.first;
    } else {
      throw Exception('必须提供封面、视频或图片');
    }

    // 创建发布参数
    return PublishParamsModel(
      actualCover,
      title: title,
      desc: description,
      topics: tags,
      video: video,
      images: images,
      location: location,
      visibleType: visibleType,
      timingTime: timingTime,
      progressCallback: (progress, message) {
        // 进度回调函数会在具体任务执行时使用
        LoggerUtil.i('发布进度: $progress%, $message');
      },
    );
  }

  // 执行发布任务
  Future<void> executeTask(PublishTask task) async {
    try {
      // 更新任务状态为准备中
      task.updateStatus(PublishTaskStatus.preparing, message: '准备发布内容');

      // 更新任务状态为上传中
      task.updateStatus(PublishTaskStatus.uploading, message: '上传内容中');

      // 设置进度回调
      task.params.progressCallback = (int progress, String? message) {
        // progress 是 0-100 的值，需要转换为 0-1 的值
        final normalizedProgress = progress / 100.0;
        task.updateProgress(
          normalizedProgress,
          message: message ?? '上传中 $progress%',
        );

        LoggerUtil.i(
          '任务进度回调: progress=$progress%, normalizedProgress=${(normalizedProgress * 100).toStringAsFixed(0)}%, message=$message',
        );

        // 直接调用外部进度回调，不依赖监听器
        if (_currentProgressCallback != null) {
          // 计算总进度
          final taskIndex = tasks.indexOf(task);
          final totalTaskProgress =
              (taskIndex + normalizedProgress) / tasks.length;
          totalProgress.value = totalTaskProgress;

          LoggerUtil.i(
            '直接调用外部进度回调: 总进度=${(totalTaskProgress * 100).toStringAsFixed(0)}%',
          );
          _currentProgressCallback!(
            totalTaskProgress,
            message ?? '上传中 $progress%',
          );
        }
      };

      // 根据内容类型选择发布方法
      PublishResponseModel response;
      if (task.params.video != null) {
        // 发布视频
        task.updateStatus(PublishTaskStatus.uploading, message: '上传视频中');
        response = await _publishVideo(task);
      } else if (task.params.images != null && task.params.images!.isNotEmpty) {
        // 发布图文
        task.updateStatus(PublishTaskStatus.uploading, message: '上传图片中');
        response = await _publishImgText(task);
      } else {
        throw Exception('没有可发布的内容');
      }

      // 设置任务结果
      task.setResult(response);
    } catch (e) {
      // 处理错误
      LoggerUtil.e('发布任务执行失败: ${e.toString()}');
      task.setError(e.toString());
    }
  }

  // 发布视频
  Future<PublishResponseModel> _publishVideo(PublishTask task) async {
    try {
      // 获取平台特定配置
      final platformConfig = task.platformConfig ?? <String, dynamic>{};

      LoggerUtil.i('发布视频，平台: ${task.platform}, 配置: $platformConfig');

      // 优先使用新的平台特定发布方法
      final response = await task.platInstance.publishVideoWithPlatformConfig(
        task.params,
        platformConfig,
      );

      // 记录发布结果
      if (response.isSuccess) {
        LoggerUtil.i('【${task.platform}】视频发布成功: ${response.dataId ?? ''}');
      } else {
        LoggerUtil.e('【${task.platform}】视频发布失败: ${response.failMsg ?? '未知错误'}');
      }

      return response;
    } catch (e) {
      LoggerUtil.e('【${task.platform}】视频发布异常: ${e.toString()}');
      return PublishResponseModel(false, failMsg: '视频发布失败: ${e.toString()}');
    }
  }

  // 发布图文
  Future<PublishResponseModel> _publishImgText(PublishTask task) async {
    try {
      // 调用平台实例的发布方法
      final response = await task.platInstance.publishImgText(task.params);

      // 记录发布结果
      if (response.isSuccess) {
        LoggerUtil.i('【${task.platform}】图文发布成功: ${response.dataId ?? ''}');
      } else {
        LoggerUtil.e('【${task.platform}】图文发布失败: ${response.failMsg ?? '未知错误'}');
      }

      return response;
    } catch (e) {
      LoggerUtil.e('【${task.platform}】图文发布异常: ${e.toString()}');
      return PublishResponseModel(false, failMsg: '图文发布失败: ${e.toString()}');
    }
  }

  // 批量发布到多个平台
  Future<List<PublishTask>> publishToMultiplePlatforms({
    required List<Map<String, dynamic>> accounts,
    required String title,
    required String description,
    required List<String> tags,
    required Map<String, dynamic>? locationInfo,
    required File? video,
    required List<File>? images,
    required File? cover,
    bool isScheduled = false,
    DateTime? scheduledTime,
    VisibleTypeEnum visibleType = VisibleTypeEnum.public,
    Function(double progress, String message)? progressCallback, // 添加进度回调
  }) async {
    if (accounts.isEmpty) {
      throw Exception('没有选择发布账号');
    }

    if ((video == null && (images == null || images.isEmpty))) {
      throw Exception('必须提供视频或图片');
    }

    // 设置发布状态
    isPublishing.value = true;
    totalProgress.value = 0.0;

    // 清空之前的任务
    tasks.clear();

    // 创建发布任务列表
    final List<PublishTask> newTasks = [];

    // 为每个账号创建任务
    for (final account in accounts) {
      try {
        // 创建通用发布参数
        final params = createPublishParams(
          title: title,
          description: description,
          tags: tags,
          locationInfo: locationInfo,
          video: video,
          images: images,
          cover: cover,
          isScheduled: isScheduled,
          scheduledTime: scheduledTime,
          visibleType: visibleType,
        );

        // 创建任务
        final task = createTask(
          platform: account['platform'],
          account: account,
          params: params,
        );

        // 添加进度监听器
        ever(task.progress, (double progress) {
          _updateOverallProgress(newTasks);
          if (progressCallback != null) {
            progressCallback(totalProgress.value, task.statusMessage.value);
          }
        });

        // 添加状态监听器
        ever(task.statusMessage, (String message) {
          if (progressCallback != null) {
            progressCallback(totalProgress.value, message);
          }
        });

        newTasks.add(task);
      } catch (e) {
        LoggerUtil.e('为账号创建任务失败: ${e.toString()}');
        // 创建一个失败的任务
        final failedTask = PublishTask(
          id: 'failed_${DateTime.now().millisecondsSinceEpoch}',
          platform: account['platform'] ?? 'unknown',
          platInstance: PlatBase(PlatformModel()), // 创建一个空的平台实例
          account: account,
          params: PublishParamsModel(File('')), // 创建一个空的参数
        );
        failedTask.setError('创建任务失败: ${e.toString()}');
        newTasks.add(failedTask);
      }
    }

    // 执行所有任务
    int completedTasks = 0;
    for (final task in newTasks) {
      if (task.status.value != PublishTaskStatus.failed) {
        await executeTask(task);
      }

      // 更新总进度
      completedTasks++;
      totalProgress.value = completedTasks / newTasks.length;

      // 调用进度回调
      if (progressCallback != null) {
        progressCallback(totalProgress.value, task.statusMessage.value);
      }
    }

    // 发布完成
    isPublishing.value = false;

    return newTasks;
  }

  // 更新总体进度
  void _updateOverallProgress(List<PublishTask> tasks) {
    if (tasks.isEmpty) return;

    // 计算总体进度
    double totalProgressValue = 0;
    for (final task in tasks) {
      totalProgressValue += task.progress.value;
    }

    // 更新总进度
    totalProgress.value = totalProgressValue / tasks.length;
  }

  // 获取发布结果统计
  Map<String, dynamic> getPublishResultStats() {
    int total = tasks.length;
    int success =
        tasks
            .where(
              (task) =>
                  task.status.value == PublishTaskStatus.completed &&
                  task.result.value?.isSuccess == true,
            )
            .length;
    int failed =
        tasks
            .where(
              (task) =>
                  task.status.value == PublishTaskStatus.failed ||
                  (task.result.value != null &&
                      task.result.value?.isSuccess == false),
            )
            .length;
    int inProgress = total - success - failed;

    return {
      'total': total,
      'success': success,
      'failed': failed,
      'inProgress': inProgress,
      'completedPercent': total > 0 ? (success + failed) / total : 0.0,
    };
  }

  // 使用平台模型发布
  Future<List<PublishTask>> publishWithPlatformModels(
    List<BasePlatformPublishModel> publishModels, {
    Function(double progress, String message)? progressCallback,
  }) async {
    if (isPublishing.value) {
      throw Exception('已有发布任务正在进行中');
    }

    try {
      isPublishing.value = true;
      totalProgress.value = 0.0;
      _currentProgressCallback = progressCallback; // 保存进度回调

      final List<PublishTask> newTasks = [];

      // 为每个平台模型创建发布任务
      for (final publishModel in publishModels) {
        try {
          // 从账号信息创建平台模型
          final platformModel = _createPlatformModelFromAccount(
            publishModel.account,
          );

          // 获取平台实例
          final platInstance = PlatManager.getPlatInstanceByName(
            publishModel.platform,
            platformModel,
          );
          if (platInstance == null) {
            throw Exception('不支持的平台: ${publishModel.platform}');
          }

          // 转换为传统的发布参数模型
          final publishParams = _convertModelToPublishParams(publishModel);

          // 创建发布任务
          final task = PublishTask(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            platform: publishModel.platform,
            platInstance: platInstance,
            account: publishModel.account,
            params: publishParams,
            platformConfig: publishModel.getPlatformConfig(), // 传递平台特定配置
          );

          newTasks.add(task);
          tasks.add(task);

          LoggerUtil.i('创建发布任务: ${publishModel.toString()}');
        } catch (e) {
          LoggerUtil.e('创建发布任务失败: ${e.toString()}');

          // 创建失败任务
          final failedTask = PublishTask(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            platform: publishModel.platform,
            platInstance: PlatBase(PlatformModel()),
            account: publishModel.account,
            params: PublishParamsModel(File('')),
          );
          failedTask.setError('创建任务失败: ${e.toString()}');
          newTasks.add(failedTask);
          tasks.add(failedTask);
        }
      }

      // 执行所有任务
      for (int i = 0; i < newTasks.length; i++) {
        final task = newTasks[i];
        final taskIndex = i;

        if (task.status.value != PublishTaskStatus.failed) {
          // 设置任务进度监听 - 在执行前设置
          if (progressCallback != null) {
            // 使用 ever 监听任务进度变化
            LoggerUtil.i('设置任务${taskIndex + 1}的进度监听器');
            ever(task.progress, (double taskProgress) {
              // 计算总进度：已完成任务 + 当前任务进度
              final totalTaskProgress =
                  (taskIndex + taskProgress) / newTasks.length;
              totalProgress.value = totalTaskProgress;

              LoggerUtil.i(
                'ever监听器触发 - 任务进度更新: 任务${taskIndex + 1}/${newTasks.length}, 任务进度=${(taskProgress * 100).toStringAsFixed(0)}%, 总进度=${(totalTaskProgress * 100).toStringAsFixed(0)}%',
              );

              progressCallback(totalTaskProgress, task.statusMessage.value);
            });

            // 使用 ever 监听状态消息变化
            ever(task.statusMessage, (String message) {
              progressCallback(totalProgress.value, message);
            });
          }

          // 执行任务
          LoggerUtil.i(
            '开始执行任务 ${taskIndex + 1}/${newTasks.length}: ${task.platform}',
          );
          await executeTask(task);
          LoggerUtil.i('任务 ${taskIndex + 1} 执行完成');
        }

        // 更新总进度为当前任务完成
        final completedProgress = (taskIndex + 1) / newTasks.length;
        totalProgress.value = completedProgress;

        // 调用进度回调
        if (progressCallback != null) {
          progressCallback(
            completedProgress,
            '任务 ${taskIndex + 1}/${newTasks.length} 完成',
          );
        }
      }

      return newTasks;
    } catch (e) {
      LoggerUtil.e('发布过程中发生错误: ${e.toString()}');
      rethrow;
    } finally {
      // 确保无论成功还是失败都重置发布状态
      isPublishing.value = false;
      _currentProgressCallback = null; // 清理进度回调
    }
  }

  // 将平台模型转换为传统的发布参数模型
  PublishParamsModel _convertModelToPublishParams(
    BasePlatformPublishModel publishModel,
  ) {
    // 验证媒体文件
    if (publishModel.video == null &&
        (publishModel.images == null || publishModel.images!.isEmpty)) {
      throw Exception('发布内容必须包含视频或图片');
    }

    // 确保cover不为空，如果为空则使用一个默认的空文件
    File coverFile;
    if (publishModel.cover != null && publishModel.cover!.existsSync()) {
      coverFile = publishModel.cover!;
    } else {
      // 创建一个临时的空文件作为封面
      coverFile = File('');
    }

    final publishParams = PublishParamsModel(
      coverFile,
      title: publishModel.title,
      desc: publishModel.description,
      topics: publishModel.topics,
      images: publishModel.images,
      video: publishModel.video,
      visibleType: _convertVisibilityFromModel(publishModel),
      location: _convertLocationFromModel(publishModel),
      timingTime: publishModel.scheduledTime?.millisecondsSinceEpoch,
    );

    LoggerUtil.i(
      '转换发布参数: video=${publishModel.video?.path}, images=${publishModel.images?.length}, cover=${coverFile.path}',
    );

    return publishParams;
  }

  // 从模型转换位置信息
  LocationModel? _convertLocationFromModel(
    BasePlatformPublishModel publishModel,
  ) {
    final selectedLocation = publishModel.selectedLocation;
    if (selectedLocation == null) return null;

    final locationModel = LocationModel();

    switch (publishModel.platform) {
      case 'douyin':
        locationModel.name = selectedLocation.address ?? '';
        locationModel.id = selectedLocation.poiId ?? '';
        locationModel.latitude = selectedLocation.latitude ?? 0;
        locationModel.longitude = selectedLocation.longitude ?? 0;
        locationModel.simpleAddress = selectedLocation.address ?? '';
        locationModel.city =
            selectedLocation.cityName ?? ''; // 使用 cityName 而不是 cityCode
        break;
      case 'xhs':
        locationModel.name = selectedLocation.poiName ?? '';
        locationModel.id = selectedLocation.poiId ?? '';
        locationModel.latitude = selectedLocation.latitude ?? 0;
        locationModel.longitude = selectedLocation.longitude ?? 0;
        locationModel.simpleAddress = selectedLocation.poiName ?? '';
        locationModel.poiType = selectedLocation.poiType;
        locationModel.city = '';
        break;
      default:
        return null;
    }

    return locationModel;
  }

  // 从模型转换可见性
  VisibleTypeEnum _convertVisibilityFromModel(
    BasePlatformPublishModel publishModel,
  ) {
    switch (publishModel.platform) {
      case 'douyin':
        final douyinModel = publishModel as DouyinPublishModel;
        switch (douyinModel.visibilityType) {
          case 0:
            return VisibleTypeEnum.public;
          case 1:
            return VisibleTypeEnum.private;
          case 2:
            return VisibleTypeEnum.priend;
          default:
            return VisibleTypeEnum.public;
        }
      case 'xhs':
        final xhsModel = publishModel as XiaohongshuPublishModel;
        return xhsModel.isPrivate
            ? VisibleTypeEnum.private
            : VisibleTypeEnum.public;
      default:
        return VisibleTypeEnum.public;
    }
  }

  // 从账号信息创建平台模型
  PlatformModel _createPlatformModelFromAccount(Map<String, dynamic> account) {
    final platform = account['platform'] ?? '';
    final accountId = account['id'] ?? '';
    final cookie = account['accessToken'] ?? account['cookie'] ?? '';

    return PlatformModel(id: accountId, platType: platform, cookie: cookie);
  }

  // 清除所有任务
  void clearTasks() {
    tasks.clear();
    totalProgress.value = 0.0;
    isPublishing.value = false;
  }
}
