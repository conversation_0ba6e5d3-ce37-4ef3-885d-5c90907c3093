import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data' as typed_data;
import 'dart:ui' as ui;

import 'package:xml/xml.dart';
import 'package:xml2json/xml2json.dart';

/// 包含文件大小和所有分片信息
class FilePartInfo {
  final int fileSize; // 文件大小
  final List<typed_data.Uint8List> parts; // 分片信息

  FilePartInfo({required this.fileSize, required this.parts});
}

class ImageSize {
  late int width;
  late int height;

  ImageSize(this.width, this.height);
}

abstract class PlatCommonUtils {
  // 获取图片宽高
  static Future<ImageSize> getImageSizeFromUint8ListSync(
    typed_data.Uint8List imageData,
  ) async {
    final Completer<ImageSize> completer = Completer();

    ui.decodeImageFromList(imageData, (ui.Image img) {
      // 当图片解码完成时，将图片宽高值传递给 Completer
      completer.complete(ImageSize(img.width, img.height));
    });

    // 等待 Completer 返回的结果
    return completer.future;
  }

  /// 判断字符串是否是Json字符串
  static bool isJsonString(String str) {
    if (str.isNotEmpty) {
      try {
        final dynamic obj = jsonDecode(str);
        // 检查是否为对象或数组
        return obj is Map || obj is List;
      } catch (e) {
        // 解析失败，不是 JSON 字符串
        return false;
      }
    }
    // 空字符串返回 false
    return false;
  }

  /// 转换xml为json对象
  static Future<Map<String, dynamic>> xml2json(String xml) async {
    final Xml2Json xml2json = Xml2Json();
    try {
      // 将 XML 转换为 JSON 字符串
      xml2json.parse(xml);
      final String jsonString = xml2json.toParker();
      // 将 JSON 字符串转换为 Map 对象
      final Map<String, dynamic> jsonObject = jsonDecode(jsonString);
      return jsonObject;
    } catch (e) {
      throw Exception('解析XML失败: $e');
    }
  }

  /// 将 JSON 转换为 XML
  /// [json] 是输入的 JSON 数据
  /// [rootName] 是最外层根节点的名称
  static String jsonToXml(dynamic json, {String rootName = 'root'}) {
    try {
      final builder = XmlBuilder();
      builder.element(
        rootName,
        nest: () {
          _buildXmlFromJson(builder, json);
        },
      );

      // 返回格式化后的 XML 字符串
      return builder.buildDocument().toXmlString(pretty: true);
    } catch (e) {
      throw Exception('JSON 转 XML 失败: $e');
    }
  }

  /// 递归构建 XML 节点
  static void _buildXmlFromJson(XmlBuilder builder, dynamic json) {
    if (json is Map<String, dynamic>) {
      json.forEach((key, value) {
        builder.element(
          key,
          nest: () {
            _buildXmlFromJson(builder, value);
          },
        );
      });
    } else if (json is List) {
      // 如果是 List，依次处理每个元素
      for (final item in json) {
        builder.element(
          'item',
          nest: () {
            _buildXmlFromJson(builder, item);
          },
        );
      }
    } else {
      // 基本值直接作为文本节点
      builder.text(json.toString());
    }
  }

  /// 获取文件大小及分片信息，并直接将文件分片
  /// [file] 是 Dart 的 File 对象
  /// [blockSize] 是分片的块大小
  static Future<FilePartInfo> getFilePartInfo(File file, int blockSize) async {
    try {
      // 获取文件大小
      final int fileSize = await file.length();

      // 读取
      final RandomAccessFile raf = await file.open();

      // 分片信息列表
      final List<typed_data.Uint8List> parts = [];
      try {
        for (int i = 0; i < (fileSize / blockSize).ceil(); i++) {
          final int start = i * blockSize; // 当前分片起始位置
          final int end =
              (i == (fileSize / blockSize).ceil() - 1)
                  ? fileSize // 最后一片的结束位置为文件大小
                  : (start + blockSize); // 其他分片的结束位置为块大小的倍数

          // 移动到分片起始位置并读取数据
          await raf.setPosition(start);
          var buffer = await raf.read(end - start);

          // 添加分片信息
          parts.add(buffer);
        }
      } finally {
        // 确保文件被关闭
        await raf.close();
      }

      return FilePartInfo(fileSize: fileSize, parts: parts);
    } catch (err) {
      throw Exception('获取分片信息失败, 失败原因: $err');
    }
  }
}
