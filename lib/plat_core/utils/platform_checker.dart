import 'dart:io';
import 'package:aitoearn_app/config/logger.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

/// 平台检查工具类
class PlatformChecker {
  // 平台标识常量
  static const String DOUYIN = 'douyin';
  static const String KWAI = 'kwai';
  static const String WX_SPH = 'wxSph';
  static const String XHS = 'xhs';
  static const String DPP = 'dpp';
  
  /// 检查当前运行平台是否为Android
  static bool isAndroid() {
    return !kIsWeb && Platform.isAndroid;
  }
  
  /// 检查当前运行平台是否为iOS
  static bool isIOS() {
    return !kIsWeb && Platform.isIOS;
  }
  
  /// 检查当前运行平台是否为移动平台（Android或iOS）
  static bool isMobilePlatform() {
    return !kIsWeb && (Platform.isAndroid || Platform.isIOS);
  }
  
  /// 检查当前运行平台是否为桌面平台（Windows、macOS或Linux）
  static bool isDesktopPlatform() {
    return !kIsWeb && (Platform.isWindows || Platform.isMacOS || Platform.isLinux);
  }
  
  /// 检查当前运行平台是否为Web
  static bool isWebPlatform() {
    return kIsWeb;
  }
  
  /// 获取当前运行平台的名称
  static String getPlatformName() {
    if (kIsWeb) return 'Web';
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    if (Platform.isFuchsia) return 'Fuchsia';
    return 'Unknown';
  }
  
  /// 检查特定功能是否支持当前平台
  /// [featureName] 功能名称
  /// [supportedPlatforms] 支持的平台列表
  /// [throwException] 如果不支持，是否抛出异常
  /// 返回是否支持
  static bool checkFeatureSupport(
    String featureName,
    List<String> supportedPlatforms, {
    bool throwException = false,
  }) {
    final currentPlatform = getPlatformName();
    final isSupported = supportedPlatforms.contains(currentPlatform);
    
    if (!isSupported) {
      final message = '$featureName 功能仅支持以下平台: ${supportedPlatforms.join(', ')}，当前平台: $currentPlatform';
      LoggerUtil.w(message);
      
      if (throwException) {
        throw UnsupportedError(message);
      }
    }
    
    return isSupported;
  }
  
  /// 检查微信视频号功能是否支持当前平台
  /// [throwException] 如果不支持，是否抛出异常
  /// 返回是否支持
  static bool checkWxSphSupport({bool throwException = false}) {
    return checkFeatureSupport(
      '微信视频号',
      ['Android', 'iOS'],
      throwException: throwException,
    );
  }
} 