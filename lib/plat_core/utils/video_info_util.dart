import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:ffmpeg_kit_flutter_minimal/ffmpeg_kit_flutter_minimal.dart';
import 'package:mime/mime.dart';

class VideoInfo {
  late StreamInformation videoStream;
  late StreamInformation audioStream;

  VideoInfo(this.videoStream, this.audioStream);
}

class VideoInfoUtil {
  /// 获取文件信息
  static Future<VideoInfo> getVideoInfo(File file) async {
    try {
      // 检查平台支持
      PlatformChecker.checkFeatureSupport('FFmpeg视频分析', [
        'Android',
        'iOS',
      ], throwException: true);

      LoggerUtil.i('开始获取文件信息, 文件: ${file.path}');

      // 检查文件是否存在
      if (!await file.exists()) {
        LoggerUtil.e('文件不存在: ${file.path}');
        throw Exception('获取文件信息失败,失败原因:文件不存在');
      }

      // 检查文件是否可读
      try {
        await file.open(); // 尝试打开文件
      } catch (e) {
        LoggerUtil.e('文件不可读: $e');
        throw Exception('获取文件信息失败,失败原因:文件不可读');
      }

      // 检查是否为文件
      final fileStat = await file.stat();
      if (fileStat.type != FileSystemEntityType.file) {
        LoggerUtil.e('路径不是文件: ${file.path}');
        throw Exception('获取文件信息失败,失败原因:路径不是文件');
      }

      // 获取文件 MIME 类型
      final mimeType = lookupMimeType(file.path);
      LoggerUtil.i('文件 MimeType: $mimeType');

      if (mimeType == null || !mimeType.contains('video')) {
        LoggerUtil.e('不支持的文件格式: $mimeType');
        throw Exception('获取文件信息失败,失败原因:不支持的文件格式');
      }

      // 获取视频文件信息
      LoggerUtil.i('开始获取视频文件信息...');
      LoggerUtil.i('要分析的文件路径: ${file.path}');

      // 调用 FFprobe 获取文件的媒体信息
      final session = await FFprobeKit.getMediaInformation(file.path);
      final mediaInfo = session.getMediaInformation();

      if (mediaInfo == null || mediaInfo.getStreams().isEmpty) {
        LoggerUtil.e('无法获取媒体信息或媒体流为空');
        throw Exception('获取文件信息失败,失败原因:无法解析媒体流');
      }

      // 解析视频信息
      StreamInformation? videoStream;
      StreamInformation? audioStream;

      try {
        videoStream = mediaInfo.getStreams().firstWhere(
          (stream) => stream.getType() == 'video',
        );
      } catch (e) {
        LoggerUtil.e('未找到视频流: $e');
        throw Exception('获取文件信息失败,失败原因:未找到视频流');
      }

      try {
        // 解析音频信息
        audioStream = mediaInfo.getStreams().firstWhere(
          (stream) => stream.getType() == 'audio',
        );
      } catch (e) {
        LoggerUtil.w('未找到音频流: $e，将使用空音频流');
        // 创建一个空的音频流信息，避免空指针异常
        audioStream = videoStream; // 临时解决方案，实际应用中应该创建一个有效的空音频流
      }

      return VideoInfo(videoStream, audioStream);
    } catch (e) {
      LoggerUtil.e('获取文件信息时发生错误: $e');
      throw Exception('获取文件信息失败,失败原因: $e');
    }
  }
}
