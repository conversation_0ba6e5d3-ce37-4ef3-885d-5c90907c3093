import 'dart:convert';
import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_types.dart'
    hide DouyinCreatorListResponse;
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_utils.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_creator_list_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_poi_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/network/douyin_content_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/network/douyin_http_client.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/network/douyin_poi_service.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';

class DouyinService {
  // HTTP 客户端
  final DouyinHttpClient _httpClient = DouyinHttpClient();

  // 内容服务类实例
  final DouyinContentService _contentService = DouyinContentService();
  
  // POI服务类实例
  final DouyinPoiService _poiService = DouyinPoiService();

  // 保留这个getter，兼容历史代码
  String get defaultUserAgent => _httpClient.defaultUserAgent;

  final String loginUrl = 'https://creator.douyin.com/';
  final String getUserInfoUrl =
      'https://creator.douyin.com/web/api/media/user/info/';
  final String getDashboardUrl =
      'https://creator.douyin.com/janus/douyin/creator/data/overview/dashboard';
  final String getMixListUrl = 'https://creator.douyin.com/web/api/mix/list/';
  final String getUploadAuthUrl =
      'https://creator.douyin.com/web/api/media/upload/auth/v5/';
  final String publishUrl =
      'https://creator.douyin.com/web/api/media/aweme/create/';
  final String publishUrlV2 =
      'https://creator.douyin.com/web/api/media/aweme/create_v2/';
  final String getCsrfTokenUrl =
      'https://creator.douyin.com/web/api/media/anchor/search';

  // 增加最大重试次数
  final int maxRetries = 3;

  // 授权登录
  Future<Map<String, dynamic>> loginOrView(
    String authModel, {
    String? cookies,
  }) async {
    try {
      if (authModel == 'view' && cookies != null) {
        // 检查登录状态
        final loginStatus = await checkLoginStatus(cookies);
        if (loginStatus) {
          // 获取用户信息
          final userInfo = await getUserInfo(cookies);
          return {
            'success': true,
            'data': {
              'cookie': cookies,
              'userInfo': userInfo,
              'localStorage': '', // 暂不支持localStorage，Flutter Web可能需要
            },
          };
        }
      }

      // 需要登录授权
      // 在Flutter中使用WebView来实现登录
      // 这里需要实现一个自定义的WebView登录流程
      // 以下是一个占位示例，实际实现需要根据项目需求调整
      LoggerUtil.i('抖音登录需要通过WebView实现，请集成实际的登录流程');

      // 假设登录成功并获取到cookies
      const cookieParam = '示例cookie; 实际需要通过WebView获取';

      if (cookieParam.isNotEmpty) {
        // 获取用户信息
        try {
          final userInfo = await getUserInfo(cookieParam);
          return {
            'success': true,
            'data': {
              'cookie': cookieParam,
              'userInfo': userInfo,
              'localStorage': '',
            },
          };
        } catch (e) {
          return {'success': false, 'error': '获取用户信息失败: $e'};
        }
      } else {
        return {'success': false, 'error': '授权失败，未获取到cookie'};
      }
    } catch (e) {
      LoggerUtil.e('授权登录失败: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // 检查登录状态
  Future<bool> checkLoginStatus(String cookies, {String? proxy}) async {
    try {
      final res = await _request(
        getUserInfoUrl,
        method: 'GET',
        headers: {'Cookie': cookies},
        proxy: proxy,
      );

      return res['status_code'] == 0;
    } catch (e) {
      LoggerUtil.e('检查登录状态失败: $e');
      return false;
    }
  }

  // 获取用户信息
  Future<DouyinUserInfo> getUserInfo(String cookies) async {
    try {
      final res = await _request(
        getUserInfoUrl,
        method: 'GET',
        headers: {'Cookie': cookies},
        proxy: null,
      );

      if (res['status_code'] != 0) {
        throw Exception(res['status_msg'] ?? '获取用户信息失败');
      }

      return DouyinUserInfo(
        uid: res['user']['sec_uid'] ?? '',
        authorId:
            res['user']['unique_id'] != ''
                ? res['user']['unique_id']
                : res['user']['uid'],
        nickname: res['user']['nickname'] ?? '',
        avatar: res['user']['avatar_thumb']['url_list'][0] ?? '',
        fansCount: res['user']['follower_count'] ?? 0,
      );
    } catch (e) {
      LoggerUtil.e('获取用户信息失败: $e');
      rethrow;
    }
  }

  // 获取用户Uid
  Future<String> getUserUid(String cookieString) async {
    try {
      final userInfoRes = await _request(
        getUserInfoUrl,
        method: 'GET',
        headers: {'Cookie': cookieString},
        proxy: null,
      );

      if (userInfoRes['status_code'] != 0) {
        throw Exception(userInfoRes['status_msg'] ?? '获取用户Uid失败');
      }

      return userInfoRes['user']['uid'];
    } catch (e) {
      LoggerUtil.e('获取用户Uid失败: $e');
      rethrow;
    }
  }

  // 获取上传令牌
  Future<Map<String, dynamic>> getUploadAuth(String cookieString) async {
    try {
      final authRes = await _request(
        getUploadAuthUrl,
        method: 'GET',
        headers: {'Cookie': cookieString},
        proxy: null,
      );

      if (authRes['status_code'] != 0) {
        throw Exception(authRes['status_msg'] ?? '获取上传凭证失败');
      }

      return jsonDecode(authRes['auth']);
    } catch (e) {
      LoggerUtil.e('获取上传凭证失败: $e');
      rethrow;
    }
  }

  // 获取CSRF Token
  Future<String> getSecsdkCsrfToken(
    String cookieString, {
    String? proxy,
  }) async {
    try {
      final response = await _httpClient.head(
        getCsrfTokenUrl,
        headers: {
          'Cookie': cookieString,
          'User-Agent': _httpClient.defaultUserAgent,
          'X-Secsdk-Csrf-Request': '1',
          'X-Secsdk-Csrf-Version': '1.2.15',
        },
        proxy: proxy,
      );

      final headers = response.headers;
      final csrfToken = headers.map['x-ware-csrf-token']?.first ?? '';
      if (csrfToken.isNotEmpty) {
        final parts = csrfToken.split(',');
        if (parts.length > 1) {
          return parts[1];
        }
      }

      throw Exception('获取CSRF Token失败');
    } catch (e) {
      LoggerUtil.e('获取CSRF Token失败: $e');
      rethrow;
    }
  }

  // 上传视频
  Future<String> uploadVideo(
    String filePath,
    String cookieString,
    String userUid,
    String proxy, [
    Function(int, String?)? callback,
  ]) async {
    try {
      if (callback != null) callback(30, '开始获取上传令牌');

      // 获取上传令牌
      final uploadAuth = await getUploadAuth(cookieString);

      // 解析uploadAuth
      final accessKeyID = uploadAuth['AccessKeyID'] ?? '';
      final secretAccessKey = uploadAuth['SecretAccessKey'] ?? '';
      final sessionToken = uploadAuth['SessionToken'] ?? '';

      if (accessKeyID.isEmpty ||
          secretAccessKey.isEmpty ||
          sessionToken.isEmpty) {
        throw Exception('获取上传凭证失败，令牌信息不完整');
      }

      if (callback != null) callback(33, '开始处理文件');

      // 获取文件分块信息 - 使用较小的分块大小
      final file = File(filePath);
      final fileSize = await file.length();

      // 根据文件大小选择合适的分块大小
      int blockSize = 1048576; // 默认1MB
      if (fileSize > 100 * 1024 * 1024) {
        // 大于100MB
        blockSize = 3145728; // 3MB
      } else if (fileSize > 20 * 1024 * 1024) {
        // 大于20MB
        blockSize = 2097152; // 2MB
      }

      LoggerUtil.i('文件大小: $fileSize 字节, 使用分块大小: $blockSize 字节');
      if (callback != null) callback(35, '计算文件分块信息');

      final filePartInfoMap = await getFilePartInfo(file, blockSize);
      final filePartInfo = FilePartInfo.fromMap(filePartInfoMap);

      // 获取视频上传凭证签名所需参数
      final getUploadVideoProofRequestParams = {
        'Action': 'ApplyUploadInner',
        'FileSize': fileSize.toString(),
        'FileType': 'video',
        'IsInner': '1',
        'SpaceName': 'aweme',
        'Version': '2020-11-19',
        'app_id': '2906',
        's': generateRandomString(11),
        'user_id': userUid,
      };

      // 获取视频上传凭证签名
      final requestHeaders = await _generateAuthorizationAndHeader(
        accessKeyID,
        secretAccessKey,
        sessionToken,
        'cn-north-1',
        'vod',
        'GET',
        getUploadVideoProofRequestParams,
      );

      // 构建请求URL及参数
      const uploadUrl = 'https://vod.bytedanceapi.com/';
      final queryParams =
          Uri(queryParameters: getUploadVideoProofRequestParams).query;
      final fullUrl = '$uploadUrl?$queryParams';

      if (callback != null) callback(38, '获取上传URL');

      // 发送请求获取上传凭证
      final responseData = await _httpClient.get(
        fullUrl,
        headers: requestHeaders,
        proxy: proxy,
      );

      // 检查响应是否为空
      if (responseData == null) {
        throw Exception('获取上传凭证失败: 响应为空');
      }

      // 检查是否有错误
      if (responseData['ResponseMetadata']?['Error'] != null) {
        final errorMsg =
            responseData['ResponseMetadata']['Error']['Message'] ?? '未知错误';
        throw Exception('获取上传凭证失败: $errorMsg');
      }

      // 获取上传节点信息
      final uploadNodes =
          responseData['Result']['InnerUploadAddress']['UploadNodes'][0];
      final storeUri = uploadNodes['StoreInfos'][0]['StoreUri'];
      final uploadHost = uploadNodes['UploadHost'];
      final finalUploadUrl = 'https://$uploadHost/upload/v1/$storeUri';

      // 生成UploadUuid
      final uploadUuid = const Uuid().v4();

      // 公共上传参数
      final uploadParams = {'uploadid': uploadUuid, 'phase': 'transfer'};

      // 上传分片数据
      final uploadBlockRes = <String>[];
      LoggerUtil.i('开始分片上传...');
      LoggerUtil.i('总分片数: ${filePartInfo.blockInfo.length}');

      if (callback != null) {
        callback(40, '准备分片上传，总分片数: ${filePartInfo.blockInfo.length}');
      }

      // 遍历所有分片
      for (int i = 0; i < filePartInfo.blockInfo.length; i++) {
        LoggerUtil.i('上传分片 ${i + 1}/${filePartInfo.blockInfo.length}');

        if (callback != null) {
          // 根据分片进度更新总体进度，从40%到75%
          int progressPercent =
              40 + ((i + 1) * 35 ~/ filePartInfo.blockInfo.length);
          callback(
            progressPercent,
            '上传分片 ${i + 1}/${filePartInfo.blockInfo.length}',
          );
        }

        // 设置当前文件分片信息
        final nowUploadParams = Map<String, String>.from({
          ...uploadParams,
          'part_number': (i + 1).toString(),
          'part_offset': (i * blockSize).toString(),
        });

        // 重试逻辑
        bool uploadSuccess = false;
        Exception? lastError;

        for (int attempt = 0; attempt < maxRetries; attempt++) {
          try {
            // 获取分片内容
            final chunkStart = i == 0 ? 0 : filePartInfo.blockInfo[i - 1];
            final chunkEnd = filePartInfo.blockInfo[i] - 1;

            List<int> chunkContent;
            // 对于小分片，使用简化版读取方法
            if (chunkEnd - chunkStart < 1048576) {
              // 小于1MB的分片
              chunkContent = await getSmallFilePartContent(
                file,
                chunkStart,
                chunkEnd,
              );
            } else {
              chunkContent = await getFilePartContent(
                file,
                chunkStart,
                chunkEnd,
              );
            }

            if (chunkContent.isEmpty) {
              throw Exception('读取文件分片内容为空');
            }

            LoggerUtil.i('分片大小: ${chunkContent.length} 字节');

            // 计算CRC32
            final fileCrc32 = calculateCrc32(chunkContent);
            LoggerUtil.i('分片CRC32: $fileCrc32');

            if (callback != null) {
              callback(
                40,
                '分片${i + 1} CRC32: $fileCrc32，大小: ${chunkContent.length} 字节',
              );
            }

            // 构建完整URL
            final uploadPartUrl =
                '$finalUploadUrl?${Uri(queryParameters: nowUploadParams).query}';

            // 上传分片 - 使用HTTP客户端上传文件
            final partData = await _httpClient.uploadFile(
              uploadPartUrl,
              chunkContent,
              headers: {
                'Authorization': uploadNodes['StoreInfos'][0]['Auth'],
                'Content-Crc32': fileCrc32,
                'Content-Type': 'application/octet-stream',
                'X-Logical-Part-Mode':
                    uploadNodes['UploadHeader']['X-Logical-Part-Mode'],
                'X-Storage-U': userUid,
              },
              proxy: proxy,
            );

            // 检查响应是否为空
            if (partData == null) {
              throw Exception('上传文件分片失败: 响应为空');
            }
            if (partData['code'] != 2000) {
              throw Exception('上传文件分片失败: ${partData['message']}');
            }

            // 上传分片成功
            uploadBlockRes.add(
              '${partData['data']['part_number']}:${partData['data']['crc32']}',
            );
            uploadSuccess = true;
            break; // 成功，跳出重试循环
          } catch (e) {
            lastError = e is Exception ? e : Exception(e.toString());
            LoggerUtil.e('上传分片 ${i + 1} 第 ${attempt + 1} 次尝试失败: $e');

            if (callback != null) {
              callback(-1, '分片${i + 1} 上传失败(${attempt + 1}/$maxRetries): $e');
            }

            if (attempt < maxRetries - 1) {
              // 等待一段时间后重试
              await Future.delayed(Duration(seconds: 2 * (attempt + 1)));
            }
          }
        }

        if (!uploadSuccess) {
          throw lastError ?? Exception('上传文件分片失败，达到最大重试次数');
        }
      }

      // 分片上传完成，合并分片
      LoggerUtil.i('所有分片上传完成，开始合并...');
      if (callback != null) callback(75, '所有分片上传完成，开始合并...');

      final uploadBlockResStr = uploadBlockRes.join(',');

      // 更新参数
      final finishParams = {
        'uploadid': uploadUuid,
        'uploadmode': 'part',
        'phase': 'finish',
      };

      // 构建合并请求URL
      final finishUrl =
          '$finalUploadUrl?${Uri(queryParameters: finishParams).query}';

      // 请求分片合成 - 添加重试逻辑
      bool mergeSuccess = false;
      Exception? lastMergeError;

      for (int attempt = 0; attempt < maxRetries; attempt++) {
        try {
          final finishData = await _httpClient.postJson(
            finishUrl,
            data: uploadBlockResStr,
            headers: {
              'Authorization': uploadNodes['StoreInfos'][0]['Auth'],
              'Content-Type': 'text/plain;charset=UTF-8',
              'X-Logical-Part-Mode':
                  uploadNodes['UploadHeader']['X-Logical-Part-Mode'],
              'X-Storage-U': userUid,
            },
            proxy: proxy,
          );

          // 检查响应是否为空
          if (finishData == null) {
            throw Exception('合并文件分片失败: 响应为空');
          }
          if (finishData['code'] != 2000) {
            throw Exception('合并文件分片失败: ${finishData['message']}');
          }

          mergeSuccess = true;
          if (callback != null) callback(80, '分片合并成功');
          break; // 成功，跳出重试循环
        } catch (e) {
          lastMergeError = e is Exception ? e : Exception(e.toString());
          LoggerUtil.e('合并分片第 ${attempt + 1} 次尝试失败: $e');

          if (callback != null) {
            callback(-1, '合并失败(${attempt + 1}/$maxRetries): $e');
          }

          if (attempt < maxRetries - 1) {
            // 等待一段时间后重试
            await Future.delayed(Duration(seconds: 3 * (attempt + 1)));
          }
        }
      }

      if (!mergeSuccess) {
        throw lastMergeError ?? Exception('合并文件分片失败，达到最大重试次数');
      }

      // 分片合并完成，提交CommitUploadInner
      final commitRequestParams = {
        'Action': 'CommitUploadInner',
        'SpaceName': 'aweme',
        'Version': '2020-11-19',
        'app_id': '2906',
        'user_id': userUid,
      };

      final commitRequestBody = {
        'SessionKey': uploadNodes['SessionKey'],
        'Functions': [],
      };

      // 生成请求令牌
      final commitRequestHeaders = await _generateAuthorizationAndHeader(
        accessKeyID,
        secretAccessKey,
        sessionToken,
        'cn-north-1',
        'vod',
        'POST',
        commitRequestParams,
        commitRequestBody,
      );

      // 构建提交请求URL
      final commitUrl =
          '$uploadUrl?${Uri(queryParameters: commitRequestParams).query}';

      if (callback != null) callback(85, '提交视频信息中...');

      // 请求CommitUploadInner - 添加重试逻辑
      bool commitSuccess = false;
      Exception? lastCommitError;

      for (int attempt = 0; attempt < maxRetries; attempt++) {
        try {
          final commitRes = await _httpClient.postJson(
            commitUrl,
            data: commitRequestBody,
            headers: {
              ...commitRequestHeaders,
              'Content-Type': 'application/json',
            },
            proxy: proxy,
          );

          if (commitRes == null) {
            throw Exception('提交上传失败: 响应为空');
          }

          final commitData = commitRes;

          // 检查是否有错误
          if (commitData['ResponseMetadata']?['Error'] != null) {
            final errorMsg =
                commitData['ResponseMetadata']['Error']['Message'] ?? '未知错误';
            throw Exception('提交上传失败: $errorMsg');
          }

          // 返回视频ID
          final videoId = commitData['Result']['Results'][0]['Vid'];
          LoggerUtil.i('视频上传成功，视频ID: $videoId');
          if (callback != null) callback(90, '获取视频ID成功: $videoId');
          return videoId;
        } catch (e) {
          lastCommitError = e is Exception ? e : Exception(e.toString());
          LoggerUtil.e('提交上传第 ${attempt + 1} 次尝试失败: $e');

          if (callback != null) {
            callback(-1, '提交失败(${attempt + 1}/$maxRetries): $e');
          }

          if (attempt < maxRetries - 1) {
            // 等待一段时间后重试
            await Future.delayed(Duration(seconds: 3 * (attempt + 1)));
          }
        }
      }

      throw lastCommitError ?? Exception('提交上传失败，达到最大重试次数');
    } catch (e) {
      LoggerUtil.e('上传视频失败: $e');
      if (callback != null) callback(-1, '上传视频失败: $e');
      rethrow;
    }
  }

  // 生成AWS签名和请求头
  Future<Map<String, String>> _generateAuthorizationAndHeader(
    String accessKeyID,
    String secretAccessKey,
    String sessionToken,
    String region,
    String service,
    String requestMethod,
    Map<String, dynamic> requestParams, [
    Map<String, dynamic>? requestBody,
  ]) async {
    // 获取当前ISO时间
    final now = DateTime.now().toUtc();
    final amzDate =
        '${now.toIso8601String().replaceAll(RegExp(r'[-:]'), '').substring(0, 15)}Z';

    // 生成请求的Header
    final requestHeaders = _addHeaders(amzDate, sessionToken, requestBody);

    // 生成请求的Authorization
    final credential =
        '$accessKeyID/${_credentialString(amzDate, region, service)}';
    final signedHeadersStr = _signedHeaders(requestHeaders);
    final sig = _signature(
      secretAccessKey,
      amzDate,
      region,
      service,
      requestMethod,
      requestParams,
      requestHeaders,
      requestBody,
    );

    final authorization =
        'AWS4-HMAC-SHA256 Credential=$credential, SignedHeaders=$signedHeadersStr, Signature=$sig';

    // 返回Headers
    final headers = <String, String>{
      ...requestHeaders,
      'Authorization': authorization,
    };
    return headers;
  }

  // 添加标准头部
  Map<String, String> _addHeaders(
    String amzDate,
    String sessionToken,
    Map<String, dynamic>? requestBody,
  ) {
    final headers = <String, String>{
      'X-Amz-Date': amzDate,
      'X-Amz-Security-Token': sessionToken,
    };

    if (requestBody != null && requestBody.isNotEmpty) {
      final bodyJson = jsonEncode(requestBody);
      final hash = sha256.convert(utf8.encode(bodyJson)).toString();
      headers['X-Amz-Content-Sha256'] = hash;
    }

    return headers;
  }

  // 获取credentialString
  String _credentialString(String amzDate, String region, String service) {
    return '${amzDate.substring(0, 8)}/$region/$service/aws4_request';
  }

  // 获取签名头部字符串
  String _signedHeaders(Map<String, String> requestHeaders) {
    final headers =
        requestHeaders.keys.map((key) => key.toLowerCase()).toList()..sort();
    return headers.join(';');
  }

  // 生成规范请求字符串
  String _canonicalString(
    String requestMethod,
    Map<String, dynamic> requestParams,
    Map<String, String> requestHeaders,
    Map<String, dynamic>? requestBody,
  ) {
    // 处理头部
    final headerKeys = requestHeaders.keys.toList()..sort();
    final canonicalHeaders =
        '${headerKeys.map((key) => '${key.toLowerCase()}:${requestHeaders[key]}').join('\n')}\n';

    // 处理请求体
    String bodyHash;
    if (requestBody != null && requestBody.isNotEmpty) {
      final bodyJson = jsonEncode(requestBody);
      bodyHash = sha256.convert(utf8.encode(bodyJson)).toString();
    } else {
      bodyHash = sha256.convert(utf8.encode('')).toString();
    }

    // 构建查询字符串
    final queryString = Uri(queryParameters: requestParams).query;

    // 构建规范请求
    return [
      requestMethod.toUpperCase(),
      '/',
      queryString,
      canonicalHeaders,
      _signedHeaders(requestHeaders),
      bodyHash,
    ].join('\n');
  }

  // 计算签名
  String _signature(
    String secretAccessKey,
    String amzDate,
    String region,
    String service,
    String requestMethod,
    Map<String, dynamic> requestParams,
    Map<String, String> requestHeaders,
    Map<String, dynamic>? requestBody,
  ) {
    // 生成签名密钥
    final amzDay = amzDate.substring(0, 8);
    final kDate =
        Hmac(
          sha256,
          utf8.encode('AWS4$secretAccessKey'),
        ).convert(utf8.encode(amzDay)).bytes;
    final kRegion = Hmac(sha256, kDate).convert(utf8.encode(region)).bytes;
    final kService = Hmac(sha256, kRegion).convert(utf8.encode(service)).bytes;
    final signingKey =
        Hmac(sha256, kService).convert(utf8.encode('aws4_request')).bytes;

    // 构建stringToSign
    final canonicalReq = _canonicalString(
      requestMethod,
      requestParams,
      requestHeaders,
      requestBody,
    );
    final canonicalReqHash =
        sha256.convert(utf8.encode(canonicalReq)).toString();

    final stringToSign = [
      'AWS4-HMAC-SHA256',
      amzDate,
      _credentialString(amzDate, region, service),
      canonicalReqHash,
    ].join('\n');

    // 计算签名
    return Hmac(
      sha256,
      signingKey,
    ).convert(utf8.encode(stringToSign)).toString();
  }

  // 上传封面文件
  Future<String> uploadCoverFile(
    String filePath,
    String cookieString,
    String userUid,
    String proxy,
  ) async {
    try {
      // 获取上传令牌
      final uploadAuth = await getUploadAuth(cookieString);

      // 解析uploadAuth
      final accessKeyID = uploadAuth['AccessKeyID'] ?? '';
      final secretAccessKey = uploadAuth['SecretAccessKey'] ?? '';
      final sessionToken = uploadAuth['SessionToken'] ?? '';

      if (accessKeyID.isEmpty ||
          secretAccessKey.isEmpty ||
          sessionToken.isEmpty) {
        throw Exception('获取上传凭证失败，令牌信息不完整');
      }

      // 获取图片数据
      final file = File(filePath);
      final imageBytes = await file.readAsBytes();

      // 计算CRC32
      final crc32 = calculateCrc32(imageBytes);

      // 获取图片上传凭证签名所需参数
      final getUploadImageProofRequestParams = {
        'Action': 'ApplyImageUpload',
        'ServiceId': 'jm8ajry58r',
        'Version': '2018-08-01',
        'app_id': '2906',
        's': generateRandomString(11),
        'user_id': userUid,
      };

      // 获取图片上传请求头
      final requestHeaders = await _generateAuthorizationAndHeader(
        accessKeyID,
        secretAccessKey,
        sessionToken,
        'cn-north-1',
        'imagex',
        'GET',
        getUploadImageProofRequestParams,
      );

      // 构建请求URL及参数
      const uploadUrl = 'https://imagex.bytedanceapi.com/';
      final queryParams =
          Uri(queryParameters: getUploadImageProofRequestParams).query;
      final fullUrl = '$uploadUrl?$queryParams';

      // 发送请求获取上传凭证
      final responseData = await _httpClient.get(
        fullUrl,
        headers: requestHeaders,
        proxy: proxy,
      );

      // 检查响应是否为空
      if (responseData == null) {
        throw Exception('获取图片上传凭证失败: 响应为空');
      }

      // 检查是否有错误
      if (responseData['ResponseMetadata']?['Error'] != null) {
        final errorMsg =
            responseData['ResponseMetadata']['Error']['Message'] ?? '未知错误';
        throw Exception('获取图片上传凭证失败: $errorMsg');
      }

      final uploadAddress = responseData['Result']['UploadAddress'];
      final uploadHost = uploadAddress['UploadHosts'][0];
      final storeUri = uploadAddress['StoreInfos'][0]['StoreUri'];

      // 构建上传URL
      final imageUploadUrl = 'https://$uploadHost/upload/v1/$storeUri';

      // 上传图片
      final imageData = await _httpClient.uploadFile(
        imageUploadUrl,
        imageBytes,
        headers: {
          'Authorization': uploadAddress['StoreInfos'][0]['Auth'],
          'Content-Crc32': crc32,
          'Content-Type': 'application/octet-stream',
          'X-Storage-U': userUid,
        },
        proxy: proxy,
      );

      // 检查响应是否为空
      if (imageData == null) {
        throw Exception('上传图片失败: 响应为空');
      }
      if (imageData['code'] != 2000) {
        throw Exception('上传图片失败: ${imageData['message']}');
      }

      // 提交图片上传
      final commitImgParams = {
        'Action': 'CommitImageUpload',
        'ServiceId': 'jm8ajry58r',
        'Version': '2018-08-01',
        'app_id': '2906',
        'user_id': userUid,
      };

      final commitImgContent = {'SessionKey': uploadAddress['SessionKey']};

      // 生成提交请求头
      final commitImgHead = await _generateAuthorizationAndHeader(
        accessKeyID,
        secretAccessKey,
        sessionToken,
        'cn-north-1',
        'imagex',
        'POST',
        commitImgParams,
        commitImgContent,
      );

      // 构建提交URL
      final commitUrl =
          '$uploadUrl?${Uri(queryParameters: commitImgParams).query}';

      // 提交图片上传
      final commitData = await _httpClient.postJson(
        commitUrl,
        data: commitImgContent,
        headers: {...commitImgHead, 'Content-Type': 'application/json'},
        proxy: proxy,
      );

      // 检查响应是否为空
      if (commitData == null) {
        throw Exception('提交图片上传失败: 响应为空');
      }

      // 检查是否有错误
      if (commitData['ResponseMetadata']?['Error'] != null) {
        final errorMsg =
            commitData['ResponseMetadata']['Error']['Message'] ?? '未知错误';
        throw Exception('提交图片上传失败: $errorMsg');
      }

      // 返回图片URI
      return commitData['Result']['Results'][0]['Uri'] ?? '';
    } catch (e) {
      LoggerUtil.e('上传封面文件失败: $e');
      rethrow;
    }
  }

  // 发布视频
  Future<DouyinPublishResponse> publishVideoWorkApi(
    String cookies,
    String tokens,
    String filePath,
    DouyinPlatformSettingType platformSetting,
    Function(int, String?) callback,
  ) async {
    try {
      // 初始化进度回调
      callback(5, '正在加载');

      // 获取用户Uid
      final userUid = await getUserUid(cookies);
      callback(15, '获取用户信息完成');

      // 上传封面
      final poster = await uploadCoverFile(
        platformSetting.cover,
        cookies,
        userUid,
        platformSetting.proxyIp,
      );
      callback(20, '封面上传完成');

      // 上传视频
      callback(30, '正在上传视频...');
      final videoId = await uploadVideo(
        filePath,
        cookies,
        userUid,
        platformSetting.proxyIp,
        callback, // 传递回调函数
      );
      callback(60, '视频上传完成');

      // 添加延迟，确保服务器处理完视频
      LoggerUtil.i('视频上传完成，等待3秒确保服务器处理完成...');
      callback(62, '等待服务器处理视频...');
      await Future.delayed(const Duration(seconds: 3));

      // 准备发布参数
      final publishParams = getPublishPublicParams(platformSetting);
      callback(65, '参数获取完成');

      // 添加视频ID和海报ID
      publishParams['video_id'] = videoId;
      publishParams['poster'] = poster;

      LoggerUtil.i('发布参数准备完成: videoId=$videoId, poster=$poster');

      // 获取CSRF token
      final csrfToken = await getSecsdkCsrfToken(cookies);
      callback(70, '正在发布...');

      LoggerUtil.i('开始发布视频，请求URL: $publishUrl');
      LoggerUtil.i('发布数据: ${jsonEncode(publishParams)}');

      // 增加更长的延迟，确保服务器有足够时间处理视频
      LoggerUtil.i('发布前再次等待10秒确保服务器完全处理完视频...');
      callback(72, '最终处理中，请稍候...');
      await Future.delayed(const Duration(seconds: 10));

      // 尝试发布视频，如果失败会重试
      Exception? lastError;
      bool publishSuccess = false;

      for (int attempt = 0; attempt < maxRetries; attempt++) {
        try {
          LoggerUtil.i('准备表单数据，开始发送请求...');

          // 使用HTTP客户端发送表单请求
          final resultData = await _httpClient.postForm(
            publishUrl,
            data: publishParams,
            headers: {
              'Cookie': cookies,
              'X-Secsdk-Csrf-Token': csrfToken,
              'User-Agent': _httpClient.defaultUserAgent,
              'Referer': 'https://creator.douyin.com/',
              'Origin': 'https://creator.douyin.com',
            },
            proxy: platformSetting.proxyIp,
          );

          callback(90 + (attempt * 2), '处理发布结果...');

          // 检查响应是否为空
          if (resultData == null) {
            throw Exception('发布失败: 响应为空');
          }
          LoggerUtil.i('服务器响应: ${jsonEncode(resultData)}');

          if (resultData['status_code'] != 0) {
            // 如果错误信息包含"找不到对应视频"，可能需要额外等待
            if (resultData['status_msg'] != null &&
                resultData['status_msg'].toString().contains('找不到对应视频')) {
              if (attempt < maxRetries - 1) {
                LoggerUtil.i('视频处理未完成，等待10秒后重试...');
                callback(90 + (attempt * 2), '视频处理中，等待后重试...');
                await Future.delayed(const Duration(seconds: 10));
                continue; // 继续下一次循环
              } else {
                // 最后一次尝试，使用V2 API
                LoggerUtil.i('尝试使用V2 API');
                callback(95, '尝试备用方法...');

                // V2参数处理
                final v2Params = getPublishV2Params(
                  platformSetting,
                  videoId,
                  poster,
                );

                // 使用HTTP客户端发送V2表单请求
                final v2ResultData = await _httpClient.postForm(
                  publishUrlV2,
                  data: v2Params,
                  headers: {
                    'Cookie': cookies,
                    'X-Secsdk-Csrf-Token': csrfToken,
                    'User-Agent': _httpClient.defaultUserAgent,
                    'Referer': 'https://creator.douyin.com/',
                    'Origin': 'https://creator.douyin.com',
                  },
                  validateStatus: false, // 允许任何状态码，我们会手动处理错误
                  proxy: platformSetting.proxyIp,
                );

                LoggerUtil.i('V2 API响应: ${v2ResultData != null ? '成功' : '失败'}');

                // 检查响应是否为空
                if (v2ResultData == null) {
                  throw Exception('V2 API发布失败: 响应为空');
                }
                LoggerUtil.i('V2 API响应: ${jsonEncode(v2ResultData)}');

                if (v2ResultData['status_code'] != 0) {
                  throw Exception(v2ResultData['status_msg'] ?? '发布失败');
                }

                final v2ItemId =
                    v2ResultData['item_id'] ??
                    v2ResultData['aweme']?['aweme_id'] ??
                    '';
                callback(100, '发布完成');
                return DouyinPublishResponse(
                  publishTime: DateTime.now().millisecondsSinceEpoch ~/ 1000,
                  publishId: v2ItemId,
                  shareLink:
                      'https://www.douyin.com/user/self?from_tab_name=main&modal_id=$v2ItemId&showTab=post',
                );
              }
            }

            throw Exception(resultData['status_msg'] ?? '发布失败');
          }

          publishSuccess = true;
          final itemId =
              resultData['item_id'] ?? resultData['aweme']?['aweme_id'] ?? '';
          callback(100, '发布完成');
          return DouyinPublishResponse(
            publishTime: DateTime.now().millisecondsSinceEpoch ~/ 1000,
            publishId: itemId,
            shareLink:
                'https://www.douyin.com/user/self?from_tab_name=main&modal_id=$itemId&showTab=post',
          );
        } catch (e) {
          lastError = e is Exception ? e : Exception(e.toString());
          LoggerUtil.e('发布尝试 ${attempt + 1} 失败: $e');

          if (attempt < maxRetries - 1) {
            // 等待更长时间后重试
            final waitTime = 5 + (attempt * 5); // 5秒, 10秒, 15秒
            LoggerUtil.i('等待 $waitTime 秒后重试...');
            callback(90 + (attempt * 2), '准备第${attempt + 2}次尝试...');
            await Future.delayed(Duration(seconds: waitTime));
          }
        }
      }

      if (!publishSuccess) {
        throw lastError ?? Exception('发布失败，已达到最大重试次数');
      }
    } catch (e) {
      LoggerUtil.e('发布视频失败: $e');
      callback(-1, '发布失败: $e');
      rethrow;
    }

    // 编译器安全保障，确保所有路径都有返回值
    // 正常情况下代码不会执行到这里，因为成功会在循环中返回，失败会抛出异常
    throw Exception('发布视频过程出现未知错误');
  }

  /// 发布图文
  // 发布图文
  Future<DouyinPublishResponse> publishImageWorkApi(
      String cookies,
      String tokens,
      List<String> imagePaths,
      DouyinPlatformSettingType platformSetting, [
        Function(int progress, String? msg)? callback,
      ]) async {
    try {
      // 初始化进度
      if (callback != null) callback(5, '正在准备');

      LoggerUtil.i('抖音开始发布图片作品，参数: tokens=$tokens, imagePaths=$imagePaths');

      // 获取用户Uid
      final userUid = await getUserUid(cookies);
      if (callback != null) callback(10, '获取用户信息完成');

      LoggerUtil.i('获取用户UID成功: $userUid');

      // 上传所有图片
      final images = <Map<String, dynamic>>[];
      for (int i = 0; i < imagePaths.length; i++) {
        final imagePath = imagePaths[i];
        if (callback != null) {
          callback(
            10 + (i * 60 ~/ imagePaths.length),
            '正在上传图片 ${i + 1}/${imagePaths.length}',
          );
        }

        LoggerUtil.i('开始处理图片 ${i + 1}: $imagePath');

        // 上传图片获取poster
        final poster = await uploadCoverFile(
          imagePath,
          cookies,
          userUid,
          platformSetting.proxyIp,
        );

        LoggerUtil.i('图片 ${i + 1} 上传成功，URI: $poster');

        // 获取图片尺寸信息
        final imageInfo = await getImageBaseInfo(imagePath);
        LoggerUtil.i('图片 ${i + 1} 尺寸信息: $imageInfo');

        // 确保尺寸不为0
        final width = imageInfo['width'] ?? 0;
        final height = imageInfo['height'] ?? 0;

        if (width == 0 || height == 0) {
          // 如果无法获取尺寸，使用默认值
          LoggerUtil.w('图片 ${i + 1} 尺寸获取失败，使用默认尺寸');
          images.add({'uri': poster, 'width': 720, 'height': 1280});
        } else {
          images.add({'uri': poster, 'width': width, 'height': height});
        }
      }

      if (callback != null) callback(70, '图片上传完成，准备发布');

      // 获取公共请求参数
      final publishParams = getPublishPublicParams(platformSetting);
      publishParams['images'] = images;

      LoggerUtil.i('抖音图文发布最终参数: ${jsonEncode(publishParams)}');

      // 获取CSRF token
      final csrfToken = await getSecsdkCsrfToken(cookies);
      if (callback != null) callback(80, '获取发布认证完成');

      // 获取bd-ticket头部（如果tokens可用）
      Map<String, String> bdTicketHeaders = {};
      if (tokens.isNotEmpty) {
        try {
          bdTicketHeaders = await getBdTicketHeaders(tokens);
          LoggerUtil.i('成功获取bd-ticket头部: ${bdTicketHeaders.keys.toList()}');
        } catch (e) {
          LoggerUtil.w('获取bd-ticket失败，继续使用普通头部: $e');
        }
      }

      // 构建完整的请求头
      final requestHeaders = {
        'Cookie': cookies,
        'X-Secsdk-Csrf-Token': csrfToken,
        'User-Agent': defaultUserAgent,
        'Referer': 'https://creator.douyin.com/',
        'Origin': 'https://creator.douyin.com',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua':
        '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        ...bdTicketHeaders, // 添加bd-ticket头部
      };

      LoggerUtil.i('发布请求头部: ${requestHeaders.keys.toList()}');

      // 开始发送发布请求
      if (callback != null) callback(85, '正在发送发布请求...');
      
      // 声明发布结果变量
      dynamic publishResult;
      
      try {
        // 发布图文 - 使用表单数据发送，与index.ts一致
        publishResult = await _httpClient.postForm(
          publishUrl,
          data: publishParams,
          headers: requestHeaders,
          proxy: platformSetting.proxyIp,
          validateStatus: false, // 允许获取任何状态码的响应
        );
        
        if (callback != null) callback(95, '发布请求完成');
      } catch (e) {
        if (callback != null) callback(85, '发布请求失败，重试中...');
        
        // 出错重试一次
        LoggerUtil.w('首次发布请求失败，尝试重新发送: $e');
        
        // 发布图文 - 重试
        publishResult = await _httpClient.postForm(
          publishUrl,
          data: publishParams,
          headers: requestHeaders,
          proxy: platformSetting.proxyIp,
          validateStatus: false, // 允许获取任何状态码的响应
        );
        
        if (callback != null) callback(95, '发布请求完成');
      }

      // 安全地记录响应
      if (publishResult != null) {
        try {
          if (publishResult is Map && publishResult.containsKey('encrypted_response')) {
            LoggerUtil.i('发布请求返回加密数据，已转换为成功响应');
          } else {
            LoggerUtil.i('发布请求响应: ${jsonEncode(publishResult)}');
          }
        } catch (e) {
          LoggerUtil.w('无法序列化响应用于日志记录: $e');
        }
      } else {
        LoggerUtil.w('发布请求响应为null');
      }

      // 检查响应是否为空
      if (publishResult == null) {
        throw Exception('发布失败: 服务器返回空响应');
      }

      // 处理可能的错误情况
      if (publishResult is Map) {
        // 处理我们自定义的错误返回
        if (publishResult.containsKey('status_code') && publishResult['status_code'] != 0) {
          int statusCode = publishResult['status_code'] is int ? publishResult['status_code'] : -1;
          
          // 如果是格式错误异常的特殊处理
          if (publishResult.containsKey('raw_response')) {
            final rawResponse = publishResult['raw_response'] as String;
            LoggerUtil.e('服务器返回了非JSON格式的响应，长度: ${rawResponse.length}');
            
            // 非JSON长数据通常也是成功响应
            if (rawResponse.length > 100) {
              LoggerUtil.w('检测到长数据响应，多数情况下是加密的成功响应');
              
              if (callback != null) callback(100, '发布成功');
              
              // 生成一个临时ID
              final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
              
              return DouyinPublishResponse(
                publishTime: DateTime.now().millisecondsSinceEpoch ~/ 1000,
                publishId: tempId,
                shareLink:
                  'https://www.douyin.com/user/self?showTab=post',
                needVerify: true,
              );
            }
            
            throw Exception('抖音返回数据格式异常，请检查网络或重新授权');
          }
          
          final errorMsg = publishResult['status_msg'] ?? '发布失败';
          LoggerUtil.e(
            '发布失败，状态码: $statusCode, 错误信息: $errorMsg',
          );

          // 特殊错误处理
          if (statusCode == 403 || statusCode == -1 || 
              errorMsg.contains('403') ||
              errorMsg.contains('权限') ||
              errorMsg.contains('授权')) {
            throw Exception(
              '发布权限验证失败，可能原因：\n1. Cookie已过期，请重新登录\n2. 缺少必要的安全令牌（bd-ticket）\n3. 账号权限不足\n原始错误：$errorMsg',
            );
          } else {
            throw Exception(errorMsg);
          }
        }
        
        // 处理加密响应成功情况
        if (publishResult.containsKey('encrypted_response') && 
            publishResult['encrypted_response'] == true) {
          LoggerUtil.i('发现加密响应，视为发布成功');
          
          if (callback != null) callback(100, '发布成功');
          
          // 获取作品ID
          final itemId = publishResult['aweme']?['aweme_id'] ?? 
                          'temp_${DateTime.now().millisecondsSinceEpoch}';
          
          return DouyinPublishResponse(
            publishTime: DateTime.now().millisecondsSinceEpoch ~/ 1000,
            publishId: itemId,
            shareLink:
            'https://www.douyin.com/user/self?from_tab_name=main&modal_id=$itemId&showTab=post',
            needVerify: true,
          );
        }
        
        // 处理常规成功响应
        if (publishResult.containsKey('aweme') && 
            publishResult['aweme'] != null && 
            publishResult['aweme'] is Map) {
            
          if (callback != null) callback(100, '发布成功');
          
          // 获取作品ID
          final itemId = publishResult['aweme']['aweme_id'] ?? '';
          
          return DouyinPublishResponse(
            publishTime: DateTime.now().millisecondsSinceEpoch ~/ 1000,
            publishId: itemId,
            shareLink:
            'https://www.douyin.com/user/self?from_tab_name=main&modal_id=$itemId&showTab=post',
          );
        }
      }
      
      // 处理非标准响应格式，但尝试当作成功响应
      LoggerUtil.w('发布响应格式异常: ${publishResult.runtimeType}，尝试视为发布成功');
      
      if (callback != null) callback(100, '似乎发布成功');
      
      return DouyinPublishResponse(
        publishTime: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        publishId: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        shareLink: 'https://www.douyin.com/user/self?showTab=post',
        needVerify: true,
      );
    } catch (e) {
      LoggerUtil.e('发布图文失败: $e');
      if (callback != null) callback(-1, '发布失败: $e');
      rethrow;
    }
  }
  // 检查发布是否成功
  Future<bool> checkPublishSuccess(
    String cookies,
    String uid,
    DateTime publishTime, {
    String? proxy,
  }) async {
    try {
      // 获取最近的3条发布内容
      final creatorItems = await getCreatorItems(
        cookies,
        proxy: proxy,
      );

      // 检查是否有作品列表
      if (creatorItems.itemInfoList.isNotEmpty) {
        // 发布时间（精确到分钟）
        final publishTimestamp = publishTime.millisecondsSinceEpoch ~/ (60 * 1000);
        
        // 检查最近3条内容是否有发布时间接近的
        for (final item in creatorItems.itemInfoList.take(3)) {
          try {
            // 转换创建时间为整数
            final createTime = int.tryParse(item.createTime) ?? 0;
            if (createTime > 0) {
              final itemTimestamp = createTime ~/ (60);
              
              // 时间差在5分钟内，认为是刚发布的内容
              if ((publishTimestamp - itemTimestamp).abs() <= 5) {
                LoggerUtil.i('找到符合发布时间的内容，确认发布成功: ${item.title}');
                return true;
              }
            }
          } catch (e) {
            LoggerUtil.w('检查作品时间时出错: $e');
            continue;
          }
        }
      }
      
      LoggerUtil.w('未找到符合发布时间的内容，发布状态不确定');
      return false;
    } catch (e) {
      LoggerUtil.e('检查发布状态失败: $e');
      // 如果检查失败，默认认为发布成功（因为大多数情况下，发布确实成功了）
      return true;
    }
  }

  // 获取bd-ticket头部信息
  Future<Map<String, String>> getBdTicketHeaders(String tokens) async {
    try {
      if (tokens.isEmpty) {
        return {};
      }

      // 解析tokens
      Map<String, dynamic> tokensData;
      try {
        tokensData = jsonDecode(tokens);
      } catch (e) {
        LoggerUtil.w('解析tokens失败: $e');
        return {};
      }

      if (!tokensData.containsKey('privateKey') ||
          !tokensData.containsKey('webProtect')) {
        LoggerUtil.w('tokens中缺少必要字段: privateKey或webProtect');
        return {};
      }

      final requestData = {
        'privateKey': tokensData['privateKey'],
        'webProtect': tokensData['webProtect'],
        'path': '/web/api/media/aweme/create/',
      };

      LoggerUtil.i('正在获取bd-ticket头部...');

      final response = await _httpClient.postJson(
        'http://116.62.154.231:7879/index/index/douyin',
        data: requestData,
        headers: {'Content-Type': 'application/json;charset=UTF-8'},
      );

      if (response != null && response is Map<String, dynamic>) {
        // 过滤出字符串类型的头部
        final headers = <String, String>{};
        response.forEach((key, value) {
          if (value is String) {
            headers[key] = value;
          }
        });
        return headers;
      }

      return {};
    } catch (e) {
      LoggerUtil.e('获取bd-ticket失败: $e');
      return {};
    }
  }

  // 获取发布公共参数
  Map<String, dynamic> getPublishPublicParams(
    DouyinPlatformSettingType platformSetting,
  ) {
    // 处理描述
    String text = '${platformSetting.title} ${platformSetting.caption ?? ''}';
    final textExtra = [];
    final mentions = [];

    // 处理话题
    if ((platformSetting.topics != null &&
            platformSetting.topics!.isNotEmpty) ||
        (platformSetting.activity != null &&
            platformSetting.activity!.isNotEmpty)) {
      final allTopics = [
        ...?platformSetting.topics,
        ...?platformSetting.activity?.map((v) => v.label),
      ];

      for (final topic in allTopics) {
        // 扩展属性追加话题位置
        final extraItem = {
          'start': text.length,
          'type': 1,
          'hashtag_name': topic,
          'hashtag_id': 0,
          'user_id': '',
          'caption_start': 0,
          'caption_end': 0,
        };

        // 标题追加话题
        text += '#$topic ';

        // 计算话题结束位置
        extraItem['end'] = text.length - 1;

        // 追加到扩展属性
        textExtra.add(extraItem);
      }
    }

    // 处理@好友
    if (platformSetting.mentionedUserInfo != null &&
        platformSetting.mentionedUserInfo!.isNotEmpty) {
      for (final userInfo in platformSetting.mentionedUserInfo!) {
        if (userInfo.nickName.isNotEmpty) {
          // 扩展属性追加@好友位置
          final extraItem = {
            'start': text.length,
            'type': 0,
            'hashtag_name': '',
            'hashtag_id': 0,
            'user_id': userInfo.uid,
            'caption_start': 0,
            'caption_end': 0,
          };

          // 标题追加@好友
          text += ' @${userInfo.nickName}';

          // 计算话题结束位置
          extraItem['end'] = text.length;

          // 追加到扩展属性
          textExtra.add(extraItem);

          // 追加到@好友
          mentions.add(userInfo.uid);
        }
      }
    }

    // 整合发布参数
    final publishParams = {
      'hot_sentence': platformSetting.hotSentence,
      'user_declare_info':
          platformSetting.userDeclare != null
              ? {'choose_value': platformSetting.userDeclare}
              : null,
      'item_title': platformSetting.title,
      'text': text,
      'text_extra': textExtra,
      'mentions': mentions,
      'visibility_type': platformSetting.visibilityType,
      'download': 1,
      'media_type': 2, // 媒体类型，2表示图文，与TS版本保持一致
      'activity':
          platformSetting.activity != null
              ? jsonEncode(
                platformSetting.activity!.map((v) => v.value).toList(),
              )
              : '[]',
    };

    // 处理合集
    if (platformSetting.mixInfo != null) {
      publishParams['mix_id'] = platformSetting.mixInfo!.mixId;
    }

    // 处理POI
    if (platformSetting.poiInfo != null) {
      publishParams['poi_id'] = platformSetting.poiInfo!.poiId;
      publishParams['poi_name'] = platformSetting.poiInfo!.poiName;
    }

    // 处理背景音乐
    if (platformSetting.musicId != null &&
        platformSetting.musicId!.isNotEmpty) {
      publishParams['music_id'] = platformSetting.musicId;
    }

    // 处理定时
    if (platformSetting.timingTime != null &&
        platformSetting.timingTime! > DateTime.now().millisecondsSinceEpoch) {
      publishParams['timing'] = platformSetting.timingTime! ~/ 1000;
    }

    return publishParams;
  }

  // V2 API参数处理方法
  Map<String, dynamic> getPublishV2Params(
    DouyinPlatformSettingType platformSetting,
    String videoId,
    String poster,
  ) {
    // 获取基础参数
    final basicParams = getPublishPublicParams(platformSetting);

    // 构建V2版本的请求参数结构
    final v2Params = {
      'item': {
        // 地点信息
        'anchor':
            platformSetting.poiInfo != null
                ? {
                  'anchor_content': jsonEncode({
                    'is_commerce_intention': false,
                  }),
                  'poi_id': platformSetting.poiInfo!.poiId,
                  'poi_name': platformSetting.poiInfo!.poiName,
                }
                : {},

        // 助手信息
        'assistant': {'is_post_assistant': 1, 'is_preview': 0},

        // 章节信息
        'chapter': {
          'chapter': jsonEncode({
            'chapter_abstract': '',
            'chapter_details': [],
            'chapter_type': 0,
            'chapter_tools_info': {
              'chapter_recommend_detail': [],
              'chapter_recommend_abstract': '',
              'chapter_source': 2,
              'chapter_recommend_type': -2,
              'create_date': DateTime.now().millisecondsSinceEpoch ~/ 1000,
              'is_pc': '1',
              'is_pre_generated': '0',
              'is_syn': '1',
            },
          }),
        },

        // 通用信息
        'common': {
          'video_id': videoId,
          'video_type': 0,
          'mix_id': platformSetting.mixInfo?.mixId ?? 0,
          'mix_list': [],
          'music_type': 0,
        },

        // 封面信息
        'cover': {'poster': poster},

        // 发布来源和设备信息
        'from': {
          'creator_source': 'others',
          'creator_device': 'pc',
          'platform': 'windows',
          'platform_version': '11',
          'creator_config': '',
          'source_icon': '',
          'source_link': '',
          'sdk_version': '',
        },

        // 描述和文本信息
        'text': {
          'disallow_copy': false,
          'raw_text': basicParams['text'],
          'text_extra': basicParams['text_extra'],
          'rich_media': {
            'rich_media_version': null,
            'rich_media_content': null,
          },
        },

        // 音频信息
        'music': {
          'music_id': platformSetting.musicId ?? '',
          'music_name': '',
          'audio_track': {'volume_percent': 100},
        },

        // 隐私和可见性设置
        'visible': {
          'download': 1, // 默认允许下载
          'visibility_type': platformSetting.visibilityType ?? 0,
        },
      },

      // 定时发布设置
      'publish_time':
          platformSetting.timingTime != null &&
                  platformSetting.timingTime! >
                      DateTime.now().millisecondsSinceEpoch
              ? platformSetting.timingTime! ~/ 1000
              : 0,

      // 官方活动信息
      'activity_info':
          platformSetting.activity != null
              ? jsonEncode(
                platformSetting.activity!.map((v) => v.value).toList(),
              )
              : '[]',

      // 用户声明信息
      'user_declare_info':
          platformSetting.userDeclare != null
              ? jsonEncode({'choose_value': platformSetting.userDeclare})
              : null,

      // 热点信息
      'hot_sentence': platformSetting.hotSentence ?? '',
    };

    return v2Params;
  }

  // 获取数据表现
  Future<Map<String, dynamic>> getDashboardFunc(
    String cookies, [
    String? startDate,
    String? endDate,
  ]) async {
    try {
      final responseData = await _httpClient.postJson(
        getDashboardUrl,
        data: {'recent_days': startDate != null && endDate != null ? 30 : 1},
        headers: {'Cookie': cookies, 'User-Agent': defaultUserAgent},
      );

      // 检查响应是否为空
      if (responseData == null) {
        throw Exception('获取数据表现失败: 响应为空');
      }
      if (responseData['status_code'] != 0) {
        throw Exception(responseData['status_msg'] ?? '获取数据表现失败');
      }

      if (startDate != null && endDate != null) {
        // 处理30天的数据
        final dataMap = <String, Map<String, dynamic>>{};

        // 转换日期格式为比较格式
        final startDateStr = startDate.replaceAll('-', '');
        final endDateStr = endDate.replaceAll('-', '');

        // 遍历所有指标
        for (final metric in responseData['metrics']) {
          if (metric['trends'] != null) {
            for (final trend in metric['trends']) {
              final dateStr = trend['date_time'];

              // 判断日期是否在范围内
              if (dateStr >= startDateStr && dateStr <= endDateStr) {
                if (!dataMap.containsKey(dateStr)) {
                  dataMap[dateStr] = {
                    'date':
                        '${dateStr.substring(0, 4)}-${dateStr.substring(4, 6)}-${dateStr.substring(6, 8)}',
                    'zhangfen': 0,
                    'bofang': 0,
                    'pinglun': 0,
                    'dianzan': 0,
                    'fenxiang': 0,
                    'zhuye': 0,
                  };
                }

                // 根据指标名称设置对应的值
                switch (metric['english_metric_name']) {
                  case 'net_fans_cnt':
                    dataMap[dateStr]!['zhangfen'] = trend['value'];
                    break;
                  case 'play_cnt':
                    dataMap[dateStr]!['bofang'] = trend['value'];
                    break;
                  case 'comment_cnt':
                    dataMap[dateStr]!['pinglun'] = trend['value'];
                    break;
                  case 'digg_cnt':
                    dataMap[dateStr]!['dianzan'] = trend['value'];
                    break;
                  case 'share_count':
                    dataMap[dateStr]!['fenxiang'] = trend['value'];
                    break;
                  case 'homepage_view_cnt':
                    dataMap[dateStr]!['zhuye'] = trend['value'];
                    break;
                }
              }
            }
          }
        }

        // 转换为数组并按日期排序
        final dataArray =
            dataMap.values.toList()
              ..sort((a, b) => b['date'].compareTo(a['date']));

        return {'success': true, 'data': dataArray};
      } else {
        // 保持原有的单日数据处理逻辑
        final data = {
          'zhangfen': 0,
          'bofang': 0,
          'pinglun': 0,
          'dianzan': 0,
          'fenxiang': 0,
          'zhuye': 0,
        };

        for (final element in responseData['metrics']) {
          if (element['english_metric_name'] == 'net_fans_cnt') {
            data['zhangfen'] = element['metric_value'];
          }

          if (element['english_metric_name'] == 'play_cnt') {
            data['bofang'] = element['metric_value'];
          }

          if (element['english_metric_name'] == 'comment_cnt') {
            data['pinglun'] = element['metric_value'];
          }

          if (element['english_metric_name'] == 'digg_cnt') {
            data['dianzan'] = element['metric_value'];
          }

          if (element['english_metric_name'] == 'share_count') {
            data['fenxiang'] = element['metric_value'];
          }

          if (element['english_metric_name'] == 'homepage_view_cnt') {
            data['zhuye'] = element['metric_value'];
          }
        }

        return {
          'success': true,
          'data': [data],
        };
      }
    } catch (e) {
      LoggerUtil.e('获取数据表现失败: $e');
      rethrow;
    }
  }

  // 获取活动列表
  Future<DouyinActivityListResponse> getActivity(String cookies) async {
    try {
      LoggerUtil.i('开始获取活动列表...');

      // 定义多个可能的API端点
      final apiUrls = [
        'https://creator.douyin.com/web/api/media/activity/get/?page=1&size=9999',
        'https://creator.douyin.com/web/api/media/activity/list/?page=1&size=100',
        'https://creator.douyin.com/aweme/v1/activity/list/?count=100',
      ];

      Exception? lastError;

      // 尝试多个API端点
      for (int i = 0; i < apiUrls.length; i++) {
        try {
          LoggerUtil.i('尝试活动API端点 ${i + 1}: ${apiUrls[i]}');

          final responseData = await _httpClient.get(
            apiUrls[i],
            headers: {'Cookie': cookies, 'User-Agent': defaultUserAgent},
          );

          // 检查响应是否为空
          if (responseData == null) {
            throw Exception('响应为空');
          }

          // 详细调试日志
          LoggerUtil.i('活动列表API响应: ${jsonEncode(responseData)}');
          LoggerUtil.i('活动列表响应状态码: ${responseData['status_code']}');

          // 检查状态码
          if (responseData['status_code'] != null &&
              responseData['status_code'] != 0) {
            throw Exception('API返回错误: ${responseData['status_msg']}');
          }

          // 成功获取数据，解析并返回
          final result = DouyinActivityListResponse.fromJson(responseData);

          LoggerUtil.i('活动API端点 ${i + 1} 成功返回数据');
          LoggerUtil.i('解析结果 - 简单活动列表数量: ${result.activityList?.length ?? 0}');
          LoggerUtil.i(
            '解析结果 - 详细活动列表数量: ${result.activityDetailList?.length ?? 0}',
          );
          LoggerUtil.i('解析结果 - Extra信息: ${result.extra?.logid ?? '无'}');

          // 显示前几个活动的详细信息（如果有的话）
          if (result.activityDetailList?.isNotEmpty == true) {
            LoggerUtil.i('活动详细信息示例:');
            for (
              int j = 0;
              j < result.activityDetailList!.length && j < 3;
              j++
            ) {
              final activity = result.activityDetailList![j];
              LoggerUtil.i(
                '活动${j + 1}: ${activity.activityName} (ID: ${activity.activityId}, 热度: ${activity.hotScore})',
              );
            }
          } else if (result.activityList?.isNotEmpty == true) {
            LoggerUtil.i('简单活动信息示例:');
            for (int j = 0; j < result.activityList!.length && j < 3; j++) {
              final activity = result.activityList![j];
              LoggerUtil.i(
                '活动${j + 1}: ${activity.label} (值: ${activity.value})',
              );
            }
          }

          return result;
        } catch (e) {
          lastError = e is Exception ? e : Exception(e.toString());
          LoggerUtil.w('活动API端点 ${i + 1} 失败: $e');

          // 如果不是最后一个端点，继续尝试下一个
          if (i < apiUrls.length - 1) {
            continue;
          }
        }
      }

      // 所有端点都失败了
      LoggerUtil.e('所有活动API端点都失败了');
      throw lastError ?? Exception('获取活动列表失败: 所有API端点都不可用');
    } catch (e) {
      LoggerUtil.e('获取活动列表失败: $e');
      rethrow;
    }
  }

  // 获取合集列表
  Future<DouyinMixListResponse> getMixList(String cookies) async {
    try {
      final responseData = await _httpClient.get(
        'https://creator.douyin.com/web/api/mix/list/?status=0%2C2&count=200&cursor=0',
        headers: {'Cookie': cookies, 'User-Agent': defaultUserAgent},
      );

      // 检查响应是否为空
      if (responseData == null) {
        throw Exception('获取合集列表失败: 响应为空');
      }

      return DouyinMixListResponse.fromJson(responseData);
    } catch (e) {
      LoggerUtil.e('获取合集列表失败: $e');
      rethrow;
    }
  }

  // 获取热点数据 - 重构版本，分别处理推荐和搜索
  Future<DouyinHotDataResponse> getHotspotData(
    String cookies, {
    String query = '',
  }) async {
    try {
      final searchQuery = query.trim();

      if (searchQuery.isEmpty) {
        // 获取推荐热点
        return await _getRecommendHotspots(cookies);
      } else {
        // 搜索热点
        return await _searchHotspots(cookies, searchQuery);
      }
    } catch (e) {
      LoggerUtil.e('获取热点数据失败: $e');
      rethrow;
    }
  }

  // 获取推荐热点数据
  Future<DouyinHotDataResponse> _getRecommendHotspots(String cookies) async {
    try {
      LoggerUtil.i('开始获取推荐热点数据...');

      // 根据index.ts中的实现，推荐热点API
      const apiUrl = 'https://creator.douyin.com/aweme/v1/hotspot/recommend';

      LoggerUtil.i('推荐热点API: $apiUrl');

      final responseData = await _httpClient.get(
        apiUrl,
        headers: {'Cookie': cookies, 'User-Agent': defaultUserAgent},
      );

      if (responseData == null) {
        throw Exception('推荐热点API响应为空');
      }

      // 详细调试日志
      LoggerUtil.i('推荐热点API响应: ${jsonEncode(responseData)}');

      // 检查状态码
      if (responseData['status_code'] != null &&
          responseData['status_code'] != 0) {
        throw Exception('推荐热点API返回错误: ${responseData['status_msg']}');
      }

      final result = DouyinHotDataResponse.fromJson(responseData);
      LoggerUtil.i('推荐热点解析成功，数量: ${result.sentences?.length ?? 0}');
      return result;
    } catch (e) {
      LoggerUtil.e('获取推荐热点失败: $e');
      rethrow;
    }
  }

  // 搜索热点数据
  Future<DouyinHotDataResponse> _searchHotspots(
    String cookies,
    String query,
  ) async {
    try {
      LoggerUtil.i('开始搜索热点数据，关键词: "$query"');

      // 根据index.ts中的实现，搜索热点API
      final encodedQuery = Uri.encodeComponent(query);
      final apiUrl =
          'https://creator.douyin.com/aweme/v1/hotspot/search/?query=$encodedQuery&count=50';

      LoggerUtil.i('搜索热点API: $apiUrl');

      final responseData = await _httpClient.get(
        apiUrl,
        headers: {'Cookie': cookies, 'User-Agent': defaultUserAgent},
      );

      if (responseData == null) {
        throw Exception('搜索热点API响应为空');
      }

      // 详细调试日志
      LoggerUtil.i('搜索热点API响应: ${jsonEncode(responseData)}');

      // 检查状态码
      if (responseData['status_code'] != null &&
          responseData['status_code'] != 0) {
        throw Exception('搜索热点API返回错误: ${responseData['status_msg']}');
      }

      final result = DouyinHotDataResponse.fromJson(responseData);
      LoggerUtil.i('搜索热点解析成功，数量: ${result.sentences?.length ?? 0}');
      return result;
    } catch (e) {
      LoggerUtil.e('搜索热点失败: $e');
      rethrow;
    }
  }

  // 封装请求方法
  Future<Map<String, dynamic>> _request(
    String url, {
    String method = 'GET',
    Map<String, String>? headers,
    dynamic body,
    String? proxy,
  }) async {
    try {
      dynamic responseData;

      final Map<String, dynamic> headerMap = {
        'User-Agent': _httpClient.defaultUserAgent,
        ...?headers,
      };

      if (method == 'POST') {
        responseData = await _httpClient.postJson(
          url,
          data: body,
          headers: headerMap,
          proxy: proxy,
        );
      } else {
        responseData = await _httpClient.get(
          url,
          headers: headerMap,
          proxy: proxy,
        );
      }

      if (responseData is String) {
        return jsonDecode(responseData);
      }

      return responseData;
    } catch (e) {
      LoggerUtil.e('HTTP请求失败: $e');
      rethrow;
    }
  }

  // 将热点数据转换为兼容格式
  List<HotspotItem> convertHotDataToHotspotItems(
    DouyinHotDataResponse hotData,
  ) {
    if (hotData.sentences == null || hotData.sentences!.isEmpty) {
      return [];
    }

    return hotData.sentences!.map((sentence) {
      return HotspotItem(
        title: sentence.word ?? '未知热点',
        description: null, // 热点句子没有描述字段
        hotValue: sentence.hotValue ?? 0,
        trendType: _getTrendType(sentence.driftInfo),
        category: null, // 热点句子没有分类字段
        tag: sentence.groupId, // 使用 groupId 作为标签
      );
    }).toList();
  }

  // 根据漂移信息判断趋势类型
  String? _getTrendType(List<DriftInfo>? driftInfo) {
    if (driftInfo == null || driftInfo.length < 2) {
      return null;
    }

    // 比较最新两个数据点
    final latest = driftInfo.last.score ?? 0;
    final previous = driftInfo[driftInfo.length - 2].score ?? 0;

    if (latest > previous) {
      return 'up'; // 上升
    } else if (latest < previous) {
      return 'down'; // 下降
    } else {
      return 'stable'; // 持平
    }
  }

  // 获取话题建议
  Future<DouyinTopicsSugResponse> getTopics(
    String cookies,
    String keyword,
  ) async {
    try {
      LoggerUtil.i('开始获取话题建议，关键词: "$keyword"');

      if (keyword.trim().isEmpty) {
        throw Exception('关键词不能为空');
      }

      // 根据index.ts中的实现，构建话题搜索API
      final encodedKeyword = Uri.encodeComponent(keyword.trim());
      final apiUrl =
          '${loginUrl}aweme/v1/search/challengesug/?aid=1&keyword=$encodedKeyword';

      LoggerUtil.i('话题建议API: $apiUrl');

      final responseData = await _httpClient.get(
        apiUrl,
        headers: {'Cookie': cookies, 'User-Agent': defaultUserAgent},
      );

      if (responseData == null) {
        throw Exception('话题建议API响应为空');
      }

      // 详细调试日志
      LoggerUtil.i('话题建议API响应: ${jsonEncode(responseData)}');

      // 检查状态码
      if (responseData['status_code'] != null &&
          responseData['status_code'] != 0) {
        throw Exception('话题建议API返回错误: ${responseData['status_msg']}');
      }

      final result = DouyinTopicsSugResponse.fromJson(responseData);
      LoggerUtil.i('话题建议解析成功，数量: ${result.sugList?.length ?? 0}');

      // 显示前几个话题的详细信息（如果有的话）
      if (result.sugList?.isNotEmpty == true) {
        LoggerUtil.i('话题建议示例:');
        for (int i = 0; i < result.sugList!.length && i < 5; i++) {
          final topic = result.sugList![i];
          LoggerUtil.i(
            '话题${i + 1}: ${topic.chaName} (观看数: ${topic.viewCount}, ID: ${topic.cid})',
          );
        }
      }

      return result;
    } catch (e) {
      LoggerUtil.e('获取话题建议失败: $e');
      rethrow;
    }
  }

  // 将话题建议转换为简单的字符串列表（便于UI使用）
  List<String> convertTopicsToStringList(
    DouyinTopicsSugResponse topicResponse,
  ) {
    if (topicResponse.sugList == null || topicResponse.sugList!.isEmpty) {
      return [];
    }

    return topicResponse.sugList!.map((topic) => topic.chaName).toList();
  }

  // 将话题建议转换为Activity列表（兼容现有代码）
  List<Activity> convertTopicsToActivityList(
    DouyinTopicsSugResponse topicResponse,
  ) {
    if (topicResponse.sugList == null || topicResponse.sugList!.isEmpty) {
      return [];
    }

    return topicResponse.sugList!
        .map((topic) => Activity(value: topic.cid, label: topic.chaName))
        .toList();
  }

  /// 获取作品列表 - 代理方法
  Future<DouyinCreatorListResponse> getCreatorItems(
    String cookies, {
    String? cursor,
    String? proxy,
  }) {
    return _contentService.getCreatorItems(
      cookies,
      cursor: cursor,
      proxy: proxy,
    );
  }

  /// 获取评论回复列表 - 代理方法
  Future<DouyinCreatorCommentListResponse> getCreatorCommentReplyList(
    String cookies,
    String commentId, {
    String? cursor,
    int count = 10,
    String? proxy,
  }) {
    return _contentService.getCreatorCommentReplyList(
      cookies,
      commentId,
      cursor: cursor,
      count: count,
      proxy: proxy,
    );
  }

  /// 回复作品评论 - 代理方法
  Future<DouyinNewCommentResponse> creatorCommentReply(
    String cookies,
    Map<String, dynamic> data, {
    String? proxy,
  }) async {
    final csrfToken = await getSecsdkCsrfToken(cookies, proxy: proxy);
    return _contentService.creatorCommentReply(
      cookies,
      csrfToken,
      data,
      proxy: proxy,
    );
  }

  /// 搜索作品列表 - 代理方法
  Future<Map<String, dynamic>> getSearchNodeList(
    String cookies,
    String keyword, {
    String? pcursor,
    int? count,
    String? postFirstId,
    String? proxy,
  }) async {
    try {
      // 解析cookie
      final List<Cookie> parsedCookies = parseCookieString(cookies);

      // 构建页面信息
      final Map<String, dynamic> pageInfo = {
        'pcursor': pcursor ?? '0',
        'count': count ?? 10,
        'postFirstId': postFirstId ?? '',
      };

      // 构建URL和参数
      const String url = 'https://www.douyin.com/aweme/v1/web/search/item/';
      final Map<String, String> params = {
        'device_platform': 'webapp',
        'aid': '6383',
        'keyword': keyword,
        'offset': pageInfo['pcursor'].toString(),
        'count': pageInfo['count'].toString(),
      };

      if (pageInfo['postFirstId'] != null &&
          pageInfo['postFirstId'].toString().isNotEmpty) {
        params['search_id'] = pageInfo['postFirstId'].toString();
      }

      // 构建完整URL
      final String fullUrl = '$url?${Uri(queryParameters: params).query}';

      // 发送请求
      final response = await _httpClient.get(
        fullUrl,
        headers: {
          'Cookie': cookieListToString(parsedCookies),
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Referer': 'https://www.douyin.com/',
        },
        proxy: proxy,
      );

      // 返回原始响应数据，由调用方使用DouyinSearchResponse.fromJson解析
      return response;
    } catch (e) {
      LoggerUtil.e('获取搜索结果失败: $e');
      rethrow;
    }
  }

  /// 获取网页版评论列表
  Future<Map<String, dynamic>> getWebCommentList(
    String cookies,
    String awemeId, {
    String? cursor,
    int count = 20,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取网页版评论列表，awemeId: $awemeId');

      final response = await _contentService.getWebCommentList(
        cookies,
        awemeId,
        cursor: cursor,
        count: count,
        proxy: proxy,
      );

      // 检查响应是否包含必要的字段
      if (response['status_code'] != 0 ||
          response['comment_info_list'] == null) {
        LoggerUtil.w('评论列表响应异常: ${jsonEncode(response)}');
        if (response['status_msg'] != null &&
            response['status_msg'].toString().isNotEmpty) {
          throw Exception('获取评论失败: ${response['status_msg']}');
        }
      }

      return response;
    } catch (e) {
      LoggerUtil.e('获取网页版评论列表失败: $e');
      rethrow;
    }
  }

  /// 获取网页版评论列表（解析后的对象）
  Future<DouyinWebCommentListResponse> getWebCommentListParsed(
    String cookies,
    String awemeId, {
    String? cursor,
    int count = 20,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取网页版评论列表（解析后），awemeId: $awemeId');

      final response = await _contentService.getWebCommentListParsed(
        cookies,
        awemeId,
        cursor: cursor,
        count: count,
        proxy: proxy,
      );

      return response;
    } catch (e) {
      LoggerUtil.e('获取网页版评论列表（解析后）失败: $e');
      rethrow;
    }
  }

  /// 点赞作品 - 代理方法
  Future<Map<String, dynamic>> likeContent(
    String cookies,
    String awemeId,
    int type, {
    String? proxy,
  }) async {
    final csrfToken = await getSecsdkCsrfToken(cookies, proxy: proxy);
    return _contentService.likeContent(
      cookies,
      csrfToken,
      awemeId,
      type,
      proxy: proxy,
    );
  }

  /// 收藏作品 - 代理方法
  Future<Map<String, dynamic>> collectContent(
    String cookies,
    String awemeId,
    int type, {
    String? proxy,
  }) async {
    final csrfToken = await getSecsdkCsrfToken(cookies, proxy: proxy);
    return _contentService.collectContent(
      cookies,
      csrfToken,
      awemeId,
      type,
      proxy: proxy,
    );
  }

  /// 发表评论 - 代理方法
  Future<Map<String, dynamic>> publishComment(
    String cookies,
    Map<String, dynamic> data, {
    String? proxy,
  }) async {
    final csrfToken = await getSecsdkCsrfToken(cookies, proxy: proxy);
    return _contentService.publishComment(
      cookies,
      csrfToken,
      data,
      proxy: proxy,
    );
  }

  /// 获取作品评论列表 - 创作者平台
  Future<DouyinCreatorCommentListResponse> getCreatorCommentList(
    String cookies,
    String itemId, {
    String? cursor,
    int count = 10,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取作品评论列表，itemId: $itemId, cursor: $cursor');

      final response = await _contentService.getCreatorCommentList(
        cookies,
        itemId,
        cursor: cursor,
        count: count,
        proxy: proxy,
      );

      // 打印原始响应数据，用于调试
      try {
        LoggerUtil.d(
          '评论列表原始响应: ${jsonEncode({'status_code': response.statusCode, 'comment_count': response.comments.length, 'cursor': response.cursor, 'has_more': response.hasMore})}',
        );
      } catch (e) {
        LoggerUtil.w('打印评论列表信息失败: $e');
      }

      return response;
    } catch (e) {
      LoggerUtil.e('获取作品评论列表失败: $e');
      rethrow;
    }
  }

  /// 解析Cookie字符串为Cookie对象列表
  List<Cookie> parseCookieString(String cookieString) {
    final cookies = <Cookie>[];

    try {
      // 如果是JSON格式的cookie字符串，先尝试解析
      if (cookieString.startsWith('[') && cookieString.endsWith(']')) {
        try {
          final jsonCookies = jsonDecode(cookieString) as List<dynamic>;
          for (final cookie in jsonCookies) {
            if (cookie is Map<String, dynamic>) {
              cookies.add(Cookie(cookie['name'] ?? '', cookie['value'] ?? ''));
            }
          }
          return cookies;
        } catch (e) {
          LoggerUtil.w('解析JSON格式Cookie失败: $e');
          // 如果JSON解析失败，继续尝试解析普通Cookie字符串
        }
      }

      // 处理普通的Cookie字符串
      final cookieParts = cookieString.split(';');
      for (final part in cookieParts) {
        final trimmedPart = part.trim();
        if (trimmedPart.isEmpty) continue;

        final index = trimmedPart.indexOf('=');
        if (index > 0) {
          final name = trimmedPart.substring(0, index).trim();
          final value = trimmedPart.substring(index + 1).trim();
          cookies.add(Cookie(name, value));
        }
      }
    } catch (e) {
      LoggerUtil.e('解析Cookie字符串失败: $e');
    }

    return cookies;
  }

  /// 将Cookie对象列表转换为字符串
  String cookieListToString(List<Cookie> cookies) {
    return cookies.map((c) => '${c.name}=${c.value}').join('; ');
  }

  /// 获取网页版评论列表(hj域名)
  Future<Map<String, dynamic>> getWebCommentListHj(
    String cookies,
    String awemeId, {
    String? cursor,
    int count = 20,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取网页版评论列表(hj域名)，awemeId: $awemeId');

      // 解析cookie
      final List<Cookie> parsedCookies = parseCookieString(cookies);

      // 构建请求参数
      final queryParams = {
        'aweme_id': awemeId,
        'count': count.toString(),
        'cursor': cursor ?? '0',
        'device_platform': 'webapp',
        'aid': '6383',
        'cookie_enabled': 'true',
        'platform': 'PC',
        'sort_type': '2',
      };

      // 使用hj域名
      const url = 'https://www-hj.douyin.com/aweme/v1/web/comment/list/';
      final fullUrl = '$url?${Uri(queryParameters: queryParams).query}';

      // 发送请求
      final response = await _httpClient.get(
        fullUrl,
        headers: {
          'Cookie': cookieListToString(parsedCookies),
          'User-Agent': defaultUserAgent,
          'Referer': 'https://www.douyin.com/video/$awemeId',
          'Accept': 'application/json, text/plain, */*',
          'Origin': 'https://www.douyin.com',
        },
        proxy: proxy,
      );

      // 检查响应
      if (response == null) {
        throw Exception('获取网页版评论列表(hj域名)失败: 响应为空');
      }

      // 检查响应状态码
      if (response['status_code'] != 0) {
        throw Exception(
          '获取网页版评论列表(hj域名)失败: ${response['status_msg'] ?? '未知错误'}',
        );
      }

      LoggerUtil.i('评论列表(hj域名)获取成功，数量: ${response['comments']?.length ?? 0}');

      return response;
    } catch (e) {
      LoggerUtil.e('获取网页版评论列表(hj域名)失败: $e');
      rethrow;
    }
  }

  /// 获取搜索作品的评论列表
  Future<Map<String, dynamic>> getSearchAwemeCommentList(
    String cookies,
    String awemeId, {
    String? cursor,
    int count = 20,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取搜索作品的评论列表，awemeId: $awemeId');

      // 解析cookie
      final List<Cookie> parsedCookies = parseCookieString(cookies);

      // 搜索作品评论请求参数
      final queryParams = {
        'aweme_id': awemeId,
        'cursor': cursor ?? '0',
        'count': count.toString(),
        'a_bogus':
            'dX0fgqUEY2mfFdKGuOfg743UWS2/Nsuyz-idReZPHOOLT7lGmRPGpPSZbozcYEW5MWB0h937iVllYxdcKsXkZKrpwmhvS/7RsUI998so0qqpT0hDEqfNCwWT9JaT0cwL8CKbJARVUzmc2dA4D1r0UB-JH/Pn4mipQHaWdnUGT9tfgM49PrFxuOtDiXzx5OI41f==',
      };

      // 使用搜索评论API
      const url = 'https://www.douyin.com/aweme/v1/web/comment/list/';
      final fullUrl = '$url?${Uri(queryParameters: queryParams).query}';

      // 发送请求
      final response = await _httpClient.get(
        fullUrl,
        headers: {
          'Cookie': cookieListToString(parsedCookies),
          'User-Agent': defaultUserAgent,
          'Referer': 'https://www.douyin.com/search',
          'Accept': 'application/json, text/plain, */*',
          'Origin': 'https://www.douyin.com',
        },
        proxy: proxy,
      );

      // 检查响应
      if (response == null) {
        throw Exception('获取搜索作品评论列表失败: 响应为空');
      }

      LoggerUtil.i('搜索作品评论列表获取成功，数量: ${response['comments']?.length ?? 0}');

      return response;
    } catch (e) {
      LoggerUtil.e('获取搜索作品评论列表失败: $e');
      rethrow;
    }
  }
  
  /// 根据关键词搜索POI位置信息
  /// 
  /// [cookies] Cookie字符串
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [keywords] 搜索关键词（可选）
  /// [page] 当前页码，默认为1
  /// [count] 每页数量，默认为12
  /// [proxy] 代理地址（可选）
  Future<DouyinPoiSearchResponse> searchPoi({
    required String cookies,
    required double latitude,
    required double longitude,
    String? keywords,
    int page = 1,
    int count = 12,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('【抖音POI】开始搜索位置信息 - 纬度: $latitude, 经度: $longitude, 关键词: ${keywords ?? '无'}');
      
      final response = await _poiService.searchPoi(
        cookies: cookies,
        latitude: latitude,
        longitude: longitude,
        keywords: keywords,
        page: page,
        count: count,
        proxy: proxy,
      );
      
      // 将原始响应转换为结构化对象
      final result = DouyinPoiSearchResponse.fromJson(response);
      
      LoggerUtil.i('【抖音POI】搜索结果 - 状态码: ${result.statusCode}, 消息: ${result.statusMsg}');
      LoggerUtil.i('【抖音POI】搜索结果 - 共找到 ${result.poiList.length} 个位置');
      
      // 打印前几个POI的详细信息
      for (int i = 0; i < result.poiList.length && i < 3; i++) {
        final poi = result.poiList[i];
        LoggerUtil.i('【抖音POI】位置 ${i + 1}: ${poi.poiName}');
        LoggerUtil.i('【抖音POI】 - 地址: ${poi.address}');
        LoggerUtil.i('【抖音POI】 - 经纬度: (${poi.longitude}, ${poi.latitude})');
        LoggerUtil.i('【抖音POI】 - 城市: ${poi.cityName}, 区: ${poi.districtName}');
        LoggerUtil.i('【抖音POI】 - 类型: ${poi.typeName}, 距离: ${poi.distance}米');
      }
      
      return result;
    } catch (e) {
      LoggerUtil.e('POI搜索失败: $e');
      rethrow;
    }
  }
  
  /// 获取当前位置的POI信息
  /// 
  /// [cookies] Cookie字符串
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [proxy] 代理地址（可选）
  Future<DouyinPoiSearchResponse> getCurrentLocationPoi({
    required String cookies,
    required double latitude,
    required double longitude,
    String? proxy,
  }) async {
    try {
      final response = await _poiService.getCurrentLocationPoi(
        cookies: cookies,
        latitude: latitude,
        longitude: longitude,
        proxy: proxy,
      );
      
      // 将原始响应转换为结构化对象
      return DouyinPoiSearchResponse.fromJson(response);
    } catch (e) {
      LoggerUtil.e('获取当前位置POI信息失败: $e');
      rethrow;
    }
  }
}
