import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_test_entry.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_test_page.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/plat_douyin_publish_test_page.dart';
import 'package:flutter/material.dart';

class DouyinDemoEntry extends StatelessWidget {
  const DouyinDemoEntry({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('抖音功能测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTestCard(
              context,
              title: 'DouyinService 直接测试',
              description: '直接调用 DouyinService 的各种方法进行测试\n包含热点、活动、话题、发布等功能',
              icon: Icons.code,
              color: Colors.blue,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DouyinTestPage(),
                  ),
                );
              },
            ),
            
            const SizedBox(height: 16),
            
            _buildTestCard(
              context,
              title: 'PlatDouyin 发布测试',
              description: '通过 PlatDouyin 类测试标准化的发布接口',
              icon: Icons.publish,
              color: Colors.green,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PlatDouyinPublishTestPage(),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),

            _buildTestCard(
              context,
              title: 'PlatDouyin 评论测试',
              description: '抖音评论测试',
              icon: Icons.publish,
              color: Colors.green,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>  DouyinTestEntry.getDouyinContentTestPage(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 32),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey.shade400),
            ],
          ),
        ),
      ),
    );
  }
}
