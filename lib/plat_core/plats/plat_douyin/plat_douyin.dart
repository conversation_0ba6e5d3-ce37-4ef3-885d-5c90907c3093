import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_base.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_types.dart';

class PlatDouyin extends PlatBase {
  final DouyinService _douyinService = DouyinService();

  PlatDouyin(super.platformModel);

  @override
  Future<PublishResponseModel> publishVideoWithPlatformConfig(
    PublishParamsModel publishParamsModel,
    Map<String, dynamic> platformConfig,
  ) async {
    try {
      // 构建抖音平台所需的发布参数，使用平台特定配置
      var setting = DouyinPlatformSettingType(
        userDeclare: platformConfig['self_declare'], // 自主声明
        title: publishParamsModel.title ?? '',
        caption: publishParamsModel.desc,
        topics: publishParamsModel.topics,
        cover: publishParamsModel.cover.path,
        visibilityType:
            platformConfig['visibility_type'] ??
            publishParamsModel.visibleType.index,
        timingTime: publishParamsModel.timingTime?.toInt(),
        proxyIp: '', // 需要设置代理IP
      );

      // 如果有位置信息，添加位置
      if (publishParamsModel.location != null) {
        setting = setting.copyWith(
          poiInfo: PoiInfo(
            poiId: publishParamsModel.location!.id,
            poiName: publishParamsModel.location!.name,
          ),
        );
      }

      // 如果有@好友信息，添加@好友（从平台配置中获取）
      final mentionedUsers =
          platformConfig['mentioned_users'] as List<Map<String, String>>?;
      if (mentionedUsers != null && mentionedUsers.isNotEmpty) {
        setting = setting.copyWith(
          mentionedUserInfo:
              mentionedUsers
                  .map(
                    (e) => MentionedUserInfo(
                      nickName: e['name'] ?? '',
                      uid: e['value'] ?? '',
                    ),
                  )
                  .toList(),
        );
      }

      // 如果有热点信息，添加热点
      if (platformConfig['hotspot_id'] != null) {
        setting = setting.copyWith(hotSentence: platformConfig['hotspot_id']);
      }

      // 如果有合集信息，添加合集
      if (platformConfig['mix_id'] != null) {
        setting = setting.copyWith(
          mixInfo: MixInfo(
            mixId: platformConfig['mix_id'],
            mixName: platformConfig['mix_name'] ?? '', // 需要提供合集名称
          ),
        );
      }

      // 如果有活动信息，添加活动
      if (platformConfig['activity_id'] != null) {
        setting = setting.copyWith(
          activity: [
            Activity(
              value: platformConfig['activity_id'],
              label: platformConfig['activity_name'] ?? '', // 需要提供活动名称
            ),
          ],
        );
      }

      LoggerUtil.i('抖音发布参数: ${setting.toString()}');
      LoggerUtil.i('平台特定配置: $platformConfig');

      // 调用发布视频API
      final result = await _douyinService.publishVideoWorkApi(
        cookieStr,
        '', // tokens - 如果需要的话
        publishParamsModel.video!.path,
        setting,
        (progress, msg) {
          publishParamsModel.progressCallback?.call(progress, msg);
          LoggerUtil.i('上传进度: $progress%, 消息: $msg');
        },
      );

      return PublishResponseModel(
        true,
        dataId: result.publishId,
        workLink: result.shareLink,
      );
    } catch (e) {
      LoggerUtil.e('发布视频失败: $e');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }

  @override
  Future<PublishResponseModel> publishVideo(publishParamsModel) async {
    try {
      // 构建抖音平台所需的发布参数
      var setting = DouyinPlatformSettingType(
        userDeclare: null, // 自主声明，根据需要设置
        title: publishParamsModel.title ?? '',
        caption: publishParamsModel.desc,
        topics: publishParamsModel.topics,
        cover: publishParamsModel.cover.path,
        visibilityType: publishParamsModel.visibleType.index,
        timingTime: publishParamsModel.timingTime?.toInt(),
        proxyIp: '', // 需要设置代理IP
      );

      // 如果有位置信息，添加位置
      if (publishParamsModel.location != null) {
        setting = setting.copyWith(
          poiInfo: PoiInfo(
            poiId: publishParamsModel.location!.id,
            poiName: publishParamsModel.location!.name,
          ),
        );
      }

      // 如果有@好友信息，添加@好友
      if (publishParamsModel.mentionedUserInfo != null &&
          publishParamsModel.mentionedUserInfo!.isNotEmpty) {
        setting = setting.copyWith(
          mentionedUserInfo:
              publishParamsModel.mentionedUserInfo!
                  .map((e) => MentionedUserInfo(nickName: e.name, uid: e.value))
                  .toList(),
        );
      }

      // 调用发布视频API
      final result = await _douyinService.publishVideoWorkApi(
        cookieStr,
        '', // tokens - 如果需要的话
        publishParamsModel.video!.path,
        setting,
        (progress, msg) {
          LoggerUtil.i('上传进度: $progress%, 消息: $msg');
        },
      );

      return PublishResponseModel(
        true,
        dataId: result.publishId,
        workLink: result.shareLink,
      );
    } catch (e) {
      LoggerUtil.e('发布视频失败: $e');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }

  @override
  Future<PublishResponseModel> publishImgText(publishParamsModel) async {
    try {
      if (publishParamsModel.images == null ||
          publishParamsModel.images!.isEmpty) {
        throw Exception('没有上传图片');
      }

      // 构建抖音平台所需的发布参数
      var setting = DouyinPlatformSettingType(
        userDeclare: null, // 自主声明，根据需要设置
        title: publishParamsModel.title ?? '',
        caption: publishParamsModel.desc,
        topics: publishParamsModel.topics,
        cover: publishParamsModel.images![0].path, // 使用第一张图片作为封面
        visibilityType: publishParamsModel.visibleType.index,
        timingTime: publishParamsModel.timingTime?.toInt(),
        proxyIp: '', // 需要设置代理IP
        mediaType: 4, // 明确设置媒体类型为图文(4)
      );

      // 如果有位置信息，添加位置
      if (publishParamsModel.location != null) {
        setting = setting.copyWith(
          poiInfo: PoiInfo(
            poiId: publishParamsModel.location!.id,
            poiName: publishParamsModel.location!.name,
          ),
        );
      }

      // 如果有@好友信息，添加@好友
      if (publishParamsModel.mentionedUserInfo != null &&
          publishParamsModel.mentionedUserInfo!.isNotEmpty) {
        setting = setting.copyWith(
          mentionedUserInfo:
              publishParamsModel.mentionedUserInfo!
                  .map((e) => MentionedUserInfo(nickName: e.name, uid: e.value))
                  .toList(),
        );
      }

      // 获取图片路径列表
      final imagePaths =
          publishParamsModel.images!.map((file) => file.path).toList();

      LoggerUtil.i(
        '准备发布图文，图片数量: ${imagePaths.length}, 媒体类型: ${setting.mediaType}',
      );

      // 添加进度回调函数
      final result = await _douyinService.publishImageWorkApi(
        cookieStr,
        '', // tokens - 如果需要的话
        imagePaths,
        setting,
        (progress, msg) {
          // 更新发布任务进度
          publishParamsModel.progressCallback?.call(progress, msg);
          LoggerUtil.i('图文上传进度: $progress%, 消息: $msg');
        },
      );

      return PublishResponseModel(
        true,
        dataId: result.publishId,
        workLink: result.shareLink,
      );
    } catch (e) {
      LoggerUtil.e('发布图文失败: $e');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }

  @override
  Future<Map<String, dynamic>> getUserInfo() async {
    try {
      final userInfo = await _douyinService.getUserInfo(cookieStr);
      return {
        'uid': userInfo.uid,
        'authorId': userInfo.authorId,
        'nickname': userInfo.nickname,
        'avatar': userInfo.avatar,
        'fansCount': userInfo.fansCount,
      };
    } catch (e) {
      LoggerUtil.e('获取用户信息失败: $e');
      rethrow;
    }
  }

  // 获取数据表现
  Future<Map<String, dynamic>> getDashboard({
    String? startDate,
    String? endDate,
  }) async {
    try {
      final result = await _douyinService.getDashboardFunc(
        cookieStr,
        startDate,
        endDate,
      );
      return result;
    } catch (e) {
      LoggerUtil.e('获取数据表现失败: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // 获取活动列表
  Future<List<Activity>> getActivities() async {
    try {
      final result = await _douyinService.getActivity(cookieStr);
      return result.activityList ?? [];
    } catch (e) {
      LoggerUtil.e('获取活动列表失败: $e');
      return [];
    }
  }

  // 获取活动详细信息列表
  Future<List<DouyinActivityDetail>> getActivityDetails() async {
    try {
      final result = await _douyinService.getActivity(cookieStr);
      return result.activityDetailList ?? [];
    } catch (e) {
      LoggerUtil.e('获取活动详细信息失败: $e');
      return [];
    }
  }

  // 获取活动完整响应信息
  Future<DouyinActivityListResponse> getActivityResponse() async {
    try {
      final result = await _douyinService.getActivity(cookieStr);
      return result;
    } catch (e) {
      LoggerUtil.e('获取活动响应失败: $e');
      rethrow;
    }
  }

  // 获取合集列表
  Future<List<MixInfo>> getMixList() async {
    try {
      final result = await _douyinService.getMixList(cookieStr);
      return result.mixList ?? [];
    } catch (e) {
      LoggerUtil.e('获取合集列表失败: $e');
      return [];
    }
  }

  // 获取热点数据
  Future<Map<String, dynamic>> getHotspotData(String query) async {
    try {
      final result = await _douyinService.getHotspotData(
        cookieStr,
        query: query,
      );

      // 使用转换方法将新的数据格式转换为HotspotItem列表
      final hotspotItems = _douyinService.convertHotDataToHotspotItems(result);

      return {
        'success': true,
        'data':
            hotspotItems
                .map(
                  (item) => {
                    'title': item.title,
                    'description': item.description,
                    'hotValue': item.hotValue,
                    'trendType': item.trendType,
                    'category': item.category,
                    'tag': item.tag,
                  },
                )
                .toList(),
        'total': result.sentences?.length ?? 0,
        'hasMore': false, // 热点数据通常不分页
      };
    } catch (e) {
      LoggerUtil.e('获取热点数据失败: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // 获取话题建议
  Future<List<String>> getTopicSuggestions(String keyword) async {
    try {
      final result = await _douyinService.getTopics(cookieStr, keyword);
      return _douyinService.convertTopicsToStringList(result);
    } catch (e) {
      LoggerUtil.e('获取话题建议失败: $e');
      return [];
    }
  }

  // 获取话题建议（Activity格式）
  Future<List<Activity>> getTopicActivities(String keyword) async {
    try {
      final result = await _douyinService.getTopics(cookieStr, keyword);
      return _douyinService.convertTopicsToActivityList(result);
    } catch (e) {
      LoggerUtil.e('获取话题Activity列表失败: $e');
      return [];
    }
  }

  // 获取话题建议完整响应
  Future<DouyinTopicsSugResponse> getTopicResponse(String keyword) async {
    try {
      final result = await _douyinService.getTopics(cookieStr, keyword);
      return result;
    } catch (e) {
      LoggerUtil.e('获取话题响应失败: $e');
      rethrow;
    }
  }
}
