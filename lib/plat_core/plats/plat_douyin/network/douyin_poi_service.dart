import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/network/douyin_http_client.dart';
import 'dart:convert'; // 导入json处理库

/// 抖音位置服务类
class DouyinPoiService {
  // HTTP 客户端
  final DouyinHttpClient _httpClient = DouyinHttpClient();
  
  /// 根据关键词搜索POI位置信息
  /// 
  /// [cookies] Cookie字符串
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [keywords] 搜索关键词（可选）
  /// [page] 当前页码，默认为1
  /// [count] 每页数量，默认为12
  Future<Map<String, dynamic>> searchPoi({
    required String cookies,
    required double latitude,
    required double longitude,
    String? keywords,
    int page = 1,
    int count = 12,
    String? proxy,
  }) async {
    try {
      // 构建请求URL和参数
      const String baseUrl = 'https://creator.douyin.com/aweme/v1/life/video_api/search/poi/';
      
      // 构建查询参数
      final Map<String, String> queryParams = {
        'count': count.toString(),
        'from_webapp': '1',
        'get_current_loc': '1',
        'is_image_album_style': '1',
        'keywords': keywords ?? '',
        'search_type': '0',
        'poi_anchor_tab': '2',
        'page': page.toString(),
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'poi_mode': '2',
        'load_interest_city_type': '1',
        'cookie_enabled': 'true',
        'screen_width': '1440',
        'screen_height': '900',
        'browser_language': 'zh-CN',
        'browser_platform': 'Win32',
        'browser_name': 'Mozilla',
        'browser_version': '5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'browser_online': 'true',
        'timezone_name': 'Asia/Shanghai',
      };
      
      // 构建完整URL
      final String fullUrl = '$baseUrl?${Uri(queryParameters: queryParams).query}';
      
      LoggerUtil.i('【抖音POI服务】开始搜索POI位置信息: lat=$latitude, lon=$longitude, 关键词="${keywords ?? ''}"');
      LoggerUtil.i('【抖音POI服务】请求URL: $baseUrl');
      LoggerUtil.i('【抖音POI服务】请求参数: ${jsonEncode(queryParams)}');
      LoggerUtil.i('【抖音POI服务】Cookie长度: ${cookies.length}');
      
      // 构建请求头
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'priority': 'u=1, i',
        'referer': 'https://creator.douyin.com/creator-micro/content/post/image?default-tab=3&enter_from=publish_page&media_type=image&type=new',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'Cookie': cookies,
      };
      
      // 发送请求
      LoggerUtil.i('【抖音POI服务】正在发送请求...');
      
      final response = await _httpClient.get(
        fullUrl,
        headers: headers,
        proxy: proxy,
      );
      
      // 检查响应
      if (response == null) {
        LoggerUtil.e('【抖音POI服务】响应为空');
        throw Exception('搜索POI位置失败: 响应为空');
      }
      
      // 打印原始响应数据
      String test  =response.toString();
      LoggerUtil.i('【抖音POI服务】响应状态码: ${response['status_code'] ?? 'null'}');
      LoggerUtil.i('【抖音POI服务】状态消息: ${response['status_msg'] ?? 'null'}');
      
      // 检查状态码
      if (response['status_code'] != 0) {
        LoggerUtil.e('【抖音POI服务】响应错误: ${response['status_msg'] ?? '未知错误'}');
        throw Exception('搜索POI位置失败: ${response['status_msg'] ?? '未知错误'}');
      }
      
      final poiList = response['poi_list'] as List? ?? [];
      LoggerUtil.i('【抖音POI服务】请求成功，返回POI数量: ${poiList.length}');
      
      // 打印完整响应数据（仅在调试时）
      try {
        LoggerUtil.d('【抖音POI服务】完整响应数据: ${jsonEncode(response)}');
      } catch (e) {
        LoggerUtil.w('【抖音POI服务】响应数据无法序列化为JSON: $e');
      }
      
      // 打印部分POI数据
      for (int i = 0; i < poiList.length && i < 3; i++) {
        final poi = poiList[i];
        LoggerUtil.i('【抖音POI服务】POI ${i + 1}: ID=${poi['poi_id']} 名称=${poi['poi_name']}');
        LoggerUtil.i('【抖音POI服务】  地址=${poi['address']} 类型=${poi['type_name']} 城市=${poi['city_name']}');
        LoggerUtil.i('【抖音POI服务】  经纬度=(${poi['longitude']}, ${poi['latitude']}) 距离=${poi['distance']}');
      }
      return response;
    } catch (e) {
      LoggerUtil.e('搜索POI位置信息失败: $e');
      rethrow;
    }
  }
  
  /// 获取当前位置的POI信息
  /// 
  /// [cookies] Cookie字符串
  /// [latitude] 纬度
  /// [longitude] 经度
  Future<Map<String, dynamic>> getCurrentLocationPoi({
    required String cookies,
    required double latitude,
    required double longitude,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('【抖音POI服务】获取当前位置POI: lat=$latitude, lon=$longitude');
      
      final response = await searchPoi(
        cookies: cookies,
        latitude: latitude,
        longitude: longitude,
        keywords: '',
        page: 1,
        count: 12,
        proxy: proxy,
      );
      
      LoggerUtil.i('【抖音POI服务】当前位置POI获取成功');
      return response;
    } catch (e) {
      LoggerUtil.e('【抖音POI服务】获取当前位置POI信息失败: $e');
      rethrow;
    }
  }
} 