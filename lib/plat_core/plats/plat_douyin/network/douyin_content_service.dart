import 'dart:convert';
import 'dart:math' as math;

import 'package:aitoearn_app/api/models/base_response.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_types.dart'
    hide DouyinCreatorListResponse;
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_creator_list_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/network/douyin_http_client.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';

/// 抖音内容服务类 - 处理获取作品信息和评论相关功能
class DouyinContentService {
  final DouyinHttpClient _httpClient = DouyinHttpClient();
  final Dio _dio = Dio();

  // 评论相关的API接口
  final String _creatorItemListUrl =
      'https://creator.douyin.com/aweme/v1/creator/item/list/';
  final String _creatorCommentListUrl =
      'https://creator.douyin.com/aweme/v1/creator/comment/list/';
  final String _creatorCommentReplyListUrl =
      'https://creator.douyin.com/aweme/v1/creator/comment/reply/list/';
  final String _creatorCommentReplyUrl =
      'https://creator.douyin.com/aweme/v1/creator/comment/reply/';
  final String _likeUrl =
      'https://creator.douyin.com/aweme/v1/creator/item/digg/';
  final String _collectUrl =
      'https://creator.douyin.com/aweme/v1/creator/item/collect/';
  final String _searchUrl = 'https://creator.douyin.com/aweme/v1/creator/find/';
  final String _webCommentUrl =
      'https://www.douyin.com/aweme/v1/web/comment/list/';
  final String _commentUrl =
      'https://creator.douyin.com/aweme/v1/creator/comment/';
  final String _webPublishCommentUrl =
      'https://www.douyin.com/aweme/v1/web/comment/publish/';

  /// 获取抖音作品详情
  ///
  /// [awemeId] 作品ID
  Future<BaseResponse<Map<String, dynamic>?>> getAwemeDetail({
    required String awemeId,
  }) async {
    try {
      LoggerUtil.i('【DouyinContentService】开始获取抖音作品详情, awemeId: $awemeId');

      const url = 'https://platapi.yikart.cn/api/douyin/aweme_detail';

      final headers = {'Content-Type': 'application/json'};

      final data = {'aweme_id': awemeId};

      LoggerUtil.i('【DouyinContentService】发送请求: $url');
      LoggerUtil.i('【DouyinContentService】请求参数: $data');

      final response = await _dio.post(
        url,
        options: Options(headers: headers),
        data: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        LoggerUtil.i('【DouyinContentService】请求成功，解析数据');

        final responseData = response.data;
        if (responseData == null) {
          return BaseResponse(code: -1, msg: '响应数据为空', data: null);
        }

        final code = responseData['code'];
        final msg = responseData['msg'] ?? '';

        if (code == 200) {
          return BaseResponse(
            code: code,
            msg: msg,
            data: responseData['data'],
            success: true,
          );
        } else {
          return BaseResponse(code: code, msg: msg, data: null);
        }
      } else {
        return BaseResponse(
          code: response.statusCode ?? -1,
          msg: '请求失败: ${response.statusMessage}',
          data: null,
        );
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【DouyinContentService】获取抖音作品详情异常: $e\n$stackTrace');

      return BaseResponse(code: -1, msg: '获取抖音作品详情异常: $e', data: null);
    }
  }

  /// 获取抖音作品评论
  ///
  /// [awemeId] 作品ID
  /// [cursor] 分页游标，默认从0开始
  /// [count] 每页数量，默认10条
  /// [shareText] 分享文本，默认为空
  /// [proxy] 代理设置，默认为空
  /// [cookie] 用户Cookie，可选参数
  Future<BaseResponse<Map<String, dynamic>?>> getVideoComments({
    required String awemeId,
    String cursor = '',
    int count = 10,
    String shareText = '',
    String proxy = '',
    String? cookie,
  }) async {
    try {
      LoggerUtil.i(
        '【DouyinContentService】开始获取抖音作品评论, awemeId: $awemeId, cursor: $cursor',
      );

      const url = 'https://platapi.yikart.cn/api/douyin/video_comment';

      final headers = {'Content-Type': 'application/json'};

      final data = {
        'aweme_id': awemeId,
        'cursor': cursor,
        'count': count,
        'share_text': shareText,
        'proxy': proxy,
      };

      // 如果提供了cookie，添加到请求数据中
      if (cookie != null && cookie.isNotEmpty) {
        data['cookie'] = cookie;
        LoggerUtil.i('【DouyinContentService】使用提供的cookie获取评论');
      }

      LoggerUtil.i('【DouyinContentService】发送请求: $url');
      LoggerUtil.i('【DouyinContentService】请求参数: $data');

      final response = await _dio.post(
        url,
        options: Options(headers: headers),
        data: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        LoggerUtil.i('【DouyinContentService】请求成功，解析数据');

        final responseData = response.data;
        if (responseData == null) {
          return BaseResponse(code: -1, msg: '响应数据为空', data: null);
        }

        final code = responseData['code'];
        final msg = responseData['msg'] ?? '';

        if (code == 200) {
          return BaseResponse(
            code: code,
            msg: msg,
            data: responseData['data'],
            success: true,
          );
        } else {
          return BaseResponse(code: code, msg: msg, data: null);
        }
      } else {
        return BaseResponse(
          code: response.statusCode ?? -1,
          msg: '请求失败: ${response.statusMessage}',
          data: null,
        );
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【DouyinContentService】获取抖音作品评论异常: $e\n$stackTrace');

      return BaseResponse(code: -1, msg: '获取抖音作品评论异常: $e', data: null);
    }
  }

  /// 获取抖音作品二级评论
  ///
  /// [awemeId] 作品ID
  /// [commentId] 一级评论ID
  /// [cursor] 分页游标，默认从0开始
  /// [count] 每页数量，默认10条
  /// [proxy] 代理设置，默认为空
  /// [cookie] 用户Cookie，可选参数
  Future<BaseResponse<Map<String, dynamic>?>> getVideoReplyComments({
    required String awemeId,
    required String commentId,
    String cursor = '',
    int count = 10,
    String proxy = '',
    String? cookie,
  }) async {
    try {
      LoggerUtil.i(
        '【DouyinContentService】开始获取抖音作品二级评论, awemeId: $awemeId, commentId: $commentId, cursor: $cursor',
      );

      const url = 'https://platapi.yikart.cn/api/douyin/video_sub_comment';

      final headers = {'Content-Type': 'application/json'};

      final data = {
        'aweme_id': awemeId,
        'comment_id': commentId,
        'cursor': cursor,
        'count': count,
        'proxy': proxy,
      };

      // 如果提供了cookie，添加到请求数据中
      if (cookie != null && cookie.isNotEmpty) {
        data['cookie'] = cookie;
        LoggerUtil.i('【DouyinContentService】使用提供的cookie获取二级评论');
      }

      LoggerUtil.i('【DouyinContentService】发送请求: $url');
      LoggerUtil.i('【DouyinContentService】请求参数: $data');

      final response = await _dio.post(
        url,
        options: Options(headers: headers),
        data: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        LoggerUtil.i('【DouyinContentService】请求成功，解析数据');

        final responseData = response.data;
        if (responseData == null) {
          return BaseResponse(code: -1, msg: '响应数据为空', data: null);
        }

        final code = responseData['code'];
        final msg = responseData['msg'] ?? '';

        if (code == 200) {
          return BaseResponse(
            code: code,
            msg: msg,
            data: responseData['data'],
            success: true,
          );
        } else {
          return BaseResponse(code: code, msg: msg, data: null);
        }
      } else {
        return BaseResponse(
          code: response.statusCode ?? -1,
          msg: '请求失败: ${response.statusMessage}',
          data: null,
        );
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【DouyinContentService】获取抖音作品二级评论异常: $e\n$stackTrace');

      return BaseResponse(code: -1, msg: '获取抖音作品二级评论异常: $e', data: null);
    }
  }

  /// 获取作品列表
  Future<DouyinCreatorListResponse> getCreatorItems(
    String cookies, {
    String? cursor,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取作品列表，cursor: $cursor');

      final queryParams = {'count': '20', 'status': '0'};

      if (cursor != null && cursor.isNotEmpty) {
        queryParams['cursor'] = cursor;
      }

      final response = await _httpClient.get(
        _creatorItemListUrl,
        queryParams: queryParams,
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
          'Accept': 'application/json, text/plain, */*',
          'Referer': 'https://creator.douyin.com/creator-micro/content/manage',
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('获取作品列表失败: 响应为空');
      }

      return DouyinCreatorListResponse.fromJson(response);
    } catch (e) {
      LoggerUtil.e('获取作品列表失败: $e');
      rethrow;
    }
  }

  /// 获取作品评论列表
  Future<DouyinCreatorCommentListResponse> getCreatorCommentList(
    String cookies,
    String itemId, {
    String? cursor,
    int count = 10,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取作品评论列表，itemId: $itemId, cursor: $cursor');

      final queryParams = {'item_id': itemId, 'count': count.toString()};

      if (cursor != null && cursor.isNotEmpty) {
        queryParams['cursor'] = cursor;
      }

      final response = await _httpClient.get(
        '$_creatorCommentListUrl?${Uri(queryParameters: queryParams).query}',
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('获取评论列表失败: 响应为空');
      }

      return DouyinCreatorCommentListResponse.fromJson(response);
    } catch (e) {
      LoggerUtil.e('获取评论列表失败: $e');
      rethrow;
    }
  }

  /// 获取评论回复列表
  Future<DouyinCreatorCommentListResponse> getCreatorCommentReplyList(
    String cookies,
    String commentId, {
    String? cursor,
    int count = 10,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取评论回复列表，commentId: $commentId, cursor: $cursor');

      final queryParams = {'comment_id': commentId, 'count': count.toString()};

      if (cursor != null && cursor.isNotEmpty) {
        queryParams['cursor'] = cursor;
      }

      final response = await _httpClient.get(
        '$_creatorCommentReplyListUrl?${Uri(queryParameters: queryParams).query}',
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('获取评论回复列表失败: 响应为空');
      }

      return DouyinCreatorCommentListResponse.fromJson(response);
    } catch (e) {
      LoggerUtil.e('获取评论回复列表失败: $e');
      rethrow;
    }
  }

  /// 回复评论
  Future<DouyinNewCommentResponse> creatorCommentReply(
    String cookies,
    String csrfToken,
    Map<String, dynamic> data, {
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('回复评论，data: $data');

      final response = await _httpClient.postJson(
        _creatorCommentReplyUrl,
        data: data,
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
          'X-Secsdk-Csrf-Token': csrfToken,
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*',
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('回复评论失败: 响应为空');
      }

      return DouyinNewCommentResponse.fromJson(response);
    } catch (e) {
      LoggerUtil.e('回复评论失败: $e');
      rethrow;
    }
  }

  /// 搜索作品列表
  Future<Map<String, dynamic>> getSearchNodeList(
    String cookies,
    String keyword, {
    String? pcursor,
    int? count,
    String? postFirstId,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('搜索作品，keyword: $keyword, pcursor: $pcursor');

      // URL 编码关键词，用于构造 Referer
      final encodedKeyword = Uri.encodeComponent(keyword);

      final queryParams = {
        'device_platform': 'webapp',
        'aid': '6383',
        'keyword': keyword,
        'offset': pcursor ?? '0',
        'count': (count ?? 1).toString(),
      };

      if (postFirstId != null && postFirstId.isNotEmpty) {
        queryParams['search_id'] = postFirstId;
      }

      // 使用正确的搜索API
      const searchUrl = 'https://www.douyin.com/aweme/v1/web/search/item/';

      LoggerUtil.i('调用搜索API: $searchUrl');
      LoggerUtil.i('搜索参数: $queryParams');

      final response = await _httpClient.get(
        '$searchUrl?${Uri(queryParameters: queryParams).query}',
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
          'Referer': 'https://www.douyin.com/search/$encodedKeyword?type=video',
          'Accept': 'application/json, text/plain, */*',
          'Origin': 'https://www.douyin.com',
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('搜索作品失败: 响应为空');
      }
      var jsons = json.encode(response);
      LoggerUtil.d('搜索结果: ${json.encode(response)}');

      return response;
    } catch (e) {
      LoggerUtil.e('搜索作品失败: $e');
      rethrow;
    }
  }

  /// 获取网页版评论列表
  Future<Map<String, dynamic>> getWebCommentList(
    String cookies,
    String itemId, {
    String? cursor,
    int count = 10,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取网页版评论列表(hj域名)，itemId: $itemId, cursor: $cursor');

      final queryParams = {
        'aweme_id': itemId,
        'count': count.toString(),
        'device_platform': 'webapp',
        'aid': '6383',
        'cookie_enabled': 'true',
        'platform': 'PC',
        'sort_type': '2',
      };

      if (cursor != null && cursor.isNotEmpty) {
        queryParams['cursor'] = cursor;
      }

      // 使用hj域名
      const url = 'https://www-hj.douyin.com/aweme/v1/web/comment/list/';

      final response = await _httpClient.get(
        '$url?${Uri(queryParameters: queryParams).query}',
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
          'Referer': 'https://www.douyin.com/video/$itemId',
          'Accept': 'application/json, text/plain, */*',
          'Origin': 'https://www.douyin.com',
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('获取网页版评论列表(hj域名)失败: 响应为空');
      }

      LoggerUtil.d('评论列表(hj域名)响应: ${json.encode(response)}');

      return response;
    } catch (e) {
      LoggerUtil.e('获取网页版评论列表(hj域名)失败: $e');
      rethrow;
    }
  }

  /// 获取网页版评论列表（返回解析后的对象）
  Future<DouyinWebCommentListResponse> getWebCommentListParsed(
    String cookies,
    String itemId, {
    String? cursor,
    int count = 20,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取网页版评论列表（解析后），itemId: $itemId, cursor: $cursor');

      final response = await getWebCommentList(
        cookies,
        itemId,
        cursor: cursor,
        count: count,
        proxy: proxy,
      );

      return DouyinWebCommentListResponse.fromJson(response);
    } catch (e) {
      LoggerUtil.e('解析网页版评论列表失败: $e');
      rethrow;
    }
  }

  /// 点赞/取消点赞作品
  Future<Map<String, dynamic>> likeContent(
    String cookies,
    String csrfToken,
    String awemeId,
    int type, {
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('点赞/取消点赞作品，awemeId: $awemeId, type: $type');

      final response = await _httpClient.postForm(
        _likeUrl,
        data: {'aweme_id': awemeId, 'type': type.toString()},
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
          'X-Secsdk-Csrf-Token': csrfToken,
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json, text/plain, */*',
          'Origin': 'https://creator.douyin.com',
          'Referer': 'https://creator.douyin.com/',
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('点赞/取消点赞作品失败: 响应为空');
      }

      return response;
    } catch (e) {
      LoggerUtil.e('点赞/取消点赞作品失败: $e');
      rethrow;
    }
  }

  /// 收藏/取消收藏作品
  Future<Map<String, dynamic>> collectContent(
    String cookies,
    String csrfToken,
    String awemeId,
    int type, {
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('收藏/取消收藏作品，awemeId: $awemeId, type: $type');

      final response = await _httpClient.postForm(
        _collectUrl,
        data: {'aweme_id': awemeId, 'type': type.toString()},
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
          'X-Secsdk-Csrf-Token': csrfToken,
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json, text/plain, */*',
          'Origin': 'https://creator.douyin.com',
          'Referer': 'https://creator.douyin.com/',
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('收藏/取消收藏作品失败: 响应为空');
      }

      return response;
    } catch (e) {
      LoggerUtil.e('收藏/取消收藏作品失败: $e');
      rethrow;
    }
  }

  /// 发表评论
  Future<Map<String, dynamic>> publishComment(
    String cookies,
    String csrfToken,
    Map<String, dynamic> data, {
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('发表评论，data: $data');

      final response = await _httpClient.postForm(
        _commentUrl,
        data: data,
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
          'X-Secsdk-Csrf-Token': csrfToken,
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json, text/plain, */*',
          'Origin': 'https://creator.douyin.com',
          'Referer': 'https://creator.douyin.com/',
        },
        proxy: proxy,
      );

      if (response == null) {
        throw Exception('发表评论失败: 响应为空');
      }

      return response;
    } catch (e) {
      LoggerUtil.e('发表评论失败: $e');
      rethrow;
    }
  }

  /// 获取搜索作品的评论列表
  Future<Map<String, dynamic>> getSearchAwemeCommentList(
    String cookies,
    String itemId, {
    String? cursor,
    int count = 20,
    String? proxy,
  }) async {
    try {
      LoggerUtil.i('获取搜索作品的评论列表，itemId: $itemId, cursor: $cursor');
      
      final queryParams = {
        'aweme_id': itemId,
        'count': count.toString(),
        'cursor': cursor ?? '0',
        // 搜索作品评论需要的特殊参数
        'a_bogus':
            'dX0fgqUEY2mfFdKGuOfg743UWS2/Nsuyz-idReZPHOOLT7lGmRPGpPSZbozcYEW5MWB0h937iVllYxdcKsXkZKrpwmhvS/7RsUI998so0qqpT0hDEqfNCwWT9JaT0cwL8CKbJARVUzmc2dA4D1r0UB-JH/Pn4mipQHaWdnUGT9tfgM49PrFxuOtDiXzx5OI41f==',
      };
      
      // 使用标准域名
      const url = 'https://www.douyin.com/aweme/v1/web/comment/list/';
      
      final response = await _httpClient.get(
        '$url?${Uri(queryParameters: queryParams).query}',
        headers: {
          'Cookie': cookies,
          'User-Agent': _httpClient.defaultUserAgent,
          'Referer': 'https://www.douyin.com/search',
          'Accept': 'application/json, text/plain, */*',
          'Origin': 'https://www.douyin.com',
        },
        proxy: proxy,
      );
      
      if (response == null) {
        throw Exception('获取搜索作品评论列表失败: 响应为空');
      }
      
      LoggerUtil.d('搜索作品评论列表响应: ${json.encode(response)}');
      
      return response;
    } catch (e) {
      LoggerUtil.e('获取搜索作品评论列表失败: $e');
      rethrow;
    }
  }

  /// 搜索抖音内容
  ///
  /// [keyword] 搜索关键词
  /// [cookie] 用户Cookie
  /// [offset] 分页偏移量，默认从0开始
  /// [count] 每页数量，默认10条
  Future<BaseResponse<Map<String, dynamic>?>> searchDouyin({
    required String keyword,
    required String cookie,
    int offset = 0,
    int count = 10,
  }) async {
    try {
      LoggerUtil.i('【DouyinContentService】开始搜索抖音内容，关键词: $keyword, offset: $offset');
      
      // 构建完整的URL，包含查询参数
      final String baseUrl = 'https://www.douyin.com/aweme/v1/web/general/search/stream/';
      final String url = '$baseUrl?aid=6383'
          '&browser_language=zh-CN'
          '&browser_name=Chrome'
          '&browser_online=true'
          '&browser_platform=Win32'
          '&browser_version=*********'
          '&channel=channel_pc_web'
          '&cookie_enabled=true'
          '&count=$count'
          '&cpu_core_num=18'
          '&device_memory=8'
          '&device_platform=webapp'
          '&downlink=10'
          '&effective_type=4g'
          '&enable_history=1'
          '&engine_name=Blink'
          '&engine_version=*********'
          '&from_group_id='
          '&is_filter_search=0'
          '&keyword=${Uri.encodeComponent(keyword)}'
          '&list_type=single'
          '&need_filter_settings=1'
          '&offset=$offset'
          '&os_name=Windows'
          '&os_version=10'
          '&pc_client_type=1'
          '&pc_libra_divert=Windows'
          '&platform=PC'
          '&query_correct_type=1'
          '&round_trip_time=100'
          '&screen_height=900'
          '&screen_width=1440'
          '&search_channel=aweme_general'
          '&search_source=normal_search'
          '&support_dash=1'
          '&support_h265=1'
          '&version_code=190600'
          '&version_name=19.6.0';
      
      // 添加完整的请求头
      final Map<String, String> headers = {
        'accept': '*/*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'priority': 'u=1, i',
        'referer': 'https://www.douyin.com/jingxuan/search/${Uri.encodeComponent(keyword)}?aid=8347d458-e8a8-40cd-a36e-2d8722de434e&enter_from=discover&source=normal_search',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie,
      };
      
      LoggerUtil.i('【DouyinContentService】发送搜索请求: $url');
      LoggerUtil.d('【DouyinContentService】Cookie长度: ${cookie.length}');
      
      // 发送GET请求
      final response = await _dio.get(
        url,
        options: Options(
          headers: headers,
          followRedirects: false,
          responseType: ResponseType.plain, // 使用plain接收原始字符串响应
          validateStatus: (status) => true, // 接受任何状态码
        ),
      );
      
      // 记录原始响应信息
      LoggerUtil.i('【DouyinContentService】搜索响应状态码: ${response.statusCode}');
      LoggerUtil.i('【DouyinContentService】搜索响应类型: ${response.data.runtimeType}');
      
      if (response.statusCode == 200) {
        LoggerUtil.i('【DouyinContentService】搜索请求成功');
        
        // 检查响应数据类型
        if (response.data == null) {
          LoggerUtil.e('【DouyinContentService】搜索响应数据为空');
          return BaseResponse(code: -1, msg: '响应数据为空', data: null);
        }
        
        // 尝试处理不同类型的响应数据
        Map<String, dynamic>? responseData;
        
        if (response.data is Map<String, dynamic>) {
          responseData = response.data as Map<String, dynamic>;
          LoggerUtil.d('【DouyinContentService】响应数据是Map类型');
        } else if (response.data is String) {
          final String dataStr = response.data as String;
          LoggerUtil.d('【DouyinContentService】响应数据是String类型，长度: ${dataStr.length}');
          
          Map<String, dynamic>? finalJson;

          try {
              // 策略：返回的是一个由换行符分割的字符串，其中包含多个JSON块。
              // 我们从后向前找到第一个有效的JSON块。
              
              // 1. 按换行符分割字符串
              final lines = dataStr.split(RegExp(r'\r?\n'));
              
              LoggerUtil.i('【DouyinContentService】响应被分割成 ${lines.length} 行，开始逆序查找JSON...');

              // 2. 从后向前遍历每一行
              for (int i = lines.length - 1; i >= 0; i--) {
                  final String line = lines[i].trim();
                  
                  // 3. 判断是否是可能的JSON对象
                  if (line.startsWith('{') && line.endsWith('}')) {
                      LoggerUtil.d('【DouyinContentService】找到可能的JSON行 (索引: $i): $line');
                      
                      // 4. 清理并尝试解析
                      try {
                          // 清理数据：移除JSON中常见的末尾逗号
                          String sanitizedJsonStr = line.replaceAll(RegExp(r',\s*(?=})'), '');
                          final decoded = json.decode(sanitizedJsonStr) as Map<String, dynamic>;
                          
                          // 成功解析，这就是我们要找的目标
                          finalJson = decoded;
                          LoggerUtil.i('【DouyinContentService】成功解析出最后一个有效JSON对象。');
                          break; // 找到后立即退出循环
                      } catch (e) {
                          LoggerUtil.w('【DouyinContentService】解析JSON行失败: $e. 继续查找...');
                          // 解析失败，继续尝试上一行
                          continue;
                      }
                  }
              }
          } catch (e) {
              LoggerUtil.e('【DouyinContentService】处理响应字符串时发生未知异常: $e');
          }

          // 根据解析结果赋值
          if (finalJson != null) {
              responseData = finalJson;
          } else {
              // 如果经过各种尝试后，仍然为null，则判定为解析失败
              LoggerUtil.e('【DouyinContentService】最终解析响应字符串为JSON失败');
              responseData = {
                  'status_code': -1,
                  'status_msg': '响应数据解析失败，无法找到有效的JSON对象',
                  'raw_response': dataStr.substring(0, math.min(1000, dataStr.length)),
              };
          }
        } else {
          // 其他类型，转为字符串后返回
          LoggerUtil.e('【DouyinContentService】响应数据类型不是Map或String: ${response.data.runtimeType}');
          responseData = {
            'status_code': -1,
            'status_msg': '响应数据类型不支持',
            'raw_response': response.data.toString(),
          };
        }
        
        // 解析状态码和消息
        final code = responseData?['status_code'] ?? 
                   responseData?['code'] ?? 
                   0;
        final msg = responseData?['status_msg'] ?? 
                   responseData?['message'] ?? 
                   responseData?['msg'] ?? 
                   (code == 0 ? '搜索成功' : '搜索失败');
        
        LoggerUtil.i('【DouyinContentService】搜索结果: code=$code, msg=$msg, 是否包含data=${responseData?.containsKey('data')}');
        
        return BaseResponse(
          code: code,
          msg: msg,
          data: responseData,
          success: code == 0 || code == 200,
        );
      } else {
        LoggerUtil.e('【DouyinContentService】搜索请求失败: ${response.statusCode} ${response.statusMessage}');
        
        // 尝试记录更多错误信息
        String errorDetails = '请求失败: ${response.statusMessage}';
        if (response.data != null) {
          errorDetails += '\n响应数据: ${response.data}';
        }
        
        return BaseResponse(
          code: response.statusCode ?? -1,
          msg: errorDetails,
          data: response.data is Map<String, dynamic> ? response.data : {'error': response.data.toString()},
        );
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【DouyinContentService】搜索抖音内容异常: $e\n$stackTrace');
      return BaseResponse(code: -1, msg: '搜索抖音内容异常: $e', data: {'error': e.toString()});
    }
  }
  
  /// 发布抖音作品评论
  /// 
  /// [awemeId] 作品ID
  /// [text] 评论内容
  /// [cookie] 用户Cookie，可选参数，如果不提供则使用当前选中的账号cookie
  /// [replyId] 回复的评论ID，如果是回复一级评论则传入一级评论ID，默认为"0"表示不是回复评论
  /// [replyToReplyId] 回复的二级评论ID，如果直接回复一级评论则传入"0"
  Future<BaseResponse<Map<String, dynamic>?>> publishVideoComment({
    required String awemeId,
    required String text,
    String? cookie,
    String replyId = "0",
    String replyToReplyId = "0",
  }) async {
    try {
      LoggerUtil.i(
        '【DouyinContentService】开始发布抖音作品评论, awemeId: $awemeId, text: $text',
      );

      String finalCookie = cookie ?? '';

      // 如果没有提供cookie，尝试从当前选中账号获取
      if (finalCookie.isEmpty) {
        try {
          // 尝试从账号服务获取当前选中账号
          if (Get.isRegistered<AccountPersistentService>()) {
            LoggerUtil.d(
              '【DouyinContentService】AccountPersistentService已注册，尝试获取当前账号',
            );
            final accountService = Get.find<AccountPersistentService>();
            final currentAccount = accountService.currentAccount.value;

            // 添加详细日志
            if (currentAccount == null) {
              LoggerUtil.w('【DouyinContentService】当前未选择任何账号');
            } else {
              LoggerUtil.d(
                '【DouyinContentService】当前账号: ${currentAccount.nickname}, 平台: ${currentAccount.type}',
              );

              if (currentAccount.refreshToken == null ||
                  currentAccount.refreshToken!.isEmpty) {
                LoggerUtil.w('【DouyinContentService】当前账号refreshToken为空');
              } else {
                LoggerUtil.d(
                  '【DouyinContentService】当前账号refreshToken长度: ${currentAccount.refreshToken!.length}',
                );
                // 仅截取cookie的前20个字符用于日志展示，避免泄露敏感信息
                final cookiePreview =
                    currentAccount.refreshToken!.length > 20
                        ? '${currentAccount.refreshToken!.substring(0, 20)}...'
                        : currentAccount.refreshToken!;
                LoggerUtil.d(
                  '【DouyinContentService】当前账号refreshToken预览: $cookiePreview',
                );
              }
            }

            if (currentAccount != null &&
                currentAccount.refreshToken != null &&
                currentAccount.refreshToken!.isNotEmpty) {
              finalCookie = currentAccount.refreshToken!;
              LoggerUtil.i('【DouyinContentService】从当前账号获取refreshToken成功');
            }
          } else {
            LoggerUtil.w('【DouyinContentService】AccountPersistentService未注册');

            // 尝试使用延迟初始化的方式获取服务
            try {
              final accountService = AccountPersistentService.to;
              final currentAccount = accountService.currentAccount.value;

              if (currentAccount != null &&
                  currentAccount.refreshToken != null &&
                  currentAccount.refreshToken!.isNotEmpty) {
                finalCookie = currentAccount.refreshToken!;
                LoggerUtil.i(
                  '【DouyinContentService】通过AccountPersistentService.to获取refreshToken成功',
                );
              } else {
                LoggerUtil.w(
                  '【DouyinContentService】通过AccountPersistentService.to获取的当前账号为空或refreshToken为空',
                );
              }
            } catch (innerE) {
              LoggerUtil.e(
                '【DouyinContentService】尝试使用AccountPersistentService.to获取服务异常: $innerE',
              );
            }
          }
        } catch (e) {
          LoggerUtil.e('【DouyinContentService】获取当前账号refreshToken异常: $e');
        }
      }

      if (finalCookie.isEmpty) {
        return BaseResponse(code: -1, msg: '缺少Cookie参数，无法发送评论', data: null);
      }
      
      // 构建完整的URL，包含查询参数，更新enter_from和previous_page为general_search
      const String baseUrl =
          'https://www.douyin.com/aweme/v1/web/comment/publish';
      final String url =
          '$baseUrl?app_name=aweme&enter_from=general_search&previous_page=general_search&device_platform=webapp&aid=6383&channel=channel_pc_web&pc_client_type=1&pc_libra_divert=Windows&update_version_code=170400&support_h265=1&support_dash=1&version_code=170400&version_name=17.4.0&cookie_enabled=true&screen_width=1440&screen_height=900&browser_language=zh-CN&browser_platform=Win32&browser_name=Chrome&browser_version=*********&browser_online=true&engine_name=Blink&engine_version=*********&os_name=Windows&os_version=10&cpu_core_num=18&device_memory=8&platform=PC&downlink=10&effective_type=4g&round_trip_time=50';

      // 构建表单数据
      final Map<String, dynamic> formData = {
        'aweme_id': awemeId,
        'comment_send_celltime':
            '${DateTime.now().millisecondsSinceEpoch % 10000}',
        'comment_video_celltime':
            '${(DateTime.now().millisecondsSinceEpoch - 5000) % 10000}',
        'one_level_comment_rank': replyId != "0" ? "2" : "-1",  // 回复评论时使用2，普通评论使用-1
        'paste_edit_method': 'non_paste',
        'text': text,
        'text_extra': '[]',  // 确保添加text_extra字段
      };

      if (replyId != "0") {
        formData['reply_id'] = replyId;
      }

      if (replyToReplyId != "0") {
        formData['reply_to_reply_id'] = replyToReplyId;
      }

      LoggerUtil.i('【DouyinContentService】发送请求: $url');
      LoggerUtil.i('【DouyinContentService】请求参数: $formData');
      
      // 添加完整的请求头
      final Map<String, String> headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'bd-ticket-guard-iteration-version': '1',  // 添加新的安全头
        'bd-ticket-guard-version': '2',           // 添加新的安全头
        'bd-ticket-guard-web-sign-type': '1',     
        'bd-ticket-guard-web-version': '2',    
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'origin': 'https://www.douyin.com',
        'priority': 'u=1, i',
        'referer': 'https://www.douyin.com/jingxuan/search?type=general&modal_id=$awemeId',  // 更新Referer格式
        'sec-ch-ua':
            '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-secsdk-csrf-token': 'DOWNGRADE',
        'Cookie': finalCookie,
      };
      
      // 使用postForm方法发送表单数据
      final response = await _dio.post(
        url,
        options: Options(
          headers: headers,
          contentType: 'application/x-www-form-urlencoded',
          followRedirects: false,
        ),
        data: formData,
      );
      
      if (response.statusCode == 200) {
        LoggerUtil.i('【DouyinContentService】请求成功，解析数据');
        
        final responseData = response.data;
        if (responseData == null) {
          return BaseResponse(code: -1, msg: '响应数据为空', data: null);
        }
        
        LoggerUtil.d('【DouyinContentService】响应数据: $responseData');
        
        final code = responseData['status_code'] ?? responseData['code'];
        final msg =
            responseData['status_msg'] ??
            responseData['message'] ?? 
            responseData['msg'] ?? 
            '';
        
        if (code == 0 || code == 200) {
          return BaseResponse(
            code: code,
            msg: msg,
            data: responseData,
            success: true,
          );
        } else {
          return BaseResponse(code: code, msg: msg, data: responseData);
        }
      } else {
        return BaseResponse(
          code: response.statusCode ?? -1,
          msg: '请求失败: ${response.statusMessage}',
          data: null,
        );
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【DouyinContentService】发布抖音作品评论异常: $e\n$stackTrace');

      return BaseResponse(code: -1, msg: '发布抖音作品评论异常: $e', data: null);
    }
  }
}
