import 'package:aitoearn_app/config/logger.dart';

/// 抖音POI搜索响应模型
class DouyinPoiSearchResponse {
  final int statusCode;
  final String statusMsg;
  final List<DouyinPoiItem> poiList;
  final int hasMore;
  final String cursor;
  
  DouyinPoiSearchResponse({
    required this.statusCode,
    required this.statusMsg,
    required this.poiList,
    required this.hasMore,
    required this.cursor,
  });
  
  factory DouyinPoiSearchResponse.fromJson(Map<String, dynamic> json) {
    final poiList = <DouyinPoiItem>[];
    
    if (json['poi_list'] != null) {
      for (var item in json['poi_list']) {
        poiList.add(DouyinPoiItem.fromJson(item));
      }
    }
    
    // 解析状态码，处理可能的类型不匹配
    int statusCodeValue = -1;
    final statusCodeRaw = json['status_code'];
    if (statusCodeRaw != null) {
      if (statusCodeRaw is int) {
        statusCodeValue = statusCodeRaw;
      } else if (statusCodeRaw is String) {
        try {
          statusCodeValue = int.parse(statusCodeRaw);
        } catch (e) {
          // 解析失败，使用默认值-1
        }
      }
    }
    
    return DouyinPoiSearchResponse(
      statusCode: statusCodeValue,
      statusMsg: json['status_msg'] ?? '',
      poiList: poiList,
      hasMore: json['has_more'] ?? 1,
      cursor: json['cursor'] ?? '',
    );
  }
  
  /// 从抖音API返回的原始数据创建POI响应
  /// 适用于抖音位置选择接口返回的数据格式
  factory DouyinPoiSearchResponse.fromRawApiResponse(Map<String, dynamic> json) {
    final poiList = <DouyinPoiItem>[];
    
    // 处理poi_list字段
    if (json['poi_list'] != null) {
      try {
        if (json['poi_list'] is List) {
          for (var item in json['poi_list']) {
            if (item is Map<String, dynamic>) {
              poiList.add(DouyinPoiItem.fromJson(item));
            }
          }
        } else if (json['poi_list'] is Map) {
          // 有时poi_list可能是一个Map而不是List
          poiList.add(DouyinPoiItem.fromJson(json['poi_list']));
        }
      } catch (e) {
        LoggerUtil.i('处理poi_list出错: $e');
      }
    }
    
    // 处理current_locs字段(可能存在于某些API响应中)
    if (json['current_locs'] != null && poiList.isEmpty) {
      try {
        if (json['current_locs'] is List) {
          for (var item in json['current_locs']) {
            if (item is Map<String, dynamic>) {
              poiList.add(DouyinPoiItem.fromJson(item));
            }
          }
        }
      } catch (e) {
        LoggerUtil.i('处理current_locs出错: $e');
      }
    }
    
    // 处理current_loc字段(单个位置对象)
    if (json['current_loc'] != null && poiList.isEmpty) {
      try {
        if (json['current_loc'] is Map<String, dynamic>) {
          poiList.add(DouyinPoiItem.fromJson(json['current_loc']));
        }
      } catch (e) {
        LoggerUtil.i('处理current_loc出错: $e');
      }
    }
    
    // 处理poi_object字段(可能来自我们自己保存的数据)
    if (json['poi_object'] != null && poiList.isEmpty) {
      try {
        if (json['poi_object'] is Map<String, dynamic>) {
          poiList.add(DouyinPoiItem.fromJson(json['poi_object']));
        }
      } catch (e) {
        LoggerUtil.i('处理poi_object出错: $e');
      }
    }
    
    // 如果上面都没有找到POI数据，但json本身看起来像一个POI项，则直接解析
    if (poiList.isEmpty && 
        (json['poi_name'] != null || json['poiName'] != null || 
         json['name'] != null)) {
      try {
        poiList.add(DouyinPoiItem.fromJson(json));
      } catch (e) {
        LoggerUtil.i('直接解析json为POI项出错: $e');
      }
    }
    
    return DouyinPoiSearchResponse(
      statusCode: json['status_code'] ?? 0,
      statusMsg: json['status_msg'] ?? '',
      poiList: poiList,
      hasMore: json['has_more'] ?? 1,
      cursor: json['cursor'] ?? '',
    );
  }
}

/// 抖音POI项模型
class DouyinPoiItem {
  final String poiId;
  final String poiName;
  final String address;
  final double latitude;
  final double longitude;
  final String typeName;
  final String cityName;
  final String districtName;
  final int distance;
  
  DouyinPoiItem({
    required this.poiId,
    required this.poiName,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.typeName,
    required this.cityName,
    this.districtName = '',
    required this.distance,
  });
  
  // 将POI项转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'poi_id': poiId,
      'poi_name': poiName,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'type_name': typeName,
      'city_name': cityName,
      'district_name': districtName,
      'distance': distance,
    };
  }
  
  /// 从JSON创建POI项
  factory DouyinPoiItem.fromJson(Map<String, dynamic> json) {
    // 处理城市信息，可能在address_info.city或city中
    String cityName = '';
    if (json['address_info'] != null && json['address_info'] is Map) {
      final addressInfo = json['address_info'];
      cityName = addressInfo['city'] ?? '';
    } else if (json['city'] != null) {
      cityName = json['city'];
    } else if (json['city_name'] != null) {
      cityName = json['city_name'];
    }
    
    // 处理区域信息
    String districtName = '';
    if (json['address_info'] != null && json['address_info'] is Map) {
      final addressInfo = json['address_info'];
      districtName = addressInfo['district'] ?? '';
    } else if (json['district'] != null) {
      districtName = json['district'];
    } else if (json['district_name'] != null) {
      districtName = json['district_name'];
    }
    
    // 处理地址信息，可能在address_info.simple_addr或address中
    String address = '';
    if (json['address_info'] != null && json['address_info'] is Map) {
      final addressInfo = json['address_info'];
      address = addressInfo['simple_addr'] ?? addressInfo['address'] ?? '';
    } else if (json['address'] != null) {
      address = json['address'];
    }
    
    // 处理POI名称，可能在poi_name或poiName中
    String poiName = json['poi_name'] ?? json['poiName'] ?? json['name'] ?? '';
    
    // 处理POI ID，可能在poi_id或poiId中
    String poiId = json['poi_id'] ?? json['poiId'] ?? json['id'] ?? '';
    
    // 处理距离信息，可能在distance中，单位可能是米或千米
    int distance = 0;
    if (json['distance'] != null) {
      try {
        final distanceValue = json['distance'];
        if (distanceValue is int) {
          distance = distanceValue;
        } else if (distanceValue is double) {
          distance = distanceValue.toInt();
        } else if (distanceValue is String) {
          // 尝试解析字符串，处理可能的千米单位
          if (distanceValue.toLowerCase().contains('km')) {
            // 如果包含km，转换为米
            final kmValue = double.tryParse(
              distanceValue.toLowerCase().replaceAll('km', '').trim()
            );
            if (kmValue != null) {
              distance = (kmValue * 1000).toInt();
            }
          } else {
            distance = int.tryParse(distanceValue) ?? 0;
          }
        }
      } catch (e) {
        LoggerUtil.i('解析距离失败: $e');
      }
    }
    
    // 处理经纬度信息，可能在location或直接在json中
    double longitude = 0.0;
    double latitude = 0.0;
    if (json['location'] != null && json['location'] is Map) {
      final location = json['location'];
      longitude = _parseDoubleValue(location['longitude']);
      latitude = _parseDoubleValue(location['latitude']);
    } else {
      longitude = _parseDoubleValue(json['longitude']);
      latitude = _parseDoubleValue(json['latitude']);
    }
    
    // 处理POI类型
    String typeName = json['type_name'] ?? json['typeName'] ?? '';
    if (typeName.isEmpty && json['poi_backend_type'] != null) {
      if (json['poi_backend_type'] is Map) {
        typeName = json['poi_backend_type']['name'] ?? '';
      }
    }
    
    return DouyinPoiItem(
      poiId: poiId,
      poiName: poiName,
      address: address,
      distance: distance,
      cityName: cityName,
      districtName: districtName,
      latitude: latitude,
      longitude: longitude,
      typeName: typeName,
    );
  }
  
  /// 辅助方法：解析double值
  static double _parseDoubleValue(dynamic value) {
    if (value == null) return 0.0;
    
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    
    return 0.0;
  }
} 
