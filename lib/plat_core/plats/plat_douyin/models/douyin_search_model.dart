import 'dart:convert';

/// 抖音搜索结果响应模型
class DouyinSearchResponse {
  final int statusCode;
  final List<DouyinSearchData>? data;
  final List<dynamic>? awemeList;
  final int hasMore;
  final int cursor;
  final DouyinExtra extra;
  final DouyinLogPb logPb;
  final String? backtrace;

  DouyinSearchResponse({
    required this.statusCode,
    required this.hasMore, required this.cursor, required this.extra, required this.logPb, this.data,
    this.awemeList,
    this.backtrace,
  });

  /// 从JSON映射创建模型
  factory DouyinSearchResponse.fromJson(Map<String, dynamic> json) {
    return DouyinSearchResponse(
      statusCode: json['status_code'] ?? 0,
      data: json['data'] != null
          ? List<DouyinSearchData>.from(
              json['data'].map((x) => DouyinSearchData.fromJson(x)))
          : null,
      awemeList: json['aweme_list'],
      hasMore: json['has_more'] ?? 0,
      cursor: json['cursor'] ?? 0,
      extra: DouyinExtra.fromJson(json['extra'] ?? {}),
      logPb: DouyinLogPb.fromJson(json['log_pb'] ?? {}),
      backtrace: json['backtrace'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'status_code': statusCode,
      'data': data?.map((x) => x.toJson()).toList(),
      'aweme_list': awemeList,
      'has_more': hasMore,
      'cursor': cursor,
      'extra': extra.toJson(),
      'log_pb': logPb.toJson(),
      'backtrace': backtrace,
    };
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

/// 搜索结果数据项
class DouyinSearchData {
  final int type;
  final DouyinAwemeInfo awemeInfo;

  DouyinSearchData({
    required this.type,
    required this.awemeInfo,
  });

  factory DouyinSearchData.fromJson(Map<String, dynamic> json) {
    return DouyinSearchData(
      type: json['type'] ?? 0,
      awemeInfo: DouyinAwemeInfo.fromJson(json['aweme_info'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'aweme_info': awemeInfo.toJson(),
    };
  }
}

/// 作品信息
class DouyinAwemeInfo {
  final String awemeId;
  final String desc;
  final int createTime;
  final DouyinAuthor author;
  final DouyinMusic? music;
  final List<DouyinChaItem>? chaList;
  final DouyinVideo video;
  final int? userDigged;
  final DouyinStatistics statistics;
  final DouyinStatus status;
  final List<DouyinTextExtra>? textExtra;
  final int isTop;
  final DouyinShareInfo shareInfo;

  DouyinAwemeInfo({
    required this.awemeId,
    required this.desc,
    required this.createTime,
    required this.author,
    required this.video, required this.statistics, required this.status, required this.isTop, required this.shareInfo, this.music,
    this.chaList,
    this.userDigged,
    this.textExtra,
  });

  factory DouyinAwemeInfo.fromJson(Map<String, dynamic> json) {
    return DouyinAwemeInfo(
      awemeId: json['aweme_id'] ?? '',
      desc: json['desc'] ?? '',
      createTime: json['create_time'] ?? 0,
      author: DouyinAuthor.fromJson(json['author'] ?? {}),
      music: json['music'] != null ? DouyinMusic.fromJson(json['music']) : null,
      chaList: json['cha_list'] != null
          ? List<DouyinChaItem>.from(
              json['cha_list'].map((x) => DouyinChaItem.fromJson(x)))
          : null,
      video: DouyinVideo.fromJson(json['video'] ?? {}),
      userDigged: json['user_digged'],
      statistics: DouyinStatistics.fromJson(json['statistics'] ?? {}),
      status: DouyinStatus.fromJson(json['status'] ?? {}),
      textExtra: json['text_extra'] != null
          ? List<DouyinTextExtra>.from(
              json['text_extra'].map((x) => DouyinTextExtra.fromJson(x)))
          : null,
      isTop: json['is_top'] ?? 0,
      shareInfo: DouyinShareInfo.fromJson(json['share_info'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'aweme_id': awemeId,
      'desc': desc,
      'create_time': createTime,
      'author': author.toJson(),
      'music': music?.toJson(),
      'cha_list': chaList?.map((x) => x.toJson()).toList(),
      'video': video.toJson(),
      'user_digged': userDigged,
      'statistics': statistics.toJson(),
      'status': status.toJson(),
      'text_extra': textExtra?.map((x) => x.toJson()).toList(),
      'is_top': isTop,
      'share_info': shareInfo.toJson(),
    };
  }
}

/// 作者信息
class DouyinAuthor {
  final String uid;
  final String nickname;
  final DouyinAvatar avatarThumb;
  final int followStatus;
  final int followerCount;
  final int totalFavorited;
  final String customVerify;
  final String secUid;

  DouyinAuthor({
    required this.uid,
    required this.nickname,
    required this.avatarThumb,
    required this.followStatus,
    required this.followerCount,
    required this.totalFavorited,
    required this.customVerify,
    required this.secUid,
  });

  factory DouyinAuthor.fromJson(Map<String, dynamic> json) {
    return DouyinAuthor(
      uid: json['uid'] ?? '',
      nickname: json['nickname'] ?? '',
      avatarThumb: DouyinAvatar.fromJson(json['avatar_thumb'] ?? {}),
      followStatus: json['follow_status'] ?? 0,
      followerCount: json['follower_count'] ?? 0,
      totalFavorited: json['total_favorited'] ?? 0,
      customVerify: json['custom_verify'] ?? '',
      secUid: json['sec_uid'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'nickname': nickname,
      'avatar_thumb': avatarThumb.toJson(),
      'follow_status': followStatus,
      'follower_count': followerCount,
      'total_favorited': totalFavorited,
      'custom_verify': customVerify,
      'sec_uid': secUid,
    };
  }
}

/// 头像信息
class DouyinAvatar {
  final String uri;
  final List<String> urlList;
  final int width;
  final int height;

  DouyinAvatar({
    required this.uri,
    required this.urlList,
    required this.width,
    required this.height,
  });

  factory DouyinAvatar.fromJson(Map<String, dynamic> json) {
    return DouyinAvatar(
      uri: json['uri'] ?? '',
      urlList: json['url_list'] != null
          ? List<String>.from(json['url_list'])
          : [],
      width: json['width'] ?? 0,
      height: json['height'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uri': uri,
      'url_list': urlList,
      'width': width,
      'height': height,
    };
  }
}

/// 音乐信息
class DouyinMusic {
  final int id;
  final String idStr;
  final String title;
  final String author;
  final String album;
  final DouyinAvatar coverMedium;
  final DouyinAvatar coverThumb;
  final DouyinPlayUrl playUrl;
  final int duration;
  final String extra;

  DouyinMusic({
    required this.id,
    required this.idStr,
    required this.title,
    required this.author,
    required this.album,
    required this.coverMedium,
    required this.coverThumb,
    required this.playUrl,
    required this.duration,
    required this.extra,
  });

  factory DouyinMusic.fromJson(Map<String, dynamic> json) {
    return DouyinMusic(
      id: json['id'] ?? 0,
      idStr: json['id_str'] ?? '',
      title: json['title'] ?? '',
      author: json['author'] ?? '',
      album: json['album'] ?? '',
      coverMedium: DouyinAvatar.fromJson(json['cover_medium'] ?? {}),
      coverThumb: DouyinAvatar.fromJson(json['cover_thumb'] ?? {}),
      playUrl: DouyinPlayUrl.fromJson(json['play_url'] ?? {}),
      duration: json['duration'] ?? 0,
      extra: json['extra'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'id_str': idStr,
      'title': title,
      'author': author,
      'album': album,
      'cover_medium': coverMedium.toJson(),
      'cover_thumb': coverThumb.toJson(),
      'play_url': playUrl.toJson(),
      'duration': duration,
      'extra': extra,
    };
  }
}

/// 播放URL信息
class DouyinPlayUrl {
  final String uri;
  final List<String> urlList;
  final int width;
  final int height;
  final String? urlKey;

  DouyinPlayUrl({
    required this.uri,
    required this.urlList,
    required this.width,
    required this.height,
    this.urlKey,
  });

  factory DouyinPlayUrl.fromJson(Map<String, dynamic> json) {
    return DouyinPlayUrl(
      uri: json['uri'] ?? '',
      urlList: json['url_list'] != null
          ? List<String>.from(json['url_list'])
          : [],
      width: json['width'] ?? 0,
      height: json['height'] ?? 0,
      urlKey: json['url_key'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uri': uri,
      'url_list': urlList,
      'width': width,
      'height': height,
      'url_key': urlKey,
    };
  }
}

/// 视频信息
class DouyinVideo {
  final DouyinPlayUrl playAddr;
  final DouyinAvatar cover;
  final int height;
  final int width;
  final DouyinAvatar dynamicCover;
  final DouyinAvatar originCover;
  final String ratio;
  final DouyinPlayUrl? downloadAddr;
  final bool hasDownloadSuffixLogoAddr;
  final DouyinPlayUrl? playAddr265;
  final int duration;

  DouyinVideo({
    required this.playAddr,
    required this.cover,
    required this.height,
    required this.width,
    required this.dynamicCover,
    required this.originCover,
    required this.ratio,
    required this.hasDownloadSuffixLogoAddr, required this.duration, this.downloadAddr,
    this.playAddr265,
  });

  factory DouyinVideo.fromJson(Map<String, dynamic> json) {
    return DouyinVideo(
      playAddr: DouyinPlayUrl.fromJson(json['play_addr'] ?? {}),
      cover: DouyinAvatar.fromJson(json['cover'] ?? {}),
      height: json['height'] ?? 0,
      width: json['width'] ?? 0,
      dynamicCover: DouyinAvatar.fromJson(json['dynamic_cover'] ?? {}),
      originCover: DouyinAvatar.fromJson(json['origin_cover'] ?? {}),
      ratio: json['ratio'] ?? '',
      downloadAddr: json['download_addr'] != null
          ? DouyinPlayUrl.fromJson(json['download_addr'])
          : null,
      hasDownloadSuffixLogoAddr: json['has_download_suffix_logo_addr'] ?? false,
      playAddr265: json['play_addr_265'] != null
          ? DouyinPlayUrl.fromJson(json['play_addr_265'])
          : null,
      duration: json['duration'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'play_addr': playAddr.toJson(),
      'cover': cover.toJson(),
      'height': height,
      'width': width,
      'dynamic_cover': dynamicCover.toJson(),
      'origin_cover': originCover.toJson(),
      'ratio': ratio,
      'download_addr': downloadAddr?.toJson(),
      'has_download_suffix_logo_addr': hasDownloadSuffixLogoAddr,
      'play_addr_265': playAddr265?.toJson(),
      'duration': duration,
    };
  }
}

/// 统计信息
class DouyinStatistics {
  final int commentCount;
  final int diggCount;
  final int downloadCount;
  final int playCount;
  final int shareCount;
  final int forwardCount;
  final int collectCount;

  DouyinStatistics({
    required this.commentCount,
    required this.diggCount,
    required this.downloadCount,
    required this.playCount,
    required this.shareCount,
    required this.forwardCount,
    required this.collectCount,
  });

  factory DouyinStatistics.fromJson(Map<String, dynamic> json) {
    return DouyinStatistics(
      commentCount: json['comment_count'] ?? 0,
      diggCount: json['digg_count'] ?? 0,
      downloadCount: json['download_count'] ?? 0,
      playCount: json['play_count'] ?? 0,
      shareCount: json['share_count'] ?? 0,
      forwardCount: json['forward_count'] ?? 0,
      collectCount: json['collect_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'comment_count': commentCount,
      'digg_count': diggCount,
      'download_count': downloadCount,
      'play_count': playCount,
      'share_count': shareCount,
      'forward_count': forwardCount,
      'collect_count': collectCount,
    };
  }
}

/// 状态信息
class DouyinStatus {
  final bool isDelete;
  final bool allowShare;
  final bool isPrivate;
  final int privatStatus;
  final bool inReviewing;
  final bool isProhibited;
  final int partSee;

  DouyinStatus({
    required this.isDelete,
    required this.allowShare,
    required this.isPrivate,
    required this.privatStatus,
    required this.inReviewing,
    required this.isProhibited,
    required this.partSee,
  });

  factory DouyinStatus.fromJson(Map<String, dynamic> json) {
    return DouyinStatus(
      isDelete: json['is_delete'] ?? false,
      allowShare: json['allow_share'] ?? true,
      isPrivate: json['is_private'] ?? false,
      privatStatus: json['private_status'] ?? 0,
      inReviewing: json['in_reviewing'] ?? false,
      isProhibited: json['is_prohibited'] ?? false,
      partSee: json['part_see'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_delete': isDelete,
      'allow_share': allowShare,
      'is_private': isPrivate,
      'private_status': privatStatus,
      'in_reviewing': inReviewing,
      'is_prohibited': isProhibited,
      'part_see': partSee,
    };
  }
}

/// 标签话题信息
class DouyinChaItem {
  final String cid;
  final String chaName;
  final String desc;
  final String hashtagId;
  final String hashtagName;
  final bool isCommerce;

  DouyinChaItem({
    required this.cid,
    required this.chaName,
    required this.desc,
    required this.hashtagId,
    required this.hashtagName,
    required this.isCommerce,
  });

  factory DouyinChaItem.fromJson(Map<String, dynamic> json) {
    return DouyinChaItem(
      cid: json['cid'] ?? '',
      chaName: json['cha_name'] ?? '',
      desc: json['desc'] ?? '',
      hashtagId: json['hashtag_id'] ?? '',
      hashtagName: json['hashtag_name'] ?? '',
      isCommerce: json['is_commerce'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cid': cid,
      'cha_name': chaName,
      'desc': desc,
      'hashtag_id': hashtagId,
      'hashtag_name': hashtagName,
      'is_commerce': isCommerce,
    };
  }
}

/// 文本附加信息
class DouyinTextExtra {
  final int start;
  final int end;
  final int type;
  final String hashtagName;
  final String hashtagId;
  final bool isCommerce;

  DouyinTextExtra({
    required this.start,
    required this.end,
    required this.type,
    required this.hashtagName,
    required this.hashtagId,
    required this.isCommerce,
  });

  factory DouyinTextExtra.fromJson(Map<String, dynamic> json) {
    return DouyinTextExtra(
      start: json['start'] ?? 0,
      end: json['end'] ?? 0,
      type: json['type'] ?? 0,
      hashtagName: json['hashtag_name'] ?? '',
      hashtagId: json['hashtag_id'] ?? '',
      isCommerce: json['is_commerce'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'start': start,
      'end': end,
      'type': type,
      'hashtag_name': hashtagName,
      'hashtag_id': hashtagId,
      'is_commerce': isCommerce,
    };
  }
}

/// 分享信息
class DouyinShareInfo {
  final String shareUrl;
  final String shareDesc;
  final String shareTitle;
  final String shareLinkDesc;
  final String shareQuote;
  final String shareDescInfo;

  DouyinShareInfo({
    required this.shareUrl,
    required this.shareDesc,
    required this.shareTitle,
    required this.shareLinkDesc,
    required this.shareQuote,
    required this.shareDescInfo,
  });

  factory DouyinShareInfo.fromJson(Map<String, dynamic> json) {
    return DouyinShareInfo(
      shareUrl: json['share_url'] ?? '',
      shareDesc: json['share_desc'] ?? '',
      shareTitle: json['share_title'] ?? '',
      shareLinkDesc: json['share_link_desc'] ?? '',
      shareQuote: json['share_quote'] ?? '',
      shareDescInfo: json['share_desc_info'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'share_url': shareUrl,
      'share_desc': shareDesc,
      'share_title': shareTitle,
      'share_link_desc': shareLinkDesc,
      'share_quote': shareQuote,
      'share_desc_info': shareDescInfo,
    };
  }
}

/// Extra信息
class DouyinExtra {
  final int now;
  final String logid;
  final List<String> fatalItemIds;
  final String searchRequestId;

  DouyinExtra({
    required this.now,
    required this.logid,
    required this.fatalItemIds,
    required this.searchRequestId,
  });

  factory DouyinExtra.fromJson(Map<String, dynamic> json) {
    return DouyinExtra(
      now: json['now'] ?? 0,
      logid: json['logid'] ?? '',
      fatalItemIds: json['fatal_item_ids'] != null
          ? List<String>.from(json['fatal_item_ids'])
          : [],
      searchRequestId: json['search_request_id'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'now': now,
      'logid': logid,
      'fatal_item_ids': fatalItemIds,
      'search_request_id': searchRequestId,
    };
  }
}

/// 日志信息
class DouyinLogPb {
  final String imprId;

  DouyinLogPb({
    required this.imprId,
  });

  factory DouyinLogPb.fromJson(Map<String, dynamic> json) {
    return DouyinLogPb(
      imprId: json['impr_id'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'impr_id': imprId,
    };
  }
} 