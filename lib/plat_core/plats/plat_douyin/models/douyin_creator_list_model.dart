import 'dart:convert';

/// 抖音作品列表响应模型
class DouyinCreatorListResponse {
  final Extra extra;
  final int statusCode;
  final String statusMsg;
  final bool hasMore;
  final List<DouyinCreatorItem> itemInfoList;
  final int totalCount;

  DouyinCreatorListResponse({
    required this.extra,
    required this.statusCode,
    required this.statusMsg,
    required this.hasMore,
    required this.itemInfoList,
    required this.totalCount,
  });

  /// 从JSON映射创建模型
  factory DouyinCreatorListResponse.fromJson(Map<String, dynamic> json) {
    return DouyinCreatorListResponse(
      extra: Extra.fromJson(json['extra'] ?? {}),
      statusCode: json['status_code'] ?? 0,
      statusMsg: json['status_msg'] ?? '',
      hasMore: json['has_more'] ?? false,
      itemInfoList: (json['item_info_list'] as List<dynamic>?)
              ?.map((item) => DouyinCreatorItem.fromJson(item))
              .toList() ??
          [],
      totalCount: json['total_count'] ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'extra': extra.toJson(),
      'status_code': statusCode,
      'status_msg': statusMsg,
      'has_more': hasMore,
      'item_info_list': itemInfoList.map((item) => item.toJson()).toList(),
      'total_count': totalCount,
    };
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}

/// 抖音作品项
class DouyinCreatorItem {
  final String anchorUserId;
  final String cursor;
  final int commentCount;
  final String coverImageUrl;
  final String createTime;
  final CreatorItemSetting creatorItemSetting;
  final int duration;
  final String itemId;
  final String itemIdPlain;
  final String itemLink;
  final int mediaType;
  final String title;

  DouyinCreatorItem({
    required this.anchorUserId,
    required this.cursor,
    required this.commentCount,
    required this.coverImageUrl,
    required this.createTime,
    required this.creatorItemSetting,
    required this.duration,
    required this.itemId,
    required this.itemIdPlain,
    required this.itemLink,
    required this.mediaType,
    required this.title,
  });

  /// 从JSON映射创建模型
  factory DouyinCreatorItem.fromJson(Map<String, dynamic> json) {
    return DouyinCreatorItem(
      anchorUserId: json['anchor_user_id'] ?? '',
      cursor: json['cursor'] ?? '',
      commentCount: json['comment_count'] ?? 0,
      coverImageUrl: json['cover_image_url'] ?? '',
      createTime: json['create_time'] ?? '',
      creatorItemSetting: CreatorItemSetting.fromJson(
          json['creator_item_setting'] ?? {}),
      duration: json['duration'] ?? 0,
      itemId: json['item_id'] ?? '',
      itemIdPlain: json['item_id_plain'] ?? '',
      itemLink: json['item_link'] ?? '',
      mediaType: json['media_type'] ?? 0,
      title: json['title'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'anchor_user_id': anchorUserId,
      'cursor': cursor,
      'comment_count': commentCount,
      'cover_image_url': coverImageUrl,
      'create_time': createTime,
      'creator_item_setting': creatorItemSetting.toJson(),
      'duration': duration,
      'item_id': itemId,
      'item_id_plain': itemIdPlain,
      'item_link': itemLink,
      'media_type': mediaType,
      'title': title,
    };
  }
}

/// 创作者项目设置
class CreatorItemSetting {
  final bool chargeCommentAudit;

  CreatorItemSetting({
    required this.chargeCommentAudit,
  });

  /// 从JSON映射创建模型
  factory CreatorItemSetting.fromJson(Map<String, dynamic> json) {
    return CreatorItemSetting(
      chargeCommentAudit: json['charge_comment_audit'] ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'charge_comment_audit': chargeCommentAudit,
    };
  }
}

/// Extra信息
class Extra {
  final String? logId;
  final dynamic now;

  Extra({
    this.logId,
    this.now,
  });

  /// 从JSON映射创建模型
  factory Extra.fromJson(Map<String, dynamic> json) {
    return Extra(
      logId: json['logid'],
      now: json['now'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'logid': logId,
      'now': now,
    };
  }
} 