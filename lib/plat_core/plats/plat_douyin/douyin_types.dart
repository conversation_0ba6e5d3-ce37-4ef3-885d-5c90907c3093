class DouyinPlatformSettingType {
  final DouyinDeclaration? userDeclare;
  final MixInfo? mixInfo;
  final String? hotSentence;
  final List<Activity>? activity;
  final List<MentionedUserInfo>? mentionedUserInfo;
  final String title;
  final String? caption;
  final List<String>? topics;
  final String cover;
  final int? timingTime;
  final int? visibilityType;
  final PoiInfo? poiInfo;
  final String? musicId;
  final String proxyIp;
  final int? mediaType; // 添加媒体类型，4表示图文

  DouyinPlatformSettingType({
    this.userDeclare,
    this.mixInfo,
    this.hotSentence,
    this.activity,
    this.mentionedUserInfo,
    required this.title,
    this.caption,
    this.topics,
    required this.cover,
    this.timingTime,
    this.visibilityType = 0,
    this.poiInfo,
    this.musicId,
    this.proxyIp = '',
    this.mediaType, // 默认不设置，由具体方法设置
  });
  
  // 复制并更新对象的方法
  DouyinPlatformSettingType copyWith({
    DouyinDeclaration? userDeclare,
    MixInfo? mixInfo,
    String? hotSentence,
    List<Activity>? activity,
    List<MentionedUserInfo>? mentionedUserInfo,
    String? title,
    String? caption,
    List<String>? topics,
    String? cover,
    int? timingTime,
    int? visibilityType,
    PoiInfo? poiInfo,
    String? musicId,
    String? proxyIp,
    int? mediaType,
  }) {
    return DouyinPlatformSettingType(
      userDeclare: userDeclare ?? this.userDeclare,
      mixInfo: mixInfo ?? this.mixInfo,
      hotSentence: hotSentence ?? this.hotSentence,
      activity: activity ?? this.activity,
      mentionedUserInfo: mentionedUserInfo ?? this.mentionedUserInfo,
      title: title ?? this.title,
      caption: caption ?? this.caption,
      topics: topics ?? this.topics,
      cover: cover ?? this.cover,
      timingTime: timingTime ?? this.timingTime,
      visibilityType: visibilityType ?? this.visibilityType,
      poiInfo: poiInfo ?? this.poiInfo,
      musicId: musicId ?? this.musicId,
      proxyIp: proxyIp ?? this.proxyIp,
      mediaType: mediaType ?? this.mediaType,
    );
  }
}

class MixInfo {
  final String mixId;
  final String mixName;
  MixInfo({required this.mixId, required this.mixName});
}

class Activity {
  final String value;
  final String label;
  Activity({required this.value, required this.label});
}

/// 抖音活动详细信息
class DouyinActivityDetail {
  final String activityId;
  final int activityLevel;
  final String activityName;
  final int activityStatus;
  final int activityType;
  final dynamic challenge;
  final List<int>? challengeIds;
  final int collectId;
  final bool collectStatus;
  final String? coverImage;
  final String? gameId;
  final int hotScore;
  final bool ifWellChosen;
  final String? jumpLink;
  final int jumpType;
  final int queryTag;
  final int rewardType;
  final String? showEndTime;
  final String? showStartTime;

  DouyinActivityDetail({
    required this.activityId,
    required this.activityLevel,
    required this.activityName,
    required this.activityStatus,
    required this.activityType,
    this.challenge,
    this.challengeIds,
    required this.collectId,
    required this.collectStatus,
    this.coverImage,
    this.gameId,
    required this.hotScore,
    required this.ifWellChosen,
    this.jumpLink,
    required this.jumpType,
    required this.queryTag,
    required this.rewardType,
    this.showEndTime,
    this.showStartTime,
  });

  factory DouyinActivityDetail.fromJson(Map<String, dynamic> json) {
    return DouyinActivityDetail(
      activityId: json['activity_id']?.toString() ?? '',
      activityLevel: json['activity_level'] ?? 0,
      activityName: json['activity_name'] ?? '',
      activityStatus: json['activity_status'] ?? 0,
      activityType: json['activity_type'] ?? 0,
      challenge: json['challenge'],
      challengeIds: json['challenge_ids'] != null
          ? List<int>.from(json['challenge_ids'].map((x) => x is String ? int.tryParse(x) ?? 0 : x))
          : null,
      collectId: json['collect_id'] ?? 0,
      collectStatus: json['collect_status'] ?? false,
      coverImage: json['cover_image'],
      gameId: json['game_id'],
      hotScore: json['hot_score'] ?? 0,
      ifWellChosen: json['if_well_chosen'] ?? false,
      jumpLink: json['jump_link'],
      jumpType: json['jump_type'] ?? 0,
      queryTag: json['query_tag'] ?? 0,
      rewardType: json['reward_type'] ?? 0,
      showEndTime: json['show_end_time'],
      showStartTime: json['show_start_time'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'activity_id': activityId,
      'activity_level': activityLevel,
      'activity_name': activityName,
      'activity_status': activityStatus,
      'activity_type': activityType,
      'challenge': challenge,
      'challenge_ids': challengeIds,
      'collect_id': collectId,
      'collect_status': collectStatus,
      'cover_image': coverImage,
      'game_id': gameId,
      'hot_score': hotScore,
      'if_well_chosen': ifWellChosen,
      'jump_link': jumpLink,
      'jump_type': jumpType,
      'query_tag': queryTag,
      'reward_type': rewardType,
      'show_end_time': showEndTime,
      'show_start_time': showStartTime,
    };
  }

  /// 转换为简单的Activity对象
  Activity toActivity() {
    return Activity(
      value: activityId,
      label: activityName,
    );
  }
}

/// 活动列表响应Extra信息
class DouyinActivityExtra {
  final String? logid;
  final int? now;

  DouyinActivityExtra({
    this.logid,
    this.now,
  });

  factory DouyinActivityExtra.fromJson(Map<String, dynamic> json) {
    return DouyinActivityExtra(
      logid: json['logid'],
      now: json['now'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'logid': logid,
      'now': now,
    };
  }
}

class MentionedUserInfo {
  final String nickName;
  final String uid;
  MentionedUserInfo({required this.nickName, required this.uid});
}

class PoiInfo {
  final String poiId;
  final String poiName;
  PoiInfo({required this.poiId, required this.poiName});
}

// 响应类型定义
class DouyinBaseResponse {
  final int statusCode;
  final String? statusMsg;
  
  DouyinBaseResponse({required this.statusCode, this.statusMsg});
  
  factory DouyinBaseResponse.fromJson(Map<String, dynamic> json) {
    return DouyinBaseResponse(
      statusCode: json['status_code'] ?? -1,
      statusMsg: json['status_msg'],
    );
  }
}

/// 抖音自主声明选项
enum DouyinDeclaration {
  /// 内容由AI生成
  aigc('aigc', '内容由AI生成'),
  
  /// 可能会引人不适
  maybeUnsuitable('maybe_unsuitable', '可能会引人不适'),
  
  /// 虚拟作品。仅供娱乐
  onlyFunNew('only_fun_new', '虚拟作品，仅供娱乐'),
  
  /// 危险行为，请勿模仿
  dangerousBehavior('dangerous_behavior', '危险行为，请勿模仿'),
  
  /// 内容自行拍摄
  selfShoot('self_shoot', '内容自行拍摄'),
  
  /// 内容取材网络
  fromNetV3('from_net_v3', '内容取材网络');
  
  /// 声明的标识符，用于API请求
  final String value;
  
  /// 声明的显示名称
  final String label;
  
  const DouyinDeclaration(this.value, this.label);
}

class DouyinUserInfo {
  final String uid;
  final String authorId;
  final String nickname;
  final String avatar;
  final int fansCount;
  
  DouyinUserInfo({
    required this.uid,
    required this.authorId,
    required this.nickname,
    required this.avatar,
    required this.fansCount,
  });
  
  factory DouyinUserInfo.fromJson(Map<String, dynamic> json) {
    return DouyinUserInfo(
      uid: json['uid'] ?? '',
      authorId: json['authorId'] ?? '',
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'] ?? '',
      fansCount: json['fansCount'] ?? 0,
    );
  }
}

class DouyinPublishResponse {
  final int publishTime;
  final String publishId;
  final String shareLink;
  // 标记是否需要验证发布结果
  // 当接收到加密或无法解析的响应时，设为true
  final bool needVerify;
  
  DouyinPublishResponse({
    required this.publishTime,
    required this.publishId,
    required this.shareLink,
    this.needVerify = false,
  });
  
  factory DouyinPublishResponse.fromJson(Map<String, dynamic> json) {
    return DouyinPublishResponse(
      publishTime: json['publishTime'] ?? 0,
      publishId: json['publishId'] ?? '',
      shareLink: json['shareLink'] ?? '',
      needVerify: json['needVerify'] ?? false,
    );
  }
}

class DouyinActivityListResponse extends DouyinBaseResponse {
  final List<Activity>? activityList;
  final List<DouyinActivityDetail>? activityDetailList;
  final DouyinActivityExtra? extra;
  
  DouyinActivityListResponse({
    required super.statusCode,
    super.statusMsg,
    this.activityList,
    this.activityDetailList,
    this.extra,
  });
  
  factory DouyinActivityListResponse.fromJson(Map<String, dynamic> json) {
    final baseResponse = DouyinBaseResponse.fromJson(json);
    
    List<Activity>? activities;
    List<DouyinActivityDetail>? activityDetails;
    DouyinActivityExtra? extraInfo;
    
    // 解析extra信息
    if (json['extra'] != null) {
      extraInfo = DouyinActivityExtra.fromJson(json['extra']);
    }
    
    // 尝试解析新的活动数据格式（activity_list字段）
    try {
      if (json['activity_list'] != null) {
        final activityListData = json['activity_list'];
        if (activityListData is List) {
          // 解析详细活动信息
          activityDetails = activityListData.map((e) => DouyinActivityDetail.fromJson(e)).toList();
          
          // 同时转换为简单的Activity列表以保持兼容性
          activities = activityDetails.map((detail) => detail.toActivity()).toList();
        }
      }
      
      // 如果没有找到activity_list，尝试其他可能的字段名
      if (activities == null) {
        // 尝试从 'activities' 字段解析
        if (json['activities'] != null) {
          final activitiesData = json['activities'];
          if (activitiesData is List) {
            activities = activitiesData.map((e) => 
              Activity(
                value: (e['id'] ?? e['activity_id'] ?? e['value'] ?? '').toString(), 
                label: e['title'] ?? e['name'] ?? e['label'] ?? e['activity_name'] ?? ''
              )
            ).toList();
          }
        }
        
        // 尝试从 'data' 字段解析
        if (activities == null && json['data'] != null) {
          final data = json['data'];
          if (data is List) {
            activities = data.map((e) => 
              Activity(
                value: (e['id'] ?? e['activity_id'] ?? e['value'] ?? '').toString(), 
                label: e['title'] ?? e['name'] ?? e['label'] ?? e['activity_name'] ?? ''
              )
            ).toList();
          } else if (data is Map && data['activities'] != null) {
            final activitiesData = data['activities'];
            if (activitiesData is List) {
              activities = activitiesData.map((e) => 
                Activity(
                  value: (e['id'] ?? e['activity_id'] ?? e['value'] ?? '').toString(), 
                  label: e['title'] ?? e['name'] ?? e['label'] ?? e['activity_name'] ?? ''
                )
              ).toList();
            }
          }
        }
        
        // 尝试从 'list' 字段解析
        if (activities == null && json['list'] != null) {
          final listData = json['list'];
          if (listData is List) {
            activities = listData.map((e) => 
              Activity(
                value: (e['id'] ?? e['activity_id'] ?? e['value'] ?? '').toString(), 
                label: e['title'] ?? e['name'] ?? e['label'] ?? e['activity_name'] ?? ''
              )
            ).toList();
          }
        }
      }
      
      // 如果都没有找到，返回空列表而不是null
      activities ??= <Activity>[];
      
    } catch (e) {
      activities = <Activity>[];
      activityDetails = <DouyinActivityDetail>[];
    }
    
    return DouyinActivityListResponse(
      statusCode: baseResponse.statusCode,
      statusMsg: baseResponse.statusMsg,
      activityList: activities,
      activityDetailList: activityDetails,
      extra: extraInfo,
    );
  }
}

class DouyinMixListResponse extends DouyinBaseResponse {
  final List<MixInfo>? mixList;
  
  DouyinMixListResponse({
    required super.statusCode,
    super.statusMsg,
    this.mixList,
  });
  
  factory DouyinMixListResponse.fromJson(Map<String, dynamic> json) {
    final baseResponse = DouyinBaseResponse.fromJson(json);
    final mixes = (json['mix_list'] as List?)?.map((e) => 
      MixInfo(mixId: e['mix_id'] ?? '', mixName: e['mix_name'] ?? '')
    ).toList();
    
    return DouyinMixListResponse(
      statusCode: baseResponse.statusCode,
      statusMsg: baseResponse.statusMsg,
      mixList: mixes,
    );
  }
}

class DouyinDashboardResponse extends DouyinBaseResponse {
  final List<Map<String, dynamic>>? data;
  
  DouyinDashboardResponse({
    required super.statusCode,
    super.statusMsg,
    this.data,
  });
}

// 热点数据相关类型
class HotspotItem {
  final String title;
  final String? description;
  final int hotValue;
  final String? trendType; // 上升、下降、持平
  final String? category;
  final String? tag;
  
  HotspotItem({
    required this.title,
    this.description,
    required this.hotValue,
    this.trendType,
    this.category,
    this.tag,
  });
  
  factory HotspotItem.fromJson(Map<String, dynamic> json) {
    return HotspotItem(
      title: json['title'] ?? 
             json['name'] ?? 
             json['content'] ?? 
             json['sentence'] ?? 
             json['text'] ?? 
             json['keyword'] ?? 
             json['word'] ??  // 搜索结果中常见
             json['hot_word'] ??  // 热词字段
             '未知热点',
      
      // 描述字段
      description: json['description'] ?? 
                   json['desc'] ?? 
                   json['summary'] ?? 
                   json['abstract'] ??
                   json['detail'],
      
      // 热度值 - 支持更多可能的字段
      hotValue: _parseHotValue(json),
      
      // 趋势类型
      trendType: json['trend_type'] ?? 
                 json['trend'] ?? 
                 json['trend_status'] ??
                 json['status'],
      
      // 分类
      category: json['category'] ?? 
                json['type'] ?? 
                json['tag_type'] ?? 
                json['class'] ??
                json['topic_type'],
      
      // 标签
      tag: json['tag'] ?? 
           json['tags'] ?? 
           json['label'] ??
           json['topic'],
    );
  }
  
  // 解析热度值的辅助方法 - 增强版
  static int _parseHotValue(Map<String, dynamic> json) {
    // 尝试多种可能的热度字段
    final candidates = [
      'hot_value',
      'heat',
      'heat_value', 
      'popularity',
      'score',
      'weight',
      'hot_score',
      'trend_value',
      'view_count',  // 浏览量
      'search_count', // 搜索量
      'discuss_count', // 讨论量
      'hotness',
    ];
    
    for (final field in candidates) {
      if (json[field] != null) {
        if (json[field] is int) {
          return json[field];
        } else if (json[field] is double) {
          return json[field].toInt();
        } else if (json[field] is String) {
          // 处理带单位的热度值，如 "1.2万"、"156.7万" 等
          final str = json[field] as String;
          if (str.contains('万')) {
            final numStr = str.replaceAll('万', '').replaceAll(',', '');
            final num = double.tryParse(numStr);
            if (num != null) {
              return (num * 10000).toInt();
            }
          } else if (str.contains('千')) {
            final numStr = str.replaceAll('千', '').replaceAll(',', '');
            final num = double.tryParse(numStr);
            if (num != null) {
              return (num * 1000).toInt();
            }
          } else {
            // 直接解析数字
            final num = int.tryParse(str.replaceAll(',', ''));
            if (num != null) {
              return num;
            }
          }
        }
      }
    }
    
    return 0; // 默认值
  }
}

class DouyinHotspotResponse extends DouyinBaseResponse {
  final List<HotspotItem>? hotspotList;
  final int? total;
  final bool? hasMore;
  
  DouyinHotspotResponse({
    required super.statusCode,
    super.statusMsg,
    this.hotspotList,
    this.total,
    this.hasMore,
  });
  
  factory DouyinHotspotResponse.fromJson(Map<String, dynamic> json) {
    final baseResponse = DouyinBaseResponse.fromJson(json);
    
    List<HotspotItem>? hotspots;
    
    try {
      // 根据index.ts的实现，推荐热点和搜索热点可能有不同的数据结构
      
      // 尝试搜索热点的数据结构 (DouyinHotDataResponse)
      if (json['data'] != null && json['data'] is Map) {
        final data = json['data'] as Map<String, dynamic>;
        
        // 搜索热点通常在 data.words 中
        if (data['words'] != null && data['words'] is List) {
          hotspots = (data['words'] as List).map((e) => HotspotItem.fromJson(e)).toList();
        }
        // 或者在 data.list 中
        else if (data['list'] != null && data['list'] is List) {
          hotspots = (data['list'] as List).map((e) => HotspotItem.fromJson(e)).toList();
        }
        // 或者在 data.results 中
        else if (data['results'] != null && data['results'] is List) {
          hotspots = (data['results'] as List).map((e) => HotspotItem.fromJson(e)).toList();
        }
      }
      
      // 尝试推荐热点的数据结构 (DouyinAllHotDataResponse)
      if (hotspots == null && json['data'] != null && json['data'] is List) {
        hotspots = (json['data'] as List).map((e) => HotspotItem.fromJson(e)).toList();
      }
      
      // 尝试直接在顶级字段中查找
      if (hotspots == null) {
        // 尝试从顶级 'list' 字段解析
        if (json['list'] != null && json['list'] is List) {
          hotspots = (json['list'] as List).map((e) => HotspotItem.fromJson(e)).toList();
        }
        // 尝试从 'hotspots' 字段解析
        else if (json['hotspots'] != null && json['hotspots'] is List) {
          hotspots = (json['hotspots'] as List).map((e) => HotspotItem.fromJson(e)).toList();
        }
        // 尝试从 'items' 字段解析
        else if (json['items'] != null && json['items'] is List) {
          hotspots = (json['items'] as List).map((e) => HotspotItem.fromJson(e)).toList();
        }
        // 尝试从 'results' 字段解析
        else if (json['results'] != null && json['results'] is List) {
          hotspots = (json['results'] as List).map((e) => HotspotItem.fromJson(e)).toList();
        }
        // 尝试从 'words' 字段解析 (搜索结果常用)
        else if (json['words'] != null && json['words'] is List) {
          hotspots = (json['words'] as List).map((e) => HotspotItem.fromJson(e)).toList();
        }
      }
      
      // 如果都没有找到，返回空列表而不是null
      hotspots ??= <HotspotItem>[];
      
    } catch (e) {
      hotspots = <HotspotItem>[];
    }
    
    return DouyinHotspotResponse(
      statusCode: baseResponse.statusCode,
      statusMsg: baseResponse.statusMsg,
      hotspotList: hotspots,
      total: json['total'] ?? json['count'],
      hasMore: json['has_more'] ?? json['hasMore'],
    );
  }
}

/// 抖音热点数据响应
class DouyinHotDataResponse {
  final Extra? extra;
  final LogPb? logPb;
  final List<HotSentence>? sentences;
  final int? statusCode;

  DouyinHotDataResponse({
    this.extra,
    this.logPb,
    this.sentences,
    this.statusCode,
  });

  factory DouyinHotDataResponse.fromJson(Map<String, dynamic> json) {
    return DouyinHotDataResponse(
      extra: json['extra'] != null ? Extra.fromJson(json['extra']) : null,
      logPb: json['log_pb'] != null ? LogPb.fromJson(json['log_pb']) : null,
      sentences: json['sentences'] != null
          ? List<HotSentence>.from(
              json['sentences'].map((x) => HotSentence.fromJson(x)))
          : null,
      statusCode: json['status_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'extra': extra?.toJson(),
      'log_pb': logPb?.toJson(),
      'sentences': sentences?.map((x) => x.toJson()).toList(),
      'status_code': statusCode,
    };
  }
}

/// 热点数据Extra信息
class Extra {
  final List<String>? fatalItemIds;
  final String? logid;
  final int? now;

  Extra({
    this.fatalItemIds,
    this.logid,
    this.now,
  });

  factory Extra.fromJson(Map<String, dynamic> json) {
    return Extra(
      fatalItemIds: json['fatal_item_ids'] != null
          ? List<String>.from(json['fatal_item_ids'])
          : null,
      logid: json['logid'],
      now: json['now'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fatal_item_ids': fatalItemIds,
      'logid': logid,
      'now': now,
    };
  }
}

/// 热点数据LogPb信息
class LogPb {
  final String? imprId;

  LogPb({
    this.imprId,
  });

  factory LogPb.fromJson(Map<String, dynamic> json) {
    return LogPb(
      imprId: json['impr_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'impr_id': imprId,
    };
  }
}

/// 热点句子信息
class HotSentence {
  final dynamic awemeInfos;
  final String? challengeId;
  final int? displayStyle;
  final List<DriftInfo>? driftInfo;
  final int? eventTime;
  final String? groupId;
  final int? hotValue;
  final String? hotlistParam;
  final int? label;
  final String? postAwemeInfo;
  final dynamic relatedWords;
  final String? sentenceId;
  final int? videoCount;
  final String? word;
  final WordCover? wordCover;
  final List<int>? wordSubBoard;
  final int? wordType;

  HotSentence({
    this.awemeInfos,
    this.challengeId,
    this.displayStyle,
    this.driftInfo,
    this.eventTime,
    this.groupId,
    this.hotValue,
    this.hotlistParam,
    this.label,
    this.postAwemeInfo,
    this.relatedWords,
    this.sentenceId,
    this.videoCount,
    this.word,
    this.wordCover,
    this.wordSubBoard,
    this.wordType,
  });

  factory HotSentence.fromJson(Map<String, dynamic> json) {
    return HotSentence(
      awemeInfos: json['aweme_infos'],
      challengeId: json['challenge_id'],
      displayStyle: json['display_style'],
      driftInfo: json['drift_info'] != null
          ? List<DriftInfo>.from(
              json['drift_info'].map((x) => DriftInfo.fromJson(x)))
          : null,
      eventTime: json['event_time'],
      groupId: json['group_id'],
      hotValue: json['hot_value'],
      hotlistParam: json['hotlist_param'],
      label: json['label'],
      postAwemeInfo: json['post_aweme_info'],
      relatedWords: json['related_words'],
      sentenceId: json['sentence_id'],
      videoCount: json['video_count'],
      word: json['word'],
      wordCover: json['word_cover'] != null
          ? WordCover.fromJson(json['word_cover'])
          : null,
      wordSubBoard: json['word_sub_board'] != null
          ? List<int>.from(json['word_sub_board'])
          : null,
      wordType: json['word_type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'aweme_infos': awemeInfos,
      'challenge_id': challengeId,
      'display_style': displayStyle,
      'drift_info': driftInfo?.map((x) => x.toJson()).toList(),
      'event_time': eventTime,
      'group_id': groupId,
      'hot_value': hotValue,
      'hotlist_param': hotlistParam,
      'label': label,
      'post_aweme_info': postAwemeInfo,
      'related_words': relatedWords,
      'sentence_id': sentenceId,
      'video_count': videoCount,
      'word': word,
      'word_cover': wordCover?.toJson(),
      'word_sub_board': wordSubBoard,
      'word_type': wordType,
    };
  }
}

/// 热点趋势信息
class DriftInfo {
  final int? score;
  final int? timestamp;

  DriftInfo({
    this.score,
    this.timestamp,
  });

  factory DriftInfo.fromJson(Map<String, dynamic> json) {
    return DriftInfo(
      score: json['score'],
      timestamp: json['timestamp'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'score': score,
      'timestamp': timestamp,
    };
  }
}

/// 热点封面信息
class WordCover {
  final String? uri;
  final List<String>? urlList;

  WordCover({
    this.uri,
    this.urlList,
  });

  factory WordCover.fromJson(Map<String, dynamic> json) {
    return WordCover(
      uri: json['uri'],
      urlList:
          json['url_list'] != null ? List<String>.from(json['url_list']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uri': uri,
      'url_list': urlList,
    };
  }
}

/// 话题建议项
class TopicSug {
  final String chaName;
  final int viewCount;
  final String cid;
  final String groupId;
  final int tag;

  TopicSug({
    required this.chaName,
    required this.viewCount,
    required this.cid,
    required this.groupId,
    required this.tag,
  });

  factory TopicSug.fromJson(Map<String, dynamic> json) {
    return TopicSug(
      chaName: json['cha_name'] ?? '',
      viewCount: json['view_count'] ?? 0,
      cid: json['cid'] ?? '',
      groupId: json['group_id'] ?? '',
      tag: json['tag'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cha_name': chaName,
      'view_count': viewCount,
      'cid': cid,
      'group_id': groupId,
      'tag': tag,
    };
  }
}

/// 话题查询记录
class WordsQueryRecord {
  final String? words;

  WordsQueryRecord({
    this.words,
  });

  factory WordsQueryRecord.fromJson(Map<String, dynamic> json) {
    return WordsQueryRecord(
      words: json['words'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'words': words,
    };
  }
}

/// 抖音话题建议响应
class DouyinTopicsSugResponse extends DouyinBaseResponse {
  final List<TopicSug>? sugList;
  final String? rid;
  final WordsQueryRecord? wordsQueryRecord;
  final Extra? extra;
  final LogPb? logPb;

  DouyinTopicsSugResponse({
    required super.statusCode,
    super.statusMsg,
    this.sugList,
    this.rid,
    this.wordsQueryRecord,
    this.extra,
    this.logPb,
  });

  factory DouyinTopicsSugResponse.fromJson(Map<String, dynamic> json) {
    final baseResponse = DouyinBaseResponse.fromJson(json);
    
    List<TopicSug>? suggestions;
    
    try {
      if (json['sug_list'] != null && json['sug_list'] is List) {
        suggestions = (json['sug_list'] as List)
            .map((e) => TopicSug.fromJson(e))
            .toList();
      }
      
      // 如果sug_list为空，尝试其他可能的字段名
      if (suggestions == null || suggestions.isEmpty) {
        // 尝试从 'data' 字段解析
        if (json['data'] != null && json['data'] is List) {
          suggestions = (json['data'] as List)
              .map((e) => TopicSug(
                    chaName: e['title'] ?? e['name'] ?? e['cha_name'] ?? '',
                    viewCount: e['view_count'] ?? e['count'] ?? 0,
                    cid: e['id'] ?? e['cid'] ?? '',
                    groupId: e['group_id'] ?? '',
                    tag: e['tag'] ?? 0,
                  ))
              .toList();
        }
        
        // 尝试从 'suggestions' 字段解析
        if ((suggestions == null || suggestions.isEmpty) && 
            json['suggestions'] != null && json['suggestions'] is List) {
          suggestions = (json['suggestions'] as List)
              .map((e) => TopicSug(
                    chaName: e['title'] ?? e['name'] ?? e['cha_name'] ?? '',
                    viewCount: e['view_count'] ?? e['count'] ?? 0,
                    cid: e['id'] ?? e['cid'] ?? '',
                    groupId: e['group_id'] ?? '',
                    tag: e['tag'] ?? 0,
                  ))
              .toList();
        }
      }
      
      // 如果都没有找到，返回空列表
      suggestions ??= <TopicSug>[];
      
    } catch (e) {
      suggestions = <TopicSug>[];
    }
    
    return DouyinTopicsSugResponse(
      statusCode: baseResponse.statusCode,
      statusMsg: baseResponse.statusMsg,
      sugList: suggestions,
      rid: json['rid'],
      wordsQueryRecord: json['words_query_record'] != null
          ? WordsQueryRecord.fromJson(json['words_query_record'])
          : null,
      extra: json['extra'] != null ? Extra.fromJson(json['extra']) : null,
      logPb: json['log_pb'] != null ? LogPb.fromJson(json['log_pb']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status_code': statusCode,
      'status_msg': statusMsg,
      'sug_list': sugList?.map((x) => x.toJson()).toList(),
      'rid': rid,
      'words_query_record': wordsQueryRecord?.toJson(),
      'extra': extra?.toJson(),
      'log_pb': logPb?.toJson(),
    };
  }
}

/// 抖音作品信息
class DouyinAwemeItem {
  final String awemeId;
  final String? desc;
  final Map<String, dynamic>? statistics;
  final Map<String, dynamic>? author;
  final Map<String, dynamic>? video;
  final Map<String, dynamic>? music;
  final Map<String, dynamic>? textExtra;
  final List<dynamic>? images;
  final int createTime;
  final int? visibility;
  final String? shareUrl;

  DouyinAwemeItem({
    required this.awemeId,
    this.desc,
    this.statistics,
    this.author,
    this.video,
    this.music,
    this.textExtra,
    this.images,
    required this.createTime,
    this.visibility,
    this.shareUrl,
  });

  factory DouyinAwemeItem.fromJson(Map<String, dynamic> json) {
    try {
      // 处理可能的数据结构1: 直接的作品数据
      if (json['aweme_id'] != null) {
        return DouyinAwemeItem(
          awemeId: json['aweme_id'] ?? '',
          desc: json['desc'],
          statistics: json['statistics'] is Map ? Map<String, dynamic>.from(json['statistics']) : null,
          author: json['author'] is Map ? Map<String, dynamic>.from(json['author']) : null,
          video: json['video'] is Map ? Map<String, dynamic>.from(json['video']) : null,
          music: json['music'] is Map ? Map<String, dynamic>.from(json['music']) : null,
          textExtra: json['text_extra'] is Map ? Map<String, dynamic>.from(json['text_extra']) : null,
          images: json['images'] is List ? List<dynamic>.from(json['images']) : null,
          createTime: json['create_time'] is int ? json['create_time'] : 
                    json['create_time'] is String ? int.tryParse(json['create_time']) ?? 0 : 0,
          visibility: json['visibility'],
          shareUrl: json['share_url'],
        );
      }
      
      // 处理可能的数据结构2: 包装在aweme_info中
      if (json['aweme_info'] != null && json['aweme_info'] is Map) {
        final awemeInfo = json['aweme_info'] as Map<String, dynamic>;
        return DouyinAwemeItem(
          awemeId: awemeInfo['aweme_id'] ?? '',
          desc: awemeInfo['desc'],
          statistics: awemeInfo['statistics'] is Map ? Map<String, dynamic>.from(awemeInfo['statistics']) : null,
          author: awemeInfo['author'] is Map ? Map<String, dynamic>.from(awemeInfo['author']) : null,
          video: awemeInfo['video'] is Map ? Map<String, dynamic>.from(awemeInfo['video']) : null,
          music: awemeInfo['music'] is Map ? Map<String, dynamic>.from(awemeInfo['music']) : null,
          textExtra: awemeInfo['text_extra'] is Map ? Map<String, dynamic>.from(awemeInfo['text_extra']) : null,
          images: awemeInfo['images'] is List ? List<dynamic>.from(awemeInfo['images']) : null,
          createTime: awemeInfo['create_time'] is int ? awemeInfo['create_time'] : 
                    awemeInfo['create_time'] is String ? int.tryParse(awemeInfo['create_time']) ?? 0 : 0,
          visibility: awemeInfo['visibility'],
          shareUrl: awemeInfo['share_url'] ?? awemeInfo['share_info']?['share_url'],
        );
      }
      
      // 处理可能的数据结构3: 作品ID在item_id字段
      if (json['item_id'] != null) {
        return DouyinAwemeItem(
          awemeId: json['item_id'] ?? '',
          desc: json['title'] ?? json['desc'],
          statistics: json['statistics'] is Map ? Map<String, dynamic>.from(json['statistics']) : 
                     {'comment_count': json['comment_count'] ?? 0},
          author: json['author'] is Map ? Map<String, dynamic>.from(json['author']) : 
                 {'uid': json['anchor_user_id'] ?? '', 'nickname': ''},
          video: json['video'] is Map ? Map<String, dynamic>.from(json['video']) : 
                {'cover': {'url_list': [json['cover_image_url'] ?? '']}},
          createTime: json['create_time'] is int ? json['create_time'] : 
                    json['create_time'] is String ? int.tryParse(json['create_time']) ?? 0 : 0,
          shareUrl: json['item_link'] ?? json['share_url'],
        );
      }
      
      // 默认情况，使用原有逻辑
      return DouyinAwemeItem(
        awemeId: json['aweme_id'] ?? json['item_id'] ?? '',
        desc: json['desc'] ?? json['title'],
        statistics: json['statistics'],
        author: json['author'],
        video: json['video'],
        music: json['music'],
        textExtra: json['text_extra'],
        images: json['images'],
        createTime: json['create_time'] ?? 0,
        visibility: json['visibility'],
        shareUrl: json['share_url'] ?? json['item_link'],
      );
    } catch (e) {
      // 发生异常时返回最小可用对象
      return DouyinAwemeItem(
        awemeId: json['aweme_id'] ?? json['item_id'] ?? '',
        createTime: 0,
      );
    }
  }
}

/// 抖音创作者作品列表响应
class DouyinCreatorListResponse {
  final int statusCode;
  final String? statusMsg;
  final List<DouyinAwemeItem> awemeList;
  final String? cursor;
  final bool hasMore;
  final Map<String, dynamic>? extra;

  DouyinCreatorListResponse({
    required this.statusCode,
    this.statusMsg,
    required this.awemeList,
    this.cursor,
    required this.hasMore,
    this.extra,
  });

  factory DouyinCreatorListResponse.fromJson(Map<String, dynamic> json) {
    final awemeList = <DouyinAwemeItem>[];
    if (json['aweme_list'] != null) {
      for (var item in json['aweme_list']) {
        awemeList.add(DouyinAwemeItem.fromJson(item));
      }
    }

    return DouyinCreatorListResponse(
      statusCode: json['status_code'] ?? -1,
      statusMsg: json['status_msg'],
      awemeList: awemeList,
      cursor: json['cursor']?.toString(),
      hasMore: json['has_more'] == 1,
      extra: json['extra'],
    );
  }
}

/// 抖音评论信息
class DouyinComment {
  final String cid;
  final String text;
  final int createTime;
  final Map<String, dynamic> user;
  final int diggCount;
  final int replyCount;
  final int replyCommentTotal;
  final int status;

  DouyinComment({
    required this.cid,
    required this.text,
    required this.createTime,
    required this.user,
    required this.diggCount,
    required this.replyCount,
    required this.replyCommentTotal,
    required this.status,
  });

  factory DouyinComment.fromJson(Map<String, dynamic> json) {
    return DouyinComment(
      cid: json['cid'] ?? '',
      text: json['text'] ?? '',
      createTime: json['create_time'] ?? 0,
      user: json['user'] ?? {},
      diggCount: json['digg_count'] ?? 0,
      replyCount: json['reply_count'] ?? 0,
      replyCommentTotal: json['reply_comment_total'] ?? 0,
      status: json['status'] ?? 0,
    );
  }
}

/// 抖音创作者评论列表响应
class DouyinCreatorCommentListResponse extends DouyinBaseResponse {
  final List<DouyinWebComment> comments;
  final String? cursor;
  final bool hasMore;
  final Map<String, dynamic>? extra;
  final bool hasVcdFilter;
  final int totalCount;

  DouyinCreatorCommentListResponse({
    required super.statusCode,
    super.statusMsg,
    required this.comments,
    this.cursor,
    required this.hasMore,
    this.extra,
    this.hasVcdFilter = false,
    this.totalCount = 0,
  });

  factory DouyinCreatorCommentListResponse.fromJson(Map<String, dynamic> json) {
    final baseResponse = DouyinBaseResponse.fromJson(json);
    final comments = <DouyinWebComment>[];
    
    // 尝试从comment_info_list字段解析
    if (json['comment_info_list'] != null) {
      for (var item in json['comment_info_list']) {
        try {
          comments.add(DouyinWebComment.fromJson(item));
        } catch (e) {
          // 解析失败时跳过此条评论
          continue;
        }
      }
    }
    // 尝试从comments字段解析（旧版兼容）
    else if (json['comments'] != null) {
      for (var item in json['comments']) {
        try {
          // 将DouyinComment格式转换为DouyinWebComment格式
          final userInfo = {
            'screen_name': item['user']['nickname'] ?? '',
            'avatar_url': item['user']['avatar_thumb'] != null ? 
                item['user']['avatar_thumb']['url_list'][0] : '',
            'user_id': item['user']['uid'] ?? '',
          };
          
          comments.add(DouyinWebComment(
            commentId: item['cid'] ?? '',
            createTime: item['create_time'] ?? 0,
            diggCount: item['digg_count'] ?? 0,
            followed: false,
            following: false,
            isAuthor: false,
            level: 1,
            replyCount: item['reply_count'] ?? 0,
            replyToUserInfo: null,
            status: item['status'] ?? 0,
            text: item['text'] ?? '',
            userBury: false,
            userDigg: false,
            userInfo: userInfo,
          ));
        } catch (e) {
          // 解析失败时跳过此条评论
          continue;
        }
      }
    }

    return DouyinCreatorCommentListResponse(
      statusCode: baseResponse.statusCode,
      statusMsg: baseResponse.statusMsg,
      comments: comments,
      cursor: json['cursor']?.toString(),
      hasMore: json['has_more'] == true || json['has_more'] == 1,
      extra: json['extra'],
      hasVcdFilter: json['has_vcd_filter'] ?? false,
      totalCount: json['total_count'] ?? 0,
    );
  }
}

/// 抖音新评论响应
class DouyinNewCommentResponse {
  final int statusCode;
  final String? statusMsg;
  final DouyinComment? comment;
  final Map<String, dynamic>? extra;

  DouyinNewCommentResponse({
    required this.statusCode,
    this.statusMsg,
    this.comment,
    this.extra,
  });

  factory DouyinNewCommentResponse.fromJson(Map<String, dynamic> json) {
    return DouyinNewCommentResponse(
      statusCode: json['status_code'] ?? -1,
      statusMsg: json['status_msg'],
      comment: json['comment'] != null ? DouyinComment.fromJson(json['comment']) : null,
      extra: json['extra'],
    );
  }
}

/// 抖音网页版评论信息
class DouyinWebComment {
  final String commentId;
  final int createTime;
  final int diggCount;
  final bool followed;
  final bool following;
  final bool isAuthor;
  final int level;
  final int replyCount;
  final Map<String, dynamic>? replyToUserInfo;
  final int status;
  final String text;
  final bool userBury;
  final bool userDigg;
  final Map<String, dynamic> userInfo;

  DouyinWebComment({
    required this.commentId,
    required this.createTime,
    required this.diggCount,
    required this.followed,
    required this.following,
    required this.isAuthor,
    required this.level,
    required this.replyCount,
    this.replyToUserInfo,
    required this.status,
    required this.text,
    required this.userBury,
    required this.userDigg,
    required this.userInfo,
  });

  factory DouyinWebComment.fromJson(Map<String, dynamic> json) {
    return DouyinWebComment(
      commentId: json['comment_id'] ?? '',
      createTime: json['create_time'] is String ? int.tryParse(json['create_time']) ?? 0 : json['create_time'] ?? 0,
      diggCount: json['digg_count'] is String ? int.tryParse(json['digg_count']) ?? 0 : json['digg_count'] ?? 0,
      followed: json['followed'] ?? false,
      following: json['following'] ?? false,
      isAuthor: json['is_author'] ?? false,
      level: json['level'] is String ? int.tryParse(json['level']) ?? 1 : json['level'] ?? 1,
      replyCount: json['reply_count'] is String ? int.tryParse(json['reply_count']) ?? 0 : json['reply_count'] ?? 0,
      replyToUserInfo: json['reply_to_user_info'],
      status: json['status'] is String ? int.tryParse(json['status']) ?? 0 : json['status'] ?? 0,
      text: json['text'] ?? '',
      userBury: json['user_bury'] ?? false,
      userDigg: json['user_digg'] ?? false,
      userInfo: json['user_info'] ?? {},
    );
  }
}

/// 抖音网页版评论列表响应
class DouyinWebCommentListResponse extends DouyinBaseResponse {
  final List<DouyinWebComment> commentInfoList;
  final String? cursor;
  final Map<String, dynamic>? extra;
  final bool hasMore;
  final bool hasVcdFilter;
  final int totalCount;

  DouyinWebCommentListResponse({
    required super.statusCode,
    super.statusMsg,
    required this.commentInfoList,
    this.cursor,
    this.extra,
    required this.hasMore,
    required this.hasVcdFilter,
    required this.totalCount,
  });

  factory DouyinWebCommentListResponse.fromJson(Map<String, dynamic> json) {
    final baseResponse = DouyinBaseResponse.fromJson(json);
    final commentInfoList = <DouyinWebComment>[];
    
    if (json['comment_info_list'] != null) {
      for (var item in json['comment_info_list']) {
        commentInfoList.add(DouyinWebComment.fromJson(item));
      }
    }

    return DouyinWebCommentListResponse(
      statusCode: baseResponse.statusCode,
      statusMsg: baseResponse.statusMsg,
      commentInfoList: commentInfoList,
      cursor: json['cursor']?.toString(),
      extra: json['extra'],
      hasMore: json['has_more'] ?? false,
      hasVcdFilter: json['has_vcd_filter'] ?? false,
      totalCount: json['total_count'] ?? 0,
    );
  }
}
