import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_types.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_search_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/network/douyin_http_client.dart';
import 'package:flutter/material.dart';

/// 抖音内容测试页面 - 用于测试作品列表、评论等功能
class DouyinContentTestPage extends StatefulWidget {
  const DouyinContentTestPage({super.key});

  @override
  State<DouyinContentTestPage> createState() => _DouyinContentTestPageState();
}

class _DouyinContentTestPageState extends State<DouyinContentTestPage> {
  // 抖音服务实例
  final DouyinService _douyinService = DouyinService();

  // Cookie字符串
  final TextEditingController _cookieController = TextEditingController(
    text:
        'UIFID_TEMP=8141a5fd5fe9126a563dac92de2cabc015080c9512c5f9d75708e9bea7ee9a768d8d09c1dea23f23d055d793ddbe5fa63b15a6b8f204d0449ef10b09d675410013d0eefdc2ba06c9793435dc226c427c7f2ad6697bb44ecf9d52e2a2e3b9908087b555c0d6de4c0269e97c03d26f9234; hevc_supported=true; passport_csrf_token=6c84a5fc8faae47e6ec3d8365dcfb74a; passport_csrf_token_default=6c84a5fc8faae47e6ec3d8365dcfb74a; __security_mc_1_s_sdk_crypt_sdk=73fdb905-4f48-abe4; bd_ticket_guard_client_web_domain=2; passport_mfa_token=CjXZU25kOQ6Orp2U0L0RzFUrk6xhXbtkXkLRpNs0t0V56rAAajICSxHwWES9EVR9r9dvgOY3%2FxpKCjwAAAAAAAAAAAAATwTcstwXNEVmDmrC1Hfc4LgT8HnTH9a40k37uXjz9ZVwbY%2BaVGFGclQgFt6k%2Bw6JQUMQiu7xDRj2sdFsIAIiAQNqTTIx; d_ticket=b91604d250766d06faa38ac247c7511d0c1d2; n_mh=DoOtSFRRU1TMHvjzNRRwjdTF6JCJgJbY4f-Srl6OBDY; passport_auth_status=5710f51e56945d0b7c901d54b7180de8%2C; passport_auth_status_ss=5710f51e56945d0b7c901d54b7180de8%2C; login_time=1747709064994; UIFID=8141a5fd5fe9126a563dac92de2cabc015080c9512c5f9d75708e9bea7ee9a768d8d09c1dea23f23d055d793ddbe5fa63b15a6b8f204d0449ef10b09d6754100152822b796a3c3eacc189273e636373193c648e8a8461860bcb80d2c85fb80a0155198231437b3f2c43bc898a2b685109b164facfbcabd4c6bb21ded92848ea0585381891619c844fde958da841a0ce87678a249e763faa6686542d9abc4030732b5233f1d5ae56b58bf4816b9253bee; SelfTabRedDotControl=%5B%5D; __security_mc_1_s_sdk_cert_key=e72080aa-4a53-95cc; __security_server_data_status=1; _bd_ticket_crypt_doamin=2; is_staff_user=false; passport_assist_user=Cj3aMakVj3v7H9jqxu3sL_Vrpuke8jBVQbh6nn79gcGM2RDKnj3G3Oud11OxCjkaXjw5TonD4fjJkKNOB6eJGkoKPAAAAAAAAAAAAABPB81JUIbxLbHK8CN37rmLNOny-Pl2NAlTWh42DO9t4cwPuB-ryhDoZC-9U7D-yKmuGBD3kfINGImv1lQgASIBA2joj0g%3D; sid_guard=d88c8a6be91224c9c775a87be63ceca9%7C1747987063%7C5184000%7CTue%2C+22-Jul-2025+07%3A57%3A43+GMT; uid_tt=1f555a4ff1f8025e584b55cf16af5a45; uid_tt_ss=1f555a4ff1f8025e584b55cf16af5a45; sid_tt=d88c8a6be91224c9c775a87be63ceca9; sessionid=d88c8a6be91224c9c775a87be63ceca9; sessionid_ss=d88c8a6be91224c9c775a87be63ceca9; sid_ucp_v1=1.0.0-KDFjMzA5OWMzNDZlZjc1NmU0MTFhMTdiZjMzYzQzYTczNWJkOTY4ZTcKHwi00-vPhwMQ99TAwQYY2hYgDDCkkvbdBTgHQPQHSAQaAmxxIiBkODhjOGE2YmU5MTIyNGM5Yzc3NWE4N2JlNjNjZWNhOQ; ssid_ucp_v1=1.0.0-KDFjMzA5OWMzNDZlZjc1NmU0MTFhMTdiZjMzYzQzYTczNWJkOTY4ZTcKHwi00-vPhwMQ99TAwQYY2hYgDDCkkvbdBTgHQPQHSAQaAmxxIiBkODhjOGE2YmU5MTIyNGM5Yzc3NWE4N2JlNjNjZWNhOQ; _bd_ticket_crypt_cookie=0cdeebba7b59d04e3364cb0c2f3650fa; __security_mc_1_s_sdk_sign_data_key_web_protect=a43008d3-4d3e-991d; x-web-secsdk-uid=520d5b72-f395-42f6-bad7-6c790f70e288; gfkadpd=2906,33638; _tea_utm_cache_2906=undefined; csrf_session_id=bf1faf1fa3f5a83b29fcce8f1c300ad6; ttwid=1%7CLl5OKrr1jZKdG8NMqdufQdzertGLNIE8-iYoGilhqIs%7C1749049507%7C5b40e9eb732c0c39a4a9562bf21925f46f02b29949f3bb27ffcb4794bb79a329; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRjZFc0pjeFFjL0Q1cHZQdWp0cHFuMHJMSW9yWUxuY3BKSzBGay96V3hQb2xJTnhPUlJ1S01XYjRxNHg2V2dHSEk2U1NOSXB5QldOeUgxdHpCbE9hRWc9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; odin_tt=9bb75624b79a81485c74c99f20ea8c37f77e51fd2577b1bc6876e4f5c34e3c8f7e107b171b1938854b8d25c653ee627608c36773ef6813c139bd186d6b845750; passport_fe_beating_status=false',
  );

  // 作品ID输入
  final TextEditingController _awemeIdController = TextEditingController();

  // 评论输入
  final TextEditingController _commentController = TextEditingController();

  // 关键词搜索
  final TextEditingController _keywordController = TextEditingController();

  // 代理设置
  final TextEditingController _proxyController = TextEditingController();

  // 状态变量
  bool _isLoading = false;
  String _resultMessage = '';
  List<DouyinAwemeItem> _awemeList = [];
  List<DouyinWebComment> _commentList = [];
  bool _isLoggedIn = false;
  bool _useProxy = false;

  @override
  void initState() {
    super.initState();
    // 设置默认代理地址
    _proxyController.text = '127.0.0.1:7890';
  }

  @override
  void dispose() {
    _cookieController.dispose();
    _awemeIdController.dispose();
    _commentController.dispose();
    _keywordController.dispose();
    _proxyController.dispose();
    super.dispose();
  }

  // 获取代理设置
  String? get _proxy {
    return _useProxy && _proxyController.text.isNotEmpty
        ? _proxyController.text
        : null;
  }

  // 测试网络连接
  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _resultMessage = '正在测试网络连接...';
    });

    try {
      final client = DouyinHttpClient();
      final response = await client.get(
        'https://creator.douyin.com/',
        proxy: _proxy,
        validateStatus: false,
      );

      setState(() {
        _isLoading = false;
        _resultMessage = response != null ? '网络连接成功' : '网络连接失败';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _resultMessage = '网络连接失败: $e';
      });
      LoggerUtil.e('网络连接测试失败: $e');
    }
  }

  // 检查登录状态
  Future<void> _checkLoginStatus() async {
    if (_cookieController.text.isEmpty) {
      setState(() {
        _resultMessage = '请输入Cookie';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _resultMessage = '正在验证登录状态...';
    });

    try {
      // 设置代理后再进行验证
      final isLoggedIn = await _douyinService.checkLoginStatus(
        _cookieController.text,
        proxy: _proxy,
      );

      setState(() {
        _isLoading = false;
        _isLoggedIn = isLoggedIn;
        _resultMessage = isLoggedIn ? '登录状态有效' : '登录状态无效，请重新获取Cookie';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _resultMessage = '验证登录状态出错: $e';
      });
    }
  }

  // 获取作品列表
  Future<void> _getCreatorItems() async {
    // if (!_isLoggedIn) {
    //   setState(() {
    //     _resultMessage = '请先验证登录状态';
    //   });
    //   return;
    // }

    setState(() {
      _isLoading = true;
      _resultMessage = '正在获取作品列表...';
      _awemeList = [];
    });

    try {
      // 添加代理参数
      final response = await _douyinService.getCreatorItems(
        _cookieController.text,
        proxy: _proxy,
      );

      setState(() {
        _isLoading = false;
        // 将新模型的itemInfoList转换为旧的awemeList格式
        _awemeList =
            response.itemInfoList
                .map(
                  (item) => DouyinAwemeItem(
                    awemeId: item.itemId,
                    desc: item.title,
                    createTime: 0,
                    shareUrl: item.itemLink,
                  ),
                )
                .toList();
        _resultMessage = '获取到 ${_awemeList.length} 个作品';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _resultMessage = '获取作品列表出错: $e';
      });
    }
  }

  // 获取作品评论列表
  Future<void> _getComments() async {
    if (_awemeIdController.text.isEmpty) {
      setState(() {
        _resultMessage = '请输入作品ID';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _resultMessage = '正在获取评论列表...';
      _commentList = [];
    });

    try {
      // 尝试三种不同的评论获取方式，按优先级排序
      bool isSuccess = false;

      // 1. 首先尝试hj域名获取评论（已发布作品）
      LoggerUtil.i('方法1: 尝试使用hj域名获取评论...');
      try {
        final response = await _douyinService.getWebCommentListHj(
          _cookieController.text,
          _awemeIdController.text,
          proxy: _proxy,
        );

        if (response['comments'] != null && response['comments'].isNotEmpty) {
          // 解析评论数据
          final comments = response['comments'];
          setState(() {
            _commentList = List<DouyinWebComment>.from(
              comments.map((comment) => DouyinWebComment.fromJson(comment)),
            );
            _resultMessage = '获取到 ${_commentList.length} 条评论 (hj域名)';
            _isLoading = false;
          });
          LoggerUtil.i('方法1成功: 使用hj域名获取评论成功');
          isSuccess = true;
          return;
        } else {
          LoggerUtil.w('方法1: hj域名返回的评论为空');
        }
      } catch (e) {
        LoggerUtil.w('方法1失败: 使用hj域名获取评论失败: $e');
      }

      // 2. 尝试搜索作品评论API
      if (!isSuccess) {
        LoggerUtil.i('方法2: 尝试使用搜索作品评论API获取评论...');
        try {
          final response = await _douyinService.getSearchAwemeCommentList(
            _cookieController.text,
            _awemeIdController.text,
            proxy: _proxy,
          );

          if (response['comments'] != null && response['comments'].isNotEmpty) {
            // 解析评论数据
            final comments = response['comments'];
            setState(() {
              _commentList = List<DouyinWebComment>.from(
                comments.map((comment) => DouyinWebComment.fromJson(comment)),
              );
              _resultMessage = '获取到 ${_commentList.length} 条评论 (搜索作品API)';
              _isLoading = false;
            });
            LoggerUtil.i('方法2成功: 使用搜索作品评论API获取评论成功');
            isSuccess = true;
            return;
          } else {
            LoggerUtil.w('方法2: 搜索作品评论API返回的评论为空');
          }
        } catch (e) {
          LoggerUtil.w('方法2失败: 使用搜索作品评论API获取评论失败: $e');
        }
      }

      // 3. 如果前两种方式都失败，最后尝试创作者平台API
      if (!isSuccess) {
        LoggerUtil.i('方法3: 尝试使用创作者平台API获取评论...');
        try {
          final response = await _douyinService.getCreatorCommentList(
            _cookieController.text,
            _awemeIdController.text,
            proxy: _proxy,
          );

          setState(() {
            _commentList = response.comments;
            _resultMessage = '获取到 ${_commentList.length} 条评论 (创作者平台API)';
            _isLoading = false;
          });
          LoggerUtil.i('方法3成功: 使用创作者平台API获取评论成功');
          isSuccess = true;

          if (_commentList.isEmpty) {
            setState(() {
              _resultMessage = '未找到评论 (创作者平台API)';
            });
          }
        } catch (e) {
          LoggerUtil.e('方法3失败: 使用创作者平台API获取评论失败: $e');
        }
      }

      // 如果所有方法都失败了
      if (!isSuccess) {
        setState(() {
          _isLoading = false;
          _resultMessage = '获取评论失败: 所有方法都尝试失败';
        });
        LoggerUtil.e('获取评论失败: 所有方法都尝试失败');
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('获取评论列表详细错误: $e');
      LoggerUtil.e('错误堆栈: $stackTrace');
      setState(() {
        _isLoading = false;
        _resultMessage = '获取评论列表出错: $e';
      });
    }
  }

  // 发表评论
  Future<void> _publishComment() async {
    if (!_isLoggedIn) {
      setState(() {
        _resultMessage = '请先验证登录状态';
      });
      return;
    }

    if (_awemeIdController.text.isEmpty) {
      setState(() {
        _resultMessage = '请输入作品ID';
      });
      return;
    }

    if (_commentController.text.isEmpty) {
      setState(() {
        _resultMessage = '请输入评论内容';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _resultMessage = '正在发表评论...';
    });

    try {
      final data = {
        'aweme_id': _awemeIdController.text,
        'text': _commentController.text,
        'channel_id': 0,
      };

      final response = await _douyinService.publishComment(
        _cookieController.text,
        data,
        proxy: _proxy,
      );

      setState(() {
        _isLoading = false;
        _resultMessage = '评论发表成功: ${response['comment']?['cid'] ?? '未知ID'}';
      });

      // 刷新评论列表
      _getComments();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _resultMessage = '发表评论出错: $e';
      });
    }
  }

  // 搜索作品
  Future<void> _searchPosts() async {
    // if (!_isLoggedIn) {
    //   setState(() {
    //     _resultMessage = '请先验证登录状态';
    //   })
    //   return;
    // }

    if (_keywordController.text.isEmpty) {
      setState(() {
        _resultMessage = '请输入搜索关键词';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _resultMessage = '正在搜索作品...';
      _awemeList = [];
    });

    try {
      final response = await _douyinService.getSearchNodeList(
        _cookieController.text,
        _keywordController.text,
        proxy: _proxy,
      );

      // 使用新的模型解析响应
      final searchResponse = DouyinSearchResponse.fromJson(response);

      if (searchResponse.statusCode == 0) {
        if (searchResponse.data != null && searchResponse.data!.isNotEmpty) {
          // 解析新的搜索结果格式（包含在data中的aweme_info）
          setState(() {
            _awemeList =
                searchResponse.data!.map((item) {
                  final awemeInfo = item.awemeInfo;

                  // 构建作品信息对象
                  return DouyinAwemeItem(
                    awemeId: awemeInfo.awemeId,
                    desc: awemeInfo.desc,
                    createTime: awemeInfo.createTime,
                    author: {
                      'uid': awemeInfo.author.uid,
                      'nickname': awemeInfo.author.nickname,
                      'avatar_thumb': {
                        'url_list': awemeInfo.author.avatarThumb.urlList,
                      },
                      'sec_uid': awemeInfo.author.secUid,
                      'follower_count': awemeInfo.author.followerCount,
                    },
                    statistics:
                        awemeInfo.statistics != null
                            ? {
                              'digg_count': awemeInfo.statistics.diggCount,
                              'comment_count':
                                  awemeInfo.statistics.commentCount,
                              'share_count': awemeInfo.statistics.shareCount,
                              'play_count': awemeInfo.statistics.playCount,
                            }
                            : null,
                    video:
                        awemeInfo.video != null
                            ? {
                              'play_addr': {
                                'url_list': [awemeInfo.video.playAddr],
                              },
                              'cover': {
                                'url_list': [awemeInfo.video.cover],
                              },
                              'width': awemeInfo.video.width,
                              'height': awemeInfo.video.height,
                              'duration': awemeInfo.video.duration,
                            }
                            : null,
                  );
                }).toList();

            _resultMessage = '搜索成功，共${_awemeList.length}条结果';
            _isLoading = false;
          });
        } else {
          setState(() {
            _resultMessage = '未找到相关作品';
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _resultMessage = '搜索失败: ${searchResponse.extra.logid}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _resultMessage = '搜索出错: $e';
        _isLoading = false;
      });
      LoggerUtil.e('搜索作品出错: $e');
    }
  }

  // 点赞作品
  Future<void> _likePost(String awemeId) async {
    if (!_isLoggedIn) {
      setState(() {
        _resultMessage = '请先验证登录状态';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _resultMessage = '正在点赞作品...';
    });

    try {
      final response = await _douyinService.likeContent(
        _cookieController.text,
        awemeId,
        1, // 1为点赞，0为取消点赞
        proxy: _proxy,
      );

      setState(() {
        _isLoading = false;
        _resultMessage = '点赞成功';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _resultMessage = '点赞作品出错: $e';
      });
    }
  }

  // 收藏作品
  Future<void> _collectPost(String awemeId) async {
    if (!_isLoggedIn) {
      setState(() {
        _resultMessage = '请先验证登录状态';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _resultMessage = '正在收藏作品...';
    });

    try {
      final response = await _douyinService.collectContent(
        _cookieController.text,
        awemeId,
        1, // 1为收藏，0为取消收藏
        proxy: _proxy,
      );

      setState(() {
        _isLoading = false;
        _resultMessage = '收藏成功';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _resultMessage = '收藏作品出错: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('抖音内容测试'),
        actions: [
          IconButton(
            icon: Icon(_isLoggedIn ? Icons.verified_user : Icons.no_accounts),
            color: _isLoggedIn ? Colors.green : Colors.red,
            onPressed: _checkLoginStatus,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 代理设置区域
            Card(
              margin: const EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          '代理设置',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const Spacer(),
                        Switch(
                          value: _useProxy,
                          onChanged: (value) {
                            setState(() {
                              _useProxy = value;
                            });
                          },
                        ),
                      ],
                    ),
                    if (_useProxy) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _proxyController,
                              decoration: const InputDecoration(
                                labelText: '代理地址',
                                border: OutlineInputBorder(),
                                hintText: '127.0.0.1:7890',
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 8,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _testConnection,
                            child: const Text('测试连接'),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Cookie输入
            TextField(
              controller: _cookieController,
              decoration: const InputDecoration(
                labelText: 'Cookie',
                border: OutlineInputBorder(),
                hintText: '输入抖音Cookie',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _checkLoginStatus,
              child: const Text('验证登录状态'),
            ),

            const Divider(height: 32),

            // 作品操作区域
            Text('作品操作', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _getCreatorItems,
                    child: const Text('获取我的作品'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _keywordController,
                    decoration: const InputDecoration(
                      labelText: '搜索关键词',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _searchPosts,
                  child: const Text('搜索'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 作品列表
            if (_awemeList.isNotEmpty) ...[
              Text(
                '作品列表 (${_awemeList.length}个)',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 200,
                child: ListView.builder(
                  itemCount: _awemeList.length,
                  itemBuilder: (context, index) {
                    final item = _awemeList[index];
                    return Card(
                      child: ListTile(
                        title: Text(
                          item.desc ?? '无标题',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        subtitle: Text('ID: ${item.awemeId}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.thumb_up, size: 20),
                              onPressed: () => _likePost(item.awemeId),
                            ),
                            IconButton(
                              icon: const Icon(Icons.bookmark, size: 20),
                              onPressed: () => _collectPost(item.awemeId),
                            ),
                            IconButton(
                              icon: const Icon(Icons.comment, size: 20),
                              onPressed: () {
                                _awemeIdController.text = item.awemeId;
                                _getComments();
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],

            const Divider(height: 32),

            // 评论操作区域
            Text('评论操作', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),

            TextField(
              controller: _awemeIdController,
              decoration: const InputDecoration(
                labelText: '作品ID',
                border: OutlineInputBorder(),
                hintText: '输入要查看或评论的作品ID',
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _getComments,
              child: const Text('获取评论列表'),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _commentController,
                    decoration: const InputDecoration(
                      labelText: '评论内容',
                      border: OutlineInputBorder(),
                      hintText: '输入要发表的评论',
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _publishComment,
                  child: const Text('发表评论'),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 评论列表
            if (_commentList.isNotEmpty) ...[
              Text(
                '评论列表 (${_commentList.length}条)',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 200,
                child: ListView.builder(
                  itemCount: _commentList.length,
                  itemBuilder: (context, index) {
                    final comment = _commentList[index];
                    return Card(
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundImage:
                              comment.userInfo['avatar_url'] != null
                                  ? NetworkImage(comment.userInfo['avatar_url'])
                                  : null,
                          child:
                              comment.userInfo['avatar_url'] == null
                                  ? const Icon(Icons.person)
                                  : null,
                        ),
                        title: Text(comment.userInfo['screen_name'] ?? '匿名用户'),
                        subtitle: Text(comment.text),
                        trailing: Text('${comment.diggCount} 赞'),
                      ),
                    );
                  },
                ),
              ),
            ],

            const Divider(height: 32),

            // 结果消息
            if (_resultMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                color: Colors.grey[200],
                child: Text(_resultMessage),
              ),

            // 加载指示器
            if (_isLoading)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
