import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_types.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_utils.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class DouyinTestPage extends StatefulWidget {
  const DouyinTestPage({super.key});

  @override
  createState() => _DouyinTestPageState();
}

class _DouyinTestPageState extends State<DouyinTestPage> {
  final DouyinService _douyinService = DouyinService();
  bool isLoading = false;
  String cookies = 'UIFID_TEMP=8141a5fd5fe9126a563dac92de2cabc015080c9512c5f9d75708e9bea7ee9a768d8d09c1dea23f23d055d793ddbe5fa63b15a6b8f204d0449ef10b09d675410013d0eefdc2ba06c9793435dc226c427c7f2ad6697bb44ecf9d52e2a2e3b9908087b555c0d6de4c0269e97c03d26f9234; hevc_supported=true; passport_csrf_token=6c84a5fc8faae47e6ec3d8365dcfb74a; passport_csrf_token_default=6c84a5fc8faae47e6ec3d8365dcfb74a; __security_mc_1_s_sdk_crypt_sdk=73fdb905-4f48-abe4; bd_ticket_guard_client_web_domain=2; passport_mfa_token=CjXZU25kOQ6Orp2U0L0RzFUrk6xhXbtkXkLRpNs0t0V56rAAajICSxHwWES9EVR9r9dvgOY3%2FxpKCjwAAAAAAAAAAAAATwTcstwXNEVmDmrC1Hfc4LgT8HnTH9a40k37uXjz9ZVwbY%2BaVGFGclQgFt6k%2Bw6JQUMQiu7xDRj2sdFsIAIiAQNqTTIx; d_ticket=b91604d250766d06faa38ac247c7511d0c1d2; n_mh=DoOtSFRRU1TMHvjzNRRwjdTF6JCJgJbY4f-Srl6OBDY; passport_auth_status=5710f51e56945d0b7c901d54b7180de8%2C; passport_auth_status_ss=5710f51e56945d0b7c901d54b7180de8%2C; login_time=1747709064994; UIFID=8141a5fd5fe9126a563dac92de2cabc015080c9512c5f9d75708e9bea7ee9a768d8d09c1dea23f23d055d793ddbe5fa63b15a6b8f204d0449ef10b09d6754100152822b796a3c3eacc189273e636373193c648e8a8461860bcb80d2c85fb80a0155198231437b3f2c43bc898a2b685109b164facfbcabd4c6bb21ded92848ea0585381891619c844fde958da841a0ce87678a249e763faa6686542d9abc4030732b5233f1d5ae56b58bf4816b9253bee; SelfTabRedDotControl=%5B%5D; __security_mc_1_s_sdk_cert_key=e72080aa-4a53-95cc; __security_server_data_status=1; _bd_ticket_crypt_doamin=2; is_staff_user=false; odin_tt=f288095bcea398b445fa530f3836b799a47af6fbd8b2ba564d13db4c1736d5068fdea6dd30387a773a15e656022f9d9d08e1202318dbf51e4c0534b1a0504c90; passport_assist_user=Cj3aMakVj3v7H9jqxu3sL_Vrpuke8jBVQbh6nn79gcGM2RDKnj3G3Oud11OxCjkaXjw5TonD4fjJkKNOB6eJGkoKPAAAAAAAAAAAAABPB81JUIbxLbHK8CN37rmLNOny-Pl2NAlTWh42DO9t4cwPuB-ryhDoZC-9U7D-yKmuGBD3kfINGImv1lQgASIBA2joj0g%3D; sid_guard=d88c8a6be91224c9c775a87be63ceca9%7C1747987063%7C5184000%7CTue%2C+22-Jul-2025+07%3A57%3A43+GMT; uid_tt=1f555a4ff1f8025e584b55cf16af5a45; uid_tt_ss=1f555a4ff1f8025e584b55cf16af5a45; sid_tt=d88c8a6be91224c9c775a87be63ceca9; sessionid=d88c8a6be91224c9c775a87be63ceca9; sessionid_ss=d88c8a6be91224c9c775a87be63ceca9; sid_ucp_v1=1.0.0-KDFjMzA5OWMzNDZlZjc1NmU0MTFhMTdiZjMzYzQzYTczNWJkOTY4ZTcKHwi00-vPhwMQ99TAwQYY2hYgDDCkkvbdBTgHQPQHSAQaAmxxIiBkODhjOGE2YmU5MTIyNGM5Yzc3NWE4N2JlNjNjZWNhOQ; ssid_ucp_v1=1.0.0-KDFjMzA5OWMzNDZlZjc1NmU0MTFhMTdiZjMzYzQzYTczNWJkOTY4ZTcKHwi00-vPhwMQ99TAwQYY2hYgDDCkkvbdBTgHQPQHSAQaAmxxIiBkODhjOGE2YmU5MTIyNGM5Yzc3NWE4N2JlNjNjZWNhOQ; _bd_ticket_crypt_cookie=0cdeebba7b59d04e3364cb0c2f3650fa; __security_mc_1_s_sdk_sign_data_key_web_protect=a43008d3-4d3e-991d; x-web-secsdk-uid=520d5b72-f395-42f6-bad7-6c790f70e288; gfkadpd=2906,33638; _tea_utm_cache_2906=undefined; csrf_session_id=bf1faf1fa3f5a83b29fcce8f1c300ad6; ttwid=1%7CLl5OKrr1jZKdG8NMqdufQdzertGLNIE8-iYoGilhqIs%7C1749049507%7C5b40e9eb732c0c39a4a9562bf21925f46f02b29949f3bb27ffcb4794bb79a329; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRjZFc0pjeFFjL0Q1cHZQdWp0cHFuMHJMSW9yWUxuY3BKSzBGay96V3hQb2xJTnhPUlJ1S01XYjRxNHg2V2dHSEk2U1NOSXB5QldOeUgxdHpCbE9hRWc9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; passport_fe_beating_status=true';  DouyinUserInfo? userInfo;
  String? errorMessage;

  // 上传相关状态
  String? selectedFileName;
  int uploadProgress = 0;
  String? uploadStatus;
  String? uploadDetailStatus;
  String? publishResult;
  String? fileSize;
  String proxyIp = ''; // 代理IP，实际使用时需要设置

  // 封面相关
  File? selectedCoverFile;
  String? selectedCoverName;

  // 上传日志，用于显示详细信息
  List<String> uploadLogs = [];
  final ScrollController _logsScrollController = ScrollController();

  // 上传设置
  final titleController = TextEditingController(text: '测试标题');
  final captionController = TextEditingController(); // 描述
  final hotSentenceController = TextEditingController(); // 热点
  final topicsController = TextEditingController(); // 话题，逗号分隔
  
  // Cookie 控制器
  final cookieController = TextEditingController();
  
  // 添加自主声明选择
  DouyinDeclaration? selectedDeclaration;

  // 添加合集、活动、位置信息
  List<MixInfo> mixList = [];
  MixInfo? selectedMix;
  
  List<Activity> activityList = [];
  Activity? selectedActivity;
  
  // 添加热点数据相关状态
  List<HotspotItem> hotspotList = [];
  String hotspotQuery = '';
  bool isLoadingHotspot = false;
  
  PoiInfo? selectedPoiInfo;
  final poiNameController = TextEditingController();
  final poiIdController = TextEditingController();
  
  // 添加@用户信息
  List<MentionedUserInfo> mentionedUsers = [];
  final mentionedNameController = TextEditingController();
  final mentionedUidController = TextEditingController();
  
  // 可见性设置
  int visibilityType = 0; // 0=公开, 1=好友可见, 2=私密
  
  // 话题搜索相关状态
  List<TopicSug> topicSuggestions = [];
  bool isLoadingTopics = false;
  final topicSearchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    titleController.text =
        '抖音测试上传 ${DateTime.now().toIso8601String().substring(0, 16)}';
    // 初始化加载合集和活动列表
    _loadMixList();
    _loadActivityList();
    _loadHotspotData();
  }

  @override
  void dispose() {
    titleController.dispose();
    captionController.dispose();
    hotSentenceController.dispose();
    topicsController.dispose();
    cookieController.dispose();
    poiNameController.dispose();
    poiIdController.dispose();
    mentionedNameController.dispose();
    mentionedUidController.dispose();
    topicSearchController.dispose();
    _logsScrollController.dispose();
    super.dispose();
  }

  // 获取当前有效的 Cookie
  String getCurrentCookies() {
    try {
      final inputCookies = cookieController.text.trim();
      if (inputCookies.isNotEmpty) {
        final cleaned = _cleanCookieString(inputCookies);
        addLog('使用输入的Cookie (${cleaned.length} 字符)');
        return cleaned;
      }
      final cleaned = _cleanCookieString(cookies);
      addLog('使用默认Cookie (${cleaned.length} 字符)');
      return cleaned;
    } catch (e) {
      addLog('Cookie处理失败: $e，使用基础默认Cookie');
      // 返回一个最基础的默认Cookie
      return 'sessionid=default; uid_tt=default';
    }
  }

  // 清理和验证 Cookie 字符串
  String _cleanCookieString(String cookieStr) {
    // 移除所有换行符和回车符
    String cleaned = cookieStr.replaceAll(RegExp(r'[\r\n\t]'), '');
    
    // 移除多余的空格
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // 检查Cookie长度，如果太长则截断或警告
    if (cleaned.length > 8192) { // HTTP头部通常限制在8KB以内
      addLog('警告: Cookie字符串过长 (${cleaned.length} 字符)，可能导致请求失败');
      // 可以选择截断或保持原样
      // cleaned = cleaned.substring(0, 8192);
    }
    
    // 验证Cookie格式 - 检查是否包含基本的Cookie字段
    if (!_isValidCookieFormat(cleaned)) {
      addLog('警告: Cookie格式可能无效，请检查Cookie内容');
    }
    
    return cleaned;
  }

  // 验证Cookie格式
  bool _isValidCookieFormat(String cookieStr) {
    if (cookieStr.isEmpty) return false;
    
    // 基本格式检查：应该包含键值对，以分号分隔
    if (!cookieStr.contains('=')) return false;
    
    // 检查是否包含常见的抖音Cookie字段
    final commonFields = ['sessionid', 'sid_guard', 'uid_tt', 'ttwid'];
    final hasCommonField = commonFields.any((field) => cookieStr.contains('$field='));
    
    if (!hasCommonField) {
      addLog('提示: Cookie中未找到常见的抖音登录字段，请确认Cookie的有效性');
    }
    
    return true;
  }

  // 验证用户输入的Cookie
  String? _validateInputCookie(String input) {
    if (input.trim().isEmpty) return null;
    
    final cleaned = _cleanCookieString(input);
    
    // 检查是否包含无效字符
    if (cleaned.contains(RegExp(r'[^\x20-\x7E]'))) {
      return 'Cookie包含无效字符，请检查输入';
    }
    
    // 检查基本格式
    if (!_isValidCookieFormat(cleaned)) {
      return 'Cookie格式无效，应包含键值对（如：name=value; name2=value2）';
    }
    
    return null; // 验证通过
  }

  // 加载合集列表
  Future<void> _loadMixList() async {
    try {
      setState(() {
        isLoading = true;
      });
      
      final response = await _douyinService.getMixList(getCurrentCookies());
      
      setState(() {
        mixList = response.mixList ?? [];
        isLoading = false;
      });
      
      addLog('加载了 ${mixList.length} 个合集');
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      addLog('加载合集失败: $e');
    }
  }
  
  // 加载活动列表
  Future<void> _loadActivityList() async {
    try {
      setState(() {
        isLoading = true;
      });
      
      addLog('开始加载活动列表...');
      final response = await _douyinService.getActivity(getCurrentCookies());
      
      setState(() {
        activityList = response.activityList ?? [];
        isLoading = false;
      });
      
      addLog('活动列表加载完成，状态码: ${response.statusCode}');
      addLog("活动列表消息: ${response.statusMsg ?? '无'}");
      addLog('加载了 ${activityList.length} 个活动');
      
      if (activityList.isEmpty) {
        addLog('警告: 活动列表为空，可能是没有可用活动或API返回格式不匹配');
      } else {
        // 打印前几个活动的信息
        for (int i = 0; i < activityList.length && i < 3; i++) {
          addLog('活动${i+1}: ${activityList[i].label} (${activityList[i].value})');
        }
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      addLog('加载活动失败: $e');
      
      // 显示更友好的错误信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载活动列表失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // 加载热点数据
  Future<void> _loadHotspotData() async {
    try {
      setState(() {
        isLoadingHotspot = true;
      });
      
      addLog('开始加载推荐热点数据...');
      final response = await _douyinService.getHotspotData(getCurrentCookies());
      
      // 使用新的数据结构
      final convertedHotspots = _douyinService.convertHotDataToHotspotItems(response);
      
      setState(() {
        hotspotList = convertedHotspots;
        isLoadingHotspot = false;
      });
      
      addLog('推荐热点数据加载完成，状态码: ${response.statusCode}');
      addLog('热点数据: ${response.sentences?.length ?? 0} 个热点句子');
      addLog('加载了 ${hotspotList.length} 个推荐热点');
      
      if (hotspotList.isEmpty) {
        addLog('警告: 热点列表为空，可能是API返回格式不匹配或无可用热点');
      } else {
        // 显示前几个热点的信息
        for (int i = 0; i < hotspotList.length && i < 3; i++) {
          addLog('热点${i+1}: ${hotspotList[i].title} (热度: ${hotspotList[i].hotValue})');
        }
      }
    } catch (e) {
      setState(() {
        isLoadingHotspot = false;
      });
      addLog('加载推荐热点数据失败: $e');
      
      // 显示更友好的错误信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载推荐热点失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // 搜索热点数据
  Future<void> _searchHotspotData(String query) async {
    try {
      setState(() {
        isLoadingHotspot = true;
        hotspotQuery = query;
      });
      
      addLog("开始搜索热点: '$query'");
      final response = await _douyinService.getHotspotData(getCurrentCookies(), query: query);
      
      // 使用新的数据结构
      final convertedHotspots = _douyinService.convertHotDataToHotspotItems(response);
      
      setState(() {
        hotspotList = convertedHotspots;
        isLoadingHotspot = false;
      });
      
      addLog('热点搜索完成，状态码: ${response.statusCode}');
      addLog('热点数据: ${response.sentences?.length ?? 0} 个热点句子');
      addLog("搜索热点 '$query' 返回 ${hotspotList.length} 个结果");
      
      if (hotspotList.isEmpty && query.isNotEmpty) {
        addLog('搜索结果为空，可能是关键词没有匹配的热点');
      }
    } catch (e) {
      setState(() {
        isLoadingHotspot = false;
      });
      addLog('搜索热点数据失败: $e');
      
      // 显示更友好的错误信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('搜索热点失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // 添加@用户
  void _addMentionedUser() {
    final name = mentionedNameController.text.trim();
    final uid = mentionedUidController.text.trim();
    
    if (name.isNotEmpty && uid.isNotEmpty) {
      setState(() {
        mentionedUsers.add(MentionedUserInfo(nickName: name, uid: uid));
        mentionedNameController.clear();
        mentionedUidController.clear();
      });
      addLog('添加@用户: $name');
    }
  }
  
  // 移除@用户
  void _removeMentionedUser(int index) {
    setState(() {
      final removedUser = mentionedUsers[index];
      mentionedUsers.removeAt(index);
      addLog('移除@用户: ${removedUser.nickName}');
    });
  }
  
  // 设置位置信息
  void _setPoiInfo() {
    final name = poiNameController.text.trim();
    final id = poiIdController.text.trim();
    
    if (name.isNotEmpty && id.isNotEmpty) {
      setState(() {
        selectedPoiInfo = PoiInfo(poiId: id, poiName: name);
        addLog('设置位置: $name');
      });
    }
  }
  
  // 清除位置信息
  void _clearPoiInfo() {
    setState(() {
      selectedPoiInfo = null;
      poiNameController.clear();
      poiIdController.clear();
      addLog('清除位置信息');
    });
  }

  // 搜索话题建议
  Future<void> _searchTopics(String keyword) async {
    if (keyword.trim().isEmpty) {
      setState(() {
        topicSuggestions = [];
      });
      return;
    }
    
    try {
      setState(() {
        isLoadingTopics = true;
      });
      
      addLog("开始搜索话题: '$keyword'");
      final response = await _douyinService.getTopics(getCurrentCookies(), keyword.trim());
      
      setState(() {
        topicSuggestions = response.sugList ?? [];
        isLoadingTopics = false;
      });
      
      addLog('话题搜索完成，状态码: ${response.statusCode}');
      addLog("搜索话题 '$keyword' 返回 ${topicSuggestions.length} 个建议");
      
      if (topicSuggestions.isEmpty) {
        addLog('搜索结果为空，可能是关键词没有匹配的话题');
      } else {
        // 显示前几个话题的详细信息
        for (int i = 0; i < topicSuggestions.length && i < 3; i++) {
          final topic = topicSuggestions[i];
          addLog('话题${i+1}: ${topic.chaName} (观看数: ${topic.viewCount})');
        }
      }
    } catch (e) {
      setState(() {
        isLoadingTopics = false;
      });
      addLog('搜索话题失败: $e');
      
      // 显示更友好的错误信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('搜索话题失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // 添加话题到输入框
  void _addTopicToInput(String topicName) {
    final currentText = topicsController.text.trim();
    final topics = currentText.isEmpty ? [] : currentText.split(',').map((e) => e.trim()).toList();
    
    if (!topics.contains(topicName)) {
      if (topics.isNotEmpty) {
        topicsController.text = '${topics.join(',')}, $topicName';
      } else {
        topicsController.text = topicName;
      }
      addLog('添加话题: $topicName');
    } else {
      addLog('话题已存在: $topicName');
    }
  }

  // 测试话题API
  Future<void> _testTopicsApi() async {
    const testKeyword = '美食';
    addLog('=== 开始测试话题API ===');
    try {
      addLog("测试关键词: '$testKeyword'");
      final response = await _douyinService.getTopics(getCurrentCookies(), testKeyword);
      
      addLog('话题API响应状态码: ${response.statusCode}');
      addLog("话题API响应消息: ${response.statusMsg ?? '无'}");
      addLog('话题建议数量: ${response.sugList?.length ?? 0}');
      
      if (response.sugList?.isNotEmpty == true) {
        addLog('话题建议示例:');
        for (int i = 0; i < response.sugList!.length && i < 5; i++) {
          final topic = response.sugList![i];
          addLog('  ${i+1}. ${topic.chaName} (观看: ${topic.viewCount}, ID: ${topic.cid})');
        }
      }
      
      addLog('=== 话题API测试结束 ===');
    } catch (e) {
      addLog('话题API测试失败: $e');
      addLog('=== 话题API测试结束 ===');
    }
  }

  // 添加日志
  void addLog(String log) {
    setState(() {
      uploadLogs.add('${DateTime.now().toString().substring(11, 19)} $log');

      // 保持日志不超过100条
      if (uploadLogs.length > 100) {
        uploadLogs = uploadLogs.sublist(uploadLogs.length - 100);
      }
    });

    // 滚动到底部
    Future.delayed(const Duration(milliseconds: 100), () {
      if (_logsScrollController.hasClients) {
        _logsScrollController.animateTo(
          _logsScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> getUserInfo() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final user = await _douyinService.getUserInfo(getCurrentCookies());
      setState(() {
        userInfo = user;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = e.toString();
        isLoading = false;
      });
    }
  }

  // 选择图片
  Future<void> pickAndUploadImages() async {
    setState(() {
      uploadStatus = null;
      uploadDetailStatus = null;
      publishResult = null;
      fileSize = null;
      uploadLogs.clear();
    });

    try {
      // 使用wechat_assets_picker选择图片
      final List<AssetEntity>? assets = await AssetPicker.pickAssets(
        context,
        pickerConfig: const AssetPickerConfig(
          maxAssets: 9, // 最多选择9张图片
          requestType: RequestType.image,
        ),
      );

      if (assets == null || assets.isEmpty) {
        setState(() {
          uploadStatus = '未选择任何图片';
        });
        return;
      }

      setState(() {
        selectedFileName = '${assets.length}张图片';
        uploadStatus = '准备上传...';
      });

      addLog('选择了 ${assets.length} 张图片');

      // 获取图片文件路径
      final List<String> imagePaths = [];
      int totalSize = 0;

      for (final asset in assets) {
        final file = await asset.file;
        if (file != null) {
          imagePaths.add(file.path);
          final fileSize = await file.length();
          totalSize += fileSize;
          addLog(
            '图片: ${path.basename(file.path)}, 大小: ${_formatFileSize(fileSize)}',
          );
        }
      }

      setState(() {
        fileSize = _formatFileSize(totalSize);
      });

      if (imagePaths.isEmpty) {
        setState(() {
          uploadStatus = '获取图片路径失败';
        });
        return;
      }

      // 获取用户ID
      final userUid = await _douyinService.getUserUid(getCurrentCookies());
      addLog('获取用户ID成功: $userUid');

      // 上传第一张图片作为封面
      final coverPath = imagePaths.first;

      // 设置平台参数
      final platformSetting = DouyinPlatformSettingType(
        title: titleController.text,
        cover: coverPath,
        visibilityType: visibilityType,
        proxyIp: proxyIp,
        userDeclare: selectedDeclaration != null ? 
            DouyinDeclaration.values.firstWhere(
              (e) => e.value == selectedDeclaration!.value,
              orElse: () => DouyinDeclaration.selfShoot
            ) : null,
        caption: captionController.text.isEmpty ? null : captionController.text,
        hotSentence: hotSentenceController.text.isEmpty ? null : hotSentenceController.text,
        topics: topicsController.text.isEmpty ? null : topicsController.text.split(',').map((e) => e.trim()).toList(),
        mixInfo: selectedMix,
        activity: selectedActivity != null ? [selectedActivity!] : null,
        poiInfo: selectedPoiInfo,
        mentionedUserInfo: mentionedUsers.isEmpty ? null : mentionedUsers,
      );

      // 上传图文
      setState(() {
        uploadStatus = '正在上传图文...';
      });

      // 测试CRC32计算 - 验证代码正确性
      final testFile = File(imagePaths.first);
      final testBytes = await testFile.readAsBytes();
      final testCrc32 = calculateCrc32(testBytes);
      addLog('CRC32测试 (第一张图片): $testCrc32');

      final result = await _douyinService.publishImageWorkApi(
        getCurrentCookies(),
        '', // tokens参数，这里暂时为空
        imagePaths,
        platformSetting,
        (progress, msg) {
          setState(() {
            uploadProgress = progress;
            if (msg != null) {
              uploadStatus = msg;
              addLog(msg);
            }
          });
        },
      );

      setState(() {
        uploadStatus = '上传成功';
        publishResult = '发布ID: ${result.publishId}\n链接: ${result.shareLink}';
      });

      addLog('上传成功: ID ${result.publishId}');
    } catch (e) {
      addLog('上传失败: $e');
      setState(() {
        uploadStatus = '上传失败';
        uploadDetailStatus = '$e';
      });
    }
  }

  // 选择封面图片
  Future<void> pickCoverImage() async {
    try {
      // 使用wechat_assets_picker选择图片
      final List<AssetEntity>? assets = await AssetPicker.pickAssets(
        context,
        pickerConfig: const AssetPickerConfig(
          maxAssets: 1, // 只选一张图片
          requestType: RequestType.image,
        ),
      );

      if (assets == null || assets.isEmpty) {
        addLog('未选择封面图片');
        return;
      }

      final imageAsset = assets.first;
      final imageFile = await imageAsset.file;

      if (imageFile == null) {
        addLog('获取封面文件失败');
        return;
      }

      // 获取文件大小
      final fileSizeBytes = await imageFile.length();

      setState(() {
        selectedCoverFile = imageFile;
        selectedCoverName = path.basename(imageFile.path);
      });

      addLog('选择封面: ${path.basename(imageFile.path)}');
      addLog('封面大小: ${_formatFileSize(fileSizeBytes)}');
    } catch (e) {
      addLog('选择封面失败: $e');
    }
  }

  // 选择并上传视频
  Future<void> pickAndUploadVideo() async {
    setState(() {
      uploadStatus = null;
      uploadDetailStatus = null;
      publishResult = null;
      uploadProgress = 0;
      fileSize = null;
      uploadLogs.clear();
    });

    try {
      // 使用wechat_assets_picker选择视频
      final List<AssetEntity>? assets = await AssetPicker.pickAssets(
        context,
        pickerConfig: const AssetPickerConfig(
          maxAssets: 1, // 只选一个视频
          requestType: RequestType.video,
        ),
      );

      if (assets == null || assets.isEmpty) {
        setState(() {
          uploadStatus = '未选择任何视频';
        });
        return;
      }

      final videoAsset = assets.first;
      final videoFile = await videoAsset.file;

      if (videoFile == null) {
        setState(() {
          uploadStatus = '获取视频文件失败';
        });
        return;
      }

      // 获取文件大小
      final fileSizeBytes = await videoFile.length();
      final videoDuration = videoAsset.videoDuration;

      setState(() {
        selectedFileName = path.basename(videoFile.path);
        fileSize = _formatFileSize(fileSizeBytes);
        uploadStatus = '准备上传视频 (${_formatDuration(videoDuration)})';
      });

      addLog('选择视频: ${path.basename(videoFile.path)}');
      addLog('视频大小: ${_formatFileSize(fileSizeBytes)}');
      addLog('视频时长: ${_formatDuration(videoDuration)}');

      // 检查文件大小
      if (fileSizeBytes > 300 * 1024 * 1024) {
        // 大于300MB
        setState(() {
          uploadStatus = '文件过大';
          uploadDetailStatus =
              '文件大小: ${_formatFileSize(fileSizeBytes)}, 建议小于300MB';
        });
        return;
      }

      // 如果没有选择封面，则自动获取视频缩略图作为封面
      File? coverFile = selectedCoverFile;
      if (coverFile == null) {
        addLog('使用视频缩略图作为封面');
        coverFile = await getVideoThumbnail(videoAsset);
        if (coverFile == null) {
          setState(() {
            uploadStatus = '获取视频封面失败';
          });
          return;
        }
        addLog('生成封面缩略图成功');
      } else {
        addLog('使用已选择的封面图片');
      }

      // 设置平台参数
      final platformSetting = DouyinPlatformSettingType(
        title: titleController.text,
        cover: coverFile.path,
        visibilityType: visibilityType,
        proxyIp: proxyIp,
        userDeclare: selectedDeclaration != null ? 
            DouyinDeclaration.values.firstWhere(
              (e) => e.value == selectedDeclaration!.value,
              orElse: () => DouyinDeclaration.selfShoot
            ) : null,
        caption: captionController.text.isEmpty ? null : captionController.text,
        hotSentence: hotSentenceController.text.isEmpty ? null : hotSentenceController.text,
        topics: topicsController.text.isEmpty ? null : topicsController.text.split(',').map((e) => e.trim()).toList(),
        mixInfo: selectedMix,
        activity: selectedActivity != null ? [selectedActivity!] : null,
        poiInfo: selectedPoiInfo,
        mentionedUserInfo: mentionedUsers.isEmpty ? null : mentionedUsers,
      );

      // 上传视频
      setState(() {
        uploadStatus = '正在上传视频...';
        uploadProgress = 0;
      });

      // 测试CRC32计算 - 验证代码正确性
      final testBytes = await File(videoFile.path).openRead(0, 1024).toList();
      final testBytesList = testBytes.expand((x) => x).toList();
      final testCrc32 = calculateCrc32(testBytesList);
      addLog('CRC32测试 (视频前1KB): $testCrc32');

      final result = await _douyinService.publishVideoWorkApi(
        getCurrentCookies(),
        '', // tokens参数，这里暂时为空
        videoFile.path,
        platformSetting,
        (progress, msg) {
          setState(() {
            uploadProgress = progress;
            if (msg != null) {
              uploadStatus = msg;
              addLog(msg);
            }
          });
        },
      );

      setState(() {
        uploadStatus = '上传成功';
        publishResult = '发布ID: ${result.publishId}\n链接: ${result.shareLink}';
      });

      addLog('上传成功: ID ${result.publishId}');
    } catch (e) {
      addLog('上传失败: $e');
      setState(() {
        uploadStatus = '上传失败';
        uploadDetailStatus = '$e';
      });
    }
  }

  // 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }

  // 格式化视频时长
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return '${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds';
  }

  // 获取视频缩略图
  Future<File?> getVideoThumbnail(AssetEntity videoAsset) async {
    try {
      // 生成缩略图
      final thumbnail = await videoAsset.thumbnailData;
      if (thumbnail == null) {
        return null;
      }

      // 保存缩略图到临时文件
      final tempDir = await getTemporaryDirectory();
      final thumbnailFile = File(
        '${tempDir.path}/thumbnail_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      await thumbnailFile.writeAsBytes(thumbnail);

      return thumbnailFile;
    } catch (e) {
      LoggerUtil.i('获取视频缩略图失败: $e');
      return null;
    }
  }

  // 测试推荐热点API
  Future<void> _testRecommendHotspotApi() async {
    addLog('=== 开始测试推荐热点API ===');
    try {
      addLog('调用推荐热点API...');
      final response = await _douyinService.getHotspotData(getCurrentCookies()); // 空query获取推荐
      
      addLog('推荐热点API调用成功');
      addLog('状态码: ${response.statusCode}');
      addLog('热点句子数量: ${response.sentences?.length ?? 0}');
      
      if (response.sentences?.isNotEmpty == true) {
        for (int i = 0; i < response.sentences!.length && i < 5; i++) {
          final sentence = response.sentences![i];
          addLog("推荐热点${i+1}: ${sentence.word ?? '未知'} (热度: ${sentence.hotValue ?? 0})");
        }
      }
      
    } catch (e) {
      addLog('测试推荐热点API失败: $e');
    }
    addLog('=== 推荐热点API测试结束 ===');
  }

  // 测试搜索热点API
  Future<void> _testSearchHotspotApi() async {
    addLog('=== 开始测试搜索热点API ===');
    try {
      const searchKeyword = '美食'; // 使用固定关键词测试
      addLog('搜索关键词: $searchKeyword');
      addLog('调用搜索热点API...');
      
      final response = await _douyinService.getHotspotData(getCurrentCookies(), query: searchKeyword);
      
      addLog('搜索热点API调用成功');
      addLog('状态码: ${response.statusCode}');
      addLog('热点句子数量: ${response.sentences?.length ?? 0}');
      
      if (response.sentences?.isNotEmpty == true) {
        for (int i = 0; i < response.sentences!.length && i < 5; i++) {
          final sentence = response.sentences![i];
          addLog("搜索热点${i+1}: ${sentence.word ?? '未知'} (热度: ${sentence.hotValue ?? 0})");
        }
      }
      
    } catch (e) {
      addLog('测试搜索热点API失败: $e');
    }
    addLog('=== 搜索热点API测试结束 ===');
  }

  // 测试活动API
  Future<void> _testActivityApi() async {
    addLog('=== 开始测试活动API ===');
    try {
      // 使用服务方法测试
      addLog('正在调用活动API...');
      await _loadActivityList();
      
    } catch (e) {
      addLog('测试活动API失败: $e');
    }
    addLog('=== 活动API测试结束 ===');
  }

  // 创建测试活动数据
  void _createTestActivityData() {
    setState(() {
      activityList = [
        Activity(value: 'test1', label: '测试活动1'),
        Activity(value: 'test2', label: '测试活动2'),
        Activity(value: 'test3', label: '我的创作活动'),
      ];
    });
    addLog('创建了 ${activityList.length} 个测试活动');
  }

  @override
  Widget build(BuildContext context) {
    return  SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cookie 设置卡片
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.cookie, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('Cookie 设置', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      Spacer(),
                    ],
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: cookieController,
                    decoration: InputDecoration(
                      labelText: '抖音 Cookie（可选）',
                      hintText: '粘贴有效的抖音 Cookie，为空时使用代码中的默认值',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.security),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (cookieController.text.trim().isNotEmpty)
                            IconButton(
                              icon: Icon(
                                _validateInputCookie(cookieController.text) == null
                                  ? Icons.check_circle : Icons.error,
                                color: _validateInputCookie(cookieController.text) == null
                                  ? Colors.green : Colors.red,
                              ),
                              onPressed: () {
                                final errorMsg = _validateInputCookie(cookieController.text);
                                if (errorMsg != null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(errorMsg),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('Cookie格式验证通过'),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                }
                              },
                            ),
                          IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              cookieController.clear();
                              setState(() {}); // 刷新状态显示
                            },
                          ),
                        ],
                      ),
                      errorText: _validateInputCookie(cookieController.text),
                    ),
                    maxLines: 3,
                    onChanged: (value) {
                      setState(() {}); // 刷新状态显示
                    },
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          cookieController.text.trim().isNotEmpty
                            ? (_validateInputCookie(cookieController.text) == null
                                ? '使用输入的 Cookie'
                                : '使用默认 Cookie')
                            : '使用代码中的默认 Cookie',
                          style: TextStyle(
                            fontSize: 12,
                            color: cookieController.text.trim().isNotEmpty
                              ? (_validateInputCookie(cookieController.text) == null ? Colors.green : Colors.red)
                              : Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 10),
          
          // 用户信息
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('抖音账号', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  const SizedBox(height: 10),
                  if (isLoading)
                    const Center(child: CircularProgressIndicator())
                  else if (errorMessage != null)
                    Text('错误: $errorMessage', style: const TextStyle(color: Colors.red))
                  else if (userInfo != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              if (userInfo!.avatar.isNotEmpty)
                                ClipOval(
                                  child: Image.network(
                                    userInfo!.avatar,
                                    width: 50,
                                    height: 50,
                                    fit: BoxFit.cover,
                                    errorBuilder: (ctx, error, _) => const Icon(Icons.person, size: 50),
                                  ),
                                )
                              else
                                const Icon(Icons.person, size: 50),
                              const SizedBox(width: 15),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(userInfo!.nickname, style: const TextStyle(fontWeight: FontWeight.bold)),
                                  Text('ID: ${userInfo!.authorId}'),
                                  Text('粉丝: ${userInfo!.fansCount}'),
                                ],
                              ),
                            ],
                          ),
                        ],
                      )
                    else
                      const Text('未登录'),
                  const SizedBox(height: 10),
                  ElevatedButton(
                    onPressed: getUserInfo,
                    child: const Text('获取用户信息'),
                  ),
                  const SizedBox(height: 10),
                  
                  // API测试按钮
                  Wrap(
                    spacing: 8.0,
                    children: [
                      ElevatedButton(
                        onPressed: _testRecommendHotspotApi,
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                        child: const Text('测试推荐热点API'),
                      ),
                      ElevatedButton(
                        onPressed: _testSearchHotspotApi,
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                        child: const Text('测试搜索热点API'),
                      ),
                      ElevatedButton(
                        onPressed: _testActivityApi,
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
                        child: const Text('测试活动API'),
                      ),
                      ElevatedButton(
                        onPressed: _createTestActivityData,
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                        child: const Text('测试活动数据'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
      
          // 热点数据卡片
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text('热点数据', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const Spacer(),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.auto_awesome),
                        label: const Text('推荐'),
                        onPressed: _loadHotspotData,
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.refresh),
                        label: const Text('刷新'),
                        onPressed: _loadHotspotData,
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  
                  // 搜索框
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          decoration: const InputDecoration(
                            labelText: '搜索热点',
                            border: OutlineInputBorder(),
                            hintText: '输入关键词搜索热点内容',
                            prefixIcon: Icon(Icons.search),
                          ),
                          onSubmitted: (value) {
                            if (value.trim().isNotEmpty) {
                              _searchHotspotData(value.trim());
                            } else {
                              _loadHotspotData(); // 空搜索时加载推荐热点
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          addLog('清空搜索，重新加载推荐热点');
                          _loadHotspotData(); // 重新加载推荐热点
                        },
                        tooltip: '清空搜索，获取推荐热点',
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  
                  // 热点列表
                  if (isLoadingHotspot)
                    const Center(child: CircularProgressIndicator())
                  else if (hotspotList.isEmpty)
                    const Text('暂无热点数据', style: TextStyle(color: Colors.grey))
                  else
                    SizedBox(
                      height: 200,
                      child: ListView.builder(
                        itemCount: hotspotList.length,
                        itemBuilder: (context, index) {
                          final hotspot = hotspotList[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              title: Text(
                                hotspot.title,
                                style: const TextStyle(fontWeight: FontWeight.bold),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (hotspot.description != null)
                                    Text(
                                      hotspot.description!,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  Row(
                                    children: [
                                      const Icon(Icons.whatshot, size: 16, color: Colors.orange),
                                      const SizedBox(width: 4),
                                      Text(
                                        '热度: ${hotspot.hotValue}',
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                      if (hotspot.category != null) ...[
                                        const SizedBox(width: 10),
                                        Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.shade100,
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: Text(
                                            hotspot.category!,
                                            style: const TextStyle(fontSize: 10, color: Colors.blue),
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ],
                              ),
                              trailing: hotspot.trendType != null
                                  ? Icon(
                                      hotspot.trendType == '上升' 
                                          ? Icons.trending_up 
                                          : hotspot.trendType == '下降'
                                              ? Icons.trending_down
                                              : Icons.trending_flat,
                                      color: hotspot.trendType == '上升' 
                                          ? Colors.green 
                                          : hotspot.trendType == '下降'
                                              ? Colors.red
                                              : Colors.grey,
                                    )
                                  : null,
                              onTap: () {
                                // 点击热点时，将其内容添加到热点输入框
                                hotSentenceController.text = hotspot.title;
                                addLog('选择热点: ${hotspot.title}');
                              },
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
      
          // 上传设置
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('上传设置', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                  const SizedBox(height: 15),
                  
                  // 标题
                  TextField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: '标题 *',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 15),
                  
                  // 描述
                  TextField(
                    controller: captionController,
                    decoration: const InputDecoration(
                      labelText: '描述',
                      border: OutlineInputBorder(),
                      hintText: '视频描述内容',
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 15),
                  
                  // 话题
                  TextField(
                    controller: topicsController,
                    decoration: const InputDecoration(
                      labelText: '话题',
                      border: OutlineInputBorder(),
                      hintText: '用逗号分隔多个话题，例如：抖音,测试',
                    ),
                  ),
                  const SizedBox(height: 10),
                  
                  // 话题搜索
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: topicSearchController,
                          decoration: const InputDecoration(
                            labelText: '搜索话题',
                            border: OutlineInputBorder(),
                            hintText: '输入关键词搜索相关话题',
                            prefixIcon: Icon(Icons.tag),
                          ),
                          onSubmitted: (value) {
                            if (value.trim().isNotEmpty) {
                              _searchTopics(value.trim());
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: () {
                          final keyword = topicSearchController.text.trim();
                          if (keyword.isNotEmpty) {
                            _searchTopics(keyword);
                          }
                        },
                        child: const Text('搜索'),
                      ),
                      const SizedBox(width: 10),
                      ElevatedButton(
                        onPressed: _testTopicsApi,
                        style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
                        child: const Text('测试API', style: TextStyle(color: Colors.white)),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  
                  // 话题建议列表
                  if (isLoadingTopics)
                    const Center(child: CircularProgressIndicator())
                  else if (topicSuggestions.isNotEmpty)
                    SizedBox(
                      height: 150,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '话题建议 (${topicSuggestions.length})',
                            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                          ),
                          const SizedBox(height: 8),
                          Expanded(
                            child: ListView.builder(
                              itemCount: topicSuggestions.length,
                              itemBuilder: (context, index) {
                                final topic = topicSuggestions[index];
                                return Card(
                                  margin: const EdgeInsets.only(bottom: 4),
                                  child: ListTile(
                                    dense: true,
                                    leading: const Icon(Icons.tag, size: 16, color: Colors.blue),
                                    title: Text(
                                      topic.chaName,
                                      style: const TextStyle(fontSize: 14),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    subtitle: Text(
                                      '观看数: ${topic.viewCount}',
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                    trailing: const Icon(Icons.add, size: 16),
                                    onTap: () {
                                      _addTopicToInput(topic.chaName);
                                      // 清空话题搜索
                                      topicSearchController.clear();
                                      setState(() {
                                        topicSuggestions = [];
                                      });
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 15),
                  
                  // 热点
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: hotSentenceController,
                          decoration: const InputDecoration(
                            labelText: '热点',
                            border: OutlineInputBorder(),
                            hintText: '添加热点话题',
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.trending_up),
                        onPressed: () {
                          // 滚动到热点数据卡片
                          Scrollable.ensureVisible(
                            context,
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut,
                          );
                        },
                        tooltip: '查看热点列表',
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  
                  // 合集选择
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<MixInfo>(
                          decoration: const InputDecoration(
                            labelText: '选择合集',
                            border: OutlineInputBorder(),
                          ),
                          value: selectedMix,
                          hint: const Text('不使用合集'),
                          items: [
                            ...mixList.map((mix) => DropdownMenuItem(
                              value: mix,
                              child: Text(mix.mixName, overflow: TextOverflow.ellipsis),
                            )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedMix = value;
                              if (value != null) {
                                addLog('选择合集: ${value.mixName}');
                              }
                            });
                          },
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: _loadMixList,
                        tooltip: '刷新合集列表',
                      ),
                      if (selectedMix != null)
                        IconButton(
                          icon: const Icon(Icons.clear, color: Colors.red),
                          onPressed: () {
                            setState(() {
                              selectedMix = null;
                              addLog('清除合集选择');
                            });
                          },
                        ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  
                  // 活动选择
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<Activity>(
                          decoration: const InputDecoration(
                            labelText: '选择活动',
                            border: OutlineInputBorder(),
                          ),
                          value: selectedActivity,
                          hint: const Text('不参与活动'),
                          items: [
                            ...activityList.map((activity) => DropdownMenuItem(
                              value: activity,
                              child: Text(activity.label, overflow: TextOverflow.ellipsis),
                            )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedActivity = value;
                              if (value != null) {
                                addLog('选择活动: ${value.label}');
                              }
                            });
                          },
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.refresh),
                        onPressed: _loadActivityList,
                        tooltip: '刷新活动列表',
                      ),
                      if (selectedActivity != null)
                        IconButton(
                          icon: const Icon(Icons.clear, color: Colors.red),
                          onPressed: () {
                            setState(() {
                              selectedActivity = null;
                              addLog('清除活动选择');
                            });
                          },
                        ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  
                  // 活动状态显示
                  if (activityList.isEmpty && !isLoading)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        border: Border.all(color: Colors.orange.shade200),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.info, color: Colors.orange, size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '当前无可用活动，可能是账号没有参与资格或API返回为空',
                              style: TextStyle(fontSize: 12, color: Colors.orange.shade700),
                            ),
                          ),
                        ],
                      ),
                    )
                  else if (activityList.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        border: Border.all(color: Colors.green.shade200),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '已找到 ${activityList.length} 个可用活动',
                        style: TextStyle(fontSize: 12, color: Colors.green.shade700),
                      ),
                    ),
                  
                  // 位置信息
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: poiNameController,
                          decoration: const InputDecoration(
                            labelText: '位置名称',
                            border: OutlineInputBorder(),
                            hintText: '例如：北京市朝阳区',
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      IconButton(
                        icon: const Icon(Icons.add_location),
                        onPressed: _setPoiInfo,
                        tooltip: '设置位置',
                      ),
                      if (selectedPoiInfo != null)
                        IconButton(
                          icon: const Icon(Icons.clear, color: Colors.red),
                          onPressed: _clearPoiInfo,
                          tooltip: '清除位置',
                        ),
                    ],
                  ),
                  if (selectedPoiInfo != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        '已设置位置: ${selectedPoiInfo!.poiName}',
                        style: const TextStyle(color: Colors.blue),
                      ),
                    ),
                  const SizedBox(height: 15),
                  
                  // @用户
                  const Text('@用户:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 5),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: mentionedNameController,
                          decoration: const InputDecoration(
                            labelText: '用户昵称',
                            hintText: '要@的用户昵称',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 10),
                      // Expanded(
                      //   child: TextField(
                      //     controller: mentionedUidController,
                      //     decoration: InputDecoration(
                      //       labelText: '用户ID',
                      //       hintText: '用户的唯一标识',
                      //       border: OutlineInputBorder(),
                      //     ),
                      //   ),
                      // ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: _addMentionedUser,
                        tooltip: '添加@用户',
                      ),
                    ],
                  ),
                  if (mentionedUsers.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Wrap(
                        spacing: 8.0,
                        runSpacing: 4.0,
                        children: mentionedUsers.asMap().entries.map((entry) {
                          final index = entry.key;
                          final user = entry.value;
                          return Chip(
                            label: Text('@${user.nickName}'),
                            deleteIcon: const Icon(Icons.clear, size: 18),
                            onDeleted: () => _removeMentionedUser(index),
                          );
                        }).toList(),
                      ),
                    ),
                  const SizedBox(height: 15),
                  
                  // 可见性设置
                  const Text('谁可见:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 5),
                  Wrap(
                    spacing: 10.0,
                    children: [
                      ChoiceChip(
                        label: const Text('公开'),
                        selected: visibilityType == 0,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              visibilityType = 0;
                              addLog('设置可见性: 公开');
                            });
                          }
                        },
                      ),
                      ChoiceChip(
                        label: const Text('好友可见'),
                        selected: visibilityType == 1,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              visibilityType = 1;
                              addLog('设置可见性: 好友可见');
                            });
                          }
                        },
                      ),
                      ChoiceChip(
                        label: const Text('私密'),
                        selected: visibilityType == 2,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              visibilityType = 2;
                              addLog('设置可见性: 私密');
                            });
                          }
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  
                  // 自主声明选择
                  const Text('自主声明:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 5),
                  Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    children: DouyinDeclaration.values.map((declaration) {
                      return FilterChip(
                        label: Text(declaration.label),
                        selected: selectedDeclaration == declaration,
                        onSelected: (selected) {
                          setState(() {
                            selectedDeclaration = selected ? declaration : null;
                            if (selected) {
                              addLog('选择声明: ${declaration.label}');
                            } else {
                              addLog('取消声明选择');
                            }
                          });
                        },
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 15),
                  
                  // 代理设置
                  TextField(
                    decoration: const InputDecoration(
                      labelText: '代理IP (留空则不使用)',
                      border: OutlineInputBorder(),
                      hintText: '格式: 127.0.0.1:8080',
                    ),
                    onChanged: (value) => setState(() => proxyIp = value),
                  ),
                ],
              ),
            ),
          ),
      
          const SizedBox(height: 16),
      
          // 上传部分
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '内容上传',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
      
                  // 添加封面选择区域
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          icon: const Icon(Icons.image),
                          label: Text(selectedCoverName ?? '选择封面图片(可选)'),
                          onPressed: pickCoverImage,
                        ),
                      ),
                      if (selectedCoverFile != null)
                        IconButton(
                          icon: const Icon(Icons.clear, color: Colors.red),
                          onPressed: () {
                            setState(() {
                              selectedCoverFile = null;
                              selectedCoverName = null;
                            });
                            addLog('已清除选择的封面');
                          },
                        ),
                    ],
                  ),
      
                  if (selectedCoverFile != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.green,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              '已选择封面: $selectedCoverName',
                              style: const TextStyle(fontSize: 12),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
      
                  const SizedBox(height: 16),
      
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        icon: const Icon(Icons.image),
                        label: const Text('上传图片'),
                        onPressed:
                        userInfo != null ? pickAndUploadImages : null,
                      ),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.video_library),
                        label: const Text('上传视频'),
                        onPressed: userInfo != null ? pickAndUploadVideo : null,
                      ),
                    ],
                  ),
      
                  const SizedBox(height: 16),
      
                  if (selectedFileName != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('已选择: $selectedFileName'),
                        if (fileSize != null) Text('文件大小: $fileSize'),
                      ],
                    ),
      
                  const SizedBox(height: 8),
      
                  if (uploadStatus != null)
                    Text(
                      '状态: $uploadStatus',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color:
                        uploadStatus!.contains('失败')
                            ? Colors.red
                            : Colors.black,
                      ),
                    ),
      
                  if (uploadDetailStatus != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        uploadDetailStatus!,
                        style: const TextStyle(color: Colors.red, fontSize: 12),
                        maxLines: 5,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
      
                  if (uploadProgress > 0)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 8),
                        LinearProgressIndicator(value: uploadProgress / 100),
                        const SizedBox(height: 4),
                        Text('进度: $uploadProgress%'),
                      ],
                    ),
      
                  if (publishResult != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '发布结果:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text(publishResult!),
                        ],
                      ),
                    ),
      
                  // 添加详细日志区域
                  if (uploadLogs.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '上传日志:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            height: 200,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: ListView.builder(
                              controller: _logsScrollController,
                              padding: const EdgeInsets.all(8),
                              itemCount: uploadLogs.length,
                              itemBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 4),
                                  child: Text(
                                    uploadLogs[index],
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontFamily: 'monospace',
                                      color:
                                      uploadLogs[index].contains('失败') ||
                                          uploadLogs[index].contains(
                                            '错误',
                                          )
                                          ? Colors.red
                                          : Colors.black87,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
