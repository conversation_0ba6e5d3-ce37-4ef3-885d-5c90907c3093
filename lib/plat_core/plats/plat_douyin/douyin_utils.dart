import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';

import 'package:aitoearn_app/config/logger.dart';
import 'package:crypto/crypto.dart';
import 'package:image/image.dart' as image_lib;
import 'package:uuid/uuid.dart';

String jsonToQueryString(Map<String, dynamic> params) {
  return params.entries
      .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value.toString())}')
      .join('&');
}

String generateRandomString(int length) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  final random = Random();
  return String.fromCharCodes(
    List.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
  );
}

// 计算字符串的CRC32值 - 使用crclib库的准确实现
String calculateCrc32(List<int> bytes) {
  try {
    // 自定义CRC32计算实现
    final crc32Table = _createCrc32Table();
    int crc = 0xFFFFFFFF;
    
    for (final byte in bytes) {
      crc = (crc >> 8) ^ crc32Table[(crc ^ byte) & 0xFF];
    }
    
    // 取反并格式化为16进制
    crc = ~crc & 0xFFFFFFFF;
    return crc.toRadixString(16).padLeft(8, '0');
  } catch (e) {
    LoggerUtil.e('CRC32计算错误: $e');
    // 返回默认值，避免上传失败
    return '00000000';
  }
}

// 文件分块信息
class FilePartInfo {
  final List<int> blockInfo;
  final int fileSize;
  
  FilePartInfo({required this.blockInfo, required this.fileSize});
  
  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'fileSize': fileSize,
      'blockInfo': blockInfo,
    };
  }
  
  // 从Map创建
  factory FilePartInfo.fromMap(Map<String, dynamic> map) {
    return FilePartInfo(
      fileSize: map['fileSize'] as int,
      blockInfo: List<int>.from(map['blockInfo']),
    );
  }
}

/// 获取文件分块信息
Future<Map<String, dynamic>> getFilePartInfo(File file, int blockSize) async {
  final fileSize = await file.length();
  final blockCount = (fileSize / blockSize).ceil();
  
  final blockInfo = <int>[];
  for (int i = 0; i < blockCount; i++) {
    final offset = i * blockSize;
    blockInfo.add(offset + ((i < blockCount - 1) ? blockSize : (fileSize - offset)));
  }
  
  return {
    'fileSize': fileSize,
    'blockInfo': blockInfo,
  };
}

/// 获取文件的部分内容
Future<List<int>> getFilePartContent(File file, int start, int end) async {
  final raf = await file.open(mode: FileMode.read);
  try {
    raf.setPositionSync(start);
    final buffer = Uint8List(end - start + 1);
    final bytesRead = raf.readIntoSync(buffer);
    return buffer.sublist(0, bytesRead);
  } finally {
    await raf.close();
  }
}

// 简化版文件读取 - 用于小文件片段
Future<List<int>> getSmallFilePartContent(File file, int start, int end) async {
  final randomAccessFile = await file.open(mode: FileMode.read);
  try {
    await randomAccessFile.setPosition(start);
    final length = end - start + 1;
    final buffer = await randomAccessFile.read(length);
    return buffer;
  } finally {
    await randomAccessFile.close();
  }
}

// 生成UUID
String generateUuid() {
  return const Uuid().v4();
}

// 将Cookie字符串转换为Map
Map<String, String> cookieStringToMap(String cookieString) {
  final Map<String, String> result = {};
  
  final cookies = cookieString.split(';');
  for (var cookie in cookies) {
    cookie = cookie.trim();
    if (cookie.isEmpty) continue;
    
    final parts = cookie.split('=');
    if (parts.length >= 2) {
      result[parts[0]] = parts.sublist(1).join('=');
    }
  }
  
  return result;
}

// 将Cookie映射转换为Cookie字符串
String mapToCookieString(Map<String, String> cookieMap) {
  return cookieMap.entries.map((e) => '${e.key}=${e.value}').join('; ');
}

// AWS签名相关工具
class AwsSignatureUtil {
  // 生成签名密钥
  static List<int> getSigningKey(String secretKey, String date, String region, String service) {
    final kDate = Hmac(sha256, utf8.encode('AWS4$secretKey')).convert(utf8.encode(date)).bytes;
    final kRegion = Hmac(sha256, kDate).convert(utf8.encode(region)).bytes;
    final kService = Hmac(sha256, kRegion).convert(utf8.encode(service)).bytes;
    final kSigning = Hmac(sha256, kService).convert(utf8.encode('aws4_request')).bytes;
    return kSigning;
  }
  
  // 计算签名
  static String calculateSignature(
    List<int> signingKey, 
    String stringToSign,
  ) {
    final signature = Hmac(sha256, signingKey).convert(utf8.encode(stringToSign));
    return signature.toString();
  }
}

// 获取图片信息
Future<Map<String, dynamic>> getImageBaseInfo(String filePath) async {
  try {
    final file = File(filePath);
    final bytes = await file.readAsBytes();
    final image = image_lib.decodeImage(bytes);
    
    if (image == null) {
      return {'width': 0, 'height': 0};
    }
    
    return {
      'width': image.width,
      'height': image.height,
    };
  } catch (e) {
    LoggerUtil.e('获取图片信息失败: $e');
    return {'width': 0, 'height': 0};
  }
}

// 创建CRC32查找表
List<int> _createCrc32Table() {
  final table = List<int>.filled(256, 0);
  
  for (int i = 0; i < 256; i++) {
    int c = i;
    for (int j = 0; j < 8; j++) {
      c = (c & 1) != 0 ? 0xEDB88320 ^ (c >> 1) : c >> 1;
    }
    table[i] = c;
  }
  
  return table;
}
