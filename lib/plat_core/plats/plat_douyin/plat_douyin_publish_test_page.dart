import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/lable_value_modles.dart';
import 'package:aitoearn_app/plat_core/enum/pub_params_enum.dart';
import 'package:aitoearn_app/plat_core/models/location_model.dart';
import 'package:aitoearn_app/plat_core/models/plat_form_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/plat_douyin.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;

class PlatDouyinPublishTestPage extends StatefulWidget {
  const PlatDouyinPublishTestPage({super.key});

  @override
  State<PlatDouyinPublishTestPage> createState() => _PlatDouyinPublishTestPageState();
}

class _PlatDouyinPublishTestPageState extends State<PlatDouyinPublishTestPage> {
  late PlatDouyin _platDouyin;
  
  // 控制器
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descController = TextEditingController();
  final TextEditingController _topicsController = TextEditingController();
  final TextEditingController _cookiesController = TextEditingController();
  final TextEditingController _mentionedController = TextEditingController();
  final TextEditingController _locationController = TextEditingController();
  
  // 文件状态
  File? _selectedVideo;
  File? _selectedCover;
  List<File> _selectedImages = [];
  
  // UI状态
  VisibleTypeEnum _selectedVisibility = VisibleTypeEnum.public;
  bool _isPublishing = false;
  double _publishProgress = 0.0;
  String _publishMessage = '';
  
  // 发布结果
  String _lastPublishResult = '';
  
  // 日志
  final List<String> _logs = [];
  final ScrollController _logsScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initializePlatDouyin();
    _addInitialContent();
  }

  void _initializePlatDouyin() {
    // 创建模拟的 PlatformModel
    final platformModel = PlatformModel(
      cookieList: [], // 这里需要实际的 Cookie 列表
    );
    
    _platDouyin = PlatDouyin(platformModel);
    _addLog('PlatDouyin 初始化完成');
  }

  void _addInitialContent() {
    _titleController.text = '抖音测试内容 - ${DateTime.now().toString().substring(0, 19)}';
    _descController.text = '这是通过 PlatDouyin 类发布的测试内容';
    _topicsController.text = '测试,抖音,发布';
    _cookiesController.text = '请输入有效的抖音 Cookie';
  }

  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _logs.add('[$timestamp] $message');
    });
    
    // 自动滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_logsScrollController.hasClients) {
        _logsScrollController.animateTo(
          _logsScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
    
    LoggerUtil.i(message);
  }

  // 选择视频文件
  Future<void> _selectVideo() async {
    try {
      final picker = ImagePicker();
      final video = await picker.pickVideo(source: ImageSource.gallery);

      if (video != null) {
        final file = File(video.path);
        setState(() {
          _selectedVideo = file;
        });
        _addLog('选择视频: ${path.basename(file.path)}');
        
        // 如果没有封面，自动选择一个默认封面
        if (_selectedCover == null) {
          _addLog('提示: 请选择封面图片');
        }
      }
    } catch (e) {
      _addLog('选择视频失败: $e');
    }
  }

  // 选择封面图片
  Future<void> _selectCover() async {
    try {
      final picker = ImagePicker();
      final image = await picker.pickImage(source: ImageSource.gallery);
      
      if (image != null) {
        final file = File(image.path);
        setState(() {
          _selectedCover = file;
        });
        _addLog('选择封面: ${path.basename(file.path)}');
      }
    } catch (e) {
      _addLog('选择封面失败: $e');
    }
  }

  // 选择图片列表
  Future<void> _selectImages() async {
    try {
      final picker = ImagePicker();
      final images = await picker.pickMultiImage();
      
      if (images.isNotEmpty) {
        final files = images.map((img) => File(img.path)).toList();
        setState(() {
          _selectedImages = files;
          // 如果没有封面，使用第一张图片作为封面
          if (_selectedCover == null && files.isNotEmpty) {
            _selectedCover = files.first;
          }
        });
        _addLog('选择图片: ${files.length} 张');
        for (int i = 0; i < files.length; i++) {
          _addLog('图片${i+1}: ${path.basename(files[i].path)}');
        }
      }
    } catch (e) {
      _addLog('选择图片失败: $e');
    }
  }

  // 发布视频
  Future<void> _publishVideo() async {
    if (_selectedVideo == null) {
      _addLog('❌ 请先选择视频文件');
      return;
    }
    
    if (_selectedCover == null) {
      _addLog('❌ 请先选择封面图片');
      return;
    }

    if (_cookiesController.text.trim().isEmpty) {
      _addLog('❌ 请输入有效的 Cookie');
      return;
    }

    setState(() {
      _isPublishing = true;
      _publishProgress = 0.0;
      _publishMessage = '准备发布视频...';
      _lastPublishResult = '';
    });

    try {
      _addLog('🚀 开始发布视频...');
      
      // 更新 Cookie
      _updateCookies();
      
      // 构建发布参数 - 视频发布
      final publishParams = PublishParamsModel(
        _selectedCover!,
        title: _titleController.text.trim(),
        desc: _descController.text.trim(),
        topics: _getTopicsList(),
        video: _selectedVideo!, // 确保video不为null
        visibleType: _selectedVisibility,
        location: _getLocationModel(),
        mentionedUserInfo: _getMentionedUsers(),
      );
      
      _addLog('📄 发布参数构建完成');
      _addLog('标题: ${publishParams.title}');
      _addLog('描述: ${publishParams.desc}');
      _addLog('话题: ${publishParams.topics}');
      _addLog('可见性: ${_selectedVisibility.label}');
      
      // 调用 PlatDouyin 的发布视频方法
      final dynamic result = _platDouyin.publishVideo(publishParams);
      
      // 检查是否是Future，如果是则等待结果
      if (result is Future) {
        final actualResult = await result as PublishResponseModel;
        _handlePublishResult(actualResult, '视频');
      } else {
        _handlePublishResult(result as PublishResponseModel, '视频');
      }
      
    } catch (e) {
      _addLog('❌ 发布视频失败: $e');
      setState(() {
        _lastPublishResult = '发布失败: $e';
      });
    } finally {
      setState(() {
        _isPublishing = false;
        _publishProgress = 0.0;
        _publishMessage = '';
      });
    }
  }

  // 发布图文
  Future<void> _publishImages() async {
    if (_selectedImages.isEmpty) {
      _addLog('❌ 请先选择图片');
      return;
    }
    
    if (_selectedCover == null) {
      _addLog('❌ 请先选择封面图片');
      return;
    }

    if (_cookiesController.text.trim().isEmpty) {
      _addLog('❌ 请输入有效的 Cookie');
      return;
    }

    setState(() {
      _isPublishing = true;
      _publishProgress = 0.0;
      _publishMessage = '准备发布图文...';
      _lastPublishResult = '';
    });

    try {
      _addLog('🚀 开始发布图文...');
      
      // 更新 Cookie
      _updateCookies();
      
      // 构建发布参数 - 图文发布，使用第一张图片作为video参数以满足构造函数要求
      final publishParams = PublishParamsModel(
        _selectedCover!,
        title: _titleController.text.trim(),
        desc: _descController.text.trim(),
        topics: _getTopicsList(),
        video: _selectedCover!, // 临时使用封面文件满足构造函数要求
        images: _selectedImages,
        visibleType: _selectedVisibility,
        location: _getLocationModel(),
        mentionedUserInfo: _getMentionedUsers(),
      );
      
      // 图文发布时清空video字段
      publishParams.video = null;
      
      _addLog('📄 发布参数构建完成');
      _addLog('标题: ${publishParams.title}');
      _addLog('描述: ${publishParams.desc}');
      _addLog('话题: ${publishParams.topics}');
      _addLog('图片数量: ${_selectedImages.length}');
      _addLog('可见性: ${_selectedVisibility.label}');
      
      // 调用 PlatDouyin 的发布图文方法
      final dynamic result = _platDouyin.publishImgText(publishParams);
      
      // 检查是否是Future，如果是则等待结果
      if (result is Future) {
        final actualResult = await result as PublishResponseModel;
        _handlePublishResult(actualResult, '图文');
      } else {
        _handlePublishResult(result as PublishResponseModel, '图文');
      }
      
    } catch (e) {
      _addLog('❌ 发布图文失败: $e');
      setState(() {
        _lastPublishResult = '发布失败: $e';
      });
    } finally {
      setState(() {
        _isPublishing = false;
        _publishProgress = 0.0;
        _publishMessage = '';
      });
    }
  }

  // 处理发布结果
  void _handlePublishResult(PublishResponseModel result, String type) {
    if (result.isSuccess) {
      _addLog('✅ $type发布成功!');
      _addLog('作品ID: ${result.dataId}');
      _addLog('分享链接: ${result.workLink}');
      setState(() {
        _lastPublishResult = '$type发布成功!\n作品ID: ${result.dataId}\n分享链接: ${result.workLink}';
      });
    } else {
      _addLog('❌ $type发布失败: ${result.failMsg}');
      setState(() {
        _lastPublishResult = '$type发布失败: ${result.failMsg}';
      });
    }
  }

  // 更新 Cookie
  void _updateCookies() {
    final cookieStr = _cookiesController.text.trim();
    if (cookieStr.isNotEmpty) {
      // 这里需要解析 Cookie 字符串并更新 PlatDouyin 的 Cookie
      // 简化处理，直接设置 cookieStr
      _platDouyin.cookieStr = cookieStr;
      _addLog('🍪 Cookie 已更新');
    }
  }

  // 获取话题列表
  List<String>? _getTopicsList() {
    final topicsText = _topicsController.text.trim();
    if (topicsText.isEmpty) return null;
    
    return topicsText.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
  }

  // 获取位置模型
  LocationModel? _getLocationModel() {
    final locationText = _locationController.text.trim();
    if (locationText.isEmpty) return null;
    
    final locationModel = LocationModel();
    locationModel.id = '123456';
    locationModel.name = locationText;
    locationModel.simpleAddress = locationText;
    locationModel.latitude = 0.0;
    locationModel.longitude = 0.0;
    locationModel.city = '测试城市';
    locationModel.poiType = null;
    
    return locationModel;
  }

  // 获取@好友列表
  List<LableValueModles>? _getMentionedUsers() {
    final mentionedText = _mentionedController.text.trim();
    if (mentionedText.isEmpty) return null;
    
    final users = mentionedText.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
    return users.map((user) {
      final model = LableValueModles();
      model.name = user;
      model.value = 'uid_$user';
      return model;
    }).toList();
  }

  // 清除选择的文件
  void _clearFiles() {
    setState(() {
      _selectedVideo = null;
      _selectedCover = null;
      _selectedImages.clear();
    });
    _addLog('🗑️ 已清除所有选择的文件');
  }

  // 清除日志
  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descController.dispose();
    _topicsController.dispose();
    _cookiesController.dispose();
    _mentionedController.dispose();
    _locationController.dispose();
    _logsScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > 768; // 大于768px使用横向布局
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('PlatDouyin 发布测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearLogs,
            tooltip: '清除日志',
          ),
        ],
      ),
      body: isLargeScreen ? _buildDesktopLayout() : _buildMobileLayout(),
    );
  }

  // 桌面端布局（横向）
  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // 左侧 - 参数设置区域
        Expanded(
          flex: 2,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(right: BorderSide(color: Colors.grey.shade300)),
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildBasicInfoSection(),
                  const SizedBox(height: 20),
                  _buildFileSelectionSection(),
                  const SizedBox(height: 20),
                  _buildAdvancedSettingsSection(),
                  const SizedBox(height: 20),
                  _buildPublishSection(),
                ],
              ),
            ),
          ),
        ),
        
        // 右侧 - 日志和结果区域
        Expanded(
          flex: 3,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildResultSection(),
                const SizedBox(height: 16),
                _buildLogSection(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 移动端布局（纵向）
  Widget _buildMobileLayout() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          // Tab栏
          Container(
            color: Colors.grey.shade100,
            child: const TabBar(
              labelColor: Colors.blue,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.blue,
              tabs: [
                Tab(icon: Icon(Icons.settings), text: '设置'),
                Tab(icon: Icon(Icons.assignment), text: '结果'),
              ],
            ),
          ),
          
          // Tab内容
          Expanded(
            child: TabBarView(
              children: [
                // 第一个Tab：参数设置
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 16),
                      _buildFileSelectionSection(),
                      const SizedBox(height: 16),
                      _buildAdvancedSettingsSection(),
                      const SizedBox(height: 16),
                      _buildPublishSection(),
                      const SizedBox(height: 32), // 底部留白
                    ],
                  ),
                ),
                
                // 第二个Tab：结果和日志
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildResultSection(),
                      const SizedBox(height: 16),
                      _buildLogSection(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('基础信息', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '标题',
                border: OutlineInputBorder(),
                hintText: '请输入作品标题',
              ),
            ),
            const SizedBox(height: 12),
            
            TextField(
              controller: _descController,
              decoration: const InputDecoration(
                labelText: '描述',
                border: OutlineInputBorder(),
                hintText: '请输入作品描述',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 12),
            
            TextField(
              controller: _topicsController,
              decoration: const InputDecoration(
                labelText: '话题',
                border: OutlineInputBorder(),
                hintText: '多个话题用逗号分隔，例如：测试,抖音',
              ),
            ),
            const SizedBox(height: 12),
            
            TextField(
              controller: _cookiesController,
              decoration: const InputDecoration(
                labelText: 'Cookie',
                border: OutlineInputBorder(),
                hintText: '请输入有效的抖音 Cookie',
              ),
              obscureText: false,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileSelectionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('文件选择', style: Theme.of(context).textTheme.titleMedium),
                TextButton(
                  onPressed: _clearFiles,
                  child: const Text('清除文件'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 视频选择
            ListTile(
              leading: const Icon(Icons.video_file, color: Colors.blue),
              title: Text(_selectedVideo == null ? '选择视频文件' : path.basename(_selectedVideo!.path)),
              subtitle: _selectedVideo == null ? const Text('发布视频时需要') : null,
              trailing: IconButton(
                icon: const Icon(Icons.folder_open),
                onPressed: _selectVideo,
              ),
              onTap: _selectVideo,
            ),
            
            // 封面选择
            ListTile(
              leading: const Icon(Icons.image, color: Colors.green),
              title: Text(_selectedCover == null ? '选择封面图片' : path.basename(_selectedCover!.path)),
              subtitle: const Text('必选项'),
              trailing: IconButton(
                icon: const Icon(Icons.folder_open),
                onPressed: _selectCover,
              ),
              onTap: _selectCover,
            ),
            
            // 图片选择
            ListTile(
              leading: const Icon(Icons.photo_library, color: Colors.orange),
              title: Text(_selectedImages.isEmpty ? '选择图片列表' : '已选择 ${_selectedImages.length} 张图片'),
              subtitle: _selectedImages.isEmpty ? const Text('发布图文时需要') : null,
              trailing: IconButton(
                icon: const Icon(Icons.folder_open),
                onPressed: _selectImages,
              ),
              onTap: _selectImages,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('高级设置', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            
            // 可见性选择
            DropdownButtonFormField<VisibleTypeEnum>(
              value: _selectedVisibility,
              decoration: const InputDecoration(
                labelText: '可见性',
                border: OutlineInputBorder(),
              ),
              items: VisibleTypeEnum.values.map((visibility) {
                return DropdownMenuItem(
                  value: visibility,
                  child: Text(visibility.label),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedVisibility = value;
                  });
                }
              },
            ),
            const SizedBox(height: 12),
            
            TextField(
              controller: _mentionedController,
              decoration: const InputDecoration(
                labelText: '@好友',
                border: OutlineInputBorder(),
                hintText: '多个好友用逗号分隔',
              ),
            ),
            const SizedBox(height: 12),
            
            TextField(
              controller: _locationController,
              decoration: const InputDecoration(
                labelText: '位置',
                border: OutlineInputBorder(),
                hintText: '请输入位置信息',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPublishSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text('发布操作', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            
            if (_isPublishing) ...[
              LinearProgressIndicator(value: _publishProgress / 100),
              const SizedBox(height: 8),
              Text(_publishMessage, style: const TextStyle(fontSize: 12)),
              const SizedBox(height: 16),
            ],
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isPublishing ? null : _publishVideo,
                    icon: const Icon(Icons.video_call),
                    label: const Text('发布视频'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isPublishing ? null : _publishImages,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('发布图文'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('发布结果', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(
                _lastPublishResult.isEmpty ? '暂无发布结果' : _lastPublishResult,
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                  color: _lastPublishResult.contains('成功') ? Colors.green.shade700 : Colors.red.shade700,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogSection() {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('日志信息', style: Theme.of(context).textTheme.titleMedium),
                  Text('共 ${_logs.length} 条', style: Theme.of(context).textTheme.bodySmall),
                ],
              ),
              const SizedBox(height: 12),
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListView.builder(
                    controller: _logsScrollController,
                    itemCount: _logs.length,
                    itemBuilder: (context, index) {
                      final log = _logs[index];
                      Color textColor = Colors.white;
                      if (log.contains('❌')) {
                        textColor = Colors.red.shade300;
                      } else if (log.contains('✅')) {
                        textColor = Colors.green.shade300;
                      } else if (log.contains('🚀')) {
                        textColor = Colors.blue.shade300;
                      } else if (log.contains('📄')) {
                        textColor = Colors.yellow.shade300;
                      }
                      
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Text(
                          log,
                          style: TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 11,
                            color: textColor,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 