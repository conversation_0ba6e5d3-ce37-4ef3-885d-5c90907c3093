import 'dart:io';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/plat_form_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/utils/dio_util/dio_util.dart';
import 'package:dio/dio.dart';

/// 所有平台的基类
class PlatBase {
  // 平台Cookie字符串
  late String cookieStr;
  // 平台Cookie列表
  late List<Cookie> cookieList;

  PlatBase(PlatformModel platformModel) {
    // 优先使用cookie字符串
    if (platformModel.cookie != null && platformModel.cookie!.isNotEmpty) {
      cookieStr = platformModel.cookie!;
      // 如果提供了cookie字符串但没有cookieList，则从字符串创建列表
      cookieList =
          platformModel.cookieList ??
          PlatformModel.cookieStringToCookies(platformModel.cookie!);
    }
    // 如果没有提供cookie字符串但有cookieList
    else if (platformModel.cookieList != null &&
        platformModel.cookieList!.isNotEmpty) {
      cookieList = platformModel.cookieList!;
      // 从列表创建字符串
      cookieStr = cookieList
          .map((cookie) => '${cookie.name}=${cookie.value}')
          .join('; ');
    }
    // 如果都没有提供
    else {
      cookieStr = '';
      cookieList = [];
    }

    LoggerUtil.i(
      '【平台】Cookie字符串: ${cookieStr.length > 20 ? '${cookieStr.substring(0, 20)}...' : cookieStr}',
    );
  }

  /// 获取用户信息
  Future getUserInfo() {
    throw UnimplementedError();
  }

  /// 发布视频（原有方法，保持兼容性）
  Future<PublishResponseModel> publishVideo(
    PublishParamsModel publishParamsModel,
  ) {
    throw Exception('该平台未实现视频发布');
  }

  /// 发布图文（原有方法，保持兼容性）
  Future<PublishResponseModel> publishImgText(
    PublishParamsModel publishParamsModel,
  ) {
    throw Exception('该平台未实现图文发布');
  }

  /// 使用平台特定参数发布视频（新方法）
  Future<PublishResponseModel> publishVideoWithPlatformConfig(
    PublishParamsModel publishParamsModel,
    Map<String, dynamic> platformConfig,
  ) {
    // 默认实现：忽略平台特定配置，调用原有方法
    return publishVideo(publishParamsModel);
  }

  /// 使用平台特定参数发布图文（新方法）
  Future<PublishResponseModel> publishImgTextWithPlatformConfig(
    PublishParamsModel publishParamsModel,
    Map<String, dynamic> platformConfig,
  ) {
    // 默认实现：忽略平台特定配置，调用原有方法
    return publishImgText(publishParamsModel);
  }

  /// 发起http请求各平台不同的请求头
  Map<String, dynamic> getMakeRequestHedears() {
    return {};
  }

  /// http 请求
  Future<Response<T>?> makeRequest<T>(
    String url, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    Options? options,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    options ??= Options(method: 'GET');

    options.headers = {
      ...getMakeRequestHedears(),
      ...?options.headers,
      'Cookie': cookieStr,
      'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
    };

    return await DioUtils.client.request<T>(
      url,
      data: data,
      queryParameters: queryParameters,
      cancelToken: cancelToken,
      options: options,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );
  }
}
