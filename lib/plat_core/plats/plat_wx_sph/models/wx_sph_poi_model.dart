import 'package:aitoearn_app/config/logger.dart';

/// 微信视频号POI搜索响应模型
class WxSphPoiSearchResponse {
  final int statusCode;
  final String statusMsg;
  final List<WxSphPoiItem> poiList;
  final String? cookies;
  final Map<String, dynamic>? address;
  final int? continueFlag;
  
  WxSphPoiSearchResponse({
    required this.statusCode,
    required this.statusMsg,
    required this.poiList,
    this.cookies,
    this.address,
    this.continueFlag,
  });
  
  /// 从JSON创建响应
  factory WxSphPoiSearchResponse.fromJson(Map<String, dynamic> json) {
    final poiList = <WxSphPoiItem>[];
    
    try {
      LoggerUtil.i('【微信视频号】解析POI响应数据开始');
      
      int code = 0;
      String msg = '';
      String? cookies;
      Map<String, dynamic>? address;
      int? continueFlag;
      
      // 检查errCode和errMsg字段
      if (json.containsKey('errCode')) {
        code = json['errCode'] ?? 0;
        msg = json['errMsg'] ?? '';
        LoggerUtil.i('【微信视频号】返回状态码: $code, 消息: $msg');
      } 
      // 兼容旧版API格式
      else if (json['baseResponse'] != null && json['baseResponse'] is Map<String, dynamic>) {
        final baseResponse = json['baseResponse'];
        code = baseResponse['ret'] ?? 0;
        msg = baseResponse['errMsg'] ?? '';
        LoggerUtil.i('【微信视频号】返回状态码(baseResponse): $code, 消息: $msg');
      }
      
      // 检查data字段
      if (json.containsKey('data') && json['data'] is Map<String, dynamic>) {
        final data = json['data'] as Map<String, dynamic>;
        
        // 获取cookies
        if (data.containsKey('cookies')) {
          cookies = data['cookies']?.toString();
          LoggerUtil.i('【微信视频号】获取到cookies: $cookies');
        }
        
        // 获取address
        if (data.containsKey('address') && data['address'] is Map) {
          address = Map<String, dynamic>.from(data['address']);
          LoggerUtil.i('【微信视频号】获取到address: $address');
        }
        
        // 获取continueFlag
        if (data.containsKey('continueFlag')) {
          continueFlag = data['continueFlag'] as int?;
          LoggerUtil.i('【微信视频号】获取到continueFlag: $continueFlag');
        }
        
        // 处理POI列表
        if (data.containsKey('list') && data['list'] is List) {
          final List<dynamic> items = data['list'];
          LoggerUtil.i('【微信视频号】找到list字段，包含${items.length}个位置项');
          
          for (var item in items) {
            if (item is Map<String, dynamic>) {
              try {
                final poiItem = WxSphPoiItem.fromJson(item);
                poiList.add(poiItem);
                LoggerUtil.i('【微信视频号】成功解析POI项: ${poiItem.poiName}');
              } catch (e) {
                LoggerUtil.e('【微信视频号】解析POI项出错: $e, 原始数据: $item');
              }
            }
          }
        }
      } 
      // 兼容旧版API格式
      else if (json['searchLocationList'] != null && json['searchLocationList'] is List) {
        final List<dynamic> poiItems = json['searchLocationList'];
        LoggerUtil.i('【微信视频号】找到searchLocationList字段，包含${poiItems.length}个位置项');
        
        for (var item in poiItems) {
          if (item is Map<String, dynamic>) {
            try {
              final poiItem = WxSphPoiItem.fromJson(item);
              poiList.add(poiItem);
              LoggerUtil.i('【微信视频号】成功解析POI项: ${poiItem.poiName}');
            } catch (e) {
              LoggerUtil.e('【微信视频号】解析POI项出错: $e, 原始数据: $item');
            }
          }
        }
      } else {
        LoggerUtil.e('【微信视频号】响应中没有找到有效的POI列表数据');
      }
      
      LoggerUtil.i('【微信视频号】共解析到${poiList.length}个POI项');
      
      return WxSphPoiSearchResponse(
        statusCode: code,
        statusMsg: msg,
        poiList: poiList,
        cookies: cookies,
        address: address,
        continueFlag: continueFlag,
      );
    } catch (e) {
      LoggerUtil.e('【微信视频号】处理POI数据出错: $e');
      LoggerUtil.e('【微信视频号】原始JSON数据: $json');
      return WxSphPoiSearchResponse(
        statusCode: -1,
        statusMsg: '解析数据出错: $e',
        poiList: [],
      );
    }
  }
  
  /// 从原始API响应创建POI响应对象
  factory WxSphPoiSearchResponse.fromRawApiResponse(Map<String, dynamic> json) {
    final poiList = <WxSphPoiItem>[];
    String? cookies;
    Map<String, dynamic>? address;
    int? continueFlag;
    
    try {
      LoggerUtil.i('【微信视频号】从原始API响应创建POI响应对象');
      
      // 检查data字段
      if (json.containsKey('data') && json['data'] is Map<String, dynamic>) {
        final data = json['data'] as Map<String, dynamic>;
        
        // 获取cookies
        if (data.containsKey('cookies')) {
          cookies = data['cookies']?.toString();
        }
        
        // 获取address
        if (data.containsKey('address') && data['address'] is Map) {
          address = Map<String, dynamic>.from(data['address']);
        }
        
        // 获取continueFlag
        if (data.containsKey('continueFlag')) {
          continueFlag = data['continueFlag'] as int?;
        }
        
        // 处理POI列表
        if (data.containsKey('list') && data['list'] is List) {
          final List<dynamic> items = data['list'];
          
          for (var item in items) {
            if (item is Map<String, dynamic>) {
              try {
                final poiItem = WxSphPoiItem.fromJson(item);
                poiList.add(poiItem);
              } catch (e) {
                LoggerUtil.e('【微信视频号】解析POI项出错: $e');
              }
            }
          }
        }
      }
      // 首先尝试处理标准的接口返回格式
      else if (json['searchLocationList'] != null && json['searchLocationList'] is List) {
        final List<dynamic> poiItems = json['searchLocationList'];
        LoggerUtil.i('【微信视频号】找到searchLocationList字段，包含${poiItems.length}个位置项');
        
        for (var item in poiItems) {
          if (item is Map<String, dynamic>) {
            try {
              final poiItem = WxSphPoiItem.fromJson(item);
              poiList.add(poiItem);
              LoggerUtil.i('【微信视频号】成功解析POI项: ${poiItem.poiName}');
            } catch (e) {
              LoggerUtil.e('【微信视频号】解析POI项出错: $e');
            }
          }
        }
      } 
      // 处理直接提供的单个POI项
      else if (json['locationId'] != null || json['name'] != null || json['uid'] != null) {
        try {
          final poiItem = WxSphPoiItem.fromJson(json);
          poiList.add(poiItem);
          LoggerUtil.i('【微信视频号】从单个JSON对象解析POI项: ${poiItem.poiName}');
        } catch (e) {
          LoggerUtil.e('【微信视频号】解析单个POI项出错: $e');
        }
      }
      // 处理缓存的POI对象
      else if (json['poi_object'] != null) {
        try {
          final poiItem = WxSphPoiItem.fromJson(json['poi_object']);
          poiList.add(poiItem);
          LoggerUtil.i('【微信视频号】从poi_object字段解析POI项: ${poiItem.poiName}');
        } catch (e) {
          LoggerUtil.e('【微信视频号】解析poi_object字段出错: $e');
        }
      } else {
        LoggerUtil.e('【微信视频号】未找到任何可解析的POI数据');
      }
    } catch (e) {
      LoggerUtil.e('【微信视频号】解析POI数据失败: $e');
      LoggerUtil.e('【微信视频号】原始JSON数据: $json');
    }
    
    // 获取状态码和消息
    int code = 0;
    String msg = '';
    
    if (json.containsKey('errCode')) {
      code = json['errCode'] ?? 0;
      msg = json['errMsg'] ?? '';
    } else if (json['baseResponse'] != null && json['baseResponse'] is Map) {
      final baseResponse = json['baseResponse'];
      code = baseResponse['ret'] ?? 0;
      msg = baseResponse['errMsg'] ?? '';
    }
    
    LoggerUtil.i('【微信视频号】共解析到${poiList.length}个POI项, 状态码: $code, 消息: $msg');
    
    return WxSphPoiSearchResponse(
      statusCode: code,
      statusMsg: msg,
      poiList: poiList,
      cookies: cookies,
      address: address,
      continueFlag: continueFlag,
    );
  }
}

/// 微信视频号POI位置项模型
class WxSphPoiItem {
  final String poiId;
  final String poiName;
  final String address;
  final double latitude;
  final double longitude;
  final String cityName;
  final String districtName;
  final String province;
  final String fullAddress;
  final int source;
  final String poiCheckSum;
  final int distance; // 单位：米
  
  WxSphPoiItem({
    required this.poiId,
    required this.poiName,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.cityName,
    this.districtName = '',
    this.province = '',
    this.fullAddress = '',
    this.source = 0,
    this.poiCheckSum = '',
    this.distance = 0,
  });
  
  /// 创建一个新的WxSphPoiItem实例，同时更新部分字段
  WxSphPoiItem copyWith({
    String? poiId,
    String? poiName,
    String? address,
    double? latitude,
    double? longitude,
    String? cityName,
    String? districtName,
    String? province,
    String? fullAddress,
    int? source,
    String? poiCheckSum,
    int? distance,
  }) {
    return WxSphPoiItem(
      poiId: poiId ?? this.poiId,
      poiName: poiName ?? this.poiName,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      cityName: cityName ?? this.cityName,
      districtName: districtName ?? this.districtName,
      province: province ?? this.province,
      fullAddress: fullAddress ?? this.fullAddress,
      source: source ?? this.source,
      poiCheckSum: poiCheckSum ?? this.poiCheckSum,
      distance: distance ?? this.distance,
    );
  }
  
  /// 从JSON创建POI项
  factory WxSphPoiItem.fromJson(Map<String, dynamic> json) {
    // 处理位置ID
    String poiId = '';
    if (json['uid'] != null) {
      poiId = json['uid'].toString();
    } else if (json['locationId'] != null) {
      poiId = json['locationId'].toString();
    } else if (json['id'] != null) {
      poiId = json['id'].toString();
    }
    
    // 处理位置名称
    String poiName = json['name'] ?? '';
    
    // 处理地址
    String address = json['address'] ?? '';
    String fullAddress = json['fullAddress'] ?? address;
    
    // 处理城市、区域和省份
    String cityName = json['city'] ?? '';
    String districtName = json['region'] ?? json['district'] ?? '';
    String province = json['province'] ?? '';
    
    // 处理经纬度
    double latitude = 0.0;
    double longitude = 0.0;
    
    if (json['latitude'] != null && json['longitude'] != null) {
      latitude = _parseDoubleValue(json['latitude']);
      longitude = _parseDoubleValue(json['longitude']);
    }
    
    // 处理来源和校验和
    int source = json['source'] ?? 0;
    String poiCheckSum = json['poiCheckSum'] ?? '';
    
    // 处理距离（如果有）
    int distance = 0;
    if (json['distance'] != null) {
      if (json['distance'] is num) {
        distance = (json['distance'] as num).toInt();
      }
    }
    
    return WxSphPoiItem(
      poiId: poiId,
      poiName: poiName,
      address: address,
      latitude: latitude,
      longitude: longitude,
      cityName: cityName,
      districtName: districtName,
      province: province,
      fullAddress: fullAddress,
      source: source,
      poiCheckSum: poiCheckSum,
      distance: distance,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'uid': poiId,
      'name': poiName,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'city': cityName,
      'region': districtName,
      'province': province,
      'fullAddress': fullAddress,
      'source': source,
      'poiCheckSum': poiCheckSum,
    };
  }
  
  /// 辅助方法：解析double值
  static double _parseDoubleValue(dynamic value) {
    if (value == null) return 0.0;
    
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    
    return 0.0;
  }
} 