import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/wx_sph_platform_model.dart';
import 'package:aitoearn_app/plat_core/models/location_model.dart';
import 'package:aitoearn_app/plat_core/models/mix_model.dart';

/// 视频号API响应模型
class WxSphApiResponse<T> {
  final int errCode;
  final String errMsg;
  final T? data;

  WxSphApiResponse({
    required this.errCode,
    required this.errMsg,
    this.data,
  });

  bool get isSuccess => errCode == 0;

  factory WxSphApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return WxSphApiResponse(
      errCode: json['errCode'] ?? -1,
      errMsg: json['errMsg'] ?? '',
      data: fromJsonT != null && json['data'] != null 
          ? fromJsonT(json['data']) 
          : null,
    );
  }
}

/// 视频号活动列表响应数据
class WxSphActivityListData {
  final List<WxSphActivity> eventList;
  final String lastBuff;
  final bool continueFlag;

  WxSphActivityListData({
    required this.eventList,
    required this.lastBuff,
    required this.continueFlag,
  });

  factory WxSphActivityListData.fromJson(Map<String, dynamic> json) {
    return WxSphActivityListData(
      eventList: (json['eventList'] as List? ?? [])
          .map((e) => WxSphActivity.fromJson(e))
          .toList(),
      lastBuff: json['lastBuff'] ?? '',
      continueFlag: json['continueFlag'] ?? false,
    );
  }
}

/// 视频号用户列表响应数据
class WxSphUserListData {
  final List<WxSphUser> list;
  final int totalCount;

  WxSphUserListData({
    required this.list,
    required this.totalCount,
  });

  factory WxSphUserListData.fromJson(Map<String, dynamic> json) {
    return WxSphUserListData(
      list: (json['list'] as List? ?? [])
          .map((e) => WxSphUser.fromJson(e))
          .toList(),
      totalCount: json['totalCount'] ?? 0,
    );
  }
}

/// 视频号位置信息
class WxSphLocation {
  final String uid;
  final String name;
  final double longitude;
  final double latitude;
  final String address;
  final String province;
  final String city;
  final String region;
  final String fullAddress;
  final int source;
  final String poiCheckSum;

  WxSphLocation({
    required this.uid,
    required this.name,
    required this.longitude,
    required this.latitude,
    required this.address,
    required this.province,
    required this.city,
    required this.region,
    required this.fullAddress,
    required this.source,
    required this.poiCheckSum,
  });

  factory WxSphLocation.fromJson(Map<String, dynamic> json) {
    return WxSphLocation(
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      longitude: (json['longitude'] ?? 0).toDouble(),
      latitude: (json['latitude'] ?? 0).toDouble(),
      address: json['address'] ?? '',
      province: json['province'] ?? '',
      city: json['city'] ?? '',
      region: json['region'] ?? '',
      fullAddress: json['fullAddress'] ?? '',
      source: json['source'] ?? 0,
      poiCheckSum: json['poiCheckSum'] ?? '',
    );
  }

  LocationModel toLocationModel() {
    return LocationModel(
      id: uid,
      name: name,
      latitude: latitude,
      longitude: longitude,
      address: fullAddress,
      city: city,
      province: province,
      simpleAddress: address,
    );
  }
}

/// 视频号合集信息
class WxSphMix {
  final String id;
  final String name;
  final String desc;
  final String? coverImgUrl;
  final int feedCount;

  WxSphMix({
    required this.id,
    required this.name,
    required this.desc,
    this.coverImgUrl,
    required this.feedCount,
  });

  factory WxSphMix.fromJson(Map<String, dynamic> json) {
    return WxSphMix(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      desc: json['desc'] ?? '',
      coverImgUrl: json['coverImgUrl'],
      feedCount: json['feedCount'] ?? 0,
    );
  }

  MixModel toMixModel() {
    return MixModel(
      id: id,
      name: name,
      coverImg: coverImgUrl ?? '',
      feedCount: feedCount,
    );
  }
}

/// 视频号服务类
class WxSphService {
  static const String _baseUrl = 'https://channels.weixin.qq.com';
  
  final Dio _dio;
  final String cookieString;

  WxSphService({
    required this.cookieString,
    Dio? dio,
  }) : _dio = dio ?? Dio();

  /// 搜索活动
  Future<List<WxSphActivity>> searchActivities(String keyword) async {
    try {
      LoggerUtil.i('搜索视频号活动: $keyword');

      final response = await _dio.get(
        '$_baseUrl/cgi-bin/mmfinderassistant-bin/assistant/get_event_list',
        queryParameters: {
          'keyword': keyword,
          'limit': 20,
        },
        options: Options(
          headers: {
            'Cookie': cookieString,
            'Referer': 'https://channels.weixin.qq.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        ),
      );

      final apiResponse = WxSphApiResponse.fromJson(
        response.data,
        (data) => WxSphActivityListData.fromJson(data),
      );

      if (!apiResponse.isSuccess) {
        throw Exception('搜索活动失败: ${apiResponse.errMsg}');
      }

      return apiResponse.data?.eventList ?? [];
    } catch (e) {
      LoggerUtil.e('搜索视频号活动失败: $e');
      rethrow;
    }
  }

  /// 搜索用户
  Future<List<WxSphUser>> searchUsers(String keyword, {int page = 1}) async {
    try {
      LoggerUtil.i('搜索视频号用户: $keyword, page: $page');

      final response = await _dio.get(
        '$_baseUrl/cgi-bin/mmfinderassistant-bin/assistant/search_finder_user',
        queryParameters: {
          'keyword': keyword,
          'page': page,
          'limit': 20,
        },
        options: Options(
          headers: {
            'Cookie': cookieString,
            'Referer': 'https://channels.weixin.qq.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        ),
      );

      final apiResponse = WxSphApiResponse.fromJson(
        response.data,
        (data) => WxSphUserListData.fromJson(data),
      );

      if (!apiResponse.isSuccess) {
        throw Exception('搜索用户失败: ${apiResponse.errMsg}');
      }

      return apiResponse.data?.list ?? [];
    } catch (e) {
      LoggerUtil.e('搜索视频号用户失败: $e');
      rethrow;
    }
  }

  /// 搜索位置
  Future<List<WxSphLocation>> searchLocations(String keyword) async {
    try {
      LoggerUtil.i('搜索视频号位置: $keyword');

      final response = await _dio.get(
        '$_baseUrl/cgi-bin/mmfinderassistant-bin/assistant/search_location',
        queryParameters: {
          'keyword': keyword,
          'limit': 20,
        },
        options: Options(
          headers: {
            'Cookie': cookieString,
            'Referer': 'https://channels.weixin.qq.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        ),
      );

      final apiResponse = WxSphApiResponse.fromJson(response.data, null);

      if (!apiResponse.isSuccess) {
        throw Exception('搜索位置失败: ${apiResponse.errMsg}');
      }

      final locationList = apiResponse.data?['list'] as List? ?? [];
      return locationList
          .map((e) => WxSphLocation.fromJson(e))
          .toList();
    } catch (e) {
      LoggerUtil.e('搜索视频号位置失败: $e');
      rethrow;
    }
  }

  /// 获取合集列表
  Future<List<WxSphMix>> getMixList() async {
    try {
      LoggerUtil.i('获取视频号合集列表');

      final response = await _dio.get(
        '$_baseUrl/cgi-bin/mmfinderassistant-bin/assistant/get_collection_list',
        options: Options(
          headers: {
            'Cookie': cookieString,
            'Referer': 'https://channels.weixin.qq.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        ),
      );

      final apiResponse = WxSphApiResponse.fromJson(response.data, null);

      if (!apiResponse.isSuccess) {
        throw Exception('获取合集列表失败: ${apiResponse.errMsg}');
      }

      final collectionList = apiResponse.data?['collectionList'] as List? ?? [];
      return collectionList
          .map((e) => WxSphMix.fromJson(e))
          .toList();
    } catch (e) {
      LoggerUtil.e('获取视频号合集列表失败: $e');
      rethrow;
    }
  }

  /// 检查登录状态
  Future<bool> checkLoginStatus() async {
    try {
      final response = await _dio.get(
        '$_baseUrl/cgi-bin/mmfinderassistant-bin/assistant/get_user_info',
        options: Options(
          headers: {
            'Cookie': cookieString,
            'Referer': 'https://channels.weixin.qq.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        ),
      );

      final apiResponse = WxSphApiResponse.fromJson(response.data, null);
      return apiResponse.isSuccess;
    } catch (e) {
      LoggerUtil.e('检查视频号登录状态失败: $e');
      return false;
    }
  }
}
