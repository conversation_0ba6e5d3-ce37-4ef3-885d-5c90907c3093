import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_types.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:aitoearn_app/plat_core/utils/video_info_util.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/models/wx_sph_poi_model.dart';

class DoiUtil {
  static Future<Map<String, dynamic>?> getVideoInfo(String filePath) async {
    try {
      final file = File(filePath);

      if (!await file.exists()) {
        return null;
      }

      PlatformChecker.checkWxSphSupport(throwException: true);

      final videoInfo = await VideoInfoUtil.getVideoInfo(file);

      final width = int.tryParse(videoInfo.videoStream.getAllProperties()?['width'] ?? '0') ?? 0;
      final height = int.tryParse(videoInfo.videoStream.getAllProperties()?['height'] ?? '0') ?? 0;
      final duration = double.tryParse(videoInfo.videoStream.getAllProperties()?['duration'] ?? '0') ?? 0.0;
      final size = await file.length();

      return {
        'width': width,
        'height': height,
        'duration': duration,
        'size': size,
      };
    } catch (e, stackTrace) {
      LoggerUtil.e('【DoiUtil】获取视频信息异常: $e');
      return null;
    }
  }

  static Future<Map<String, dynamic>?> getFilePartInfo(String filePath, int blockSize) async {
    try {
      final file = File(filePath);

      if (!await file.exists()) {
        return null;
      }

      final fileSize = await file.length();
      final blockCount = (fileSize / blockSize).ceil();
      final blockInfo = <int>[];

      for (int i = 0; i < blockCount; i++) {
        final endPos = (i + 1) * blockSize > fileSize ? fileSize : (i + 1) * blockSize;
        blockInfo.add(endPos);
      }

      return {
        'fileSize': fileSize,
        'blockCount': blockCount,
        'blockInfo': blockInfo,
      };
    } catch (e, stackTrace) {
      LoggerUtil.e('【DoiUtil】计算文件分块信息异常: $e');
      return null;
    }
  }

  static Future<Uint8List?> getFilePartContent(String filePath, int start, int end) async {
    try {
      final file = File(filePath);

      if (!await file.exists()) {
        return null;
      }

      try {
        final completeBytes = await file.readAsBytes();
        if (end > completeBytes.length) {
          end = completeBytes.length;
        }
        return completeBytes.sublist(start, end);
      } catch (e) {
        final raf = await file.open(mode: FileMode.read);
        try {
          await raf.setPosition(start);
          final bytesToRead = end - start;
          final buffer = Uint8List(bytesToRead);
          final bytesRead = await raf.readInto(buffer);

          if (bytesRead != bytesToRead) {
            return null;
          }

          return buffer;
        } finally {
          await raf.close();
        }
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【DoiUtil】获取文件部分内容异常: $e');
      return null;
    }
  }

  static bool _isMobilePlatform() {
    return Platform.isAndroid || Platform.isIOS;
  }
}

class WxSphService {
  static final Dio _dio = Dio();

  static const String wxSphBaseUrl = 'https://channels.weixin.qq.com';
  static const int fileBlockSize = 8 * 1024 * 1024;
  static const String defaultUserAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.0.0 Safari/537.36';

  static void _checkPlatformSupport() {
    PlatformChecker.checkWxSphSupport(throwException: true);
  }
  
  static Future<Map<String, dynamic>> makeRequest({
    required String url,
    required String cookieStr,
    String method = 'GET',
    Map<String, dynamic>? data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? additionalHeaders,
    String? proxy,
  }) async {
    try {
      final headers = {
        'User-Agent': defaultUserAgent,
        'Cookie': cookieStr,
        ...?additionalHeaders,
      };

      String requestUrl = url;

      final needsAidRid = !url.contains('_aid=') && !url.contains('_rid=');
      final isWxSphApi = url.contains('cgi-bin/mmfinderassistant-bin');

      if (needsAidRid && isWxSphApi) {
        final aid = '${_generateRandomHex(8)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(12)}';
        final rid = '${_generateRandomHex(8)}-${_generateRandomHex(8)}';

        final separator = url.contains('?') ? '&' : '?';
        requestUrl = '$url${separator}_aid=$aid&_rid=$rid&_pageUrl=${Uri.encodeComponent("https://channels.weixin.qq.com/micro/content/post/create")}';
      }

      dynamic requestData;
      if (data != null) {
        requestData = jsonEncode(data);
      }

      final options = BaseOptions(
        headers: headers,
        followRedirects: true,
        validateStatus: (status) => true,
        contentType: 'application/json',
      );

      final dio = Dio(options);

      dio.interceptors.add(InterceptorsWrapper(
        onRequest: (options, handler) {
          if (method != 'GET' && !options.headers.containsKey('Content-Type')) {
            options.headers['Content-Type'] = 'application/json';
          }
          return handler.next(options);
        },
        onResponse: (response, handler) {
          return handler.next(response);
        },
        onError: (DioException e, handler) {
          LoggerUtil.e('请求错误: ${e.message}');
          return handler.next(e);
        },
      ));

      final startTime = DateTime.now().millisecondsSinceEpoch;

      final Response response = await dio.request(
        requestUrl,
        data: method != 'GET' ? requestData : null,
        queryParameters: queryParameters,
        options: Options(method: method),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is String) {
          final responseStr = response.data as String;

          if (responseStr.trim().startsWith('<!DOCTYPE html>') ||
              responseStr.trim().startsWith('<html>')) {
            return {
              'errCode': -2,
              'errMsg': '接收到HTML响应，请重新登录微信视频号'
            };
          }

          try {
            final jsonData = jsonDecode(responseStr);
            return jsonData;
          } catch (e) {
            return {'errCode': -1, 'errMsg': '返回数据格式错误: $e'};
          }
        } else if (response.data is Map) {
          return response.data;
        } else {
          return {'errCode': -1, 'errMsg': '返回数据格式错误: 非字符串或Map'};
        }
      } else {
        return {
          'errCode': response.statusCode,
          'errMsg': '请求失败: ${response.statusCode}',
        };
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('请求异常: $e');
      return {'errCode': -1, 'errMsg': '请求异常: $e'};
    }
  }

  static Future<Map<String, dynamic>> uploadFile({
    required String url,
    required Uint8List fileContent,
    required String cookieStr,
    required Map<String, String> headers,
    String method = 'PUT',
    String? proxy,
  }) async {
    try {
      final dio = Dio(BaseOptions(
        headers: headers,
        followRedirects: true,
        validateStatus: (status) => true,
      ));

      final response = await dio.request(
        url,
        data: fileContent,
        options: Options(method: method),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data is String) {
          try {
            final jsonData = jsonDecode(response.data);
            return jsonData;
          } catch (e) {
            return {'errCode': -1, 'errMsg': '上传返回数据格式错误'};
          }
        } else if (response.data is Map) {
          return response.data;
        } else {
          return {'errCode': -1, 'errMsg': '上传返回数据格式错误'};
        }
      } else {
        return {
          'errCode': response.statusCode,
          'errMsg': '上传请求失败: ${response.statusCode}',
        };
      }
    } catch (e) {
      LoggerUtil.e('上传异常: $e');
      return {'errCode': -1, 'errMsg': '上传异常: $e'};
    }
  }

  static String generateUniqueTaskId() {
    const uuid = Uuid();
    return uuid.v4();
  }

  static Future<WxSphUserInfo?> getUserInfo(String cookieStr) async {
    try {
      const url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/auth/auth_data';

      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
      );

      if (response['errCode'] == 0) {
        final userData = response['data']['finderUser'] ?? {};

        final userInfo = WxSphUserInfo(
          authorId: userData['uniqId'] ?? '',
          nickname: userData['nickname'] ?? '',
          avatar: userData['headImgUrl'] ?? '',
          fansCount: userData['fansCount'] ?? 0,
        );

        return userInfo;
      } else {
        return null;
      }
    } catch (e) {
      LoggerUtil.e('【视频号API】获取用户信息异常: $e');
      return null;
    }
  }

  static Future<List<WxSphDashboardData>?> getDashboardData(
    String cookieStr, {
    String? startDate,
    String? endDate,
  }) async {
    try {
      if (cookieStr.isEmpty) {
        return [];
      }

      const url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/statistic/new_post_total_data';

      final endTs = endDate != null
          ? (DateTime.parse(endDate).millisecondsSinceEpoch ~/ 1000).toString()
          : (DateTime.now().subtract(const Duration(days: 1)).millisecondsSinceEpoch ~/ 1000).toString();

      final startTs = startDate != null
          ? (DateTime.parse(startDate).millisecondsSinceEpoch ~/ 1000).toString()
          : (DateTime.now().subtract(const Duration(days: 2)).millisecondsSinceEpoch ~/ 1000).toString();

      final data = {
        'endTs': endTs,
        'interval': 3,
        'startTs': startTs,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
      );

      if (response['errCode'] == 0) {
        if (!response.containsKey('data') || response['data'] == null || !response['data'].containsKey('totalData')) {
          return [];
        }

        final totalData = response['data']['totalData'];
        final dates = totalData['browse'] ?? [];
        final followData = totalData['follow'] ?? [];
        final browseData = totalData['browse'] ?? [];
        final commentData = totalData['comment'] ?? [];
        final likeData = totalData['like'] ?? [];
        final forwardData = totalData['forward'] ?? [];
        final snscoverData = totalData['snscover'] ?? [];

        final dataList = <WxSphDashboardData>[];

        for (int i = 0; i < dates.length; i++) {
          dataList.add(WxSphDashboardData(
            date: dates[i],
            zhangfen: _parseIntValue(followData[i]),
            bofang: _parseIntValue(browseData[i]),
            pinglun: _parseIntValue(commentData[i]),
            dianzan: _parseIntValue(likeData[i]),
            fenxiang: _parseIntValue(forwardData[i]),
            zhuye: _parseIntValue(snscoverData[i]),
          ));
        }

        return dataList;
      } else {
        return [];
      }
    } catch (e) {
      LoggerUtil.e('【视频号API】获取数据统计异常: $e');
      return [];
    }
  }

  static int _parseIntValue(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      try {
        return int.parse(value);
      } catch (_) {
        return 0;
      }
    }
    return 0;
  }

  static Future<List<WxSphMixInfo>?> getMixList(String cookieStr) async {
    try {
      if (cookieStr.isEmpty) {
        return [];
      }

      const url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/collection/get_collection_list';

      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'GET',
      );

      if (response['errCode'] == 0) {
        if (!response.containsKey('data') || response['data'] == null) {
          return [];
        }

        final collectionList = response['data']['collectionList'] ?? [];
        final mixList = <WxSphMixInfo>[];

        for (final item in collectionList) {
          mixList.add(WxSphMixInfo.fromJson(item));
        }

        return mixList;
      } else {
        return [];
      }
    } catch (e) {
      LoggerUtil.e('【视频号API】获取合集列表异常: $e');
      return [];
    }
  }

  static Future<List<WxSphLocation>?> getLocationList(
    String cookieStr, {
    required String query,
    required double longitude,
    required double latitude,
  }) async {
    try {
      const url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/helper/helper_search_location';

      final data = {
        'keyword': query,
        'longitude': longitude,
        'latitude': latitude,
      };

      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
      );

      if (response['errCode'] == 0) {
        final locationList = response['data']['list'] ?? [];
        final locations = <WxSphLocation>[];

        for (final item in locationList) {
          locations.add(WxSphLocation(
            poiId: item['uid'] ?? '',
            poiName: item['name'] ?? '',
            poiAddress: item['address'] ?? '',
            poiCity: item['city'] ?? '',
            latitude: item['latitude'] ?? 0.0,
            longitude: item['longitude'] ?? 0.0,
          ));
        }

        return locations;
      } else {
        return null;
      }
    } catch (e) {
      LoggerUtil.e('【视频号API】获取位置列表异常: $e');
      return null;
    }
  }
  
  /// 获取作品列表
  static Future<List<WxSphPost>?> getPostList(
    String cookieStr, {
    int pageNo = 1,
    int pageSize = 10,
  }) async {
    try {
      LoggerUtil.i('【视频号API】获取作品列表: 页码=$pageNo, 每页数量=$pageSize');
      
      // 检查cookie是否为空
      if (cookieStr.isEmpty) {
        LoggerUtil.e('【视频号API】获取作品列表失败: Cookie为空');
        return [];
      }
      
      const url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/post/post_list';
      
      final data = {
        'pageNo': pageNo,
        'pageSize': pageSize,
      };
      
      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
      );
      
      if (response['errCode'] == 0) {
        // 确保data字段存在
        if (!response.containsKey('data') || response['data'] == null) {
          LoggerUtil.e('【视频号API】获取作品列表失败: 响应中缺少data字段');
          return [];
        }
        
        final postList = response['data']['list'] ?? [];
        
        final posts = <WxSphPost>[];
        
        for (final item in postList) {
          posts.add(WxSphPost.fromJson(item));
        }
        
        LoggerUtil.i('【视频号API】获取作品列表成功: ${posts.length} 条记录');
        return posts;
      } else if (response['errCode'] == -2) {
        // 特殊处理登录失效的情况
        LoggerUtil.e('【视频号API】获取作品列表失败: ${response['errMsg']}');
        return [];
      } else {
        LoggerUtil.e('【视频号API】获取作品列表失败: ${response['errMsg']}');
        return [];
      }
    } catch (e) {
      LoggerUtil.e('【视频号API】获取作品列表异常: $e');
      return [];
    }
  }
  
  /// 获取评论列表
  static Future<List<WxSphComment>?> getCommentList(
    String cookieStr,
    String exportId,
  ) async {
    try {
      LoggerUtil.i('【视频号API】获取评论列表: exportId=$exportId');
      
      const url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/comment/comment_list';
      
      final data = {
        'exportId': exportId,
        'lastBuff': '',
        'limit': 20,
      };
      
      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
      );
      
      if (response['errCode'] == 0) {
        final commentList = response['data']['comment'] ?? [];
        
        final comments = <WxSphComment>[];
        
        for (final item in commentList) {
          comments.add(WxSphComment.fromJson(item));
        }
        
        LoggerUtil.i('【视频号API】获取评论列表成功: ${comments.length} 条记录');
        return comments;
      } else {
        LoggerUtil.e('【视频号API】获取评论列表失败: ${response['errMsg']}');
        return null;
      }
    } catch (e) {
      LoggerUtil.e('【视频号API】获取评论列表异常: $e');
      return null;
    }
  }
  
  /// 创建评论
  static Future<bool> createComment(
    String cookieStr,
    String exportId,
    String content, {
    String? replyCommentId,
  }) async {
    try {
      LoggerUtil.i('【视频号API】创建评论: exportId=$exportId, content=$content');
      
      const url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/comment/create_comment';
      
      final data = {
        'exportId': exportId,
        'content': content,
      };
      
      if (replyCommentId != null) {
        data['replyCommentId'] = replyCommentId;
      }
      
      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
      );
      
      if (response['errCode'] == 0) {
        LoggerUtil.i('【视频号API】创建评论成功');
        return true;
      } else {
        LoggerUtil.e('【视频号API】创建评论失败: ${response['errMsg']}');
        return false;
      }
    } catch (e) {
      LoggerUtil.e('【视频号API】创建评论异常: $e');
      return false;
    }
  }
  
  /// 获取发布追踪密钥
  static Future<String?> getPublishTraceKey(String cookieStr) async {
    try {
      LoggerUtil.i('【视频号API】获取发布追踪密钥');
      
      // 生成随机的aid和rid参数
      final aid = '${_generateRandomHex(8)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(12)}';
      final rid = '${_generateRandomHex(8)}-${_generateRandomHex(8)}';
      
      // 使用正确的URL路径
      final url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/post/get-finder-post-trace-key?_aid=$aid&_rid=$rid&_pageUrl=${Uri.encodeComponent("https://channels.weixin.qq.com/micro/content/post/create")}';
      
      // 从Cookie中提取微信UIN
      final wechatUin = extractWechatUinFromCookie(cookieStr);
      
      // 构建请求数据
      final data = {
        'objectId': '',
        'pluginSessionId': null,
        'rawKeyBuff': null,
        'reqScene': 7,
        'scene': 7,
        '_log_finder_id': 'v2_060000231003b20faec8c7e48910cad4cb07e530b077af00753ab89cdab7a2294556d14fa4c9@finder',
        '_log_finder_uin': '',
      };
      
      // 设置请求头
      final additionalHeaders = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/micro/content/post/create',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      };
      
      // 如果成功提取到UIN，添加到请求头
      if (wechatUin.isNotEmpty) {
        additionalHeaders['X-WECHAT-UIN'] = wechatUin;
      } else {
        // 如果无法从cookie提取，尝试使用固定值
        additionalHeaders['X-WECHAT-UIN'] = '2776714997';
      }
      
      // 添加设备指纹ID
      additionalHeaders['finger-print-device-id'] = 'c4db9fe872282a20230a90d89afcd682';
      
      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
        additionalHeaders: additionalHeaders,
      );
      
      LoggerUtil.i('【视频号API】获取发布追踪密钥响应: $response');
      
      if (response['errCode'] == 0) {
        final traceKey = response['data']['traceKey'];
        LoggerUtil.i('【视频号API】获取发布追踪密钥成功: $traceKey');
        return traceKey;
      } else {
        LoggerUtil.e('【视频号API】获取发布追踪密钥失败: ${response['errMsg']}');
        LoggerUtil.e('【视频号API】错误响应: $response');
        return null;
      }
    } catch (e) {
      LoggerUtil.e('【视频号API】获取发布追踪密钥异常: $e');
      return null;
    }
  }
  
  /// 获取上传参数
  static Future<Map<String, dynamic>> getUploadParams(String cookieStr) async {
    try {
      LoggerUtil.i('【视频号API】获取上传参数');
      
      // 生成随机的aid和rid参数
      final aid = '${_generateRandomHex(8)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(12)}';
      final rid = '${_generateRandomHex(8)}-${_generateRandomHex(8)}';
      
      // 添加查询字符串参数到URL
      final url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/helper/helper_upload_params?_aid=$aid&_rid=$rid';
      
      LoggerUtil.i('【视频号API】获取上传参数URL: $url');
      
      // 构建请求体
      final data = {
        'reqScene': 7,
        'scene': 7,
      };
      
      // 发起请求
      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
      );
      
      // 检查响应
      if (response['errCode'] != 0) {
        final errorMsg = response['errMsg'] ?? '未知错误';
        LoggerUtil.e('【视频号API】获取上传参数失败: $errorMsg');
        return {};
      }
      
      // 提取上传参数
      final params = response['data'] ?? {};
      
      // 添加视频和图片文件类型
      // params['videoFileType'] = 'video/mp4';
      // params['pictureFileType'] = 'image/jpeg';
      
      // 添加authKey，如果没有则使用空字符串
      if (!params.containsKey('authKey') || params['authKey'] == null) {
        params['authKey'] = '';
      }
      
      LoggerUtil.i('【视频号API】获取上传参数成功: $params');
      return params;
    } catch (e, stackTrace) {
      LoggerUtil.e('【视频号API】获取上传参数异常: $e');
      LoggerUtil.e('【视频号API】异常堆栈: $stackTrace');
      return {};
    }
  }
  
  /// 申请上传视频
  static Future<Map<String, dynamic>?> applyVideoUpload(
    String cookieStr,
    Map<String, dynamic> uploadParams,
    File videoFile,
  ) async {
    try {
      LoggerUtil.i('【视频号API】申请上传视频: ${videoFile.path}');
      LoggerUtil.i('【视频号API】原始上传参数: $uploadParams');
      
      final fileSize = await videoFile.length();
      final taskId = generateUniqueTaskId();
      
      // 计算分块信息
      const blockSize = 8 * 1024 * 1024; // 8MB分块
      final blockCount = (fileSize / blockSize).ceil();
      final blockInfo = <int>[];
      
      for (int i = 0; i < blockCount; i++) {
        final endPos = (i + 1) * blockSize > fileSize ? fileSize : (i + 1) * blockSize;
        blockInfo.add(endPos);
      }
      
      LoggerUtil.i('【视频号API】视频分块信息: 总大小=$fileSize字节, 分块数=$blockCount, 分块大小=$blockInfo');
      
      // 构建X-Arguments参数字符串 - 与curl命令保持一致
      final arguments = {
        'apptype': uploadParams['appType'] ?? '251',
        'filetype': uploadParams['videoFileType'] ?? '20302',
        'weixinnum': uploadParams['uin'],
        'filekey': path.basename(videoFile.path),
        'filesize': fileSize,
        'taskid': taskId,
        'scene': uploadParams['scene'] ?? '2',
      };
      
      String argumentsString = '';
      arguments.forEach((key, value) {
        if (value != null) {
          argumentsString += '$key=$value&';
        }
      });
      
      // 去掉最后一个&符号
      if (argumentsString.endsWith('&')) {
        argumentsString = argumentsString.substring(0, argumentsString.length - 1);
      }
      
      LoggerUtil.i('【视频号API】上传参数X-Arguments: $argumentsString');
      
      // 构建请求体 - 参考index.ts
      final requestBody = {
        'BlockPartLength': blockInfo,
        'BlockSum': blockInfo.length,
      };
      
      LoggerUtil.i('【视频号API】上传请求体: $requestBody');
      
      // 设置请求头 - 与curl命令保持一致
      final headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Arguments': argumentsString,
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      };
      
      // 如果uploadParams包含authKey，添加到请求头
      if (uploadParams.containsKey('authKey') && uploadParams['authKey'] != null && uploadParams['authKey'].toString().isNotEmpty) {
        headers['Authorization'] = uploadParams['authKey'];
        LoggerUtil.i('【视频号API】添加Authorization头: ${uploadParams['authKey']}');
      }
      
      // 更新URL域名为finderassistancee.video.qq.com与curl命令一致
      const url = 'https://finderassistancee.video.qq.com/applyuploaddfs';
      
      LoggerUtil.i('【视频号API】发送申请上传请求: $url');
      LoggerUtil.i('【视频号API】请求头: $headers');
      
      // 使用JSON格式发送请求体
      final response = await uploadFile(
        url: url,
        fileContent: Uint8List.fromList(utf8.encode(jsonEncode(requestBody))),
        cookieStr: cookieStr,
        headers: headers,
        method: 'POST',
      );
      
      LoggerUtil.i('【视频号API】申请上传响应: $response');
      
      if (response.containsKey('UploadID')) {
        LoggerUtil.i('【视频号API】申请上传视频成功: UploadID=${response['UploadID']}');
        
        return {
          'uploadResult': {
            'uploadid': response['UploadID'],
            'filekey': response['FileKey'] ?? path.basename(videoFile.path),
            'filetype': arguments['filetype'],
            'weixinnum': arguments['weixinnum'],
          },
          'taskId': taskId,
          'fileSize': fileSize,
          'blockInfo': blockInfo,
          'scene': arguments['scene'],
          'apptype': arguments['apptype'],
          'authKey': uploadParams['authKey'],
          // 保存原始uploadParams以备后续使用
          'originalParams': uploadParams,
        };
      } else {
        LoggerUtil.e('【视频号API】申请上传视频失败: ${response['errMsg'] ?? response['errmsg'] ?? '未知错误'}');
        return null;
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【视频号API】申请上传视频异常: $e');
      LoggerUtil.e('【视频号API】异常堆栈: $stackTrace');
      return null;
    }
  }
  
  /// 重试函数
  static Future<bool> retryWhile(
    Future<bool> Function() action,
    int maxRetries,
    {Duration delay = const Duration(seconds: 2)}
  ) async {
    int retryCount = 0;
    bool success = false;
    String? lastError;

    while (retryCount < maxRetries && !success) {
      try {
        if (retryCount > 0) {
          LoggerUtil.i('├───> 重试操作 (${retryCount + 1}/$maxRetries)');
          await Future.delayed(delay);
        }
        
        success = await action();
        
        if (success) {
          if (retryCount > 0) {
            LoggerUtil.i('└───> 重试成功');
          }
          return true;
        }
      } catch (e) {
        lastError = e.toString();
        LoggerUtil.e('└───> 重试出错: $e');
      }
      
      retryCount++;
    }
    
    if (!success && retryCount >= maxRetries) {
      LoggerUtil.e('└───> 重试失败，已达到最大重试次数: $maxRetries');
      if (lastError != null) {
        LoggerUtil.e('└───> 最后一次错误: $lastError');
      }
    }
    
    return false;
  }

  /// 上传视频分块
  static Future<bool> uploadVideoChunk(
    String cookieStr,
    Map<String, dynamic> uploadInfo,
    File videoFile,
    int chunkIndex,
    int totalChunks,
    Uint8List chunkData,
    void Function(int progress, String message)? progressCallback,
  ) async {
    try {
      final uploadResult = uploadInfo['uploadResult'];
      final taskId = uploadInfo['taskId'];
      final originalParams = uploadInfo['originalParams'];
      
      LoggerUtil.i('├───> 上传视频分块 ${chunkIndex + 1}/$totalChunks (${chunkData.length}字节)');
      
      // 构建X-Arguments参数 - 严格按照curl命令实现
      final arguments = {
        'apptype': originalParams['appType'] ?? '251',
        'filetype': originalParams['videoFileType'] ?? '20302',
        'weixinnum': originalParams['uin'],
        'filekey': uploadResult['filekey'],
        'filesize': uploadInfo['fileSize'],
        'taskid': taskId,
        'scene': originalParams['scene'] ?? '2',
      };
      
      String argumentsString = '';
      arguments.forEach((key, value) {
        if (value != null) {
          argumentsString += '$key=$value&';
        }
      });
      
      // 去掉最后一个&符号
      if (argumentsString.endsWith('&')) {
        argumentsString = argumentsString.substring(0, argumentsString.length - 1);
      }
      
      LoggerUtil.i('├───> X-Arguments: $argumentsString');
      
      // 构建URL查询参数 - 严格按照curl命令实现
      final uploadId = uploadResult['uploadid'];
      // 注意: 这里域名改为 finderassistancee.video.qq.com，URL参数顺序修改为与curl命令一致
      final uploadUrl = 'https://finderassistancee.video.qq.com/uploadpartdfs?PartNumber=${chunkIndex + 1}&UploadID=$uploadId&QuickUpload=2';
      
      // 创建请求头 - 严格按照curl命令实现
      final headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/octet-stream',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Arguments': argumentsString,
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      };
      
      // 如果uploadParams包含authKey，添加到请求头
      if (originalParams.containsKey('authKey') && originalParams['authKey'] != null && originalParams['authKey'].toString().isNotEmpty) {
        headers['Authorization'] = originalParams['authKey'];
      }
      
      // 添加 Content-MD5 头，如果有条件计算的话
      // 注意：在实际环境中，您可能需要计算文件块的MD5值
      // 以下是一个示例，实际项目中可能需要根据需要实现
      try {
        final digest = await compute(calculateMd5, chunkData);
        if (digest != null) {
          headers['Content-MD5'] = digest;
          LoggerUtil.i('├───> 已添加Content-MD5: $digest');
        }
      } catch (e) {
        LoggerUtil.w('├───> 无法计算Content-MD5: $e');
      }
      
      // 进度回调
      final message = '正在上传视频分块 ${chunkIndex + 1}/$totalChunks';
      progressCallback?.call(((chunkIndex + 1) * 100) ~/ totalChunks, message);
      
      // 使用重试机制上传分块
      String? errorMsg;
      bool uploadSuccess = await retryWhile(
        () async {
          try {
            LoggerUtil.i('├───> 开始上传分块 ${chunkIndex + 1}/$totalChunks');
            LoggerUtil.i('├───> 上传URL: $uploadUrl');
            LoggerUtil.i('├───> 请求头: $headers');
            
            // 上传分块 - 使用PUT方法
            final response = await uploadFile(
              url: uploadUrl,
              fileContent: chunkData,
              cookieStr: cookieStr,
              headers: headers,
              method: 'PUT', // 使用PUT方法，与curl命令保持一致
            );
            
            if (response.containsKey('ETag') && response['ETag'] != null) {
              LoggerUtil.i('└───> 分块 ${chunkIndex + 1}/$totalChunks 上传成功 (ETag: ${response['ETag']})');
              
              // 保存ETag信息到uploadInfo - 与TypeScript保持一致
              if (!uploadInfo.containsKey('partInfo')) {
                uploadInfo['partInfo'] = <Map<String, dynamic>>[];
              }
              
              (uploadInfo['partInfo'] as List).add({
                'PartNumber': chunkIndex + 1,
                'ETag': response['ETag'],
              });
              
              LoggerUtil.i('└───> 分块 ${chunkIndex + 1}/$totalChunks 上传成功');
              return true;
            } else {
              // 尝试获取错误信息
              if (response.containsKey('X-Errno')) {
                errorMsg = '上传视频失败,失败原因: X-Errno=${response['X-Errno']}';
              } else if (response.containsKey('errMsg')) {
                errorMsg = '上传视频失败,失败原因: ${response['errMsg']}';
              } else if (response.containsKey('errmsg')) {
                errorMsg = '上传视频失败,失败原因: ${response['errmsg']}';
              } else {
                errorMsg = '上传视频失败,失败原因: 未知错误';
              }
              
              LoggerUtil.e('└───> 分块 ${chunkIndex + 1}/$totalChunks 上传失败: $errorMsg');
              LoggerUtil.e('└───> 完整响应: $response');
              return false;
            }
          } catch (e) {
            errorMsg = '上传视频失败,失败原因: $e';
            LoggerUtil.e('└───> 分块 ${chunkIndex + 1}/$totalChunks 上传异常: $e');
            return false;
          }
        },
        3, // 最多重试3次
        delay: const Duration(seconds: 2),
      );

      if (!uploadSuccess && errorMsg != null) {
        LoggerUtil.e('└───> 分块 ${chunkIndex + 1}/$totalChunks 上传最终失败: $errorMsg');
      }
      
      return uploadSuccess;
    } catch (e) {
      LoggerUtil.e('└───> 分块 ${chunkIndex + 1}/$totalChunks 上传异常: $e');
      return false;
    }
  }
  
  /// 计算MD5哈希值 - 在隔离线程中运行
  static String? calculateMd5(Uint8List data) {
    try {
      // 使用crypto包计算MD5
      var digest = md5.convert(data);
      return digest.toString();
    } catch (e) {
      return null;
    }
  }
  
  /// 完成视频上传
  static Future<bool> completeVideoUpload(
    String cookieStr,
    Map<String, dynamic> uploadInfo,
  ) async {
    try {
      LoggerUtil.i('├───> 完成视频上传');
      
      final uploadResult = uploadInfo['uploadResult'];
      final taskId = uploadInfo['taskId'];
      final uploadId = uploadResult['uploadid'];
      final originalParams = uploadInfo['originalParams'];
      
      // 检查是否有分块上传的信息
      if (!uploadInfo.containsKey('partInfo') || (uploadInfo['partInfo'] as List).isEmpty) {
        LoggerUtil.e('└───> 完成视频上传失败: 缺少分块上传信息');
        return false;
      }
      
      // 构建X-Arguments参数 - 与curl命令保持一致
      final arguments = {
        'apptype': originalParams['appType'] ?? '251',
        'filetype': originalParams['videoFileType'] ?? '20304',
        'weixinnum': originalParams['uin'],
        'filekey': uploadResult['filekey'],
        'filesize': uploadInfo['fileSize'],
        'taskid': taskId,
        'scene': originalParams['scene'] ?? '2',
      };
      
      String argumentsString = '';
      arguments.forEach((key, value) {
        if (value != null && value.toString().isNotEmpty) {
          argumentsString += '$key=$value&';
        }
      });
      
      // 去掉最后一个&符号
      if (argumentsString.endsWith('&')) {
        argumentsString = argumentsString.substring(0, argumentsString.length - 1);
      }
      
      // 构建请求体 - 与TypeScript实现保持一致
      final requestBody = {
        'TransFlag': '0_0',
        'PartInfo': uploadInfo['partInfo'],
      };
      
      // 构建URL - 更新域名与curl命令保持一致
      final url = 'https://finderassistancee.video.qq.com/completepartuploaddfs?UploadID=$uploadId';
      
      // 创建请求头 - 与curl命令保持一致
      final headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Arguments': argumentsString,
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      };
      
      // 如果uploadParams包含authKey，添加到请求头
      if (originalParams.containsKey('authKey') && originalParams['authKey'] != null && originalParams['authKey'].toString().isNotEmpty) {
        headers['Authorization'] = originalParams['authKey'];
      }
      
      LoggerUtil.i('├───> 完成上传URL: $url');
      LoggerUtil.i('├───> 完成上传请求头: $headers');
      LoggerUtil.i('├───> 完成上传请求体: $requestBody');
      
      // 发送请求
      final response = await uploadFile(
        url: url,
        fileContent: Uint8List.fromList(utf8.encode(jsonEncode(requestBody))),
        cookieStr: cookieStr,
        headers: headers,
        method: 'POST',
      );
      
      // 详细打印完整的响应数据
      LoggerUtil.i('└───> 视频上传完成响应数据: $response');
      
      if (response.containsKey('DownloadURL')) {
        // 修改下载URL，修复路径问题并替换域名
        String downloadUrl = response['DownloadURL'].toString();
        
        // 调试信息
        LoggerUtil.i('└───> 原始下载URL: $downloadUrl');
        
        // 使用辅助方法修复URL
        downloadUrl = fixVideoUrl(downloadUrl);
        
        LoggerUtil.i('└───> 视频上传完成，获取到下载URL: $downloadUrl');
        LoggerUtil.i('└───> 视频上传完整返回数据: ${response.toString()}');
        
        // 保存下载URL到uploadInfo - 与TypeScript保持一致
        uploadInfo['downloadUrl'] = downloadUrl;
        
        return true;
      } else {
        final errorMsg = response['errMsg'] ?? response['errmsg'] ?? response['X-Errno'] ?? '未知错误';
        LoggerUtil.e('└───> 完成视频上传失败: $errorMsg');
        LoggerUtil.e('└───> 错误响应完整数据: $response');
        return false;
      }
    } catch (e) {
      LoggerUtil.e('└───> 完成视频上传异常: $e');
      return false;
    }
  }
  
  /// 上传视频封面
  static Future<String?> uploadCoverFile(
    String cookieStr,
    Map<String, dynamic> uploadParams,
    File coverFile,
  ) async {
    try {
      LoggerUtil.i('【视频号API】上传视频封面: ${coverFile.path}');
      LoggerUtil.i('【视频号API】封面上传参数: $uploadParams');
      
      // 读取封面文件内容
      final coverBytes = await coverFile.readAsBytes();
      final fileSize = coverBytes.length;
      final taskId = generateUniqueTaskId();
      
      LoggerUtil.i('【视频号API】封面文件大小: $fileSize 字节');
      
      // 构建X-Arguments参数字符串 - 与curl命令保持一致
      final arguments = {
        'apptype': uploadParams['appType'] ?? '251',
        'filetype': uploadParams['pictureFileType'], // 强制使用image/jpeg，这是微信视频号要求的格式
        'weixinnum': uploadParams['uin'],
        'filekey': 'finder_video_img.jpeg', // 固定名称，与TypeScript保持一致
        'filesize': fileSize,
        'taskid': taskId,
        'scene': uploadParams['scene'] ?? '2',
      };
      
      String argumentsString = '';
      arguments.forEach((key, value) {
        if (value != null) {
          argumentsString += '$key=$value&';
        }
      });
      
      // 去掉最后一个&符号
      if (argumentsString.endsWith('&')) {
        argumentsString = argumentsString.substring(0, argumentsString.length - 1);
      }
      
      LoggerUtil.i('【视频号API】封面上传X-Arguments: $argumentsString');
      
      // 构建请求体 - 与TypeScript保持一致
      final requestBody = {
        'BlockPartLength': [fileSize], // 单个分块
        'BlockSum': 1,
      };
      
      // 设置请求头 - 与curl命令保持一致
      final headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Arguments': argumentsString,
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      };
      
      // 如果uploadParams包含authKey，添加到请求头
      if (uploadParams.containsKey('authKey') && uploadParams['authKey'] != null && uploadParams['authKey'].toString().isNotEmpty) {
        headers['Authorization'] = uploadParams['authKey'];
        LoggerUtil.i('【视频号API】添加Authorization头: ${uploadParams['authKey']}');
      }
      
      // 1. 申请上传 - 使用uploadFile方法
      // 修改域名：从finderassistancea改为finderassistancee
      const applyUrl = 'https://finderassistancee.video.qq.com/applyuploaddfs';
      
      LoggerUtil.i('【视频号API】发送申请上传封面请求: $applyUrl');
      LoggerUtil.i('【视频号API】请求头: $headers');
      LoggerUtil.i('【视频号API】请求体: $requestBody');
      
      final uploadIdRes = await uploadFile(
        url: applyUrl,
        fileContent: Uint8List.fromList(utf8.encode(jsonEncode(requestBody))),
        cookieStr: cookieStr,
        headers: headers,
        method: 'POST',
      );
      
      LoggerUtil.i('【视频号API】申请上传封面响应: $uploadIdRes');
      
      if (!uploadIdRes.containsKey('UploadID')) {
        LoggerUtil.e('【视频号API】申请上传封面失败: 未获取到UploadID');
        return null;
      }
      
      final uploadId = uploadIdRes['UploadID'];
      
      // 2. 上传封面文件 - 使用uploadFile方法
      // 注意URL格式与curl命令保持一致，参数顺序是PartNumber、UploadID、QuickUpload
      // 修改域名：从finderassistancea改为finderassistancee
      final uploadUrl = 'https://finderassistancee.video.qq.com/uploadpartdfs?PartNumber=1&UploadID=$uploadId&QuickUpload=2';
      
      // 创建请求头 - 与curl命令保持一致
      final uploadHeaders = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/octet-stream',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Arguments': argumentsString,
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      };
      
      // 如果uploadParams包含authKey，添加到请求头
      if (uploadParams.containsKey('authKey') && uploadParams['authKey'] != null && uploadParams['authKey'].toString().isNotEmpty) {
        uploadHeaders['Authorization'] = uploadParams['authKey'];
      }
      
      // 尝试计算Content-MD5并添加到请求头
      try {
        final digest = await compute(calculateMd5, coverBytes);
        if (digest != null) {
          uploadHeaders['Content-MD5'] = digest;
          LoggerUtil.i('【视频号API】已添加Content-MD5: $digest');
        }
      } catch (e) {
        LoggerUtil.w('【视频号API】无法计算Content-MD5: $e');
      }
      
      LoggerUtil.i('【视频号API】上传封面URL: $uploadUrl');
      LoggerUtil.i('【视频号API】上传封面请求头: $uploadHeaders');
      
      // 上传封面
      final uploadPartRes = await uploadFile(
        url: uploadUrl,
        fileContent: coverBytes,
        cookieStr: cookieStr,
        headers: uploadHeaders,
        method: 'PUT', // 使用PUT方法，与curl命令保持一致
      );
      
      LoggerUtil.i('【视频号API】上传封面响应: $uploadPartRes');
      
      if (!uploadPartRes.containsKey('ETag')) {
        LoggerUtil.e('【视频号API】上传封面失败: 未获取到ETag');
        return null;
      }
      
      // 上传成功，准备完成上传请求
      final uploadPartInfo = [
        {
          'PartNumber': 1,
          'ETag': uploadPartRes['ETag'],
        },
      ];
      
      // 3. 完成上传 - 使用uploadFile方法
      // 修改域名：从finderassistancea改为finderassistancee
      final completeUrl = 'https://finderassistancee.video.qq.com/completepartuploaddfs?UploadID=$uploadId';
      
      // 构建完成上传的请求体
      final completeRequestBody = {
        'TransFlag': '0_0',
        'PartInfo': uploadPartInfo,
      };
      
      LoggerUtil.i('【视频号API】完成上传URL: $completeUrl');
      LoggerUtil.i('【视频号API】完成上传请求头: $headers');
      LoggerUtil.i('【视频号API】完成上传请求体: $completeRequestBody');
      
      final completeResponse = await uploadFile(
        url: completeUrl,
        fileContent: Uint8List.fromList(utf8.encode(jsonEncode(completeRequestBody))),
        cookieStr: cookieStr,
        headers: headers,
        method: 'POST',
      );
      
      LoggerUtil.i('【视频号API】完成上传响应: $completeResponse');
      
      if (completeResponse.containsKey('DownloadURL')) {
        // 修改下载URL，修复路径问题并替换域名
        String downloadUrl = completeResponse['DownloadURL'].toString();
        
        // 调试信息
        LoggerUtil.i('└───> 原始下载URL: $downloadUrl');
        
        // 使用辅助方法修复URL
        // downloadUrl = fixVideoUrl(downloadUrl);
        
        LoggerUtil.i('└───> 视频上传完成，获取到下载URL: $downloadUrl');
        LoggerUtil.i('└───> 视频上传完整返回数据: ${completeResponse.toString()}');

        return downloadUrl;
      } else {
        final errorMsg = completeResponse['errMsg'] ?? completeResponse['errmsg'] ?? completeResponse['X-Errno'] ?? '未知错误';
        LoggerUtil.e('└───> 完成视频上传失败: $errorMsg');
        LoggerUtil.e('└───> 错误响应完整数据: $completeResponse');
        return null;
      }
    } catch (e) {
      LoggerUtil.e('└───> 上传封面异常: $e');
      return null;
    }
  }
  
  /// 创建视频发布
  static Future<Map<String, dynamic>?> createVideoPost(
    String cookieStr,
    String traceKey,
    int startUploadTime,
    int endUploadTime,
    Map<String, dynamic> clipResult,
    WxSphPublishParams publishParams,
  ) async {
    try {
      LoggerUtil.i('【视频号API】创建视频发布');
      LoggerUtil.i('【视频号API】发布参数: ${publishParams.toJson()}');
      LoggerUtil.i('【视频号API】裁剪结果: $clipResult');

      // 验证关键字段
      if (clipResult['clipKey'] == null || clipResult['clipKey'].toString().isEmpty) {
        LoggerUtil.e('【视频号API】clipKey为空，无法发布');
        throw Exception('clipKey为空，无法发布');
      }

      if (clipResult['url'] == null || clipResult['url'].toString().isEmpty) {
        LoggerUtil.e('【视频号API】视频URL为空，无法发布');
        throw Exception('视频URL为空，无法发布');
      }

      // 检查封面URL
      LoggerUtil.i('【视频号API】clipResult中的thumbUrl: ${clipResult['thumbUrl']}');
      LoggerUtil.i('【视频号API】publishParams中的cover: ${publishParams.cover}');

      // 确保使用网络URL作为封面
      String coverUrl = '';
      if (clipResult['thumbUrl'] != null && clipResult['thumbUrl'].toString().startsWith('http')) {
        coverUrl = clipResult['thumbUrl'].toString();
      } else if (publishParams.cover != null && publishParams.cover!.startsWith('http')) {
        coverUrl = publishParams.cover!;
      } else {
        LoggerUtil.w('【视频号API】没有找到有效的网络封面URL，将使用空字符串');
      }

      LoggerUtil.i('【视频号API】最终使用的封面URL: $coverUrl');

      // 生成随机的aid和rid参数 - 与成功的curl请求保持一致
      final aid = '${_generateRandomHex(8)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(12)}';
      final rid = '${_generateRandomHex(8)}-${_generateRandomHex(8)}';

      // 使用与成功curl请求一致的URL格式
      final url = 'https://channels.weixin.qq.com/micro/content/cgi-bin/mmfinderassistant-bin/post/post_create?_aid=$aid&_rid=$rid&_pageUrl=${Uri.encodeComponent("https://channels.weixin.qq.com/micro/content/post/create")}';

      LoggerUtil.i('【视频号API】创建视频发布URL: $url');

      // 处理标题、@好友、话题
      String description = publishParams.title ?? '';
      final topics = <String>[];
      final mentionedUser = <Map<String, dynamic>>[];

      // 处理话题
      if (publishParams.topics != null && publishParams.topics!.isNotEmpty) {
        for (final topic in publishParams.topics!) {
          description += ' #$topic';
          topics.add(topic);
        }
      }

      // 处理@用户 - 与桌面端保持一致
      if (publishParams.mentionedUserInfo != null && publishParams.mentionedUserInfo!.isNotEmpty) {
        for (final userInfo in publishParams.mentionedUserInfo!) {
          if (userInfo.nickName != null && userInfo.nickName!.isNotEmpty) {
            description += ' @${userInfo.nickName}';
            mentionedUser.add({
              'nickname': userInfo.nickName,
            });
          }
        }
      }

      // 处理合集 - 与成功的curl请求保持一致
      Map<String, dynamic> topicMix = {};
      if (publishParams.mixInfo != null &&
          publishParams.mixInfo!.mixId.isNotEmpty &&
          publishParams.mixInfo!.mixName.isNotEmpty) {
        topicMix = {
          'collectionId': publishParams.mixInfo!.mixId,
          'collectionName': publishParams.mixInfo!.mixName,
        };
      } else {
        // 使用与成功curl请求一致的finderTopicInfo格式
        topicMix = {
          'finderTopicInfo': '<finder><version>1</version><valuecount>1</valuecount><style><at></at></style><value0><![CDATA[${publishParams.title ?? ''}]]></value0></finder>',
        };
      }

      // 处理POI - 与桌面端保持一致
      Map<String, dynamic> poiInfo = {};
      if (publishParams.poiInfo != null && publishParams.poiInfo!.poiId.isNotEmpty) {
        poiInfo = {
          'latitude': publishParams.poiInfo!.latitude,
          'longitude': publishParams.poiInfo!.longitude,
          'city': publishParams.poiInfo!.poiCity,
          'poiName': publishParams.poiInfo!.poiName,
          'address': publishParams.poiInfo!.poiAddress,
          'poiClassifyId': publishParams.poiInfo!.poiId,
        };
      }

      // 构建请求数据 - 与成功的curl请求完全一致
      final requestData = {
        'objectType': 0,
        'longitude': 0,
        'latitude': 0,
        'feedLongitude': 0,
        'feedLatitude': 0,
        'originalFlag': 0,
        'topics': topics,
        'isFullPost': 1,
        'handleFlag': 2,
        'videoClipTaskId': clipResult['clipKey']?.toString() ?? '',
        'traceInfo': {
          'traceKey': traceKey,
          'uploadCdnStart': startUploadTime ~/ 1000, // 转换为秒
          'uploadCdnEnd': endUploadTime ~/ 1000, // 转换为秒
        },
        'traceInfo': {
          'traceKey': traceKey,
          'uploadCdnStart': startUploadTime,
          'uploadCdnEnd': endUploadTime,
        },
        'objectDesc': {
          'mpTitle': '',
          'description': description,
          'extReading': {},
          'mediaType': 4,
          'location': poiInfo,
          'topic': topicMix,
          'event': publishParams.event ?? {},
          'mentionedUser': mentionedUser,
          'media': [
            {
              'url': clipResult['url'],
              'fileSize': clipResult['fileSize']?.toString() ?? '0', // 转为字符串，与curl请求一致
              'thumbUrl': clipResult['thumbUrl'] ?? publishParams.cover, // 使用clipResult中的thumbUrl
              'fullThumbUrl': clipResult['thumbUrl'] ?? publishParams.cover, // 使用clipResult中的thumbUrl
              'mediaType': 4,
              'videoPlayLen': clipResult['duration'],
              'width': clipResult['width'],
              'height': clipResult['height'],
              'md5sum': generateUniqueTaskId(), // 生成新的UUID
              'cardShowStyle': 2, // 添加与curl请求一致的字段
              'coverUrl': clipResult['thumbUrl'] ?? publishParams.cover, // 使用网络URL，不是本地路径
              'fullCoverUrl': clipResult['thumbUrl'] ?? publishParams.cover, // 使用网络URL，不是本地路径
              'urlCdnTaskId': clipResult['clipKey'],
            },
          ],
          'postFlag': publishParams.postFlag,
          'member': {},
        },
        'report': {
          'clipKey': clipResult['clipKey'],
          'draftId': clipResult['clipKey'],
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
          '_log_finder_uin': '',
          '_log_finder_id': 'v2_060000231003b20faec8c7e48910cad4cb07e530b077af00753ab89cdab7a2294556d14fa4c9@finder',
          'rawKeyBuff': null,
          'pluginSessionId': null,
          'scene': 7,
          'reqScene': 7,
          'height': clipResult['height'],
          'width': clipResult['width'],
          'duration': clipResult['duration'],
          'fileSize': clipResult['fileSize'],
          'uploadCost': endUploadTime - startUploadTime,
        },
        'postFlag': publishParams.postFlag ?? 0,
        'mode': 1,
        'clientid': generateUniqueTaskId(), // 生成新的UUID
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(), // 转为字符串，与curl一致
        '_log_finder_uin': '',
        '_log_finder_id': 'v2_060000231003b20faec8c7e48910cad4cb07e530b077af00753ab89cdab7a2294556d14fa4c9@finder',
        'rawKeyBuff': null,
        'pluginSessionId': null,
        'scene': 7,
        'reqScene': 7,
      };

      // 处理定时发布 - 与桌面端保持一致
      if (publishParams.timingTime != null && publishParams.timingTime! > DateTime.now().millisecondsSinceEpoch) {
        requestData['effectiveTime'] = publishParams.timingTime! ~/ 1000; // 转换为秒级时间戳
      }

      LoggerUtil.i('【视频号API】创建视频发布请求数据: ${jsonEncode(requestData)}');

      // 打印关键字段用于调试
      LoggerUtil.i('【视频号API】关键字段检查:');
      LoggerUtil.i('  - videoClipTaskId: ${requestData['videoClipTaskId']}');
      LoggerUtil.i('  - objectType: ${requestData['objectType']}');
      LoggerUtil.i('  - handleFlag: ${requestData['handleFlag']}');
      LoggerUtil.i('  - isFullPost: ${requestData['isFullPost']}');
      // LoggerUtil.i('  - media count: ${requestData['objectDesc']['media']?.length}');
      // LoggerUtil.i('  - media url: ${requestData['objectDesc']['media']?[0]?['url']}');
      // LoggerUtil.i('  - media urlCdnTaskId: ${requestData['objectDesc']['media']?[0]?['urlCdnTaskId']}');
      LoggerUtil.i('【视频号API】请求URL: $url');
      LoggerUtil.i('【视频号API】Cookie长度: ${cookieStr.length}');

      // 检查Cookie中的关键字段
      final cookieFields = ['wxuin', 'mm_lang', 'finder_username'];
      for (final field in cookieFields) {
        if (cookieStr.contains(field)) {
          LoggerUtil.i('【视频号API】Cookie包含字段: $field');
        } else {
          LoggerUtil.w('【视频号API】Cookie缺少字段: $field');
        }
      }

      // 从Cookie中提取微信UIN
      final wechatUin = extractWechatUinFromCookie(cookieStr);

      // 使用与成功curl请求完全一致的请求头
      final response = await _dio.post(
        url,
        data: jsonEncode(requestData),
        options: Options(
          headers: {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Cookie': cookieStr,
            'Origin': 'https://channels.weixin.qq.com',
            'Referer': 'https://channels.weixin.qq.com/micro/content/post/create',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'X-WECHAT-UIN': wechatUin.isNotEmpty ? wechatUin : '2776714997',
            'finger-print-device-id': 'c4db9fe872282a20230a90d89afcd682',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
          },
          responseType: ResponseType.json,
          receiveTimeout: const Duration(seconds: 15),
          sendTimeout: const Duration(seconds: 15),
        ),
      );

      LoggerUtil.i('【视频号API】创建视频发布响应状态: ${response.statusCode}');
      LoggerUtil.i('【视频号API】创建视频发布响应数据: ${response.data}');

      if (response.statusCode == 200 && response.data['errCode'] == 0) {
        // 检查baseResp字段 - 与成功的curl请求保持一致
        if (response.data['data'] != null &&
            response.data['data']['baseResp'] != null &&
            response.data['data']['baseResp']['errcode'] == 0) {
          LoggerUtil.i('【视频号API】创建视频发布成功');

          // 获取最新发布的ID和分享链接
          final lastWorkInfo = await getLastPublishId(cookieStr);

          if (lastWorkInfo != null) {
            return {
              'lastPublishId': lastWorkInfo['lastPublishId'] ?? '',
              'previewVideoLink': lastWorkInfo['previewVideoLink'] ?? '',
            };
          } else {
            LoggerUtil.e('【视频号API】获取发布ID失败');
            return null;
          }
        } else {
          final errMsg = response.data['data']?['baseResp']?['errmsg'] ?? '未知错误';
          LoggerUtil.e('【视频号API】创建视频发布失败: $errMsg');
          return null;
        }
      } else {
        final errMsg = response.data['errMsg'] ?? '发布失败';
        LoggerUtil.e('【视频号API】创建视频发布失败: $errMsg');
        throw Exception('发布失败,失败原因:$errMsg');
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【视频号API】创建视频发布异常: $e');
      LoggerUtil.e('【视频号API】异常堆栈: $stackTrace');
      return null;
    }
  }
  
  /// 获取最新发布的视频ID和预览链接 - 与成功的curl请求保持一致
  static Future<Map<String, dynamic>?> getLastPublishId(String cookieStr) async {
    try {
      LoggerUtil.i('【视频号API】获取最新发布ID');

      // 生成随机的aid和rid参数
      final aid = '${_generateRandomHex(8)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(12)}';
      final rid = '${_generateRandomHex(8)}-${_generateRandomHex(8)}';

      // 使用与成功curl请求一致的URL格式
      final workListUrl = 'https://channels.weixin.qq.com/micro/content/cgi-bin/mmfinderassistant-bin/post/post_list?_aid=$aid&_rid=$rid&_pageUrl=${Uri.encodeComponent("https://channels.weixin.qq.com/micro/content/post/create")}';

      // 构建请求体 - 与桌面端完全一致
      final data = {
        'pageSize': 20,
        'currentPage': 1,
        'rawKeyBuff': null,
        'pluginSessionId': null,
        'scene': 7,
        'reqScene': 7,
      };

      // 从Cookie中提取微信UIN
      final wechatUin = extractWechatUinFromCookie(cookieStr);

      // 发起请求 - 使用与成功curl请求一致的方式
      final response = await _dio.post(
        workListUrl,
        data: jsonEncode(data),
        options: Options(
          headers: {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Cookie': cookieStr,
            'Origin': 'https://channels.weixin.qq.com',
            'Referer': 'https://channels.weixin.qq.com/micro/content/post/create',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'X-WECHAT-UIN': wechatUin.isNotEmpty ? wechatUin : '2776714997',
            'finger-print-device-id': 'c4db9fe872282a20230a90d89afcd682',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
          },
          responseType: ResponseType.json,
        ),
      );

      if (response.statusCode != 200 || response.data['errCode'] != 0) {
        LoggerUtil.e('【视频号API】获取作品列表失败: ${response.data['errMsg']}');
        return null;
      }

      // 检查是否有作品列表
      final workList = response.data['data']?['list'] as List?;
      if (workList == null || workList.isEmpty) {
        LoggerUtil.e('【视频号API】未找到任何作品');
        return null;
      }

      // 获取最新的一条作品
      final work = workList[0];

      LoggerUtil.i('【视频号API】最新发布作品ID: ${work['exportId']}');

      // 获取预览链接 - 使用与成功curl请求一致的格式
      final previewAid = '${_generateRandomHex(8)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(12)}';
      final previewRid = '${_generateRandomHex(8)}-${_generateRandomHex(8)}';
      final previewUrl = 'https://channels.weixin.qq.com/micro/content/cgi-bin/mmfinderassistant-bin/post/get_object_short_link?_aid=$previewAid&_rid=$previewRid&_pageUrl=${Uri.encodeComponent("https://channels.weixin.qq.com/micro/content/post/create")}';

      final previewData = {
        'exportId': work['exportId'],
        'nonceId': work['objectNonce'],
      };

      // 发起请求获取预览链接
      final previewResponse = await _dio.post(
        previewUrl,
        data: jsonEncode(previewData),
        options: Options(
          headers: {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Cookie': cookieStr,
            'Origin': 'https://channels.weixin.qq.com',
            'Referer': 'https://channels.weixin.qq.com/micro/content/post/create',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'X-WECHAT-UIN': wechatUin.isNotEmpty ? wechatUin : '2776714997',
            'finger-print-device-id': 'c4db9fe872282a20230a90d89afcd682',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
          },
          responseType: ResponseType.json,
        ),
      );

      String previewLink = '';
      if (previewResponse.statusCode == 200 && previewResponse.data['errCode'] == 0) {
        previewLink = previewResponse.data['data']?['shortLink'] ?? '';
        LoggerUtil.i('【视频号API】获取预览链接成功: $previewLink');
      } else {
        LoggerUtil.e('【视频号API】获取预览链接失败: ${previewResponse.data['errMsg']}');
      }

      // 返回结果 - 与桌面端保持一致的格式
      return {
        'lastPublishId': work['desc']?['media']?[0]?['md5sum'] ?? work['exportId'] ?? '',
        'previewVideoLink': previewLink,
      };
    } catch (e) {
      LoggerUtil.e('【视频号API】获取最新发布ID异常: $e');
      return null;
    }
  }
  
  static Future<bool> checkLoginStatus(String cookieStr) async {
    try {
      final userInfo = await getUserInfo(cookieStr);
      return userInfo != null;
    } catch (e) {
      LoggerUtil.e('【视频号API】检查登录状态异常: $e');
      return false;
    }
  }
  
  /// 视频作品发布
  static Future<Map<String, dynamic>> publishVideoWorkApi({
    required String cookieStr,
    required String filePath,
    required WxSphPublishParams platformSetting,
    required Function(int progress, String? msg) callback,
  }) async {
    try {
      LoggerUtil.i('┌─────────────── 视频号发布流程开始 ───────────────┐');
      LoggerUtil.i('视频文件: $filePath');
      LoggerUtil.i('发布参数: ${platformSetting.toJson()}');
      
      // 步骤1: 加载中...
      callback(5, '加载中...');
      
      // 获取视频文件信息
      final fileInfo = await DoiUtil.getVideoInfo(filePath);
      if (fileInfo == null) {
        LoggerUtil.e('└───> 获取视频文件信息失败');
        throw Exception('获取视频文件信息失败');
      }
      LoggerUtil.i('└───> 视频信息: $fileInfo');
      callback(10, null);
      
      // 步骤2: 获取发布追踪密钥
      LoggerUtil.i('├───> 阶段2: 获取发布追踪密钥');
      final traceKey = await getPublishTraceKey(cookieStr);
      if (traceKey == null) {
        LoggerUtil.e('└───> 获取发布追踪密钥失败');
        throw Exception('获取发布追踪密钥失败');
      }
      LoggerUtil.i('└───> 获取到追踪密钥: $traceKey');
      callback(15, null);
      
      // 步骤3: 获取上传参数
      LoggerUtil.i('├───> 阶段3: 获取上传参数');
      final uploadParams = await getUploadParams(cookieStr);
      LoggerUtil.i('└───> 获取到上传参数');
      callback(20, null);
      
      // 步骤4: 计算文件分块信息
      LoggerUtil.i('├───> 阶段4: 计算文件分块信息');
      final filePartInfo = await DoiUtil.getFilePartInfo(filePath, fileBlockSize);
      if (filePartInfo == null) {
        LoggerUtil.e('└───> 计算文件分块信息失败');
        throw Exception('计算文件分块信息失败');
      }
      LoggerUtil.i('└───> 文件分块信息: 总大小=${filePartInfo['fileSize']}字节, 分块数=${filePartInfo['blockCount']}');
      callback(25, null);
      
      // 步骤5: 开始上传视频
      final startUploadTime = DateTime.now().millisecondsSinceEpoch ~/ 1000; // 秒级时间戳
      LoggerUtil.i('├───> 阶段5: 上传视频 (开始时间: $startUploadTime)');
      
      // 申请上传视频
      final uploadInfo = await applyVideoUpload(cookieStr, uploadParams, File(filePath));
      if (uploadInfo == null) {
        LoggerUtil.e('└───> 申请上传视频失败');
        throw Exception('申请上传视频失败');
      }
      LoggerUtil.i('└───> 申请上传视频成功');
      
      // 分块上传视频
      callback(30, '正在上传视频...');
      final videoFile = File(filePath);
      final blockInfo = filePartInfo['blockInfo'] as List<int>;
      
      LoggerUtil.i('├───> 开始分块上传视频 (${blockInfo.length}个分块)');
      for (int i = 0; i < blockInfo.length; i++) {
        // 计算分块范围 - 严格按照index.ts实现
        final chunkStart = i == 0 ? 0 : blockInfo[i - 1];
        final chunkEnd = blockInfo[i] - 1;
        
        LoggerUtil.i('├───> 上传分块 ${i+1}/${blockInfo.length} ($chunkStart-$chunkEnd)');
        
        // 读取分块数据
        final chunkData = await DoiUtil.getFilePartContent(filePath, chunkStart, chunkEnd);
        if (chunkData == null) {
          LoggerUtil.e('└───> 读取视频分块数据失败');
          throw Exception('读取视频分块数据失败');
        }
        
        // 计算上传进度
        final progressStart = 30 + ((i * 20) ~/ blockInfo.length);
        final progressEnd = 30 + (((i + 1) * 20) ~/ blockInfo.length);
        
        // 上传分块
        final uploadSuccess = await uploadVideoChunk(
          cookieStr,
          uploadInfo,
          videoFile,
          i,
          blockInfo.length,
          chunkData,
          (progress, message) {
            // 计算实际进度，确保在分配的范围内
            final actualProgress = progressStart + ((progressEnd - progressStart) * progress ~/ 100);
            callback(actualProgress, message);
          },
        );
        
        if (!uploadSuccess) {
          LoggerUtil.e('└───> 上传视频分块 ${i+1}/${blockInfo.length} 失败');
          throw Exception('上传视频分块失败');
        }
        
        LoggerUtil.i('└───> 上传分块 ${i+1}/${blockInfo.length} 成功');
      }
      
      // 完成视频上传
      LoggerUtil.i('├───> 完成视频上传');
      final completeSuccess = await completeVideoUpload(cookieStr, uploadInfo);
      if (!completeSuccess) {
        LoggerUtil.e('└───> 完成视频上传失败');
        throw Exception('完成视频上传失败');
      }
      
      final remoteVideoUrl = uploadInfo['downloadUrl'];
      LoggerUtil.i('└───> 视频上传成功: URL地址=$remoteVideoUrl');
      
      final endUploadTime = DateTime.now().millisecondsSinceEpoch ~/ 1000; // 秒级时间戳
      LoggerUtil.i('├───> 视频上传完成 (耗时: ${endUploadTime - startUploadTime}秒)');
      
      // 步骤6: 提交剪辑任务
      LoggerUtil.i('├───> 阶段6: 提交视频处理任务');
      callback(50, '正在处理视频...');
      final clipResult = await clipVideo(
        cookieStr,
        traceKey,
        startUploadTime,
        endUploadTime,
        remoteVideoUrl,
        {
          'width': fileInfo['width'],
          'height': fileInfo['height'],
          'size': fileInfo['size'],
          'duration': fileInfo['duration'],
        },
      );
      
      if (clipResult == null) {
        LoggerUtil.e('└───> 提交视频处理任务失败');
        throw Exception('提交视频处理任务失败');
      }
      LoggerUtil.i('└───> 提交视频处理任务成功: clipKey=${clipResult['clipKey']}');
      
      // 步骤7: 上传封面
      LoggerUtil.i('├───> 阶段7: 上传视频封面');
      callback(60, '正在上传封面...');
      final coverUrl = await uploadCoverFile(cookieStr, uploadParams, File(platformSetting.cover));
      if (coverUrl == null) {
        LoggerUtil.e('└───> 上传封面失败');
        throw Exception('上传封面失败');
      }
      LoggerUtil.i('└───> 封面上传成功: $coverUrl');

      // 更新封面URL
      platformSetting.cover = coverUrl;
      
      // 步骤8: 获取裁剪结果
      LoggerUtil.i('├───> 阶段8: 获取视频处理结果');
      callback(70, '等待视频处理...');
      
      String? clipId = clipResult['clipKey'];
      if (clipId == null) {
        LoggerUtil.e('└───> 裁剪ID为空');
        throw Exception('裁剪ID为空');
      }
      
      LoggerUtil.i('└───> 裁剪ID: $clipId');
      Map<String, dynamic>? processedResult;
      
      // 轮询获取裁剪结果
      int retryCount = 0;
      const maxRetries = 60; // 增加最大重试次数
      bool isSuccess = false;
      
      LoggerUtil.i('├───> 开始轮询处理结果 (最多$maxRetries次)');
      while (retryCount < maxRetries) {
        retryCount++;
        LoggerUtil.i('├───> 轮询次数: $retryCount/$maxRetries');
        
        // 进度更新
        callback(70 + (retryCount * 10 ~/ maxRetries), '等待视频处理结果 $retryCount/$maxRetries');
        
        final result = await getClipResult(cookieStr, clipId);
        
        if (result == null) {
          LoggerUtil.i('└───> 获取处理结果为空，继续轮询');
          await Future.delayed(const Duration(seconds: 3));
          continue;
        }
        
        // 如果结果中包含视频URL，检查并修复URL格式
        if (result.containsKey('url') && result['url'] != null && result['url'].toString().isNotEmpty) {
          String videoUrl = result['url'].toString();
          LoggerUtil.i('└───> 处理结果中的原始视频URL: $videoUrl');
          
          // 使用辅助方法修复URL
          // videoUrl = fixVideoUrl(videoUrl);
          result['url'] = videoUrl;
        }
        
        // 如果结果中没有status字段，说明接口格式可能变化
        if (!result.containsKey('status')) {
          LoggerUtil.e('└───> 处理结果缺少status字段: $result');
          
          // 检查是否包含flag字段（新版API可能使用flag代替status）
          if (result.containsKey('flag')) {
            final flag = result['flag'];
            LoggerUtil.i('└───> 检测到flag字段: $flag');
            
            // flag=1表示成功
            if (flag == 1) {
              LoggerUtil.i('└───> 根据flag判断视频处理成功');
              
              // 构建兼容的结果对象
              processedResult = {
                'clipKey': clipId,
                'url': result['url'] ?? '',
                'fileSize': result['fileSize'] ?? result['size'] ?? 0,
                'duration': result['duration'] ?? 0,
                'width': result['width'] ?? 0,
                'height': result['height'] ?? 0,
              };
              isSuccess = true;
              break;
            }
          }
        } else {
          // 检查状态
          final status = result['status'];
          if (status == 2) {
            // 处理成功
            LoggerUtil.i('└───> 视频处理成功');
            processedResult = {
              'clipKey': clipId,
              'url': result['url'] ?? '',
              'fileSize': result['fileSize'] ?? result['size'] ?? 0,
              'duration': result['duration'] ?? 0,
              'width': result['width'] ?? 0,
              'height': result['height'] ?? 0,
            };
            isSuccess = true;
            break;
          } else if (status == 3) {
            // 处理失败
            final errMsg = result['errMsg'] ?? '未知错误';
            final errCode = result['errCode'] ?? 0;
            LoggerUtil.e('└───> 视频处理失败: $errMsg (错误码: $errCode)');
            
            // 特殊处理300800错误 - 如果是Cookie无效，抛出特定异常以便上层重新授权
            if (errCode == 300800) {
              throw Exception('Cookie无效或已过期，请重新授权: $errMsg');
            }
            
            throw Exception('视频处理失败: $errMsg');
          }
        }
        
        // 处理中，继续轮询
        LoggerUtil.i('└───> 视频处理中，继续轮询');
        
        // 增加重试间隔时间，避免请求频率过高
        int waitTime = 3;
        if (retryCount > 10) waitTime = 5;
        if (retryCount > 20) waitTime = 8;
        if (retryCount > 40) waitTime = 10;
        
        await Future.delayed(Duration(seconds: waitTime));
      }
      
      if (!isSuccess) {
        LoggerUtil.e('└───> 获取视频处理结果超时');
        throw Exception('获取视频处理结果超时，请检查网络连接和Cookie有效性');
      }
      
      LoggerUtil.i('└───> 视频处理完成');
      
      // 步骤9: 发布视频
      LoggerUtil.i('├───> 阶段9: 发布视频');
      callback(90, '正在发布视频...');
      
      // 确保处理结果非空
      if (processedResult == null) {
        LoggerUtil.e('└───> 处理结果为空，无法发布视频');
        throw Exception('视频处理失败，无法发布视频');
      }
      
      // 记录完整的最终发布参数
      final finalPublishParams = {...platformSetting.toJson()};
      finalPublishParams['cover'] = platformSetting.cover;
      LoggerUtil.i('└───> 最终发布参数: $finalPublishParams');
      
      final publishResult = await createVideoPost(
        cookieStr,
        traceKey,
        startUploadTime,
        endUploadTime,
        processedResult,
        platformSetting,
      );
      
      if (publishResult == null) {
        LoggerUtil.e('└───> 发布视频失败');
        throw Exception('发布视频失败');
      }
      
      // 完成
      LoggerUtil.i('└───> 发布视频成功: publishId=${publishResult['publishId']}');
      LoggerUtil.i('└───> 发布视频成功，完整结果数据: ${jsonEncode(publishResult)}');
      LoggerUtil.i('└─────────────── 视频号发布流程结束 ───────────────┘');
      callback(100, '发布成功');
      
      final Map<String, dynamic> finalResult = {
        'publishTime': publishResult['publishTime'],
        'publishId': publishResult['publishId'],
        'shareLink': publishResult['shareLink'],
      };
      
      // 添加完整数据供调试
      if (publishResult.containsKey('fullData')) {
        finalResult['fullData'] = Map<String, dynamic>.from(publishResult['fullData']);
      }
      
      LoggerUtil.i('视频号上传发布最终结果: $finalResult');
      return finalResult;
    } catch (e) {
      LoggerUtil.e('视频号发布失败: $e');
      LoggerUtil.i('└─────────────── 视频号发布流程结束 [异常] ───────────────┘');
      throw Exception('发布视频作品失败: $e');
    }
  }
  
  static String _generateRandomHex(int length) {
    final random = Random();
    final buffer = StringBuffer();
    for (var i = 0; i < length; i++) {
      buffer.write(random.nextInt(16).toRadixString(16));
    }
    return buffer.toString();
  }

  static String extractWechatUinFromCookie(String cookieStr) {
    try {
      if (cookieStr.isEmpty) {
        return '';
      }

      final cookies = cookieStr.split(';');
      for (var cookie in cookies) {
        cookie = cookie.trim();

        if (cookie.startsWith('wxuin=')) {
          final uin = cookie.substring('wxuin='.length);
          return uin;
        }
      }

      for (var cookie in cookies) {
        cookie = cookie.trim();

        if (cookie.startsWith('passport_uin=')) {
          final uin = cookie.substring('passport_uin='.length);
          return uin;
        }
      }

      for (var cookie in cookies) {
        cookie = cookie.trim();

        if (cookie.startsWith('o_cookie=')) {
          final uin = cookie.substring('o_cookie='.length);
          return uin;
        }
      }

      return '';
    } catch (e) {
      LoggerUtil.e('【微信视频号】提取UIN异常: $e');
      return '';
    }
  }

  static String fixVideoUrl(String url) {
    if (url.isEmpty) return url;

    String fixedUrl = url;

    if (fixedUrl.startsWith('http://')) {
      fixedUrl = fixedUrl.replaceAll('http://', 'https://');
    }

    if (fixedUrl.contains('wxapp.tc.qq.com')) {
      fixedUrl = fixedUrl.replaceAll('wxapp.tc.qq.com', 'finder.video.qq.com');
    }

    return fixedUrl;
  }
  
  /// 裁剪上传的视频
  static Future<Map<String, dynamic>?> clipVideo(
    String cookieStr,
    String traceKey,
    int startUploadTime,
    int endUploadTime,
    String remoteFileUrl,
    Map<String, dynamic> fileInfo,
  ) async {
    try {
      LoggerUtil.i('├───> 开始裁剪视频');
      LoggerUtil.i('├───> 裁剪参数: traceKey=$traceKey, remoteFileUrl=$remoteFileUrl');
      LoggerUtil.i('├───> 文件信息: $fileInfo');
      LoggerUtil.i('├───> 上传时间: 开始=$startUploadTime, 结束=$endUploadTime');
      
      // 生成随机的aid和rid参数
      final aid = '${_generateRandomHex(8)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(12)}';
      final rid = '${_generateRandomHex(8)}-${_generateRandomHex(8)}';
      
      // 添加查询字符串参数到URL - 完全匹配curl命令中的URL
      final url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/post/post_clip_video?_aid=$aid&_rid=$rid&_pageUrl=${Uri.encodeComponent("https://channels.weixin.qq.com/micro/content/post/create")}';
      
      LoggerUtil.i('├───> 完整裁剪请求URL: $url');
      
      // 验证和完善fileInfo字段
      if (!fileInfo.containsKey('width') || !fileInfo.containsKey('height') || 
          !fileInfo.containsKey('size') || !fileInfo.containsKey('duration')) {
        LoggerUtil.e('└───> 裁剪视频失败: 文件信息不完整');
        return null;
      }
      
      // 确保视频时长至少为3秒，与TypeScript保持一致
      if (fileInfo['duration'] < 3) {
        LoggerUtil.e('└───> 裁剪视频失败: 视频时长不能小于3秒!');
        return null;
      }
      
      // 计算目标高度 - 保持纵横比
      const targetWidth = 1920;
      final targetHeight = (fileInfo['height'] * targetWidth / fileInfo['width']).round();
      
      // 从Cookie中提取微信UIN
      final wechatUin = extractWechatUinFromCookie(cookieStr);
      LoggerUtil.i('├───> 从Cookie中提取的微信UIN: $wechatUin');
      
      // 构建请求参数 - 完全按照curl命令中的JSON结构
      final data = {
        'url': remoteFileUrl,
        'timeStart': 0,
        'cropDuration': 0,
        'height': fileInfo['height'],
        'width': fileInfo['width'],
        'x': 0,
        'y': 0,
        'clipOriginVideoInfo': {
          'width': fileInfo['width'],
          'height': fileInfo['height'],
          'duration': fileInfo['duration'],
          'fileSize': fileInfo['size'],
        },
        'traceInfo': {
          'traceKey': traceKey,
          'uploadCdnStart': startUploadTime,
          'uploadCdnEnd': endUploadTime,
        },
        'targetWidth': targetWidth,
        'targetHeight': targetHeight,
        'type': 4,
        'useAstraThumbCover': 1, // 根据curl请求设置为1
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        '_log_finder_uin': '',
        '_log_finder_id': 'v2_060000231003b20faec8c7e48910cad4cb07e530b077af00753ab89cdab7a2294556d14fa4c9@finder',
        'rawKeyBuff': null,
        'pluginSessionId': null,
        'scene': 7,
        'reqScene': 7,
      };
      
      LoggerUtil.i('├───> 裁剪请求数据: $data');
      
      // 设置请求头 - 完全匹配curl命令中的所有头部
      final additionalHeaders = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/micro/content/post/create',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      };
      
      // 如果成功提取到UIN，添加到请求头
      if (wechatUin.isNotEmpty) {
        additionalHeaders['X-WECHAT-UIN'] = wechatUin;
      } else {
        // 如果无法从cookie提取，尝试使用固定值（仅用于调试）
        LoggerUtil.w('└───> 无法从Cookie提取UIN，尝试使用curl中的UIN');
        additionalHeaders['X-WECHAT-UIN'] = '2776714997'; // 来自curl命令
      }
      
      // 添加设备指纹ID（来自curl命令）
      additionalHeaders['finger-print-device-id'] = 'c4db9fe872282a20230a90d89afcd682';
      
      // 发送请求
      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
        additionalHeaders: additionalHeaders,
      );
      
      LoggerUtil.i('├───> 裁剪视频响应: $response');
      LoggerUtil.i('├───> 裁剪视频完整返回数据: ${jsonEncode(response)}');
      
      if (response['errCode'] == 0) {
        // 检查返回的数据中是否包含clipKey字段
        if (response['data'] != null && response['data'].containsKey('clipKey')) {
          final clipKey = response['data']['clipKey'];
          LoggerUtil.i('└───> 获取到裁剪ID: $clipKey');
          LoggerUtil.i('└───> 裁剪成功完整数据: ${jsonEncode(response['data'])}');
          
          // 构建返回结果，与TypeScript中的返回值结构完全一致
          final Map<String, dynamic> result = {
            'clipKey': clipKey,  // 使用clipKey作为键名，与TypeScript保持一致
            'url': remoteFileUrl,
            'fileSize': fileInfo['size'],
            'duration': fileInfo['duration'].ceil(), // 向上取整，与TypeScript保持一致
            'width': fileInfo['width'],
            'height': fileInfo['height'],
          };
          
          LoggerUtil.i('└───> 返回给调用方的裁剪结果: $result');
          return result;
        } else {
          LoggerUtil.e('└───> 裁剪视频响应中缺少clipKey字段');
          LoggerUtil.e('└───> 完整响应: $response');
          return null;
        }
      } else {
        final errMsg = response['errMsg'] ?? '未知错误';
        LoggerUtil.e('└───> 裁剪视频失败: $errMsg');
        LoggerUtil.e('└───> 错误响应: $response');
        return null;
      }
    } catch (e) {
      LoggerUtil.e('└───> 裁剪视频异常: $e');
      return null;
    }
  }
  
  /// 获取裁剪结果
  static Future<Map<String, dynamic>?> getClipResult(
    String cookieStr,
    String clipId,
  ) async {
    try {
      LoggerUtil.i('├───> 获取视频处理结果: clipId=$clipId');
      
      // 生成随机的aid和rid参数
      final aid = '${_generateRandomHex(8)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(4)}-${_generateRandomHex(12)}';
      final rid = '${_generateRandomHex(8)}-${_generateRandomHex(8)}';
      
      // 使用正确的URL，包含查询字符串参数
      final url = '$wxSphBaseUrl/cgi-bin/mmfinderassistant-bin/post/post_clip_video_result?_aid=$aid&_rid=$rid&_pageUrl=${Uri.encodeComponent("https://channels.weixin.qq.com/micro/content/post/create")}';
      
      // 从Cookie中提取微信UIN
      final wechatUin = extractWechatUinFromCookie(cookieStr);
      LoggerUtil.i('├───> 从Cookie中提取的微信UIN: $wechatUin');
      
      // 构建正确的请求参数 - 根据实际API请求参数调整
      final data = {
        'clipKey': clipId,
        'draftId': clipId,  // 添加draftId参数，值与clipKey相同
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'pluginSessionId': null,
        'rawKeyBuff': null,
        'reqScene': 7,
        'scene': 7,
        // 添加缺少的参数
        '_log_finder_id': 'v2_060000231003b20faec8c7e48910cad4cb07e530b077af00753ab89cdab7a2294556d14fa4c9@finder', // 添加有效值
        '_log_finder_uin': '', // 可选参数
      };
      
      // 设置请求头 - 更新以符合实际请求
      final additionalHeaders = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/micro/content/post/create', // 更新Referer
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', // 更新User-Agent
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
      };
      
      // 如果成功提取到UIN，添加到请求头
      if (wechatUin.isNotEmpty) {
        additionalHeaders['X-WECHAT-UIN'] = wechatUin;
      } else {
        // 如果无法从cookie提取，尝试使用固定值（仅用于调试）
        LoggerUtil.w('└───> 无法从Cookie提取UIN，尝试使用curl中的UIN');
        additionalHeaders['X-WECHAT-UIN'] = '2776714997'; // 来自curl命令
      }
      
      // 添加设备指纹ID（来自curl命令）
      additionalHeaders['finger-print-device-id'] = 'c4db9fe872282a20230a90d89afcd682';
      
      // 发送请求
      final response = await makeRequest(
        url: url,
        cookieStr: cookieStr,
        method: 'POST',
        data: data,
        additionalHeaders: additionalHeaders,
      );
      
      LoggerUtil.i('├───> 获取处理结果响应: $response');
      LoggerUtil.i('├───> 获取处理结果完整返回数据: ${jsonEncode(response)}');
      
      if (response['errCode'] == 0) {
        // 检查data字段是否存在
        if (!response.containsKey('data') || response['data'] == null) {
          LoggerUtil.e('└───> 获取处理结果响应中缺少data字段');
          return null;
        }
        
        final responseData = response['data'];
        LoggerUtil.i('└───> 处理结果data字段完整内容: ${jsonEncode(responseData)}');
        
        // 检查flag字段（实际返回中使用flag而非status）
        if (!responseData.containsKey('flag')) {
          LoggerUtil.e('└───> 处理结果缺少flag字段: $responseData');
          return null;
        }
        
        final flag = responseData['flag'];
        final flagText = flag == 1 ? '成功' : '处理中';
        LoggerUtil.i('└───> 处理状态: $flagText (flag=$flag)');
        
        // 转换fileSize为整数
        int fileSize = 0;
        if (responseData.containsKey('fileSize')) {
          if (responseData['fileSize'] is String) {
            fileSize = int.tryParse(responseData['fileSize']) ?? 0;
          } else if (responseData['fileSize'] is int) {
            fileSize = responseData['fileSize'];
          }
        }
        
        // 记录完整URL，方便调试
        if (responseData.containsKey('url')) {
          String videoUrl = responseData['url']?.toString() ?? '';
          if (videoUrl.isNotEmpty) {
            // 修复视频URL
            // videoUrl = fixVideoUrl(videoUrl);
            responseData['url'] = videoUrl;
            LoggerUtil.i('└───> 修复后的视频URL: $videoUrl');
          }
        }
        
        // 返回处理后的响应数据，兼容原代码的status字段
        final Map<String, dynamic> result = {
          'status': flag == 1 ? 2 : 1, // 转换flag为status：1=处理中，2=成功
          'clipKey': clipId,
          'url': responseData['url'] ?? '',
          'fileSize': fileSize,
          'size': fileSize, // 保留size字段兼容性
          'duration': responseData['duration'] ?? 0,
          'width': responseData['width'] ?? 0,
          'height': responseData['height'] ?? 0,
          'vbitrate': responseData['vbitrate'] ?? 0,
          'vfps': responseData['vfps'] ?? 0,
          'md5': responseData['md5'] ?? '',
          'flag': flag, // 保留原始flag字段
        };
        
        // 将responseData中的其他字段添加到result中
        responseData.forEach((key, value) {
          if (!result.containsKey(key)) {
            result[key] = value;
          }
        });
        
        LoggerUtil.i('└───> 返回给调用方的处理结果: $result');
        return result;
      } else if (response['errCode'] == 300800) {
        // 特殊处理300800错误码 - 通常是授权问题
        LoggerUtil.e('└───> 获取处理结果失败: ${response['errMsg']} (错误码: 300800)');
        LoggerUtil.e('└───> 可能是Cookie无效或权限不足');
        return {
          'status': 3, // 处理失败
          'errMsg': response['errMsg'] ?? 'request failed',
          'errCode': response['errCode'],
        };
      } else {
        LoggerUtil.e('└───> 获取处理结果失败: ${response['errMsg']}');
        LoggerUtil.e('└───> 错误响应: $response');
        return null;
      }
    } catch (e) {
      LoggerUtil.e('└───> 获取处理结果异常: $e');
      return null;
    }
  }
  
  /// 提取微信视频号finger-print-device-id
  /// 尝试从Cookie或者请求头中提取
  /// 
  /// [cookieStr] cookie字符串
  /// 返回finger-print-device-id，如果提取失败则返回null或随机生成一个
  static String? extractFingerPrintId(String cookieStr) {
    try {
      // 这里根据实际情况提取，如果无法提取可以返回null或随机生成一个
      final regExp = RegExp(r'finger-print-device-id=([^;]+)');
      final match = regExp.firstMatch(cookieStr);
      if (match != null && match.groupCount >= 1) {
        return match.group(1);
      }
      
      // 如果没有找到，返回null或生成一个默认值
      return null;
    } catch (e) {
      LoggerUtil.e('提取微信视频号finger-print-device-id失败: $e');
      return null;
    }
  }
  
  /// 搜索微信视频号POI位置
  /// 
  /// [cookieStr] 用户的完整Cookie字符串
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [keyword] 搜索关键词，可选
  /// 返回POI搜索结果
  static Future<WxSphPoiSearchResponse> searchPoi({
    required String cookieStr,
    double latitude = 0,
    double longitude = 0,
    String keyword = '',
  }) async {
    try {
      LoggerUtil.i('【微信视频号】搜索POI位置，经纬度: $latitude, $longitude，关键词: $keyword');
      
      // 检查Cookie是否为空
      if (cookieStr.isEmpty) {
        LoggerUtil.e('【微信视频号】Cookie为空，无法进行POI搜索');
        return WxSphPoiSearchResponse(
          statusCode: -1,
          statusMsg: '微信视频号Cookie为空，请重新登录',
          poiList: [],
        );
      }
      
      // 检查Cookie格式
      bool hasRequiredCookieField = cookieStr.contains('wxuin=') || 
                                   cookieStr.contains('passport_uin=') || 
                                   cookieStr.contains('o_cookie=');
      
      if (!hasRequiredCookieField) {
        LoggerUtil.e('【微信视频号】Cookie格式错误，缺少必要字段');
        // 尝试打印Cookie片段用于调试
        final shortCookie = cookieStr.length > 100 
            ? cookieStr.substring(0, 50) + '...' + cookieStr.substring(cookieStr.length - 50)
            : cookieStr;
        LoggerUtil.e('【微信视频号】Cookie片段: $shortCookie');
        
        return WxSphPoiSearchResponse(
          statusCode: -1,
          statusMsg: '微信视频号Cookie格式错误，请重新登录',
          poiList: [],
        );
      }
      
      // 获取当前时间戳
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      
      // 提取必要的认证信息
      final wxUin = extractWechatUinFromCookie(cookieStr);
      final fingerPrintId = extractFingerPrintId(cookieStr) ?? 'c4db9fe872282a20230a90d89afcd682';
      
      LoggerUtil.i('【微信视频号】X-WECHAT-UIN: $wxUin, finger-print-device-id: $fingerPrintId');
      
      if (wxUin.isEmpty) {
        LoggerUtil.e('【微信视频号】无法从Cookie提取UIN，搜索POI可能会失败');
      }
      
      // 打印部分Cookie以便调试
      final shortenedCookie = cookieStr.length > 100 
          ? cookieStr.substring(0, 50) + '...' + cookieStr.substring(cookieStr.length - 50)
          : cookieStr;
      LoggerUtil.i('【微信视频号】Cookie(部分): $shortenedCookie');
      
      // 分析Cookie内容
      final cookieParts = cookieStr.split(';');
      LoggerUtil.i('【微信视频号】Cookie包含${cookieParts.length}个部分');
      
      // 检查重要Cookie字段
      final importantFields = ['wxuin', 'passport_uin', 'o_cookie', 'pgv_pvid', 'pgv_info', 'wxunionid'];
      for (final field in importantFields) {
        bool hasField = false;
        for (final part in cookieParts) {
          if (part.trim().startsWith('$field=')) {
            hasField = true;
            LoggerUtil.i('【微信视频号】找到Cookie字段: $field');
            break;
          }
        }
        if (!hasField) {
          LoggerUtil.i('【微信视频号】未找到Cookie字段: $field');
        }
      }
      
      // 构建请求数据
      final requestData = {
        "query": keyword,
        "cookies": "",
        "longitude": longitude,
        "latitude": latitude,
        "timestamp": timestamp,
        "_log_finder_uin": wxUin,
        "rawKeyBuff": null,
        "pluginSessionId": null,
        "scene": 7,
        "reqScene": 7
      };
      
      // 构建请求头
      final headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Origin': 'https://channels.weixin.qq.com',
        'Referer': 'https://channels.weixin.qq.com/micro/content/post/finderNewLifeCreate',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-WECHAT-UIN': wxUin,
        'finger-print-device-id': fingerPrintId,
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Cookie': cookieStr,
      };
      
      // 打印请求数据和URL
      LoggerUtil.i('【微信视频号】请求数据: ${jsonEncode(requestData)}');
      LoggerUtil.i('【微信视频号】请求URL: https://channels.weixin.qq.com/micro/content/cgi-bin/mmfinderassistant-bin/helper/helper_search_location');
      
      // 使用更具体的选项配置
      final options = Options(
        headers: headers,
        method: 'POST',
        contentType: 'application/json',
        responseType: ResponseType.json,
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        validateStatus: (status) {
          // 允许所有状态码通过，我们会在后面处理
          return true;
        },
      );
      
      // 发送请求
      final response = await _dio.post(
        'https://channels.weixin.qq.com/micro/content/cgi-bin/mmfinderassistant-bin/helper/helper_search_location',
        data: requestData,
        options: options,
      );
      
      // 记录响应状态码
      LoggerUtil.i('【微信视频号】响应状态码: ${response.statusCode}');
      
      // if (response.statusCode == 201) {
      //   // 201通常表示认证问题
      //   LoggerUtil.e('【微信视频号】认证失败，可能需要刷新Cookie或重新登录');
      //   return WxSphPoiSearchResponse(
      //     statusCode: response.statusCode ?? -1,
      //     statusMsg: '认证失败，请重新登录微信视频号',
      //     poiList: [],
      //   );
      // } else
        if (response.statusCode == 200||response.statusCode == 201) {
        // 打印部分响应数据以便调试
        final responseStr = jsonEncode(response.data);
        LoggerUtil.i('【微信视频号】搜索POI位置响应数据: ${responseStr.length > 300 ? responseStr.substring(0, 300) + '...' : responseStr}');
        
        // 检查响应数据中是否有baseResponse字段
        if (response.data != null && response.data is Map) {
          final data = response.data as Map;
          
          // 打印baseResponse字段内容
          if (data.containsKey('baseResponse')) {
            final baseResponse = data['baseResponse'];
            LoggerUtil.i('【微信视频号】baseResponse: $baseResponse');
            
            if (baseResponse != null && baseResponse is Map) {
              final ret = baseResponse['ret'];
              LoggerUtil.i('【微信视频号】baseResponse.ret: $ret');
              
              if (ret != null && ret != 0) {
                final errMsg = baseResponse['errMsg'] ?? '未知错误';
                LoggerUtil.e('【微信视频号】API返回错误: $errMsg (code: $ret)');
                return WxSphPoiSearchResponse(
                  statusCode: ret,
                  statusMsg: errMsg,
                  poiList: [],
                );
              }
            }
          } else {
            LoggerUtil.e('【微信视频号】响应中没有baseResponse字段');
          }
          
          // 检查searchLocationList字段
          if (data.containsKey('searchLocationList')) {
            final locationList = data['searchLocationList'];
            LoggerUtil.i('【微信视频号】搜索结果条数: ${locationList is List ? locationList.length : 0}');
          } else {
            LoggerUtil.e('【微信视频号】响应中没有searchLocationList字段');
          }
        }
        
        // 解析响应数据
        final result = WxSphPoiSearchResponse.fromJson(response.data);
        LoggerUtil.i('【微信视频号】解析后的POI数量: ${result.poiList.length}');
        return result;
      } else {
        LoggerUtil.e('【微信视频号】搜索POI位置失败: ${response.statusCode}');
        return WxSphPoiSearchResponse(
          statusCode: response.statusCode ?? -1,
          statusMsg: response.statusMessage ?? '请求失败',
          poiList: [],
        );
      }
    } catch (e) {
      LoggerUtil.e('【微信视频号】搜索POI位置异常: $e');
      return WxSphPoiSearchResponse(
        statusCode: -1,
        statusMsg: '请求异常: $e',
        poiList: [],
      );
    }
  }

  /// 搜索活动 - 使用桌面端相同的接口
  static Future<List<WxSphActivity>> searchActivities(String keyword, String cookieStr) async {
    try {
      LoggerUtil.i('【微信视频号】搜索活动: $keyword');

      // 构建请求数据，完全按照桌面端的格式
      final requestData = {
        'query': keyword,
      };

      LoggerUtil.i('【微信视频号】活动搜索请求数据: $requestData');

      final response = await _dio.post(
        'https://channels.weixin.qq.com/cgi-bin/mmfinderassistant-bin/post/post_search_event',
        data: requestData,
        options: Options(
          headers: {
            'Cookie': cookieStr,
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data;
        LoggerUtil.i('【微信视频号】活动搜索响应: ${data.toString()}');

        if (data['errCode'] == 0 && data['data'] != null) {
          // 根据桌面端的响应结构解析
          final eventList = data['data']['eventList'] as List? ?? [];
          final activities = eventList.map((e) {
            return WxSphActivity(
              eventTopicId: e['eventTopicId']?.toString() ?? '',
              eventName: e['eventName']?.toString() ?? '',
              eventCreatorNickname: e['eventCreatorNickname']?.toString() ?? '',
            );
          }).toList();

          LoggerUtil.i('【微信视频号】成功解析${activities.length}个活动');
          return activities;
        }
      }

      LoggerUtil.w('【微信视频号】搜索活动失败: ${response.data}');
      return [];
    } catch (e) {
      LoggerUtil.e('【微信视频号】搜索活动异常: $e');
      return [];
    }
  }

  /// 搜索用户 - 完全按照桌面端实现
  static Future<List<WxSphUser>> searchUsers(String keyword, String cookieStr, {int page = 1}) async {
    try {
      LoggerUtil.i('【微信视频号】搜索用户: $keyword, page: $page');

      // 完全按照桌面端的实现：POST方法，简单的请求头
      final requestBody = {
        'pageSize': 10,
        'currentPage': page,
        'offset': (page - 1) * 10,
        'query': keyword,
      };

      LoggerUtil.i('【微信视频号】用户搜索请求体: $requestBody');

      final response = await _dio.post(
        'https://channels.weixin.qq.com/cgi-bin/mmfinderassistant-bin/helper/helper_search_finder_account',
        data: requestBody,
        options: Options(
          headers: {
            'Cookie': cookieStr,
          },
        ),
      );

      LoggerUtil.i('【微信视频号】用户搜索响应状态: ${response.statusCode}');
      LoggerUtil.i('【微信视频号】用户搜索响应类型: ${response.data.runtimeType}');

      // 检查响应是否为字符串（可能是HTML错误页面）
      if (response.data is String) {
        final responseStr = response.data as String;
        LoggerUtil.w('【微信视频号】收到非JSON响应: ${responseStr.substring(0, responseStr.length > 200 ? 200 : responseStr.length)}...');

        // 检查是否是登录页面
        if (responseStr.contains('login') || responseStr.contains('登录')) {
          LoggerUtil.e('【微信视频号】可能需要重新登录');
          return [];
        }

        LoggerUtil.e('【微信视频号】收到意外的响应格式');
        return [];
      }

      if (response.statusCode == 200 && response.data != null) {
        try {
          final data = response.data as Map<String, dynamic>;
          LoggerUtil.i('【微信视频号】用户搜索响应数据: $data');

          if (data['errCode'] == 0 && data['data'] != null) {
            // 根据桌面端的响应结构解析: data.data.list
            final userList = data['data']['list'] as List? ?? [];
            final users = userList.map((e) {
              return WxSphUser(
                username: e['username']?.toString() ?? '',
                headImgUrl: e['headImgUrl']?.toString() ?? '',
                nickName: e['nickName']?.toString() ?? '',
                authImgUrl: e['authImgUrl']?.toString(),
                authCompanyName: e['authCompanyName']?.toString(),
              );
            }).toList();

            LoggerUtil.i('【微信视频号】成功解析${users.length}个用户');
            return users;
          } else {
            LoggerUtil.w('【微信视频号】搜索用户API返回错误: errCode=${data['errCode']}, errMsg=${data['errMsg']}');
          }
        } catch (e) {
          LoggerUtil.e('【微信视频号】解析响应数据失败: $e');
          LoggerUtil.e('【微信视频号】原始响应: ${response.data}');
        }
      }

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data;
        LoggerUtil.i('【微信视频号】用户搜索响应: ${data.toString()}');

        if (data['errCode'] == 0 && data['data'] != null) {
          // 根据桌面端的响应结构解析
          final userList = data['data']['list'] as List? ?? [];
          final users = userList.map((e) {
            return WxSphUser(
              username: e['username']?.toString() ?? '',
              headImgUrl: e['headImgUrl']?.toString() ?? '',
              nickName: e['nickName']?.toString() ?? '',
              authImgUrl: e['authImgUrl']?.toString(),
              authCompanyName: e['authCompanyName']?.toString(),
            );
          }).toList();

          LoggerUtil.i('【微信视频号】成功解析${users.length}个用户');
          return users;
        }
      }

      LoggerUtil.w('【微信视频号】搜索用户失败: ${response.data}');
      return [];
    } catch (e) {
      LoggerUtil.e('【微信视频号】搜索用户异常: $e');
      return [];
    }
  }
}