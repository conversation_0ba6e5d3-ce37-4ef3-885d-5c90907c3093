import 'dart:io';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/plat_form_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/plat_wx_sph.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_webview.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:video_player/video_player.dart';

class WxSphTestPage extends StatefulWidget {
  const WxSphTestPage({super.key});

  @override
  State<WxSphTestPage> createState() => _WxSphTestPageState();
}

class _WxSphTestPageState extends State<WxSphTestPage> {
  String _statusText = '等待授权...';
  bool _isAuthorized = false;
  String _cookieStr = '';
  PlatWxSph? _platWxSph;
  
  // 视频文件
  File? _videoFile;
  // 封面文件
  File? _coverFile;
  
  // 进度
  int _progress = 0;
  String _progressMessage = '';
  
  // 视频播放器控制器
  VideoPlayerController? _videoController;
  
  // Cookie输入控制器
  final TextEditingController _cookieController = TextEditingController();
  // 是否显示Cookie输入框
  bool _showCookieInput = false;
  
  @override
  void initState() {
    super.initState();
    _checkPlatformSupport();
  }
  
  @override
  void dispose() {
    _videoController?.dispose();
    _cookieController.dispose();
    super.dispose();
  }
  
  void _checkPlatformSupport() {
    try {
      PlatformChecker.checkWxSphSupport(throwException: true);
      setState(() {
        _statusText = '当前平台支持微信视频号';
      });
    } catch (e) {
      setState(() {
        _statusText = '当前平台不支持微信视频号: $e';
      });
    }
  }
  
  Future<void> _authorize() async {
    try {
      await WxSphWebView.openAuthWebView(
        context,
        onSuccess: (cookieString) {
          setState(() {
            _isAuthorized = true;
            _cookieStr = cookieString;
            _statusText = '授权成功';
            
            // 创建平台实例
            final platformModel = PlatformModel(
              id: '1',
              platType: '微信视频号',
              cookie: cookieString,
            );
            _platWxSph = PlatWxSph(platformModel);
          });
          
          // 获取用户信息
          _getUserInfo();
        },
        onFailed: (errorMsg) {
          setState(() {
            _isAuthorized = false;
            _statusText = '授权失败: $errorMsg';
          });
        },
      );
    } catch (e) {
      setState(() {
        _statusText = '授权异常: $e';
      });
    }
  }
  
  void _setCookie() {
    final cookieString = _cookieController.text.trim();
    if (cookieString.isEmpty) {
      setState(() {
        _statusText = 'Cookie不能为空';
      });
      return;
    }
    
    // 检查Cookie是否包含关键字段
    if (!cookieString.contains('sessionid')) {
      setState(() {
        _statusText = 'Cookie无效: 缺少sessionid字段，这是微信视频号API必需的';
      });
      return;
    }
    
    setState(() {
      _isAuthorized = true;
      _cookieStr = cookieString;
      _statusText = '已设置Cookie';
      _showCookieInput = false;
      
      // 创建平台实例
      final platformModel = PlatformModel(
        id: '1',
        platType: '微信视频号',
        cookie: cookieString,
      );
      _platWxSph = PlatWxSph(platformModel);
      
      // 记录Cookie设置成功的日志
      LoggerUtil.i('【微信视频号】手动设置Cookie成功，长度: ${cookieString.length}');
    });
    
    // 自动获取用户信息进行验证
    _getUserInfo();
  }
  
  Future<void> _getUserInfo() async {
    if (_platWxSph == null) {
      setState(() {
        _statusText = '请先授权';
      });
      return;
    }
    
    try {
      setState(() {
        _statusText = '正在获取用户信息...';
      });
      
      final userInfo = await _platWxSph!.getUserInfo();
      if ((userInfo.containsKey('errCode') && userInfo['errCode'] != 0)) {
        setState(() {
          _isAuthorized = false;
          _statusText = '授权失败: Cookie无效或已过期';
          LoggerUtil.e('【微信视频号】获取用户信息失败: $userInfo');
        });
        return;
      }
      
      setState(() {
        _statusText = '用户信息: $userInfo';
      });
    } catch (e) {
      setState(() {
        _isAuthorized = false;
        _statusText = '获取用户信息失败: $e';
        LoggerUtil.e('【微信视频号】获取用户信息异常: $e');
      });
    }
  }
  
  Future<void> _pickVideo() async {
    try {
      final picker = ImagePicker();
      final pickedVideo = await picker.pickVideo(source: ImageSource.gallery);
      
      if (pickedVideo == null) return;
      
      // 释放之前的控制器
      _videoController?.dispose();
      
      setState(() {
        _videoFile = File(pickedVideo.path);
        _statusText = '已选择视频: ${_videoFile!.path}';
      });
      
      // 创建视频播放器控制器
      _videoController = VideoPlayerController.file(_videoFile!)
        ..initialize().then((_) {
          setState(() {});
        });
    } catch (e) {
      setState(() {
        _statusText = '选择视频失败: $e';
      });
    }
  }
  
  Future<void> _pickCover() async {
    try {
      final picker = ImagePicker();
      final pickedImage = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedImage == null) return;
      
      setState(() {
        _coverFile = File(pickedImage.path);
        _statusText = '已选择封面: ${_coverFile!.path}';
      });
    } catch (e) {
      setState(() {
        _statusText = '选择封面失败: $e';
      });
    }
  }
  
  Future<void> _publishVideo() async {
    try {
      setState(() {
        _progress = 0;
        _progressMessage = '准备发布...';
      });
      
      // 创建发布参数
      final publishParams = PublishParamsModel(
        _coverFile!,
        title: '测试视频',
        desc: '这是一个测试视频',
        topics: ['测试'],
        video: _videoFile,
        progressCallback: (progress, message) {
          setState(() {
            _progress = progress;
            _progressMessage = message ?? '';
          });
        },
      );
      
      // 发布视频并等待结果
      final result = await _platWxSph!.publishVideo(publishParams);
      
      setState(() {
        _statusText = '发布请求已完成: ${result.isSuccess ? '成功' : '失败'}';
      });
    } catch (e) {
      setState(() {
        _statusText = '发布视频失败: $e';
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('微信视频号测试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 状态显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('状态: $_statusText',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('授权状态: ${_isAuthorized ? '已授权' : '未授权'}'),
                    if (_isAuthorized)
                      Text('Cookie长度: ${_cookieStr.length}',
                        style: const TextStyle(fontSize: 12),
                      ),
                  ],
                ),
              ),
            ),
            
            // Cookie输入相关
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _showCookieInput = !_showCookieInput;
                      });
                    },
                    child: Text(_showCookieInput ? '隐藏Cookie输入' : '手动输入Cookie'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _authorize,
                    child: const Text('授权登录'),
                  ),
                ),
              ],
            ),
            
            // Cookie输入框
            if (_showCookieInput)
              Column(
                children: [
                  const SizedBox(height: 8),
                  TextField(
                    controller: _cookieController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'Cookie字符串',
                      hintText: '粘贴Cookie字符串，必须包含sessionid字段',
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    '提示: Cookie格式如 "sessionid=abc123; wxuin=12345; deviceid=67890"',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _setCookie,
                    child: const Text('设置Cookie'),
                  ),
                ],
              ),
            
            // 用户信息按钮
            ElevatedButton(
              onPressed: _isAuthorized ? _getUserInfo : null,
              child: const Text('获取用户信息'),
            ),
            
            const Divider(),
            
            // 选择视频和封面
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isAuthorized ? _pickVideo : null,
                    child: const Text('选择视频'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isAuthorized ? _pickCover : null,
                    child: const Text('选择封面'),
                  ),
                ),
              ],
            ),
            
            // 视频预览
            if (_videoController != null && _videoController!.value.isInitialized)
              AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    VideoPlayer(_videoController!),
                    IconButton(
                      icon: Icon(
                        _videoController!.value.isPlaying 
                            ? Icons.pause 
                            : Icons.play_arrow,
                        size: 48,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        setState(() {
                          _videoController!.value.isPlaying
                              ? _videoController!.pause()
                              : _videoController!.play();
                        });
                      },
                    ),
                  ],
                ),
              ),
            
            // 封面预览
            if (_coverFile != null)
              Container(
                height: 120,
                margin: const EdgeInsets.symmetric(vertical: 8),
                child: Image.file(_coverFile!),
              ),
            
            // 发布按钮
            ElevatedButton(
              onPressed: _isAuthorized && _videoFile != null && _coverFile != null 
                  ? _publishVideo 
                  : null,
              child: const Text('发布视频'),
            ),
            
            // 进度条
            if (_progress > 0)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Text('进度: $_progress% - $_progressMessage'),
                  LinearProgressIndicator(value: _progress / 100),
                ],
              ),
          ],
        ),
      ),
    );
  }
} 