import 'dart:async';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:flutter/material.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// 微信视频号WebView授权工具
class WxSphWebView {
  /// 默认登录URL
  static const String loginUrl = 'https://channels.weixin.qq.com';
  
  /// 检查登录状态的Cookie字段
  static const String cookieCheckField = 'sessionid';
  
  /// Cookie管理器
  static final cookieManager = WebviewCookieManager();
  
  /// 打开WebView进行授权
  /// [context] 页面上下文
  /// [onSuccess] 授权成功回调
  /// [onFailed] 授权失败回调
  static Future<void> openAuthWebView(
    BuildContext context, {
    required Function(String cookieString) onSuccess,
    required Function(String errorMsg) onFailed,
  }) async {
    try {
      // 检查平台支持
      PlatformChecker.checkWxSphSupport(throwException: true);
      
      LoggerUtil.i('【微信视频号】打开授权WebView');
      
      // 清除历史Cookie
      await cookieManager.clearCookies();
      
      // 创建WebView控制器
      final controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              LoggerUtil.i('【微信视频号】页面开始加载: $url');
            },
            onPageFinished: (String url) async {
              LoggerUtil.i('【微信视频号】页面加载完成: $url');
              
              // 检查是否已登录
              final cookies = await cookieManager.getCookies(loginUrl);
              final hasLoginCookie = cookies.any(
                (cookie) => cookie.name.contains(cookieCheckField) && cookie.value.isNotEmpty,
              );
              
              if (hasLoginCookie) {
                LoggerUtil.i('【微信视频号】检测到登录状态，获取Cookie');
                
                // 将Cookie列表转换为字符串
                final cookieString = cookies.map((c) => '${c.name}=${c.value}').join('; ');
                
                // 登录成功，返回Cookie
                Navigator.of(context).pop(); // 关闭WebView
                onSuccess(cookieString);
              }
            },
            onWebResourceError: (WebResourceError error) {
              LoggerUtil.e('【微信视频号】WebView资源错误: ${error.description}');
            },
          ),
        )
        ..loadRequest(Uri.parse(loginUrl));
      
      // 显示WebView页面
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Dialog.fullscreen(
          child: Column(
            children: [
              AppBar(
                title: const Text('微信视频号授权'),
                leading: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    Navigator.pop(context);
                    onFailed('用户取消授权');
                  },
                ),
              ),
              Expanded(
                child: WebViewWidget(controller: controller),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      LoggerUtil.e('【微信视频号】打开授权WebView异常: $e');
      onFailed('打开授权WebView异常: $e');
    }
  }
  
  /// 检查Cookie是否有效
  static Future<bool> checkCookieValid(String cookieString) async {
    try {
      // 检查是否包含关键Cookie
      return cookieString.contains(cookieCheckField);
    } catch (e) {
      LoggerUtil.e('【微信视频号】检查Cookie有效性异常: $e');
      return false;
    }
  }
} 