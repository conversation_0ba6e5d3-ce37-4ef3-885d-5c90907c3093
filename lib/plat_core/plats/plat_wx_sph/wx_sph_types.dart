/// 视频号用户信息
class WxSphUserInfo {
  final String authorId;
  final String nickname;
  final String avatar;
  final int fansCount;

  WxSphUserInfo({
    required this.authorId, 
    required this.nickname, 
    required this.avatar,
    required this.fansCount
  });

  factory WxSphUserInfo.fromJson(Map<String, dynamic> json) {
    return WxSphUserInfo(
      authorId: json['authorId'] ?? '',
      nickname: json['nickname'] ?? '',
      avatar: json['avatar'] ?? '',
      fansCount: json['fansCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'authorId': authorId,
      'nickname': nickname,
      'avatar': avatar,
      'fansCount': fansCount,
    };
  }
}

/// 视频号数据统计
class WxSphDashboardData {
  final String date;
  final int zhangfen; // 涨粉数
  final int bofang; // 播放数
  final int pinglun; // 评论数
  final int dianzan; // 点赞数
  final int fenxiang; // 分享数
  final int zhuye; // 主页访问数

  WxSphDashboardData({
    required this.date,
    required this.zhangfen,
    required this.bofang,
    required this.pinglun,
    required this.dianzan,
    required this.fenxiang,
    required this.zhuye,
  });

  factory WxSphDashboardData.fromJson(Map<String, dynamic> json) {
    return WxSphDashboardData(
      date: json['date'] ?? '',
      zhangfen: json['zhangfen'] ?? 0,
      bofang: json['bofang'] ?? 0,
      pinglun: json['pinglun'] ?? 0,
      dianzan: json['dianzan'] ?? 0,
      fenxiang: json['fenxiang'] ?? 0,
      zhuye: json['zhuye'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'zhangfen': zhangfen,
      'bofang': bofang,
      'pinglun': pinglun,
      'dianzan': dianzan,
      'fenxiang': fenxiang,
      'zhuye': zhuye,
    };
  }
}

/// 视频号位置信息
class WxSphLocation {
  final String poiId;
  final String poiName;
  final String poiAddress;
  final String poiCity;
  final double latitude;
  final double longitude;

  WxSphLocation({
    required this.poiId,
    required this.poiName,
    required this.poiAddress,
    required this.poiCity,
    required this.latitude,
    required this.longitude,
  });

  factory WxSphLocation.fromJson(Map<String, dynamic> json) {
    return WxSphLocation(
      poiId: json['poiId'] ?? '',
      poiName: json['poiName'] ?? '',
      poiAddress: json['poiAddress'] ?? '',
      poiCity: json['poiCity'] ?? '',
      latitude: json['latitude'] ?? 0.0,
      longitude: json['longitude'] ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'poiId': poiId,
      'poiName': poiName,
      'poiAddress': poiAddress,
      'poiCity': poiCity,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

/// 视频号合集信息
class WxSphMixInfo {
  final String mixId;
  final String mixName;
  final String? desc;
  final String? coverImgUrl;
  final int feedCount;

  WxSphMixInfo({
    required this.mixId,
    required this.mixName,
    this.desc,
    this.coverImgUrl,
    this.feedCount = 0,
  });

  factory WxSphMixInfo.fromJson(Map<String, dynamic> json) {
    return WxSphMixInfo(
      mixId: json['id'] ?? json['mixId'] ?? '',
      mixName: json['name'] ?? json['mixName'] ?? '',
      desc: json['desc'],
      coverImgUrl: json['coverImgUrl'],
      feedCount: json['feedCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mixId': mixId,
      'mixName': mixName,
      'desc': desc,
      'coverImgUrl': coverImgUrl,
      'feedCount': feedCount,
    };
  }
}

/// 视频号作品评论
class WxSphComment {
  final String commentId;
  final String commentNickname;
  final String commentContent;
  final String commentHeadurl;
  final String commentCreatetime;
  final int commentLikeCount;
  final bool readFlag;
  final int likeFlag;
  final String? replyCommentId;
  final String? replyContent;
  final List<WxSphComment> levelTwoComment;

  WxSphComment({
    required this.commentId,
    required this.commentNickname,
    required this.commentContent,
    required this.commentHeadurl,
    required this.commentCreatetime,
    this.commentLikeCount = 0,
    this.readFlag = false,
    this.likeFlag = 0,
    this.replyCommentId,
    this.replyContent,
    this.levelTwoComment = const [],
  });

  factory WxSphComment.fromJson(Map<String, dynamic> json) {
    final List<dynamic> levelTwoCommentJson = json['levelTwoComment'] ?? [];
    
    return WxSphComment(
      commentId: json['commentId'] ?? '',
      commentNickname: json['commentNickname'] ?? '',
      commentContent: json['commentContent'] ?? '',
      commentHeadurl: json['commentHeadurl'] ?? '',
      commentCreatetime: json['commentCreatetime'] ?? '',
      commentLikeCount: json['commentLikeCount'] ?? 0,
      readFlag: json['readFlag'] ?? false,
      likeFlag: json['likeFlag'] ?? 0,
      replyCommentId: json['replyCommentId'],
      replyContent: json['replyContent'],
      levelTwoComment: levelTwoCommentJson.map((item) => WxSphComment.fromJson(item)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'commentId': commentId,
      'commentNickname': commentNickname,
      'commentContent': commentContent,
      'commentHeadurl': commentHeadurl,
      'commentCreatetime': commentCreatetime,
      'commentLikeCount': commentLikeCount,
      'readFlag': readFlag,
      'likeFlag': likeFlag,
      'replyCommentId': replyCommentId,
      'replyContent': replyContent,
      'levelTwoComment': levelTwoComment.map((comment) => comment.toJson()).toList(),
    };
  }
}

/// 视频号作品信息
class WxSphPost {
  final String objectId;
  final String exportId;
  final int createTime;
  final int likeCount;
  final int commentCount;
  final int readCount;
  final int forwardCount;
  final String? title;
  final String? description;
  final String? videoUrl;
  final String? thumbUrl;
  final String? coverUrl;
  final int mediaType;
  final int videoPlayLen;

  WxSphPost({
    required this.objectId,
    required this.exportId,
    required this.createTime,
    this.likeCount = 0,
    this.commentCount = 0,
    this.readCount = 0,
    this.forwardCount = 0,
    this.title,
    this.description,
    this.videoUrl,
    this.thumbUrl,
    this.coverUrl,
    this.mediaType = 4, // 默认为视频类型
    this.videoPlayLen = 0,
  });

  factory WxSphPost.fromJson(Map<String, dynamic> json) {
    var desc = json['desc'] ?? {};
    var media = {};
    
    // 安全地获取media数据
    if (desc['media'] != null && desc['media'] is List && desc['media'].isNotEmpty) {
      media = desc['media'][0] ?? {};
    }
    
    // 安全地获取title数据
    String? title;
    if (desc['shortTitle'] != null && desc['shortTitle'] is List && desc['shortTitle'].isNotEmpty) {
      title = desc['shortTitle'][0]['shortTitle'] ?? '';
    }
    
    return WxSphPost(
      objectId: json['objectId'] ?? '',
      exportId: json['exportId'] ?? '',
      createTime: json['createTime'] ?? 0,
      likeCount: json['likeCount'] ?? 0,
      commentCount: json['commentCount'] ?? 0,
      readCount: json['readCount'] ?? 0,
      forwardCount: json['forwardCount'] ?? 0,
      title: title,
      description: desc['description'] ?? '',
      videoUrl: media['url'] ?? '',
      thumbUrl: media['thumbUrl'] ?? '',
      coverUrl: media['coverUrl'] ?? '',
      mediaType: media['mediaType'] ?? 4,
      videoPlayLen: media['videoPlayLen'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'objectId': objectId,
      'exportId': exportId,
      'createTime': createTime,
      'likeCount': likeCount,
      'commentCount': commentCount,
      'readCount': readCount,
      'forwardCount': forwardCount,
      'title': title,
      'description': description,
      'videoUrl': videoUrl,
      'thumbUrl': thumbUrl,
      'coverUrl': coverUrl,
      'mediaType': mediaType,
      'videoPlayLen': videoPlayLen,
    };
  }
}

/// 视频号发布参数
class WxSphPublishParams {
  /// 标题
  final String title;
  
  /// 描述
  final String? description;
  
  /// 话题列表
  final List<String>? topics;
  
  /// 合集信息
  final WxSphMixInfo? mixInfo;
  
  /// 位置信息
  final WxSphLocation? poiInfo;
  
  /// 提及用户信息
  final List<WxSphMentionedUser>? mentionedUserInfo;
  
  /// 活动信息
  final Map<String, dynamic>? event;
  
  /// 定时发布时间
  final int? timingTime;
  
  /// 发布标志
  final int postFlag;
  
  /// 封面图片路径
  String cover;
  
  WxSphPublishParams({
    required this.title,
    required this.cover, this.description,
    this.topics,
    this.mixInfo,
    this.poiInfo,
    this.mentionedUserInfo,
    this.event,
    this.timingTime,
    this.postFlag = 1,
  });
  
  /// 转换为JSON格式，用于日志记录
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'topics': topics,
      'mixInfo': mixInfo?.toJson(),
      'poiInfo': poiInfo?.toJson(),
      'mentionedUserInfo': mentionedUserInfo?.map((user) => user.toJson()).toList(),
      'event': event,
      'timingTime': timingTime,
      'postFlag': postFlag,
      'cover': cover,
    };
  }
}

/// 视频号@用户信息
class WxSphMentionedUser {
  final String? nickName;
  
  WxSphMentionedUser({
    this.nickName,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'nickName': nickName,
    };
  }
}

/// 视频号发布结果
class WxSphPublishResult {
  final int publishTime;
  final String publishId;
  final String shareLink;

  WxSphPublishResult({
    required this.publishTime,
    required this.publishId,
    required this.shareLink,
  });

  factory WxSphPublishResult.fromJson(Map<String, dynamic> json) {
    return WxSphPublishResult(
      publishTime: json['publishTime'] ?? 0,
      publishId: json['publishId'] ?? '',
      shareLink: json['shareLink'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'publishTime': publishTime,
      'publishId': publishId,
      'shareLink': shareLink,
    };
  }
}

/// 视频号活动信息
class WxSphActivity {
  final String eventTopicId;
  final String eventName;
  final String eventCreatorNickname;

  WxSphActivity({
    required this.eventTopicId,
    required this.eventName,
    required this.eventCreatorNickname,
  });

  factory WxSphActivity.fromJson(Map<String, dynamic> json) {
    return WxSphActivity(
      eventTopicId: json['eventTopicId'] ?? '',
      eventName: json['eventName'] ?? '',
      eventCreatorNickname: json['eventCreatorNickname'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'eventTopicId': eventTopicId,
      'eventName': eventName,
      'eventCreatorNickname': eventCreatorNickname,
    };
  }

  @override
  String toString() => eventName;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is WxSphActivity && other.eventTopicId == eventTopicId;
  }

  @override
  int get hashCode => eventTopicId.hashCode;
}

/// 视频号用户信息（用于@功能）
class WxSphUser {
  final String username;
  final String headImgUrl;
  final String nickName;
  final String? authImgUrl;
  final String? authCompanyName;

  WxSphUser({
    required this.username,
    required this.headImgUrl,
    required this.nickName,
    this.authImgUrl,
    this.authCompanyName,
  });

  factory WxSphUser.fromJson(Map<String, dynamic> json) {
    return WxSphUser(
      username: json['username'] ?? '',
      headImgUrl: json['headImgUrl'] ?? '',
      nickName: json['nickName'] ?? '',
      authImgUrl: json['authImgUrl'],
      authCompanyName: json['authCompanyName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'headImgUrl': headImgUrl,
      'nickName': nickName,
      'authImgUrl': authImgUrl,
      'authCompanyName': authCompanyName,
    };
  }

  @override
  String toString() => nickName;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WxSphUser && other.username == username;
  }

  @override
  int get hashCode => username.hashCode;
}