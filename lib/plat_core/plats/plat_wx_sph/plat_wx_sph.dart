import 'dart:async';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/models/wx_sph_platform_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_base.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/models/wx_sph_poi_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_types.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';

class PlatWxSph extends PlatBase {
  PlatWxSph(super.platformModel);

  static const int _fileBlockSize = 8 * 1024 * 1024;

  String _getUniqueTaskId() {
    const uuid = Uuid();
    return uuid.v4().replaceAll('-', '');
  }

  void _checkPlatformSupport() {
    PlatformChecker.checkWxSphSupport(throwException: true);
  }
  
  @override
  Future<Map<String, dynamic>> getUserInfo() async {
    try {
      _checkPlatformSupport();

      final userInfo = await WxSphService.getUserInfo(cookieStr);
      if (userInfo == null) {
        throw Exception('获取用户信息失败');
      }

      return {
        'uid': userInfo.authorId,
        'nickname': userInfo.nickname,
        'avatar': userInfo.avatar,
        'fansCount': userInfo.fansCount,
      };
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取用户信息异常: $e');
      rethrow;
    }
  }
  
  @override
  Future<PublishResponseModel> publishVideo(PublishParamsModel publishParamsModel) async {
    try {
      final progressCallback = publishParamsModel.progressCallback;
      progressCallback?.call(0, '准备处理...');

      if (publishParamsModel.video != null) {
        return await _publishVideoAsync(publishParamsModel);
      } else if (publishParamsModel.images != null && publishParamsModel.images!.isNotEmpty) {
        return await _publishImagesAsync(publishParamsModel);
      } else {
        return PublishResponseModel(false, failMsg: '缺少视频或图片文件');
      }
    } catch (e) {
      LoggerUtil.e('【视频号平台】发布异常: $e');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }
  
  Future<PublishResponseModel> _publishVideoAsync(PublishParamsModel publishParamsModel) async {
    try {
      _checkPlatformSupport();

      if (publishParamsModel.video == null) {
        throw Exception('视频文件不能为空');
      }

      final videoFile = publishParamsModel.video!;
      final coverFile = publishParamsModel.cover;

      if (!await videoFile.exists()) {
        throw Exception('视频文件不存在或无法访问');
      }

      if (!await coverFile.exists()) {
        throw Exception('封面文件不存在或无法访问');
      }

      final progressCallback = publishParamsModel.progressCallback;

      progressCallback?.call(5, '检查登录状态');
      final isLoggedIn = await WxSphService.checkLoginStatus(cookieStr);
      if (!isLoggedIn) {
        throw Exception('未登录或登录已过期，请重新登录');
      }

      progressCallback?.call(10, '获取发布参数');
      final traceKey = await WxSphService.getPublishTraceKey(cookieStr);
      if (traceKey == null) {
        throw Exception('获取发布追踪密钥失败');
      }

      progressCallback?.call(12, '获取上传参数');
      final uploadParams = await WxSphService.getUploadParams(cookieStr);

      final startUploadTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      progressCallback?.call(15, '申请上传视频');
      final uploadInfo = await WxSphService.applyVideoUpload(cookieStr, uploadParams, videoFile);
      if (uploadInfo == null) {
        throw Exception('申请上传视频失败');
      }

      progressCallback?.call(20, '开始上传视频');
      final fileSize = uploadInfo['fileSize'] as int;
      final totalChunks = (fileSize / _fileBlockSize).ceil();

      final videoBytes = await videoFile.readAsBytes();
      final videoMd5 = md5.convert(videoBytes).toString();

      const videoWidth = 1280;
      const videoHeight = 720;
      const videoDuration = 15;

      for (int i = 0; i < totalChunks; i++) {
        final start = i * _fileBlockSize;
        final end = (i + 1) * _fileBlockSize > fileSize ? fileSize : (i + 1) * _fileBlockSize;
        final chunkData = videoBytes.sublist(start, end);

        progressCallback?.call(20 + ((i + 1) * 40) ~/ totalChunks, '上传视频片段 ${i + 1}/$totalChunks');

        final uploadSuccess = await WxSphService.uploadVideoChunk(
          cookieStr,
          uploadInfo,
          videoFile,
          i,
          totalChunks,
          chunkData,
          progressCallback,
        );

        if (!uploadSuccess) {
          throw Exception('上传视频片段 ${i + 1}/$totalChunks 失败');
        }
      }

      progressCallback?.call(60, '完成视频上传');
      final completeSuccess = await WxSphService.completeVideoUpload(cookieStr, uploadInfo);
      if (!completeSuccess) {
        throw Exception('完成视频上传失败');
      }

      final remoteFileUrl = uploadInfo['downloadUrl'] ?? '';
      if (remoteFileUrl.isEmpty) {
        throw Exception('获取视频URL失败');
      }

      progressCallback?.call(65, '开始上传封面');
      final coverKey = await WxSphService.uploadCoverFile(
        cookieStr,
        uploadInfo['originalParams'],
        coverFile,
      );
      if (coverKey == null) {
        throw Exception('上传封面失败');
      }

      final endUploadTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      progressCallback?.call(70, '处理视频');

      final fileInfo = {
        'md5': videoMd5,
        'size': fileSize,
        'width': videoWidth,
        'height': videoHeight,
        'duration': videoDuration,
      };

      final clipData = await WxSphService.clipVideo(
        cookieStr,
        traceKey,
        startUploadTime,
        endUploadTime,
        remoteFileUrl,
        fileInfo,
      );

      if (clipData == null) {
        throw Exception('视频裁剪失败');
      }

      progressCallback?.call(75, '获取处理结果');

      String? clipId = clipData['clipKey'];
      if (clipId == null) {
        throw Exception('裁剪ID为空');
      }

      Map<String, dynamic>? clipResult;
      int retryCount = 0;
      const maxRetries = 30;
      bool isSuccess = false;

      while (retryCount < maxRetries) {
        retryCount++;
        final result = await WxSphService.getClipResult(cookieStr, clipId);

        if (result == null) {
          progressCallback?.call(75, '等待视频处理结果 $retryCount/$maxRetries');
          await Future.delayed(const Duration(seconds: 3));
          continue;
        }

        final status = result['status'];
        if (status == 2) {
          clipResult = {
            'clipKey': clipId,
            'url': result['url'] ?? '',
            'fileSize': result['size'] ?? 0,
            'duration': result['duration'] ?? 0,
            'width': result['width'] ?? 0,
            'height': result['height'] ?? 0,
            'thumbUrl': coverKey
          };
          isSuccess = true;
          break;
        } else if (status == 3) {
          final errMsg = result['errMsg'] ?? '未知错误';
          final errCode = result['errCode'] ?? 0;

          if (errCode == 300800) {
            throw Exception('Cookie无效或已过期，请重新授权: $errMsg');
          }

          throw Exception('视频处理失败: $errMsg');
        } else {
          progressCallback?.call(75, '等待视频处理结果 $retryCount/$maxRetries');

          int waitTime = 3;
          if (retryCount > 10) waitTime = 5;
          if (retryCount > 20) waitTime = 8;

          await Future.delayed(Duration(seconds: waitTime));
        }
      }

      if (!isSuccess) {
        throw Exception('获取裁剪结果超时，请检查网络连接和Cookie有效性');
      }

      progressCallback?.call(85, '准备发布视频');

      final publishParams = WxSphPublishParams(
        title: publishParamsModel.title ?? '',
        topics: publishParamsModel.topics,
        description: publishParamsModel.desc,
        postFlag: 1,
        cover: coverFile.path,
      );

      progressCallback?.call(90, '发布视频');

      if (clipResult == null) {
        throw Exception('视频处理失败，无法发布视频');
      }

      final publishResult = await WxSphService.createVideoPost(
        cookieStr,
        traceKey,
        startUploadTime,
        endUploadTime,
        clipResult,
        publishParams,
      );

      if (publishResult == null) {
        throw Exception('发布视频失败');
      }

      progressCallback?.call(100, '发布成功');

      return PublishResponseModel(
        true,
        dataId: publishResult['publishId'],
        workLink: publishResult['shareLink'],
      );
    } catch (e, stackTrace) {
      LoggerUtil.e('【视频号平台】发布视频异常: $e');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }

  Future<PublishResponseModel> _publishImagesAsync(PublishParamsModel publishParamsModel) async {
    try {
      if (publishParamsModel.images == null || publishParamsModel.images!.isEmpty) {
        throw Exception('图片列表不能为空');
      }

      final coverFile = publishParamsModel.cover;
      final imageFiles = publishParamsModel.images!;

      if (!await coverFile.exists()) {
        throw Exception('封面文件不存在或无法访问');
      }

      for (int i = 0; i < imageFiles.length; i++) {
        final imageFile = imageFiles[i];
        if (!await imageFile.exists()) {
          throw Exception('图片文件${i+1}不存在或无法访问');
        }
      }

      final progressCallback = publishParamsModel.progressCallback;
      progressCallback?.call(10, '检查登录状态');

      return PublishResponseModel(
        false,
        failMsg: '微信视频号暂不支持图文发布功能，请使用视频发布',
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】发布图文异常: $e');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }

  Future<List<WxSphMixInfo>?> getMixList() async {
    try {
      return await WxSphService.getMixList(cookieStr);
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取合集列表异常: $e');
      return null;
    }
  }

  Future<List<WxSphLocation>?> getLocationList({
    required String query,
    required double longitude,
    required double latitude,
  }) async {
    try {
      return await WxSphService.getLocationList(
        cookieStr,
        query: query,
        longitude: longitude,
        latitude: latitude,
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取位置列表异常: $e');
      return null;
    }
  }

  Future<List<WxSphPost>?> getPostList({
    int pageNo = 1,
    int pageSize = 10,
  }) async {
    try {
      return await WxSphService.getPostList(
        cookieStr,
        pageNo: pageNo,
        pageSize: pageSize,
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取作品列表异常: $e');
      return null;
    }
  }

  Future<List<WxSphComment>?> getCommentList(String exportId) async {
    try {
      return await WxSphService.getCommentList(cookieStr, exportId);
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取评论列表异常: $e');
      return null;
    }
  }

  Future<bool> createComment(
    String exportId,
    String content, {
    String? replyCommentId,
  }) async {
    try {
      return await WxSphService.createComment(
        cookieStr,
        exportId,
        content,
        replyCommentId: replyCommentId,
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】创建评论异常: $e');
      return false;
    }
  }

  Future<List<WxSphDashboardData>?> getDashboardData({
    String? startDate,
    String? endDate,
  }) async {
    try {
      return await WxSphService.getDashboardData(
        cookieStr,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取数据统计异常: $e');
      return null;
    }
  }

  @override
  getMakeRequestHedears() {
    return {
      'Referer': 'https://channels.weixin.qq.com/',
      'Origin': 'https://channels.weixin.qq.com/',
    };
  }

  @override
  Future<PublishResponseModel> publishImgText(PublishParamsModel publishParamsModel) async {
    try {
      final progressCallback = publishParamsModel.progressCallback;
      progressCallback?.call(0, '准备处理...');

      return PublishResponseModel(
        false,
        failMsg: '微信视频号暂不支持图文发布',
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】发布图文异常: $e');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }

  Future<WxSphPoiSearchResponse> searchPoi({
    required double latitude,
    required double longitude,
    String keyword = '',
  }) async {
    return await WxSphService.searchPoi(
      cookieStr: cookieStr,
      latitude: latitude,
      longitude: longitude,
      keyword: keyword,
    );
  }

  Future<List<WxSphActivity>> searchActivities(String keyword) async {
    try {
      _checkPlatformSupport();
      return await WxSphService.searchActivities(keyword, cookieStr);
    } catch (e) {
      LoggerUtil.e('【视频号平台】搜索活动失败: $e');
      return [];
    }
  }

  Future<List<WxSphUser>> searchUsers(String keyword, {int page = 1}) async {
    try {
      _checkPlatformSupport();
      return await WxSphService.searchUsers(keyword, cookieStr, page: page);
    } catch (e) {
      LoggerUtil.e('【视频号平台】搜索用户失败: $e');
      return [];
    }
  }

  Future<bool> checkWxSphLoginStatus() async {
    try {
      _checkPlatformSupport();
      return await WxSphService.checkLoginStatus(cookieStr);
    } catch (e) {
      LoggerUtil.e('【视频号平台】检查登录状态失败: $e');
      return false;
    }
  }
}
