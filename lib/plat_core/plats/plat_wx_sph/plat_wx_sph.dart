import 'dart:async';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/models/wx_sph_platform_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_base.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/models/wx_sph_poi_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_types.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';

class PlatWxSph extends PlatBase {
  PlatWxSph(super.platformModel);
  
  /// 视频分块大小：8MB
  static const int _fileBlockSize = 8 * 1024 * 1024;
  
  /// 生成唯一任务ID
  String _getUniqueTaskId() {
    const uuid = Uuid();
    return uuid.v4().replaceAll('-', '');
  }
  
  /// 检查平台支持
  void _checkPlatformSupport() {
    PlatformChecker.checkWxSphSupport(throwException: true);
  }
  
  @override
  Future<Map<String, dynamic>> getUserInfo() async {
    try {
      // 检查平台支持
      _checkPlatformSupport();
      
      LoggerUtil.i('【视频号平台】开始获取用户信息');
      
      final userInfo = await WxSphService.getUserInfo(cookieStr);
      if (userInfo == null) {
        throw Exception('获取用户信息失败');
      }
      
      LoggerUtil.i('【视频号平台】获取用户信息结果: ${userInfo.toJson()}');
      
      // 返回统一格式的用户信息
      return {
        'uid': userInfo.authorId,
        'nickname': userInfo.nickname,
        'avatar': userInfo.avatar,
        'fansCount': userInfo.fansCount,
      };
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取用户信息异常: $e');
      rethrow;
    }
  }
  
  @override
  Future<PublishResponseModel> publishVideo(PublishParamsModel publishParamsModel) async {
    try {
      // 保存进度回调函数的引用
      final progressCallback = publishParamsModel.progressCallback;
      
      // 先发送开始处理的进度
      progressCallback?.call(0, '准备处理...');
      
      // 判断是视频发布还是图文发布
      if (publishParamsModel.video != null) {
        // 视频发布
        return await _publishVideoAsync(publishParamsModel);
      } else if (publishParamsModel.images != null && publishParamsModel.images!.isNotEmpty) {
        // 图文发布
        return await _publishImagesAsync(publishParamsModel);
      } else {
        // 无效的发布参数
        return PublishResponseModel(
          false,
          failMsg: '缺少视频或图片文件',
        );
      }
    } catch (e) {
      LoggerUtil.e('【视频号平台】发布异常: $e');
      return PublishResponseModel(
        false,
        failMsg: e.toString(),
      );
    }
  }
  
  /// 异步发布视频
  Future<PublishResponseModel> _publishVideoAsync(PublishParamsModel publishParamsModel) async {
    try {
      // 检查平台支持
      _checkPlatformSupport();
      
      LoggerUtil.i('【视频号平台】开始异步发布视频流程');
      
      if (publishParamsModel.video == null) {
        LoggerUtil.e('【视频号平台】视频文件为空');
        throw Exception('视频文件不能为空');
      }
      
      final videoFile = publishParamsModel.video!;
      final coverFile = publishParamsModel.cover;
      
      // 检查文件是否存在
      if (!await videoFile.exists()) {
        LoggerUtil.e('【视频号平台】视频文件不存在: ${videoFile.path}');
        throw Exception('视频文件不存在或无法访问');
      }
      
      if (!await coverFile.exists()) {
        LoggerUtil.e('【视频号平台】封面文件不存在: ${coverFile.path}');
        throw Exception('封面文件不存在或无法访问');
      }
      
      // 检查文件大小
      final videoSize = await videoFile.length();
      final coverSize = await coverFile.length();
      
      LoggerUtil.i('【视频号平台】视频文件: ${videoFile.path}, 大小: ${(videoSize / 1024 / 1024).toStringAsFixed(2)}MB');
      LoggerUtil.i('【视频号平台】封面文件: ${coverFile.path}, 大小: ${(coverSize / 1024 / 1024).toStringAsFixed(2)}MB');
      
      // 进度回调
      final progressCallback = publishParamsModel.progressCallback;
      
      // 1. 检查登录状态
      LoggerUtil.i('【视频号平台】检查登录状态');
      progressCallback?.call(5, '检查登录状态');
      
      final isLoggedIn = await WxSphService.checkLoginStatus(cookieStr);
      LoggerUtil.i('【视频号平台】登录状态: $isLoggedIn');
      
      if (!isLoggedIn) {
        LoggerUtil.e('【视频号平台】未登录或登录已过期');
        throw Exception('未登录或登录已过期，请重新登录');
      }
      
      // 2. 获取发布追踪密钥
      LoggerUtil.i('【视频号平台】获取发布追踪密钥');
      progressCallback?.call(10, '获取发布参数');
      
      final traceKey = await WxSphService.getPublishTraceKey(cookieStr);
      if (traceKey == null) {
        LoggerUtil.e('【视频号平台】获取发布追踪密钥失败');
        throw Exception('获取发布追踪密钥失败');
      }
      
      LoggerUtil.i('【视频号平台】获取到的追踪密钥: $traceKey');
      
      // 3. 获取上传参数
      LoggerUtil.i('【视频号平台】获取上传参数');
      progressCallback?.call(12, '获取上传参数');
      
      final uploadParams = await WxSphService.getUploadParams(cookieStr);
      
      LoggerUtil.i('【视频号平台】获取到的上传参数: $uploadParams');
      
      // 4. 上传视频
      final startUploadTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      LoggerUtil.i('【视频号平台】开始上传时间: $startUploadTime');
      
      // 4.1 申请上传视频
      LoggerUtil.i('【视频号平台】申请上传视频');
      progressCallback?.call(15, '申请上传视频');
      
      final uploadInfo = await WxSphService.applyVideoUpload(cookieStr, uploadParams, videoFile);
      if (uploadInfo == null) {
        LoggerUtil.e('【视频号平台】申请上传视频失败');
        throw Exception('申请上传视频失败');
      }
      
      LoggerUtil.i('【视频号平台】申请上传视频成功: $uploadInfo');
      
      // 4.2 分块上传视频
      LoggerUtil.i('【视频号平台】开始分块上传视频');
      progressCallback?.call(20, '开始上传视频');
      
      // 获取视频文件信息
      final fileSize = uploadInfo['fileSize'] as int;
      final totalChunks = (fileSize / _fileBlockSize).ceil();
      
      LoggerUtil.i('【视频号平台】视频文件大小: $fileSize 字节, 分块数: $totalChunks');
      
      // 计算文件MD5
      LoggerUtil.i('【视频号平台】开始计算视频MD5');
      final videoBytes = await videoFile.readAsBytes();
      final videoMd5 = md5.convert(videoBytes).toString();
      LoggerUtil.i('【视频号平台】视频MD5: $videoMd5');
      
      // 读取视频宽高和时长（实际应该使用FFmpeg或其他库，这里简化处理）
      const videoWidth = 1280;
      const videoHeight = 720;
      const videoDuration = 15; // 秒
      LoggerUtil.i('【视频号平台】视频信息: 宽=$videoWidth, 高=$videoHeight, 时长=$videoDuration秒');
      
      // 分块上传
      for (int i = 0; i < totalChunks; i++) {
        final start = i * _fileBlockSize;
        final end = (i + 1) * _fileBlockSize > fileSize ? fileSize : (i + 1) * _fileBlockSize;
        final chunkSize = end - start;
        
        LoggerUtil.i('【视频号平台】上传视频分块 ${i + 1}/$totalChunks, 大小: ${(chunkSize / 1024 / 1024).toStringAsFixed(2)}MB');
        
        final chunkData = videoBytes.sublist(start, end);
        
        progressCallback?.call(20 + ((i + 1) * 40) ~/ totalChunks, '上传视频片段 ${i + 1}/$totalChunks');
        
        final uploadSuccess = await WxSphService.uploadVideoChunk(
          cookieStr,
          uploadInfo,
          videoFile,
          i,
          totalChunks,
          chunkData,
          progressCallback,
        );
        
        if (!uploadSuccess) {
          LoggerUtil.e('【视频号平台】上传视频分块 ${i + 1}/$totalChunks 失败');
          throw Exception('上传视频片段 ${i + 1}/$totalChunks 失败');
        }
        
        LoggerUtil.i('【视频号平台】上传视频分块 ${i + 1}/$totalChunks 成功');
      }
      
      // 4.3 完成视频上传
      LoggerUtil.i('【视频号平台】完成视频上传');
      progressCallback?.call(60, '完成视频上传');
      
      final completeSuccess = await WxSphService.completeVideoUpload(cookieStr, uploadInfo);
      if (!completeSuccess) {
        LoggerUtil.e('【视频号平台】完成视频上传失败');
        throw Exception('完成视频上传失败');
      }
      
      LoggerUtil.i('【视频号平台】视频上传完成');
      
      // 获取上传后的视频URL
      final remoteFileUrl = uploadInfo['downloadUrl'] ?? '';
      if (remoteFileUrl.isEmpty) {
        LoggerUtil.e('【视频号平台】获取视频URL失败');
        throw Exception('获取视频URL失败');
      }
      
      LoggerUtil.i('【视频号平台】视频远程URL: $remoteFileUrl');
      
      // 5. 上传封面
      LoggerUtil.i('【视频号平台】开始上传封面');
      progressCallback?.call(65, '开始上传封面');
      
      final coverKey = await WxSphService.uploadCoverFile(
        cookieStr,
        uploadInfo['originalParams'],
        coverFile,
      );
      if (coverKey == null) {
        LoggerUtil.e('【视频号平台】上传封面失败');
        throw Exception('上传封面失败');
      }
      
      LoggerUtil.i('【视频号平台】封面上传成功: coverKey=$coverKey');
      
      // 记录上传结束时间 - 确保使用秒级时间戳
      final endUploadTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      LoggerUtil.i('【视频号平台】结束上传时间: $endUploadTime, 耗时: ${endUploadTime - startUploadTime}秒');
      
      // 6. 裁剪视频
      LoggerUtil.i('【视频号平台】开始裁剪视频');
      progressCallback?.call(70, '处理视频');
      
      // 准备视频信息
      final uploadResult = uploadInfo['uploadResult'];
      final fileKey = uploadResult['filekey'];
      LoggerUtil.i('【视频号平台】视频fileKey: $fileKey');
      
      final fileInfo = {
        'md5': videoMd5,
        'size': fileSize,
        'width': videoWidth,
        'height': videoHeight,
        'duration': videoDuration,
      };
      
      LoggerUtil.i('【视频号平台】视频信息: $fileInfo');
      
      final clipData = await WxSphService.clipVideo(
        cookieStr,
        traceKey,
        startUploadTime,
        endUploadTime,
        remoteFileUrl,
        fileInfo,
      );
      
      if (clipData == null) {
        LoggerUtil.e('【视频号平台】视频裁剪失败');
        throw Exception('视频裁剪失败');
      }
      
      LoggerUtil.i('【视频号平台】视频裁剪请求成功: $clipData');
      
      // 7. 获取裁剪结果
      LoggerUtil.i('【视频号平台】等待裁剪结果');
      progressCallback?.call(75, '获取处理结果');
      
      String? clipId = clipData['clipKey'];
      if (clipId == null) {
        LoggerUtil.e('【视频号平台】裁剪ID为空');
        throw Exception('裁剪ID为空');
      }
      
      LoggerUtil.i('【视频号平台】裁剪ID: $clipId');
      Map<String, dynamic>? clipResult;
      
      // 轮询获取裁剪结果
      int retryCount = 0;
      const maxRetries = 30;
      bool isSuccess = false;
      
      while (retryCount < maxRetries) {
        retryCount++;
        LoggerUtil.i('【视频号平台】轮询裁剪结果 $retryCount/$maxRetries');
        
        final result = await WxSphService.getClipResult(cookieStr, clipId);
        
        if (result == null) {
          LoggerUtil.i('【视频号平台】获取裁剪结果为空，继续轮询');
          progressCallback?.call(75, '等待视频处理结果 $retryCount/$maxRetries');
          await Future.delayed(const Duration(seconds: 3));
          continue;
        }
        
        // 检查状态
        final status = result['status'];
        if (status == 2) {
          // 处理成功
          LoggerUtil.i('【视频号平台】视频处理成功');
          clipResult = {
            'clipKey': clipId,
            'url': result['url'] ?? '',
            'fileSize': result['size'] ?? 0,
            'duration': result['duration'] ?? 0,
            'width': result['width'] ?? 0,
            'height': result['height'] ?? 0,
            'thumbUrl':coverKey
          };
          isSuccess = true;
          break;
        } else if (status == 3) {
          // 处理失败
          final errMsg = result['errMsg'] ?? '未知错误';
          final errCode = result['errCode'] ?? 0;
          LoggerUtil.e('【视频号平台】视频处理失败: $errMsg (错误码: $errCode)');
          
          // 特殊处理300800错误 - 如果是Cookie无效，抛出特定异常以便上层重新授权
          if (errCode == 300800) {
            throw Exception('Cookie无效或已过期，请重新授权: $errMsg');
          }
          
          throw Exception('视频处理失败: $errMsg');
        } else {
          // 处理中，继续轮询
          LoggerUtil.i('【视频号平台】视频处理中，继续轮询');
          progressCallback?.call(75, '等待视频处理结果 $retryCount/$maxRetries');
          
          // 增加重试间隔时间，避免请求频率过高
          int waitTime = 3;
          if (retryCount > 10) waitTime = 5;
          if (retryCount > 20) waitTime = 8;
          
          await Future.delayed(Duration(seconds: waitTime));
        }
      }
      
      if (!isSuccess) {
        LoggerUtil.e('【视频号平台】获取裁剪结果超时');
        throw Exception('获取裁剪结果超时，请检查网络连接和Cookie有效性');
      }
      
      LoggerUtil.i('【视频号平台】视频处理完成: $clipResult');
      
      // 8. 构建发布参数
      LoggerUtil.i('【视频号平台】准备发布视频');
      progressCallback?.call(85, '准备发布视频');
      
      // 将发布参数转换为视频号需要的格式
      final publishParams = WxSphPublishParams(
        title: publishParamsModel.title ?? '',
        topics: publishParamsModel.topics,
        description: publishParamsModel.desc,
        postFlag: 1, // 默认为原创
        cover: coverFile.path, // 添加封面参数
      );
      
      LoggerUtil.i('【视频号平台】发布参数: ${publishParams.toJson()}');
      
      // 9. 发布视频
      LoggerUtil.i('【视频号平台】正式发布视频');
      progressCallback?.call(90, '发布视频');
      
      // 确保clipResult非空
      if (clipResult == null) {
        LoggerUtil.e('【视频号平台】裁剪结果为空，无法发布视频');
        throw Exception('视频处理失败，无法发布视频');
      }
      
      final publishResult = await WxSphService.createVideoPost(
        cookieStr,
        traceKey,
        startUploadTime,
        endUploadTime,
        clipResult, // 现在确保了clipResult非空
        publishParams,
      );
      
      if (publishResult == null) {
        LoggerUtil.e('【视频号平台】发布视频失败');
        throw Exception('发布视频失败');
      }
      
      // 10. 返回发布结果
      LoggerUtil.i('【视频号平台】发布视频成功: $publishResult');
      progressCallback?.call(100, '发布成功');
      
      return PublishResponseModel(
        true,
        dataId: publishResult['publishId'],
        workLink: publishResult['shareLink'],
      );
    } catch (e, stackTrace) {
      LoggerUtil.e('【视频号平台】发布视频异常: $e');
      LoggerUtil.e('【视频号平台】异常堆栈: $stackTrace');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }
  
  /// 异步发布图文
  Future<PublishResponseModel> _publishImagesAsync(PublishParamsModel publishParamsModel) async {
    try {
      LoggerUtil.i('【视频号平台】开始异步发布图文流程');
      
      if (publishParamsModel.images == null || publishParamsModel.images!.isEmpty) {
        LoggerUtil.e('【视频号平台】图片列表为空');
        throw Exception('图片列表不能为空');
      }
      
      final coverFile = publishParamsModel.cover;
      final imageFiles = publishParamsModel.images!;
      
      // 检查封面文件是否存在
      if (!await coverFile.exists()) {
        LoggerUtil.e('【视频号平台】封面文件不存在: ${coverFile.path}');
        throw Exception('封面文件不存在或无法访问');
      }
      
      // 检查图片文件是否存在
      for (int i = 0; i < imageFiles.length; i++) {
        final imageFile = imageFiles[i];
        if (!await imageFile.exists()) {
          LoggerUtil.e('【视频号平台】图片文件${i+1}不存在: ${imageFile.path}');
          throw Exception('图片文件${i+1}不存在或无法访问');
        }
      }
      
      // 进度回调
      final progressCallback = publishParamsModel.progressCallback;
      progressCallback?.call(10, '检查登录状态');
      
      // 目前微信视频号暂不支持图文发布功能，返回错误
      LoggerUtil.e('【视频号平台】微信视频号暂不支持图文发布功能');
      return PublishResponseModel(
        false,
        failMsg: '微信视频号暂不支持图文发布功能，请使用视频发布',
      );
    } catch (e, stackTrace) {
      LoggerUtil.e('【视频号平台】发布图文异常: $e');
      LoggerUtil.e('【视频号平台】异常堆栈: $stackTrace');
      return PublishResponseModel(false, failMsg: e.toString());
    }
  }
  
  /// 获取合集列表
  Future<List<WxSphMixInfo>?> getMixList() async {
    try {
      return await WxSphService.getMixList(cookieStr);
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取合集列表异常: $e');
      return null;
    }
  }
  
  /// 获取位置列表
  Future<List<WxSphLocation>?> getLocationList({
    required String query,
    required double longitude,
    required double latitude,
  }) async {
    try {
      return await WxSphService.getLocationList(
        cookieStr,
        query: query,
        longitude: longitude,
        latitude: latitude,
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取位置列表异常: $e');
      return null;
    }
  }
  
  /// 获取作品列表
  Future<List<WxSphPost>?> getPostList({
    int pageNo = 1,
    int pageSize = 10,
  }) async {
    try {
      return await WxSphService.getPostList(
        cookieStr,
        pageNo: pageNo,
        pageSize: pageSize,
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取作品列表异常: $e');
      return null;
    }
  }
  
  /// 获取评论列表
  Future<List<WxSphComment>?> getCommentList(String exportId) async {
    try {
      return await WxSphService.getCommentList(cookieStr, exportId);
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取评论列表异常: $e');
      return null;
    }
  }
  
  /// 创建评论
  Future<bool> createComment(
    String exportId,
    String content, {
    String? replyCommentId,
  }) async {
    try {
      return await WxSphService.createComment(
        cookieStr,
        exportId,
        content,
        replyCommentId: replyCommentId,
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】创建评论异常: $e');
      return false;
    }
  }
  
  /// 获取数据统计
  Future<List<WxSphDashboardData>?> getDashboardData({
    String? startDate,
    String? endDate,
  }) async {
    try {
      return await WxSphService.getDashboardData(
        cookieStr,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】获取数据统计异常: $e');
      return null;
    }
  }
  
  @override
  getMakeRequestHedears() {
    return {
      'Referer': 'https://channels.weixin.qq.com/',
      'Origin': 'https://channels.weixin.qq.com/',
    };
  }
  
  @override
  Future<PublishResponseModel> publishImgText(PublishParamsModel publishParamsModel) async {
    try {
      // 保存进度回调函数的引用
      final progressCallback = publishParamsModel.progressCallback;
      
      // 先发送开始处理的进度
      progressCallback?.call(0, '准备处理...');
      
      // 视频号暂不支持图文发布
      return PublishResponseModel(
        false,
        failMsg: '微信视频号暂不支持图文发布',
      );
    } catch (e) {
      LoggerUtil.e('【视频号平台】发布图文异常: $e');
      return PublishResponseModel(
        false,
        failMsg: e.toString(),
      );
    }
  }
  
  /// 搜索POI位置
  /// 
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [keyword] 搜索关键词
  /// 返回POI搜索结果
  Future<WxSphPoiSearchResponse> searchPoi({
    required double latitude,
    required double longitude,
    String keyword = '',
  }) async {
    // 使用基类的cookieStr
    return await WxSphService.searchPoi(
      cookieStr: this.cookieStr,
      latitude: latitude,
      longitude: longitude,
      keyword: keyword,
    );
  }

  /// 搜索活动
  Future<List<WxSphActivity>> searchActivities(String keyword) async {
    try {
      _checkPlatformSupport();
      LoggerUtil.i('【视频号平台】搜索活动: $keyword');

      return await WxSphService.searchActivities(keyword, cookieStr);
    } catch (e) {
      LoggerUtil.e('【视频号平台】搜索活动失败: $e');
      return [];
    }
  }

  /// 搜索用户
  Future<List<WxSphUser>> searchUsers(String keyword, {int page = 1}) async {
    try {
      _checkPlatformSupport();
      LoggerUtil.i('【视频号平台】搜索用户: $keyword, page: $page');

      return await WxSphService.searchUsers(keyword, cookieStr, page: page);
    } catch (e) {
      LoggerUtil.e('【视频号平台】搜索用户失败: $e');
      return [];
    }
  }

  /// 检查登录状态 - 使用现有的静态方法
  Future<bool> checkWxSphLoginStatus() async {
    try {
      _checkPlatformSupport();

      return await WxSphService.checkLoginStatus(cookieStr);
    } catch (e) {
      LoggerUtil.e('【视频号平台】检查登录状态失败: $e');
      return false;
    }
  }
}
