import 'package:aitoearn_app/config/logger.dart';

/// 快手POI搜索响应模型
class KwaiPoiSearchResponse {
  final int statusCode;
  final String statusMsg;
  final List<KwaiPoiItem> poiList;
  final bool hasMore;

  KwaiPoiSearchResponse({
    required this.statusCode,
    required this.statusMsg,
    required this.poiList,
    required this.hasMore,
  });

  /// 从JSON创建响应
  factory KwaiPoiSearchResponse.fromJson(Map<String, dynamic> json) {
    final poiList = <KwaiPoiItem>[];
    
    // 处理附近位置数据列表 - 兼容两种不同格式
    if (json['data'] != null) {
      try {
        // 处理locations字段 (新格式)
        if (json['data']['locations'] != null && json['data']['locations'] is List) {
          final List<dynamic> locationsList = json['data']['locations'];
          for (var item in locationsList) {
            if (item is Map<String, dynamic>) {
              poiList.add(KwaiPoiItem.fromJson(item));
            }
          }
        }
        // 处理nearbyInfo字段 (旧格式)
        else if (json['data']['nearbyInfo'] != null && json['data']['nearbyInfo'] is List) {
          final List<dynamic> nearbyList = json['data']['nearbyInfo'];
          for (var item in nearbyList) {
            if (item is Map<String, dynamic>) {
              poiList.add(KwaiPoiItem.fromJson(item));
            }
          }
        }
        
        // 获取城市名称
        final cityName = json['data']['cityName'] ?? '';
        if (cityName.isNotEmpty && poiList.isNotEmpty) {
          for (var poi in poiList) {
            if (poi.cityName.isEmpty) {
              poi = poi.copyWith(cityName: cityName);
            }
          }
        }
      } catch (e) {
        LoggerUtil.i('处理位置数据出错: $e');
      }
    }
    
    return KwaiPoiSearchResponse(
      statusCode: json['result'] ?? 0,
      statusMsg: json['message'] ?? '',
      poiList: poiList,
      hasMore: json['data']?['hasMore'] ?? json['data']?['pcursor'] != null,
    );
  }

  /// 从原始API响应创建POI响应对象
  factory KwaiPoiSearchResponse.fromRawApiResponse(Map<String, dynamic> json) {
    final poiList = <KwaiPoiItem>[];
    
    try {
      // 处理locations字段（新API格式）
      if (json['data'] != null && json['data']['locations'] != null) {
        final List<dynamic> locationsList = json['data']['locations'];
        for (var item in locationsList) {
          if (item is Map<String, dynamic>) {
            poiList.add(KwaiPoiItem.fromJson(item));
          }
        }
      }
      
      // 处理nearbyInfo字段（旧API格式）
      else if (json['data'] != null && json['data']['nearbyInfo'] != null) {
        final List<dynamic> nearbyList = json['data']['nearbyInfo'];
        for (var item in nearbyList) {
          if (item is Map<String, dynamic>) {
            poiList.add(KwaiPoiItem.fromJson(item));
          }
        }
      }
      
      // 处理可能直接提供的单个位置对象
      else if (json['nearbyInfo'] != null) {
        final List<dynamic> nearbyList = json['nearbyInfo'];
        for (var item in nearbyList) {
          if (item is Map<String, dynamic>) {
            poiList.add(KwaiPoiItem.fromJson(item));
          }
        }
      }
      
      // 处理位置列表字段
      else if (json['poi_list'] != null) {
        final List<dynamic> poiItems = json['poi_list'];
        for (var item in poiItems) {
          if (item is Map<String, dynamic>) {
            poiList.add(KwaiPoiItem.fromJson(item));
          }
        }
      }
      
      // 处理locations字段（直接提供）
      else if (json['locations'] != null) {
        final List<dynamic> locationsList = json['locations'];
        for (var item in locationsList) {
          if (item is Map<String, dynamic>) {
            poiList.add(KwaiPoiItem.fromJson(item));
          }
        }
      }
      
      // 如果json本身看起来像一个POI项，则直接解析
      else if (json['address'] != null || json['name'] != null || json['title'] != null) {
        poiList.add(KwaiPoiItem.fromJson(json));
      }
      
      // 处理全局城市名称
      final cityName = json['data']?['cityName'] ?? '';
      if (cityName.isNotEmpty && poiList.isNotEmpty) {
        for (int i = 0; i < poiList.length; i++) {
          if (poiList[i].cityName.isEmpty) {
            poiList[i] = poiList[i].copyWith(cityName: cityName);
          }
        }
      }
    } catch (e) {
      LoggerUtil.i('解析快手POI数据失败: $e');
    }
    
    return KwaiPoiSearchResponse(
      statusCode: json['result'] ?? 0,
      statusMsg: json['message'] ?? '',
      poiList: poiList,
      hasMore: json['data']?['hasMore'] ?? json['data']?['pcursor'] != null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'result': statusCode,
      'message': statusMsg,
      'data': {
        'nearbyInfo': poiList.map((poi) => poi.toJson()).toList(),
        'hasMore': hasMore,
      },
    };
  }
}

/// 快手POI位置项模型
class KwaiPoiItem {
  final String poiId;
  final String poiName;
  final String address;
  final double latitude;
  final double longitude;
  final String cityName;
  final String districtName;
  final int distance;
  
  KwaiPoiItem({
    required this.poiId,
    required this.poiName,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.cityName,
    this.districtName = '',
    required this.distance,
  });
  
  /// 创建一个新的KwaiPoiItem实例，同时更新部分字段
  KwaiPoiItem copyWith({
    String? poiId,
    String? poiName,
    String? address,
    double? latitude,
    double? longitude,
    String? cityName,
    String? districtName,
    int? distance,
  }) {
    return KwaiPoiItem(
      poiId: poiId ?? this.poiId,
      poiName: poiName ?? this.poiName,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      cityName: cityName ?? this.cityName,
      districtName: districtName ?? this.districtName,
      distance: distance ?? this.distance,
    );
  }
  
  /// 从JSON创建POI项
  factory KwaiPoiItem.fromJson(Map<String, dynamic> json) {
    // 处理位置ID - 新格式使用id或idString
    String poiId = '';
    if (json['id'] != null) {
      poiId = json['id'].toString();
    } else if (json['idString'] != null) {
      poiId = json['idString'];
    } else if (json['poiId'] != null) {
      poiId = json['poiId'];
    }
    
    // 处理位置名称 - 新格式使用title
    String poiName = json['title'] ?? json['name'] ?? '';
    
    // 处理地址 - 可能为null
    String address = json['address'] ?? '';
    
    // 处理城市和区域
    String cityName = json['city'] ?? '';
    String districtName = json['district'] ?? '';
    
    // 处理经纬度
    double latitude = 0.0;
    double longitude = 0.0;
    
    if (json['latitude'] != null && json['longitude'] != null) {
      latitude = _parseDoubleValue(json['latitude']);
      longitude = _parseDoubleValue(json['longitude']);
    } else if (json['location'] != null) {
      // 处理location字符串，格式如："30.7001626,112.6271791"
      final locationParts = json['location'].toString().split(',');
      if (locationParts.length == 2) {
        latitude = _parseDoubleValue(locationParts[0]);
        longitude = _parseDoubleValue(locationParts[1]);
      }
    }
    
    // 处理距离 - 新API没有直接提供距离
    int distance = 0;
    if (json['distance'] != null) {
      if (json['distance'] is num) {
        distance = (json['distance'] as num).toInt();
      } else if (json['distance'] is String) {
        try {
          final distanceStr = json['distance'].toString();
          if (distanceStr.contains('km')) {
            // 如果包含km，转换为米
            final kmValue = double.tryParse(
              distanceStr.replaceAll(RegExp(r'[^0-9.]'), '')
            );
            if (kmValue != null) {
              distance = (kmValue * 1000).toInt();
            }
          } else if (distanceStr.contains('m')) {
            // 如果包含m，直接解析数字部分
            final mValue = int.tryParse(
              distanceStr.replaceAll(RegExp(r'[^0-9]'), '')
            );
            if (mValue != null) {
              distance = mValue;
            }
          } else {
            // 尝试直接解析为整数
            distance = int.tryParse(distanceStr) ?? 0;
          }
        } catch (e) {
          LoggerUtil.i('解析距离失败: $e');
        }
      }
    }
    
    return KwaiPoiItem(
      poiId: poiId,
      poiName: poiName,
      address: address,
      latitude: latitude,
      longitude: longitude,
      cityName: cityName,
      districtName: districtName,
      distance: distance,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'poiId': poiId,
      'title': poiName,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'city': cityName,
      'district': districtName,
      'distance': distance,
      'location': '$latitude,$longitude',
    };
  }

  /// 辅助方法：解析double值
  static double _parseDoubleValue(dynamic value) {
    if (value == null) return 0.0;

    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }

    return 0.0;
  }
}
