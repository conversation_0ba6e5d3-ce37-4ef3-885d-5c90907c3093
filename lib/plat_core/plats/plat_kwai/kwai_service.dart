import 'package:aitoearn_app/config/app_config.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/utils/dio_util/dio_util.dart';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';

class KwaiService {
  /// 快手API签名接口
  static const String kwaiSignUrl = '${AppConfig.appHostApi}/tools/common/kwaiSign';
  
  /// 快手创作者平台基础URL
  static const String kwaiBaseUrl = 'https://cp.kuaishou.com';
  
  /// 快手上传视频的API URL
  static const String kwaiUploadBaseUrl = 'https://upload.kuaishouzt.com';
  
  /// 获取快手签名
  /// [json] 需要签名的JSON数据，必须包含kuaishou.web.cp.api_ph字段
  static Future<String?> getKwaiSign(Map<String, dynamic> json) async {
    try {
      // 确保json中包含必要的字段
      if (!json.containsKey('kuaishou.web.cp.api_ph')) {
        LoggerUtil.e('签名请求缺少必要字段: kuaishou.web.cp.api_ph');
        return null;
      }
      
      // 添加当前时间戳，确保每次请求签名都是最新的
      // json['timestamp'] = DateTime.now().millisecondsSinceEpoch.toString();
      
      final requestData = {
        'json': json,
        'type': 'json'
      };
      
      LoggerUtil.i('【快手签名请求】URL: $kwaiSignUrl');
      LoggerUtil.i('【快手签名请求】数据: $requestData');
      
      final response = await DioUtils.defaultClient.request(
        kwaiSignUrl,
        data: requestData,
        options: Options(method: 'POST'),
      );
      
      LoggerUtil.i('【快手签名响应】状态码: ${response?.statusCode}');
      LoggerUtil.i('【快手签名响应】数据: ${response?.data}');
      
      if (response?.statusCode == 200 || response?.statusCode == 201) {
        final data = response?.data;
        if (data != null && data['code'] == 0) {
          return data['data'];
        } else {
          LoggerUtil.e('获取签名失败: ${data?['msg'] ?? '未知错误'}');
          return null;
        }
      } else {
        LoggerUtil.e('获取签名请求失败: ${response?.statusCode}');
        return null;
      }
    } catch (e) {
      LoggerUtil.e('获取签名异常: $e');
      return null;
    }
  }
  
  /// 发送带签名的请求
  /// [url] 请求的URL，不包含签名部分
  /// [json] 请求的JSON数据，必须包含kuaishou.web.cp.api_ph字段
  /// [method] 请求方法，默认为POST
  /// [cookieStr] Cookie字符串，用于身份验证
  /// [queryParameters] 额外的查询参数
  static Future<Response?> requestWithSign({
    required String url,
    required Map<String, dynamic> json,
    String method = 'POST',
    Map<String, dynamic>? queryParameters,
    String? cookieStr,
  }) async {
    try {
      LoggerUtil.i('【快手API请求开始】URL: $url');
      LoggerUtil.i('【快手API请求】数据: $json');
      LoggerUtil.i('【快手API请求】方法: $method');
      LoggerUtil.i('【快手API请求】查询参数: $queryParameters');
      LoggerUtil.i('【快手API请求】Cookie: ${cookieStr?.substring(0, 50)}...');
      
      // 添加时间戳，确保每次请求都是新鲜的
      // json['_'] = DateTime.now().millisecondsSinceEpoch.toString();
      
      final sign = await getKwaiSign(json);
      if (sign == null) {
        LoggerUtil.e('【快手API请求】获取签名失败');
        return null;
      }
      
      LoggerUtil.i('【快手API请求】获取到签名: $sign');
      
      // 构建带签名和其他必要参数的URL
      var signedUrl = '$url';
      
      // 构建查询参数，包含必要的字段
      final Map<String, dynamic> finalQueryParams = {
        '__NS_sig3': sign,
      };
      
      // // 添加基本必要参数
      // final Map<String, dynamic> baseParams = {
      //   'kpn': 'kuaishou_cp',
      //   'subBiz': 'CP/CREATOR_PLATFORM',
      //   'kpf': 'PC_WEB',
      // };
      //
      // 合并参数
      // finalQueryParams.addAll(baseParams);
      
      // 合并用户提供的查询参数
      if (queryParameters != null) {
        finalQueryParams.addAll(queryParameters);
      }
      
      // 构建完整URL
      final Uri uri = Uri.parse(signedUrl).replace(
        queryParameters: finalQueryParams.map((key, value) => 
          MapEntry(key, value.toString())
        ),
      );
      
      signedUrl = uri.toString();
      LoggerUtil.i('【快手API请求】最终URL: $signedUrl');
      
      // 设置请求头，包含Cookie
      final headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': 'https://cp.kuaishou.com/',
        'Origin': 'https://cp.kuaishou.com',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json;charset=UTF-8',
        'sec-ch-ua': '"Google Chrome";v="122", "Chromium";v="122", "Not(A:Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
      };
      
      // 添加Cookie到请求头
      if (cookieStr != null && cookieStr.isNotEmpty) {
        headers['Cookie'] = cookieStr;
      }
      
      LoggerUtil.i('【快手API请求】请求头: ${headers.keys.join(", ")}');
      
      final response = await DioUtils.client.request(
        signedUrl,
        data: method != 'GET' ? json : null,
        options: Options(
          method: method,
          headers: headers,
        ),
      );
      
      LoggerUtil.i('【快手API响应】状态码: ${response?.statusCode}');
      LoggerUtil.i('【快手API响应】数据: ${response?.data}');
      
      return response;
    } catch (e) {
      LoggerUtil.e('【快手API请求异常】: $e');
      return null;
    }
  }
  
  /// 获取用户信息
  /// [apiPh] 用户的api_ph参数，从cookie中获取
  /// [cookieStr] 用户的完整cookie字符串
  static Future<Map<String, dynamic>?> getUserInfo(String apiPh, String cookieStr) async {
    try {
      LoggerUtil.i('【快手API】获取用户信息，apiPh: $apiPh');
      
      final json = {
        'kuaishou.web.cp.api_ph': apiPh,
        // 添加随机数和时间戳，避免缓存
        // 'random': DateTime.now().millisecondsSinceEpoch.toString(),
      };
      
      final response = await requestWithSign(
        url: '$kwaiBaseUrl/rest/cp/creator/pc/home/<USER>',
        json: json,
        cookieStr: cookieStr,
      );
      
      if (response == null) {
        LoggerUtil.e('【快手API】获取用户信息失败: 请求返回为空');
        return null;
      }
      
      if (response.statusCode != 200 && response.statusCode != 201) {
        LoggerUtil.e('【快手API】获取用户信息失败: 状态码 ${response.statusCode}');
        return null;
      }
      
      // 详细记录响应数据
      LoggerUtil.i('【快手API】获取用户信息响应: ${response.data}');
      
      // 检查响应结果
      if (response.data['result'] != 1) {
        LoggerUtil.e('【快手API】获取用户信息失败: 错误码 ${response.data['result']}, 消息: ${response.data['message']}');
        
        // 如果是登录过期或需要重新登录的错误
        if (response.data['result'] == 500002) {
          LoggerUtil.e('【快手API】Cookie已过期，需要重新登录');
        }
        
        return response.data; // 返回错误信息，让调用方处理
      }
      
      LoggerUtil.i('【快手API】获取用户信息成功');
      return response.data;
    } catch (e) {
      LoggerUtil.e('【快手API】获取用户信息异常: $e');
      return null;
    }
  }
  
  /// 上传视频预处理
  /// [apiPh] 用户的api_ph参数
  /// [cookieStr] 用户的完整cookie字符串
  static Future<Map<String, dynamic>?> uploadVideoPre(String apiPh, String cookieStr) async {
    try {
      LoggerUtil.i('【快手API】上传视频预处理');
      
      final json = {
        'kuaishou.web.cp.api_ph': apiPh,
        'uploadType': 1
      };
      
      final response = await requestWithSign(
        url: '$kwaiBaseUrl/rest/cp/works/v2/video/pc/upload/pre',
        json: json,
        cookieStr: cookieStr,
      );
      
      if (response == null) {
        LoggerUtil.e('【快手API】上传视频预处理失败: 请求返回为空');
        return null;
      }
      
      if (response.statusCode != 200 && response.statusCode != 201) {
        LoggerUtil.e('【快手API】上传视频预处理失败: 状态码 ${response.statusCode}');
        return null;
      }
      
      if (response.data['result'] != 1) {
        LoggerUtil.e('【快手API】上传视频预处理失败: ${response.data['message']}');
        return null;
      }
      
      LoggerUtil.i('【快手API】上传视频预处理成功: ${response.data}');
      return response.data;
    } catch (e) {
      LoggerUtil.e('【快手API】上传视频预处理异常: $e');
      return null;
    }
  }
  
  /// 上传视频分片
  /// [token] 上传凭证
  /// [fragmentId] 分片ID
  /// [data] 分片数据
  static Future<Map<String, dynamic>?> uploadVideoFragment(String token, String fragmentId, List<int> data) async {
    try {
      LoggerUtil.i('【快手API】上传视频分片: fragmentId=$fragmentId');
      
      final url = '$kwaiUploadBaseUrl/api/upload/fragment?upload_token=$token&fragment_id=$fragmentId';
      
      LoggerUtil.i('【快手API】上传视频分片URL: $url');
      
      final response = await DioUtils.client.request(
        url,
        data: Stream.fromIterable([data]),
        options: Options(
          method: 'POST',
          headers: {
            'Content-Type': 'application/octet-stream',
          },
          contentType: 'application/octet-stream',
        ),
      );
      
      LoggerUtil.i('【快手API】上传视频分片响应: ${response?.data}');
      
      if (response?.statusCode != 200 && response?.statusCode != 201) {
        LoggerUtil.e('【快手API】上传视频分片失败: 状态码 ${response?.statusCode}');
        return null;
      }
      
      return response?.data;
    } catch (e) {
      LoggerUtil.e('【快手API】上传视频分片异常: $e');
      return null;
    }
  }
  
  /// 完成视频分片上传
  /// [token] 上传凭证
  /// [fragmentCount] 分片总数
  /// [apiPh] 用户的api_ph参数
  /// [cookieStr] 用户的完整cookie字符串
  static Future<Map<String, dynamic>?> completeVideoUpload(String token, int fragmentCount, String apiPh, String cookieStr) async {
    try {
      LoggerUtil.i('【快手API】完成视频分片上传: token=$token, fragmentCount=$fragmentCount');
      
      final url = '$kwaiUploadBaseUrl/api/upload/complete?upload_token=$token&fragment_count=$fragmentCount';
      
      LoggerUtil.i('【快手API】完成视频分片上传URL: $url');
      
      final json = {
        'kuaishou.web.cp.api_ph': apiPh,
        'uploadType': 1
      };
      
      final headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': 'https://cp.kuaishou.com/',
        'Origin': 'https://cp.kuaishou.com',
      };
      
      if (cookieStr.isNotEmpty) {
        headers['Cookie'] = cookieStr;
      }
      
      final response = await DioUtils.client.request(
        url,
        data: json,
        options: Options(
          method: 'POST',
          headers: headers,
        ),
      );
      
      LoggerUtil.i('【快手API】完成视频分片上传响应: ${response?.data}');
      
      if (response?.statusCode != 200 && response?.statusCode != 201) {
        LoggerUtil.e('【快手API】完成视频分片上传失败: 状态码 ${response?.statusCode}');
        return null;
      }
      
      return response?.data;
    } catch (e) {
      LoggerUtil.e('【快手API】完成视频分片上传异常: $e');
      return null;
    }
  }
  
  /// 视频上传结束
  /// [apiPh] 用户的api_ph参数
  /// [cookieStr] 用户的完整cookie字符串
  /// [token] 上传凭证
  /// [fileName] 文件名
  /// [fileType] 文件类型，如'video/mp4'
  /// [fileLength] 文件大小
  static Future<Map<String, dynamic>?> finishVideoUpload(
    String apiPh, 
    String cookieStr, 
    String token, 
    String fileName, 
    String fileType, 
    int fileLength
  ) async {
    try {
      LoggerUtil.i('【快手API】视频上传结束');
      
      final json = {
        'kuaishou.web.cp.api_ph': apiPh,
        'token': token,
        'fileName': fileName,
        'fileType': fileType,
        'fileLength': fileLength,
      };
      
      final response = await requestWithSign(
        url: '$kwaiBaseUrl/rest/cp/works/v2/video/pc/upload/finish',
        json: json,
        cookieStr: cookieStr,
      );
      
      if (response == null) {
        LoggerUtil.e('【快手API】视频上传结束失败: 请求返回为空');
        return null;
      }
      
      if (response.statusCode != 200 && response.statusCode != 201) {
        LoggerUtil.e('【快手API】视频上传结束失败: 状态码 ${response.statusCode}');
        return null;
      }
      
      if (response.data['result'] != 1) {
        LoggerUtil.e('【快手API】视频上传结束失败: ${response.data['message']}');
        return null;
      }
      
      LoggerUtil.i('【快手API】视频上传结束成功: ${response.data}');
      return response.data;
    } catch (e) {
      LoggerUtil.e('【快手API】视频上传结束异常: $e');
      return null;
    }
  }
  
  /// 上传视频封面
  /// [apiPh] 用户的api_ph参数
  /// [cookieStr] 用户的完整cookie字符串
  /// [coverFile] 封面文件数据
  static Future<Map<String, dynamic>?> uploadVideoCover(String apiPh, String cookieStr, List<int> coverFile) async {
    try {
      LoggerUtil.i('【快手API】上传视频封面');
      
      final json = {
        'kuaishou.web.cp.api_ph': apiPh,
      };
      
      // 创建FormData对象
      final formData = FormData.fromMap({
        'kuaishou.web.cp.api_ph': apiPh,
        'file': MultipartFile.fromBytes(
          coverFile,
          filename: 'cover.jpg',
          contentType: MediaType.parse('image/jpeg'),
        ),
      });
      
      // 获取签名
      final sign = await getKwaiSign(json);
      if (sign == null) {
        LoggerUtil.e('【快手API】获取签名失败');
        return null;
      }
      
      LoggerUtil.i('【快手API】获取到签名: $sign');
      
      // 构建带签名的URL
      final signedUrl = '$kwaiBaseUrl/rest/cp/works/v2/video/pc/upload/cover/upload?__NS_sig3=$sign';
      
      // 设置请求头
      final headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': 'https://cp.kuaishou.com/',
        'Origin': 'https://cp.kuaishou.com',
      };
      
      if (cookieStr.isNotEmpty) {
        headers['Cookie'] = cookieStr;
      }
      
      final response = await DioUtils.client.request(
        signedUrl,
        data: formData,
        options: Options(
          method: 'POST',
          headers: headers,
        ),
      );
      
      LoggerUtil.i('【快手API】上传视频封面响应: ${response?.data}');
      
      if (response?.statusCode != 200 && response?.statusCode != 201) {
        LoggerUtil.e('【快手API】上传视频封面失败: 状态码 ${response?.statusCode}');
        return null;
      }
      
      return response?.data;
    } catch (e) {
      LoggerUtil.e('【快手API】上传视频封面异常: $e');
      return null;
    }
  }
  
  /// 提交发布视频
  /// [apiPh] 用户的api_ph参数
  /// [cookieStr] 用户的完整cookie字符串
  /// [submitParams] 发布参数
  static Future<Map<String, dynamic>?> submitVideo(String apiPh, String cookieStr, Map<String, dynamic> submitParams) async {
    try {
      LoggerUtil.i('【快手API】提交发布视频');
      
      // 确保参数中包含api_ph
      submitParams['kuaishou.web.cp.api_ph'] = apiPh;
      
      final response = await requestWithSign(
        url: '$kwaiBaseUrl/rest/cp/works/v2/video/pc/submit',
        json: submitParams,
        cookieStr: cookieStr,
      );
      
      if (response == null) {
        LoggerUtil.e('【快手API】提交发布视频失败: 请求返回为空');
        return null;
      }
      
      if (response.statusCode != 200 && response.statusCode != 201) {
        LoggerUtil.e('【快手API】提交发布视频失败: 状态码 ${response.statusCode}');
        return null;
      }
      
      if (response.data['result'] != 1) {
        LoggerUtil.e('【快手API】提交发布视频失败: ${response.data['message']}');
        return null;
      }
      
      LoggerUtil.i('【快手API】提交发布视频成功: ${response.data}');
      return response.data;
    } catch (e) {
      LoggerUtil.e('【快手API】提交发布视频异常: $e');
      return null;
    }
  }
  
  /// 获取作品列表
  /// [apiPh] 用户的api_ph参数
  /// [cookieStr] 用户的完整cookie字符串
  /// [queryType] 查询类型：0=全部作品，1=已发布，2=待发布，3=未通过
  static Future<Map<String, dynamic>?> getWorksList(String apiPh, String cookieStr, String queryType) async {
    try {
      LoggerUtil.i('【快手API】获取作品列表');
      
      final json = {
        'kuaishou.web.cp.api_ph': apiPh,
        'cursor': DateTime.now().millisecondsSinceEpoch,
        'queryType': queryType,
        'limit': 20,
        'timeRangeType': 5,
        'keyword': '',
        'startTime': DateTime.now().millisecondsSinceEpoch - 1000 * 60 * 60 * 24 * 30, // 30天前
        'endTime': DateTime.now().millisecondsSinceEpoch,
      };
      
      final response = await requestWithSign(
        url: '$kwaiBaseUrl/rest/cp/works/v2/video/pc/photo/list',
        json: json,
        cookieStr: cookieStr,
      );
      
      if (response == null) {
        LoggerUtil.e('【快手API】获取作品列表失败: 请求返回为空');
        return null;
      }
      
      if (response.statusCode != 200 && response.statusCode != 201) {
        LoggerUtil.e('【快手API】获取作品列表失败: 状态码 ${response.statusCode}');
        return null;
      }
      
      LoggerUtil.i('【快手API】获取作品列表成功');
      return response.data;
    } catch (e) {
      LoggerUtil.e('【快手API】获取作品列表异常: $e');
      return null;
    }
  }
  
  /// 获取附近的POI位置
  /// [apiPh] 用户的api_ph参数，从cookie中获取
  /// [cookieStr] 用户的完整cookie字符串
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [count] 返回的位置数量，默认20
  static Future<Map<String, dynamic>?> searchNearbyPoi({
    required String apiPh, 
    required String cookieStr,
    required double latitude,
    required double longitude,
    int count = 20,
  }) async {
    try {
      LoggerUtil.i('【快手API】获取附近POI位置，经纬度: $latitude, $longitude');
      
      // 构建请求的JSON数据
      final json = {
        'kuaishou.web.cp.api_ph': apiPh,
        'location': '$latitude,$longitude',
        'count': count,
      };
      
      // 获取签名
      final sign = await getKwaiSign(json);
      if (sign == null) {
        LoggerUtil.e('【快手API】获取签名失败');
        return null;
      }
      
      LoggerUtil.i('【快手API】获取到签名: $sign');
      
      // 构建必要的查询参数
      final queryParameters = {
        'kpn': 'kuaishou_cp',
        'subBiz': 'CP/CREATOR_PLATFORM',
        'kpf': 'PC_WEB',
        'kuaishou.web.cp.api_ph': apiPh,
        '__NS_sig3': sign,
      };
      
      LoggerUtil.i('【快手API】查询参数: $queryParameters');
      
      // 设置请求头
      final headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://cp.kuaishou.com',
        'Referer': 'https://cp.kuaishou.com/article/publish/video?tabType=2',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'Cookie': cookieStr,
      };
      
      // 直接使用Dio发送请求，模拟浏览器行为
      final dio = Dio();
      final url = '$kwaiBaseUrl/rest/cp/works/v2/common/pc/nearby';
      
      LoggerUtil.i('【快手API】直接请求URL: $url');
      
      final response = await dio.request(
        url,
        data: json,
        queryParameters: queryParameters,
        options: Options(
          method: 'POST',
          headers: headers,
        ),
      );
      
      if (response.statusCode != 200 && response.statusCode != 201) {
        LoggerUtil.e('【快手API】获取附近POI位置失败: 状态码 ${response.statusCode}');
        return null;
      }
      
      if (response.data['result'] != 1) {
        LoggerUtil.e('【快手API】获取附近POI位置失败: ${response.data['message']}');
        return null;
      }
      String data = response.toString();
      LoggerUtil.i('【快手API】获取附近POI位置成功: ${response.data}');
      return response.data;
    } catch (e) {
      LoggerUtil.e('【快手API】获取附近POI位置异常: $e');
      return null;
    }
  }
} 