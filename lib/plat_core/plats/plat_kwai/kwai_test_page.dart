import 'dart:async';
import 'dart:io';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/plat_form_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_kwai/kwai_user_info_page.dart';
import 'package:aitoearn_app/plat_core/plats/plat_kwai/plat_kwai.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

class KwaiTestPage extends StatefulWidget {
  const KwaiTestPage({super.key});

  @override
  State<KwaiTestPage> createState() => _KwaiTestPageState();
}

class _KwaiTestPageState extends State<KwaiTestPage> {
  final TextEditingController _cookieController = TextEditingController();
  final TextEditingController _descController = TextEditingController();
  final TextEditingController _topicsController = TextEditingController();
  
  File? _videoFile;
  File? _coverFile;
  List<File> _imageFiles = [];
  
  String _resultMessage = '';
  bool _isLoading = false;
  double _uploadProgress = 0.0;
  String _uploadStatus = '';
  
  // 添加订阅，用于接收异步进度更新
  StreamSubscription? _progressSubscription;
  
  @override
  void dispose() {
    _cookieController.dispose();
    _descController.dispose();
    _topicsController.dispose();
    _progressSubscription?.cancel();
    super.dispose();
  }
  
  // 从cookie字符串中解析cookie
  List<Cookie> _parseCookies(String cookieStr) {
    List<Cookie> cookies = [];
    
    final cookiePairs = cookieStr.split(';');
    for (var pair in cookiePairs) {
      pair = pair.trim();
      if (pair.isEmpty) continue;
      
      final index = pair.indexOf('=');
      if (index > 0) {
        final name = pair.substring(0, index).trim();
        final value = pair.substring(index + 1).trim();
        
        final cookie = Cookie(name, value);
        cookies.add(cookie);
      }
    }
    
    return cookies;
  }
  
  // 选择视频
  Future<void> _pickVideo() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickVideo(source: ImageSource.gallery);
      
      if (pickedFile != null) {
        setState(() {
          _videoFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      LoggerUtil.e('选择视频失败: $e');
    }
  }
  
  // 选择封面
  Future<void> _pickCover() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedFile != null) {
        setState(() {
          _coverFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      LoggerUtil.e('选择封面失败: $e');
    }
  }
  
  // 选择图片
  Future<void> _pickImages() async {
    try {
      final picker = ImagePicker();
      final pickedFiles = await picker.pickMultiImage();
      
      if (pickedFiles.isNotEmpty) {
        setState(() {
          _imageFiles = pickedFiles.map((file) => File(file.path)).toList();
        });
      }
    } catch (e) {
      LoggerUtil.e('选择图片失败: $e');
    }
  }
  
  // 获取用户信息
  Future<void> _getUserInfo() async {
    setState(() {
      _isLoading = true;
      _resultMessage = '正在获取用户信息...';
    });
    
    try {
      final cookies = _parseCookies(_cookieController.text);
      final platformModel = PlatformModel(cookieList: cookies);
      final platKwai = PlatKwai(platformModel);
      
      final userInfo = await platKwai.getUserInfo();
      
      setState(() {
        _resultMessage = '用户信息获取成功: $userInfo';
      });
    } catch (e) {
      setState(() {
        _resultMessage = '获取用户信息失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  // 发布视频
  Future<void> _publishVideo() async {
    if (_videoFile == null || _coverFile == null) {
      setState(() {
        _resultMessage = '请先选择视频和封面';
      });
      return;
    }
    
    // 取消之前的订阅
    _progressSubscription?.cancel();
    
    setState(() {
      _isLoading = true;
      _resultMessage = '正在发布视频...';
      _uploadProgress = 0.0;
      _uploadStatus = '准备上传...';
    });
    
    // 创建进度控制器
    final progressController = StreamController<Map<String, dynamic>>();
    
    // 订阅进度更新
    _progressSubscription = progressController.stream.listen((data) {
      if (mounted) {
        setState(() {
          _uploadProgress = (data['progress'] as int).toDouble();
          if (data['message'] != null) {
            _uploadStatus = data['message'] as String;
          }
        });
      }
    });
    
    try {
      final cookies = _parseCookies(_cookieController.text);
      final platformModel = PlatformModel(cookieList: cookies);
      final platKwai = PlatKwai(platformModel);
      
      // 解析话题
      final topics = _topicsController.text.isEmpty 
          ? <String>[] 
          : _topicsController.text.split(',').map((e) => e.trim()).toList().cast<String>();
      
      final publishParams = PublishParamsModel(
        _coverFile!,
        desc: _descController.text,
        topics: topics,
        video: _videoFile,
        progressCallback: (progress, message) {
          // 将进度更新发送到流中
          progressController.add({
            'progress': progress,
            'message': message,
          });
        },
      );
      
      // 异步执行发布
      setState(() {
        _resultMessage = '正在发布中...';
      });
      
      // 执行发布并等待结果
      final result = await platKwai.publishVideo(publishParams);
      
      if (mounted) {
        setState(() {
          _resultMessage = result.isSuccess 
              ? '视频发布成功\n${result.extraMsg ?? ''}' 
              : '发布失败: ${result.failMsg}';
          _isLoading = false;
        });
      }
    } catch (e) {
      progressController.close();
      if (mounted) {
        setState(() {
          _resultMessage = '发布视频失败: $e';
          _isLoading = false;
        });
      }
    }
  }
  
  // 发布图文
  Future<void> _publishImages() async {
    if (_imageFiles.isEmpty || _coverFile == null) {
      setState(() {
        _resultMessage = '请先选择图片和封面';
      });
      return;
    }
    
    // 取消之前的订阅
    _progressSubscription?.cancel();
    
    setState(() {
      _isLoading = true;
      _resultMessage = '正在发布图文...';
      _uploadProgress = 0.0;
      _uploadStatus = '准备上传...';
    });
    
    // 创建进度控制器
    final progressController = StreamController<Map<String, dynamic>>();
    
    // 订阅进度更新
    _progressSubscription = progressController.stream.listen((data) {
      if (mounted) {
        setState(() {
          _uploadProgress = (data['progress'] as int).toDouble();
          if (data['message'] != null) {
            _uploadStatus = data['message'] as String;
          }
        });
      }
    });
    
    try {
      final cookies = _parseCookies(_cookieController.text);
      final platformModel = PlatformModel(cookieList: cookies);
      final platKwai = PlatKwai(platformModel);
      
      // 解析话题
      final topics = _topicsController.text.isEmpty 
          ? <String>[] 
          : _topicsController.text.split(',').map((e) => e.trim()).toList().cast<String>();
      
      final publishParams = PublishParamsModel(
        _coverFile!,
        desc: _descController.text,
        topics: topics,
        images: _imageFiles,
        progressCallback: (progress, message) {
          // 将进度更新发送到流中
          progressController.add({
            'progress': progress,
            'message': message,
          });
        },
      );
      
      // 异步执行发布
      setState(() {
        _resultMessage = '正在发布中...';
      });
      
      // 执行发布并等待结果
      final result = await platKwai.publishImgText(publishParams);
      
      if (mounted) {
        setState(() {
          _resultMessage = result.isSuccess 
              ? '图文发布成功\n${result.extraMsg ?? ''}' 
              : '发布失败: ${result.failMsg}';
          _isLoading = false;
        });
      }
    } catch (e) {
      progressController.close();
      if (mounted) {
        setState(() {
          _resultMessage = '发布图文失败: $e';
          _isLoading = false;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('快手平台测试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Cookie设置', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: _cookieController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: '输入Cookie',
                hintText: '请输入包含kuaishou.web.cp.api_ph的Cookie',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            
            const Text('内容设置', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: _descController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: '描述',
                hintText: '请输入作品描述',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _topicsController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: '话题',
                hintText: '请输入话题，用逗号分隔',
              ),
            ),
            const SizedBox(height: 16),
            
            const Text('媒体选择', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _pickVideo,
                  child: const Text('选择视频'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _pickCover,
                  child: const Text('选择封面'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _pickImages,
                  child: const Text('选择图片'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            if (_videoFile != null)
              Text('已选择视频: ${_videoFile!.path}'),
            if (_coverFile != null)
              Text('已选择封面: ${_coverFile!.path}'),
            if (_imageFiles.isNotEmpty)
              Text('已选择${_imageFiles.length}张图片'),
            
            const SizedBox(height: 16),
            
            const Text('操作', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _getUserInfo,
                  child: const Text('获取用户信息'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _publishVideo,
                  child: const Text('发布视频'),
                ),
                ElevatedButton(
                  onPressed: _isLoading ? null : _publishImages,
                  child: const Text('发布图文'),
                ),
                ElevatedButton(
                  onPressed: _isLoading 
                      ? null 
                      : () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const KwaiUserInfoPage(),
                            ),
                          );
                        },
                  child: const Text('查看用户信息详情'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_isLoading) ...[
              const Text('上传进度', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              LinearProgressIndicator(value: _uploadProgress / 100),
              const SizedBox(height: 4),
              Text('${_uploadProgress.toInt()}% - $_uploadStatus'),
              const SizedBox(height: 16),
            ],
            
            const Text('结果', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              width: double.infinity,
              child: _isLoading && _resultMessage.isEmpty
                  ? const Center(child: CircularProgressIndicator())
                  : Text(_resultMessage),
            ),
          ],
        ),
      ),
    );
  }
} 