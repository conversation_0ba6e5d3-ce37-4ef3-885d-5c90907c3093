import 'dart:io';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/plat_form_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_kwai/plat_kwai.dart';
import 'package:flutter/material.dart';

class KwaiUserInfoPage extends StatefulWidget {
  const KwaiUserInfoPage({super.key});

  @override
  State<KwaiUserInfoPage> createState() => _KwaiUserInfoPageState();
}

class _KwaiUserInfoPageState extends State<KwaiUserInfoPage> {
  final TextEditingController _cookieController = TextEditingController();
  
  bool _isLoading = false;
  Map<String, dynamic>? _userInfo;
  String _errorMessage = '';
  
  @override
  void dispose() {
    _cookieController.dispose();
    super.dispose();
  }
  
  // 从cookie字符串中解析cookie
  List<Cookie> _parseCookies(String cookieStr) {
    List<Cookie> cookies = [];
    
    final cookiePairs = cookieStr.split(';');
    for (var pair in cookiePairs) {
      pair = pair.trim();
      if (pair.isEmpty) continue;
      
      final index = pair.indexOf('=');
      if (index > 0) {
        final name = pair.substring(0, index).trim();
        final value = pair.substring(index + 1).trim();
        
        final cookie = Cookie(name, value);
        cookies.add(cookie);
      }
    }
    
    return cookies;
  }
  
  // 获取用户信息
  Future<void> _getUserInfo() async {
    if (_cookieController.text.isEmpty) {
      setState(() {
        _errorMessage = '请输入Cookie';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _userInfo = null;
    });
    
    try {
      final cookies = _parseCookies(_cookieController.text);
      final platformModel = PlatformModel(cookieList: cookies);
      final platKwai = PlatKwai(platformModel);
      
      final userInfo = await platKwai.getUserInfo();
      
      setState(() {
        _userInfo = userInfo;
      });
    } catch (e) {
      LoggerUtil.e('获取用户信息失败: $e');
      setState(() {
        _errorMessage = '获取用户信息失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('快手用户信息'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Cookie设置', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            TextField(
              controller: _cookieController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: '输入Cookie',
                hintText: '请输入包含kuaishou.web.cp.api_ph的Cookie',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            
            Center(
              child: ElevatedButton(
                onPressed: _isLoading ? null : _getUserInfo,
                child: Text(_isLoading ? '正在获取...' : '获取用户信息'),
              ),
            ),
            const SizedBox(height: 24),
            
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_errorMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(4),
                ),
                width: double.infinity,
                child: Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.red),
                ),
              )
            else if (_userInfo != null) ...[
              const Text('用户基本信息', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              
              _buildInfoItem('用户ID', _userInfo!['uid'] ?? ''),
              _buildInfoItem('昵称', _userInfo!['nickname'] ?? ''),
              _buildInfoItem('快手ID', _userInfo!['kwaiId'] ?? '未设置'),
              _buildInfoItem('粉丝数', '${_userInfo!['fansCount'] ?? 0}'),
              _buildInfoItem('关注数', '${_userInfo!['followCount'] ?? 0}'),
              _buildInfoItem('获赞数', '${_userInfo!['likeCount'] ?? 0}'),
              _buildInfoItem('简介', _userInfo!['description'] ?? ''),
              
              const SizedBox(height: 16),
              const Text('原始数据', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                width: double.infinity,
                child: Text(
                  _userInfo.toString(),
                  style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label: ',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
} 