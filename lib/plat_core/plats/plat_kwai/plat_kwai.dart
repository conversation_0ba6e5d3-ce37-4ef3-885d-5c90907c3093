import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_base.dart';
import 'package:aitoearn_app/plat_core/plats/plat_kwai/kwai_service.dart';
import 'package:aitoearn_app/utils/dio_util/dio_util.dart';
import 'package:dio/dio.dart';
import 'package:path/path.dart' as path;

class PlatKwai extends PlatBase {
  PlatKwai(super.platformModel);
  
  /// 视频分块大小：4MB
  static const int _fileBlockSize = 4 * 1024 * 1024; // 4MB
  
  /// 从cookie中获取api_ph
  String? _getApiPh() {
    try {
      for (var cookie in cookieList) {
        if (cookie.name == 'kuaishou.web.cp.api_ph') {
          LoggerUtil.i('【快手平台】获取到api_ph: ${cookie.value}');
          return cookie.value;
        }
      }
      LoggerUtil.e('【快手平台】未找到api_ph in cookies: $cookieList');
      return null;
    } catch (e) {
      LoggerUtil.e('【快手平台】获取api_ph失败: $e');
      return null;
    }
  }
  
  @override
  Future<Map<String, dynamic>> getUserInfo() async {
    try {
      LoggerUtil.i('【快手平台】开始获取用户信息');
      final apiPh = _getApiPh();
      if (apiPh == null) {
        throw Exception('未找到必要的Cookie: kuaishou.web.cp.api_ph');
      }
      
      LoggerUtil.i('【快手平台】调用getUserInfo, apiPh: $apiPh');
      
      final userInfoResponse = await KwaiService.getUserInfo(apiPh, cookieStr);
      if (userInfoResponse == null) {
        throw Exception('获取用户信息失败');
      }
      
      LoggerUtil.i('【快手平台】获取用户信息结果: $userInfoResponse');
      
      // 检查API返回的结果码
      if (userInfoResponse['result'] != 1) {
        // 如果是登录过期或需要重新登录的错误
        if (userInfoResponse['result'] == 500002) {
          throw Exception('快手登录已过期，请重新登录');
        } else {
          throw Exception('获取用户信息失败: ${userInfoResponse['message'] ?? '未知错误'}');
        }
      }
      
      // 根据快手API返回的数据结构进行解析
      final data = userInfoResponse['data'] ?? {};
      final result = {
        'uid': data['userId']?.toString() ?? '',
        'nickname': data['userName'] ?? '',
        'avatar': '', // API返回中没有头像信息，可能需要从其他接口获取
        'fansCount': data['fansCnt'] ?? 0,
        'followCount': data['followCnt'] ?? 0,
        'likeCount': data['likeCnt'] ?? 0,
        'description': data['desc'] ?? '',
        'kwaiId': data['userKwaiId'] ?? '',
      };
      
      LoggerUtil.i('【快手平台】解析用户信息完成: $result');
      return result;
    } catch (e) {
      LoggerUtil.e('【快手平台】获取用户信息异常: $e');
      rethrow;
    }
  }
  
  @override
  Future<PublishResponseModel> publishVideo(PublishParamsModel publishParamsModel) async {
    try {
      // 保存进度回调函数的引用
      final progressCallback = publishParamsModel.progressCallback;
      
      // 先发送开始处理的进度
      progressCallback?.call(0, '准备处理...');
      
      // 调用实际的异步发布视频方法
      final result = await _publishVideoAsync(publishParamsModel);
      
      // 确保最终进度到达100%
      progressCallback?.call(100, '处理完成');
      
      return result;
    } catch (e) {
      LoggerUtil.e('【快手平台】发布视频异常: $e');
      return PublishResponseModel(
        false,
        failMsg: e.toString(),
      );
    }
  }
  
  // 实际的异步发布视频方法
  Future<PublishResponseModel> _publishVideoAsync(PublishParamsModel publishParamsModel) async {
    try {
      final apiPh = _getApiPh();
      if (apiPh == null) {
        throw Exception('未找到必要的Cookie: kuaishou.web.cp.api_ph');
      }
      
      if (publishParamsModel.video == null) {
        throw Exception('视频文件不能为空');
      }
      
      final videoFile = publishParamsModel.video!;
      final coverFile = publishParamsModel.cover;
      
      // 进度回调
      final progressCallback = publishParamsModel.progressCallback;
      
      // 1. 视频预处理获取上传凭证
      LoggerUtil.i('【快手平台】开始视频上传预处理');
      progressCallback?.call(5, '开始视频上传预处理');
      
      final preResult = await KwaiService.uploadVideoPre(apiPh, cookieStr);
      if (preResult == null || preResult['data'] == null) {
        throw Exception('获取视频上传凭证失败');
      }
      
      final token = preResult['data']['token'];
      LoggerUtil.i('【快手平台】获取到上传凭证: $token');
      progressCallback?.call(10, '获取上传凭证成功，准备上传视频');
      
      // 2. 视频分片上传
      LoggerUtil.i('【快手平台】开始视频分片上传');
      final videoUploadResult = await _uploadVideoWithChunks(
        apiPh: apiPh,
        token: token,
        videoFile: videoFile,
        callback: (progress, message) {
          // 进度回调，视频上传占总进度的70%（10%-80%）
          final totalProgress = 10 + ((progress * 70) ~/ 100);
          progressCallback?.call(totalProgress, message);
        },
      );
      
      if (videoUploadResult == null) {
        throw Exception('视频上传失败');
      }
      
      // 3. 上传封面
      LoggerUtil.i('【快手平台】开始上传封面');
      progressCallback?.call(80, '视频上传完成，开始上传封面');
      
      final coverBytes = await coverFile.readAsBytes();
      final coverUploadResult = await KwaiService.uploadVideoCover(apiPh, cookieStr, coverBytes);
      if (coverUploadResult == null || coverUploadResult['data'] == null) {
        throw Exception('封面上传失败');
      }
      
      final coverKey = coverUploadResult['data']['coverKey'];
      LoggerUtil.i('【快手平台】获取到封面Key: $coverKey');
      progressCallback?.call(85, '封面上传完成');
      
      // 4. 提交视频发布
      LoggerUtil.i('【快手平台】开始提交视频发布');
      progressCallback?.call(90, '开始提交视频发布');
      
      // 格式化描述和话题
      final formattedCaption = _formatCaption(
        publishParamsModel.desc ?? '', 
        publishParamsModel.topics
      );
      
      // 构建提交参数
      final submitParams = {
        ...videoUploadResult,
        'coverKey': coverKey,
        'coverTimeStamp': 0,
        'coverType': 1,
        'coverTitle': '',
        'photoType': 0,
        'collectionId': '',
        'publishTime': 0,
        'longitude': '',
        'latitude': '',
        'poiId': 0,
        'notifyResult': 0,
        'domain': '',
        'secondDomain': '',
        'coverCropped': false,
        'pkCoverKey': '',
        'profileCoverKey': '',
        'downloadType': 1,
        'disableNearbyShow': false,
        'allowSameFrame': true,
        'movieId': '',
        'openPrePreview': false,
        'declareInfo': {},
        'activityIds': [],
        'riseQuality': false,
        'chapters': [],
        'projectId': '',
        'recTagIdList': [],
        'videoInfoMeta': '',
        'previewUrlErrorMessage': '',
        'triggerH265': false,
        'caption': formattedCaption,
        'photoStatus': 1, // 添加必要的photoStatus参数，1表示立即发布
      };
      
      // 添加位置信息
      if (publishParamsModel.location != null) {
        submitParams['poiId'] = publishParamsModel.location?.id ?? 0;
        submitParams['latitude'] = publishParamsModel.location?.latitude ?? '';
        submitParams['longitude'] = publishParamsModel.location?.longitude ?? '';
      }
      
      LoggerUtil.i('【快手平台】视频提交参数: $submitParams');
      
      final submitResult = await KwaiService.submitVideo(apiPh, cookieStr, submitParams);
      if (submitResult == null) {
        throw Exception('视频提交失败');
      }
      
      LoggerUtil.i('【快手平台】提交发布成功: ${submitResult['message'] ?? ''}');
      progressCallback?.call(95, '视频提交成功');
      
      // 发布成功，直接返回结果，不再查询作品列表
      if (submitResult['result'] == 1) {
        LoggerUtil.i('【快手平台】发布视频成功');
        progressCallback?.call(100, '发布成功');
        
        // 返回发布成功的结果
        return PublishResponseModel(
          true,
          dataId: '', // 暂时无法获取ID，需要等待作品列表更新
          workLink: '', // 暂时无法获取链接
          extraMsg: '发布成功，作品将在审核后显示'
        );
      }
      
      // 如果提交不成功，则尝试查询作品列表
      LoggerUtil.i('【快手平台】查询发布结果');
      progressCallback?.call(96, '查询发布结果');
      
      // 尝试最多3次查询，每次间隔2秒
      var publishId = '';
      var shareLink = '';
      
      for (int attempt = 0; attempt < 3; attempt++) {
        LoggerUtil.i('【快手平台】查询发布结果 (${attempt + 1}/3)...');
        progressCallback?.call(97 + attempt, '查询发布结果 (${attempt + 1}/3)...');
        
        // 等待2秒后再查询
        if (attempt > 0) {
          await Future.delayed(const Duration(seconds: 2));
        }
        
        try {
          final worksList = await KwaiService.getWorksList(apiPh, cookieStr, '2'); // 查询待发布
          if (worksList != null && worksList['data'] != null && worksList['data']['list'] != null) {
            final works = worksList['data']['list'];
            
            // 查找匹配的作品
            for (var work in works) {
              if (work['unPublishCoverKey'] == coverKey) {
                publishId = work['photoId'] ?? '';
                shareLink = work['shareUrl'] ?? '';
                break;
              }
            }
            
            if (publishId.isNotEmpty) {
              break; // 找到了，退出循环
            }
          }
        } catch (e) {
          LoggerUtil.e('【快手平台】查询作品列表失败: $e');
          // 继续尝试
        }
      }
      
      // 即使没有找到作品，也返回成功，因为提交已经成功
      LoggerUtil.i('【快手平台】发布成功，publishId: $publishId, shareLink: $shareLink');
      progressCallback?.call(100, '发布成功');
      
      return PublishResponseModel(
        true,
        dataId: publishId,
        workLink: shareLink,
        extraMsg: publishId.isEmpty ? '发布成功，作品将在审核后显示' : null
      );
    } catch (e) {
      LoggerUtil.e('【快手平台】发布视频失败: $e');
      return PublishResponseModel(
        false,
        failMsg: e.toString(),
      );
    }
  }
  
  /// 视频分片上传
  Future<Map<String, dynamic>?> _uploadVideoWithChunks({
    required String apiPh,
    required String token,
    required File videoFile,
    required Function(int progress, String message) callback,
  }) async {
    try {
      // 获取文件信息
      final fileSize = await videoFile.length();
      final fileName = path.basename(videoFile.path);
      final fileExtension = path.extension(videoFile.path).replaceAll('.', '');
      final fileType = 'video/$fileExtension';
      
      LoggerUtil.i('【快手平台】视频文件信息 - 大小: $fileSize字节, 名称: $fileName, 类型: $fileType');
      
      // 计算分片信息
      final fragmentCount = (fileSize / _fileBlockSize).ceil();
      final List<int> fragmentSizes = [];
      
      for (int i = 0; i < fragmentCount; i++) {
        final start = i * _fileBlockSize;
        final end = min((i + 1) * _fileBlockSize, fileSize);
        fragmentSizes.add((end - start).toInt());
      }
      
      LoggerUtil.i('【快手平台】视频分片信息 - 总分片数: $fragmentCount, 分片大小: $fragmentSizes');
      
      // 逐个上传分片
      for (int i = 0; i < fragmentCount; i++) {
        final start = i * _fileBlockSize;
        final end = min((i + 1) * _fileBlockSize, fileSize);
        
        // 计算进度
        final progress = ((i / fragmentCount) * 70).toInt(); // 占总进度的70%
        callback(progress, '上传视频分片 ${i+1}/$fragmentCount');
        
        // 读取分片数据
        final randomAccessFile = await videoFile.open(mode: FileMode.read);
        await randomAccessFile.setPosition(start);
        final chunkData = await randomAccessFile.read(end - start);
        await randomAccessFile.close();
        
        // 上传分片
        LoggerUtil.i('【快手平台】上传第${i+1}个分片，大小: ${chunkData.length}字节');
        final uploadResult = await KwaiService.uploadVideoFragment(token, i.toString(), chunkData);
        
        if (uploadResult == null || uploadResult['result'] != 1) {
          LoggerUtil.e('【快手平台】上传分片失败: ${uploadResult ?? '无响应'}');
          throw Exception('上传视频分片失败');
        }
      }
      
      // 通知服务器分片上传完成
      callback(75, '通知服务器分片上传完成');
      LoggerUtil.i('【快手平台】所有分片上传完成，通知服务器');
      
      final completeResult = await KwaiService.completeVideoUpload(token, fragmentCount, apiPh, cookieStr);
      if (completeResult == null || completeResult['result'] != 1) {
        LoggerUtil.e('【快手平台】通知服务器分片上传完成失败: ${completeResult ?? '无响应'}');
        throw Exception('通知服务器分片上传完成失败');
      }
      
      // 视频上传结束
      callback(85, '视频上传结束');
      LoggerUtil.i('【快手平台】视频上传结束');
      
      final finishResult = await KwaiService.finishVideoUpload(
        apiPh, 
        cookieStr, 
        token, 
        fileName, 
        fileType, 
        fileSize
      );
      
      if (finishResult == null || finishResult['result'] != 1) {
        LoggerUtil.e('【快手平台】视频上传结束失败: ${finishResult ?? '无响应'}');
        throw Exception('视频上传结束失败');
      }
      
      callback(90, '视频上传完成');
      LoggerUtil.i('【快手平台】视频上传完成: ${finishResult['data']}');
      
      return finishResult['data'];
    } catch (e) {
      LoggerUtil.e('【快手平台】视频分片上传异常: $e');
      return null;
    }
  }
  
  @override
  Future<PublishResponseModel> publishImgText(PublishParamsModel publishParamsModel) async {
    try {
      // 保存进度回调函数的引用
      final progressCallback = publishParamsModel.progressCallback;
      
      // 先发送开始处理的进度
      progressCallback?.call(0, '准备处理...');
      
      // 调用实际的异步发布图文方法
      final result = await _publishImgTextAsync(publishParamsModel);
      
      // 确保最终进度到达100%
      progressCallback?.call(100, '处理完成');
      
      return result;
    } catch (e) {
      LoggerUtil.e('【快手平台】发布图文异常: $e');
      return PublishResponseModel(
        false,
        failMsg: e.toString(),
      );
    }
  }
  
  // 实际的异步发布图文方法
  Future<PublishResponseModel> _publishImgTextAsync(PublishParamsModel publishParamsModel) async {
    try {
      final apiPh = _getApiPh();
      if (apiPh == null) {
        throw Exception('未找到必要的Cookie: kuaishou.web.cp.api_ph');
      }
      
      if (publishParamsModel.images == null || publishParamsModel.images!.isEmpty) {
        throw Exception('图片不能为空');
      }
      
      // 1. 上传图片
      final imageUrls = await _uploadImages(apiPh, publishParamsModel.images!);
      if (imageUrls.isEmpty) {
        throw Exception('图片上传失败');
      }
      
      // 2. 发布图文
      final publishResult = await _publishImages(
        apiPh: apiPh,
        imageUrls: imageUrls,
        caption: publishParamsModel.desc ?? '',
        topics: publishParamsModel.topics,
        location: publishParamsModel.location,
      );
      
      if (publishResult == null) {
        throw Exception('图文发布失败');
      }
      
      return PublishResponseModel(
        true,
        dataId: publishResult['photoId'] ?? '',
        workLink: publishResult['shareUrl'] ?? '',
      );
    } catch (e) {
      LoggerUtil.e('发布图文失败: $e');
      return PublishResponseModel(
        false,
        failMsg: e.toString(),
      );
    }
  }
  
  /// 上传图片列表
  Future<List<String>> _uploadImages(String apiPh, List<File> imageFiles) async {
    final List<String> imageUrls = [];
    
    LoggerUtil.i('【快手平台】开始上传图片列表，共${imageFiles.length}张图片');
    
    for (var i = 0; i < imageFiles.length; i++) {
      final imageFile = imageFiles[i];
      try {
        LoggerUtil.i('【快手平台】上传第${i+1}张图片: ${imageFile.path}');
        // 获取上传凭证
        final json = {
          'kuaishou.web.cp.api_ph': apiPh,
        };
        
        LoggerUtil.i('【快手平台】获取图片上传凭证');
        final response = await KwaiService.requestWithSign(
          url: '${KwaiService.kwaiBaseUrl}/rest/cp/photo/image/upload/token',
          json: json,
          method: 'POST',
          cookieStr: cookieStr,
        );
        
        if (response?.statusCode != 200 && response?.statusCode != 201) {
          LoggerUtil.e('【快手平台】获取图片上传凭证失败: ${response?.statusCode}');
          continue;
        }
        
        final uploadToken = response?.data['uploadToken'];
        if (uploadToken == null) {
          LoggerUtil.e('【快手平台】获取图片上传凭证失败: ${response?.data}');
          continue;
        }
        
        LoggerUtil.i('【快手平台】获取到图片上传凭证: $uploadToken');
        
        // 上传图片文件
        final formData = FormData.fromMap({
          'file': await MultipartFile.fromFile(imageFile.path),
        });
        
        LoggerUtil.i('【快手平台】开始上传图片文件');
        final uploadResponse = await DioUtils.client.request(
          '${KwaiService.kwaiBaseUrl}/rest/cp/photo/image/upload',
          data: formData,
          options: Options(
            method: 'POST',
            headers: {
              'Cookie': cookieStr,
              'Authorization': 'Bearer $uploadToken',
            },
          ),
        );
        
        LoggerUtil.i('【快手平台】图片上传响应: ${uploadResponse?.data}');
        
        if (uploadResponse?.statusCode != 200 && uploadResponse?.statusCode != 201) {
          LoggerUtil.e('【快手平台】上传图片失败: ${uploadResponse?.statusCode}');
          continue;
        }
        
        final imageUrl = uploadResponse?.data?['url'];
        if (imageUrl != null) {
          imageUrls.add(imageUrl);
          LoggerUtil.i('【快手平台】图片上传成功，URL: $imageUrl');
        }
      } catch (e) {
        LoggerUtil.e('【快手平台】上传图片异常: $e');
      }
    }
    
    LoggerUtil.i('【快手平台】图片上传完成，成功上传${imageUrls.length}张图片');
    return imageUrls;
  }
  
  /// 发布图文
  Future<Map<String, dynamic>?> _publishImages({
    required String apiPh,
    required List<String> imageUrls,
    required String caption,
    List<String>? topics,
    dynamic location,
  }) async {
    try {
      LoggerUtil.i('【快手平台】开始发布图文');
      LoggerUtil.i('【快手平台】发布参数 - imageUrls: $imageUrls, caption: $caption, topics: $topics, location: $location');
      
      final formattedCaption = _formatCaption(caption, topics);
      LoggerUtil.i('【快手平台】格式化后的描述: $formattedCaption');
      
      final publishData = {
        'kuaishou.web.cp.api_ph': apiPh,
        'imageUrls': imageUrls,
        'caption': formattedCaption,
      };
      
      // 添加位置信息
      if (location != null) {
        publishData['poiId'] = location.id;
        publishData['poiName'] = location.name;
        LoggerUtil.i('【快手平台】添加位置信息 - poiId: ${location.id}, poiName: ${location.name}');
      }
      
      LoggerUtil.i('【快手平台】发布图文请求数据: $publishData');
      final response = await KwaiService.requestWithSign(
        url: '${KwaiService.kwaiBaseUrl}/api/rest/cp/photo/publish/images',
        json: publishData,
        method: 'POST',
        cookieStr: cookieStr,
      );
      
      if (response?.statusCode != 200 && response?.statusCode != 201) {
        LoggerUtil.e('【快手平台】发布图文失败: ${response?.statusCode}');
        return null;
      }
      
      LoggerUtil.i('【快手平台】发布图文成功: ${response?.data}');
      return response?.data;
    } catch (e) {
      LoggerUtil.e('【快手平台】发布图文异常: $e');
      return null;
    }
  }
  
  /// 格式化描述文本，添加话题标签
  String _formatCaption(String caption, List<String>? topics) {
    if (topics == null || topics.isEmpty) {
      LoggerUtil.i('【快手平台】没有话题，使用原始描述: $caption');
      return caption;
    }
    
    // 添加话题标签
    final topicsText = topics.map((topic) => '#$topic').join(' ');
    final result = '$caption $topicsText';
    LoggerUtil.i('【快手平台】添加话题后的描述: $result');
    return result;
  }
  
  @override
  Map<String, dynamic> getMakeRequestHedears() {
    return {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
      'Referer': 'https://cp.kuaishou.com/',
      'Origin': 'https://cp.kuaishou.com',
    };
  }
}
