import 'dart:convert';
import 'dart:math';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_home_feed_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_note_detail_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_poi_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart';
import 'package:dio/dio.dart';

/// 小红书服务类
class XhsService {
  static final Dio _dio = Dio();

  // 账号信息
  final dynamic account;

  // 构造函数
  XhsService({this.account});

  // 获取账号Cookie
  String get cookieString {
    if (account != null && account.cookie != null) {
      return account.accessToken;
    }
    return '';
  }

  /// 获取小红书用户信息
  ///
  /// [cookie] 用户登录Cookie
  /// 返回用户信息，如果获取失败则返回null
  static Future<Map<String, dynamic>?> getUserInfo(String cookie) async {
    try {
      final response = await _dio.get(
        'https://creator.xiaohongshu.com/api/galaxy/creator/home/<USER>',
        options: Options(
          headers: {
            'Cookie': cookie,
            'Referer': 'https://creator.xiaohongshu.com/',
            'Origin': 'https://creator.xiaohongshu.com/',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          },
        ),
      );

      if (response.statusCode == 200 && response.data['result'] == 0) {
        return response.data['data'];
      } else {
        LoggerUtil.e('获取小红书用户信息失败: ${response.data['msg'] ?? '未知错误'}');
        return null;
      }
    } catch (e) {
      LoggerUtil.e('获取小红书用户信息异常: $e');
      return null;
    }
  }

  /// 从cookie中提取用户ID
  ///
  /// [cookieStr] cookie字符串
  /// 返回用户ID，如果提取失败则返回null
  static String? extractUserIdFromCookie(String cookieStr) {
    try {
      final cookies = cookieStr.split(';');
      for (var cookie in cookies) {
        cookie = cookie.trim();
        if (cookie.startsWith('x-user-id-creator.xiaohongshu.com=')) {
          return cookie.substring('x-user-id-creator.xiaohongshu.com='.length);
        }
      }
      return null;
    } catch (e) {
      LoggerUtil.e('提取小红书用户ID失败: $e');
      return null;
    }
  }

  /// 提取Cookie中的x-s和x-t字段
  ///
  /// [cookieStr] 完整的Cookie字符串
  /// 返回包含x-s和x-t的Map
  static Map<String, String> extractXsXtHeaders(String cookieStr) {
    final Map<String, String> headers = {};
    cookieStr =
        'abRequestId=3b3f1172-e515-5a2b-83f8-9dde179c4f3a; a1=196a9ae613fn49uwy6e6yzk98aj0z6ukt1m5nnaxq50000317016; webId=b4ee448513192325799b22d43a6437aa; gid=yjW2Y0Yf8D9fyjK0j0dKyyI0qiI4j7EvKdKv6CAuTjY0V2288uK7TI888qyW8yK80dfJdfK4; customerClientId=484541279664605; webBuild=4.70.2; customer-sso-sid=68c517521743575565529647ruy6lxdf4eldeqaj; x-user-id-creator.xiaohongshu.com=5bb0576b4eacab3854b2886c; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517521743575561056695sq3lmyv3jvox6x7o; galaxy_creator_session_id=0gWhrqSYdTiLvCp1yeggkYPl8YGbea85DpV9; galaxy.creator.beaker.session.id=1751292398233027416373; unread={%22ub%22:%22685b49ec0000000011000cd2%22%2C%22ue%22:%226857a46f000000001c035aba%22%2C%22uc%22:24}; acw_tc=0a4ad66817513443924464750ea0abf4c2b7a0617d7fce71b925671e01420c; web_session=0400697d88569f39d83aaa2f563a4be70a6f04; websectiga=984412fef754c018e472127b8effd174be8a5d51061c991aadd200c69a2801d6; sec_poison_id=e8a1ca60-c482-4528-b499-a3c10cdc5cb1; xsecappid=xhs-pc-web; loadts=1751345125316';
    try {
      // 临时解决方案：使用固定的x-s和x-t值
      headers['x-s'] =
          'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjM2NGY2MzljNDlmMzQ3NmRhMmZmNjVmODJmNDA3OTgxZjdjMTRlYzNhNTFmYWZhNGQ5ZDhmYWQ0MzEzNDNhZTM4ZTBhNGFhNDczZGYyYzdlZWExYjIwMWNlODBkNDJlMTViZWQyMjAyYjQzMmUwM2VjNGY3OGM1NTZlMDU1MzIyNTVkYWU1NmY2ZDNkOWM4N2RjZTZmOWIyYjk0MjI2YTkxMTQ3YmNjYjdlOWNlZTk2NTY5NmE2YjRlNjYzMGE5NDZiMzcyYzdjMThjMjkyYWE2NjE3MTkzYjJhYjBmYTA1ZjVlOTA1MzcwNWVhNGZmZGEyZWQxOWVlNGRiMmFkOTI3YzQwYWM4YWVmNTc0MDc3ODJjODlmNzZiNTMwNzI1ZWYxZjA5NDY5N2QwM2JhMTk2MzYzY2U5ODQ1MjdiOTUyYzMwMzQ3YzI4MzY5M2M2NTJjZjNhNzRmYTFkNTRmYjM4NjliNjVlOGU3NDk1ZWJiMTA5MjA3NWJhYjllODliZDExNmQxODQ2YjM2ZWYyZGFkMWVmNzY0YzRlZDA2ODQ3In0=';
      headers['x-t'] = '${DateTime.now().millisecondsSinceEpoch}';

      // 添加x-s-common头信息
      headers['x-s-common'] =
          '2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahIHjIj2eHjwjQgynEDJ74A'
          'HjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0qIN0HjNsQh+aHCH0rE+frEGnL9P/+f'
          'J0cE4g4E+fL92gk3w/YYy0mC+dp34ebT+nE1GgYl+/ZIPeZAP/qIP/GjNsQh+jHCHjHV'
          'HdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP';

      LoggerUtil.i('使用固定的x-s和x-t头信息');
      return headers;
    } catch (e) {
      LoggerUtil.e('提取x-s和x-t失败: $e');
      // 返回空值，调用方会使用默认值
      return headers;
    }
  }

  /// 搜索小红书POI位置
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [keyword] 搜索关键词，可选
  /// [page] 页码，从1开始，默认为1
  /// [size] 每页数量，默认为50
  /// 返回POI搜索结果
  static Future<XhsPoiSearchResponse> searchPoi({
    required String cookieStr,
    required double latitude,
    required double longitude,
    String keyword = '',
    int page = 1,
    int size = 50,
  }) async {
    try {
      LoggerUtil.i('【小红书】搜索POI位置，经纬度: $latitude, $longitude，关键词: $keyword');

      // 获取当前时间戳
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 构建请求数据
      final requestData = {
        'latitude': latitude,
        'longitude': longitude,
        'keyword': keyword,
        'page': page,
        'size': size,
        'source': 'WEB',
        'type': 1,
      };

      // 获取动态签名
      String apiPath = '/web_api/sns/v1/local/poi/creator/search';
      Map<String, String> signatures;
      try {
        signatures = await _getReverseResult(cookieStr, {
          'url': apiPath,
          'data': requestData,
        });
        LoggerUtil.i(
          '【小红书】获取到POI动态签名: X-s=${(signatures['X-s']?.substring(0, 20) ?? '')}..., X-t=${signatures['X-t'] ?? ''}',
        );
      } catch (e) {
        LoggerUtil.e('【小红书】获取POI动态签名失败，将使用固定签名: $e');
        signatures = {
          'X-s':
              'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjM2NGY2MzljNDlmMzQ3NmRhMmZmNjVmODJmNDA3OTgxZjdjMTRlYzNhNTFmYWZhNGQ5ZDhmYWQ0MzEzNDNhZTM4ZTBhNGFhNDczZGYyYzdlZWExYjIwMWNlODBkNDJlMTViZWQyMjAyYjQzMmUwM2VjNGY3OGM1NTZlMDU1MzIyNTVkYWU1NmY2ZDNkOWM4N2RjZTZmOWIyYjk0MjI2YTkxMTQ3YmNjYjdlOWNlZTk2NTY5NmE2YjRlNjYzMGE5NDZiMzcyYzdjMThjMjkyYWE2NjE3MTkzYjJhYjBmYTA1ZjVlOTA1MzcwNWVhNGZmZGEyZWQxOWVlNGRiMmFkOTI3YzQwYWM4YWVmNTc0MDc3ODJjODlmNzZiNTMwNzI1ZWYxZjA5NDY5N2QwM2JhMTk2MzYzY2U5ODQ1MjdiOTUyYzMwMzQ3YzI4MzY5M2M2NTJjZjNhNzRmYTFkNTRmYjM4NjliNjVlOGU3NDk1ZWJiMTA5MjA3NWJhYjllODliZDExNmQxODQ2YjM2ZWYyZGFkMWVmNzY0YzRlZDA2ODQ3In0=',
          'X-t': timestamp.toString(),
        };
      }

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      LoggerUtil.i(
        '【小红书】x-s: ${signatures['X-s'] ?? ''}, x-t: ${signatures['X-t'] ?? ''}',
      );

      // 构建请求头
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'authorization': '',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://creator.xiaohongshu.com',
        'priority': 'u=1, i',
        'referer': 'https://creator.xiaohongshu.com/',
        'sec-ch-ua':
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-s': signatures['X-s'] ?? '',
        'x-t': signatures['X-t'] ?? '',
        'x-b3-traceid': traceId,
        'Cookie': cookieStr,
      };

      // 发送请求
      final response = await _dio.post(
        'https://edith.xiaohongshu.com$apiPath',
        data: requestData,
        options: Options(headers: headers),
      );

      if (response.statusCode == 200) {
        // 打印部分响应数据以便调试
        final responseStr = jsonEncode(response.data);
        LoggerUtil.i(
          '【小红书】搜索POI位置成功，响应数据: ${responseStr.length > 100 ? responseStr.substring(0, 100) + '...' : responseStr}',
        );

        return XhsPoiSearchResponse.fromJson(response.data);
      } else {
        LoggerUtil.e('【小红书】搜索POI位置失败: ${response.statusCode}');
        return XhsPoiSearchResponse(
          statusCode: response.statusCode ?? -1,
          statusMsg: response.statusMessage ?? '请求失败',
          poiList: [],
          hasMore: false,
          cursor: 0,
        );
      }
    } catch (e) {
      LoggerUtil.e('【小红书】搜索POI位置异常: $e');
      return XhsPoiSearchResponse(
        statusCode: -1,
        statusMsg: '请求异常: $e',
        poiList: [],
        hasMore: false,
        cursor: 0,
      );
    }
  }

  /// 搜索小红书内容
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [request] 搜索请求参数
  /// 返回内容搜索结果
  static Future<XhsSearchContentResponse> searchContent({
    required String cookieStr,
    required XhsSearchContentRequest request,
  }) async {
    try {
      LoggerUtil.i('【小红书】搜索内容，关键词: ${request.keyword}，页码: ${request.page}');
      // cookieStr =
      //     'abRequestId=3b3f1172-e515-5a2b-83f8-9dde179c4f3a; a1=196a9ae613fn49uwy6e6yzk98aj0z6ukt1m5nnaxq50000317016; webId=b4ee448513192325799b22d43a6437aa; gid=yjW2Y0Yf8D9fyjK0j0dKyyI0qiI4j7EvKdKv6CAuTjY0V2288uK7TI888qyW8yK80dfJdfK4; customerClientId=484541279664605; customer-sso-sid=68c517521743575565529647ruy6lxdf4eldeqaj; x-user-id-creator.xiaohongshu.com=5bb0576b4eacab3854b2886c; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517521743575561056695sq3lmyv3jvox6x7o; galaxy_creator_session_id=0gWhrqSYdTiLvCp1yeggkYPl8YGbea85DpV9; galaxy.creator.beaker.session.id=1751292398233027416373; xsecappid=xhs-pc-web; webBuild=4.71.0; acw_tc=0ad6fb2017518761111043262e0e5e5bb2e327bf3041ec0dcb98ad47a825a0; websectiga=59d3ef1e60c4aa37a7df3c23467bd46d7f1da0b1918cf335ee7f2e9e52ac04cf; sec_poison_id=510d03c5-972d-4bcd-b707-3969c03ad4c6; web_session=0400697d88569f39d83a64cb5e3a4b84935b66; loadts=1751876415715; unread={%22ub%22:%22686b7df500000000170345d8%22%2C%22ue%22:%22686b487500000000120210d9%22%2C%22uc%22:12}'; // 检查Cookie是否为空
      if (cookieStr.isEmpty) {
        LoggerUtil.e('【小红书】Cookie为空，无法搜索内容');
        return XhsSearchContentResponse(
          code: -1,
          success: false,
          msg: 'Cookie为空，请先登录账号',
          data: null,
        );
      }

      // 打印部分Cookie信息，方便调试
      LoggerUtil.i(
        '【小红书】Cookie长度: ${cookieStr.length}，前100字符: ${cookieStr.length > 100 ? cookieStr.substring(0, 100) + '...' : cookieStr}',
      );

      // 获取当前时间戳
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 构建请求数据
      final requestData = {
        'keyword': request.keyword,
        'page': request.page,
        'page_size': request.pageSize,
        'sort': request.sort,
        'note_type': request.noteType,
        'ext_flags': request.extFlags,
        'geo': request.geo,
        'image_formats': request.imageFormats,
        'search_id': '2f0317tbnlukh28d138rt', // 添加固定的search_id
      };

      if (request.filters.isNotEmpty) {
        requestData['filters'] =
            request.filters.map((filter) => filter.toJson()).toList();
      }

      LoggerUtil.i('【小红书】搜索内容请求参数: ${jsonEncode(requestData)}');

      // 获取动态签名
      String apiPath = '/api/sns/web/v1/search/notes';
      Map<String, String> signatures;
      try {
        signatures = await _getReverseResult(cookieStr, {
          'url': apiPath,
          'data': requestData,
        });
        LoggerUtil.i(
          '【小红书】获取到动态签名: X-s=${(signatures['X-s']?.substring(0, 20) ?? '')}..., X-t=${signatures['X-t'] ?? ''}',
        );
      } catch (e) {
        LoggerUtil.e('【小红书】获取动态签名失败，将使用固定签名: $e');
        signatures = {
          'X-s':
              'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjM2NGY2MzljNDlmMzQ3NmRhMmZmNjVmODJmNDA3OTgxZjdjMTRlYzNhNTFmYWZhNGQ5ZDhmYWQ0MzEzNDNhZTM4ZTBhNGFhNDczZGYyYzdlZWExYjIwMWNlODBkNDJlMTViZWQyMjAyYjQzMmUwM2VjNGY3OGM1NTZlMDU1MzIyNTVkYWU1NmY2ZDNkOWM4N2RjZTZmOWIyYjk0MjI2YTkxMTQ3YmNjYjdlOWNlZTk2NTY5NmE2YjRlNjYzMGE5NDZiMzcyYzdjMThjMjkyYWE2NjE3MTkzYjJhYjBmYTA1ZjVlOTA1MzcwNWVhNGZmZGEyZWQxOWVlNGRiMmFkOTI3YzQwYWM4YWVmNTc0MDc3ODJjODlmNzZiNTMwNzI1ZWYxZjA5NDY5N2QwM2JhMTk2MzYzY2U5ODQ1MjdiOTUyYzMwMzQ3YzI4MzY5M2M2NTJjZjNhNzRmYTFkNTRmYjM4NjliNjVlOGU3NDk1ZWJiMTA5MjA3NWJhYjllODliZDExNmQxODQ2YjM2ZWYyZGFkMWVmNzY0YzRlZDA2ODQ3In0=',
          'X-t': timestamp.toString(),
        };
      }

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      // 构建请求头
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://www.xiaohongshu.com',
        'priority': 'u=1, i',
        'referer': 'https://www.xiaohongshu.com/',
        'sec-ch-ua':
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-b3-traceid': traceId,
        'x-mns': 'unload',
        'x-s': signatures['X-s'] ?? '',
        'x-t': signatures['X-t'] ?? '',
        'x-s-common':
            '2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahIHjIj2eHjwjQgynEDJ74AHjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0qIN0HjNsQh+aHCH0rE+frEGnL9P/+fJ0cE4g4E+fL92gk3w/YYy0mC+dp34ebT+nE1GgYl+/ZIPeZAP/qIP/GjNsQh+jHCHjHVHdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP8F8LagY/P9Ql4FpUzfpS2BcI8nT1GFbC/L88JdbFyrSiafpDwLMra7pFLDDAa7+8J7QgabmFz7Qjp0mcwp4fanD68p40+fp8qgzELLbILrDA+9p3JpH9LLI3+LSk+d+DJfRSL98lnLYl49IUqgcMc0mrJFShtMmozBD6qM8FyFSh8o+h4g4U+obFyLSi4nbQz/+SPFlnPrDApSzQcA4SPopFJeQmzBMA/o8Szb+NqM+c4ApQzg8Ayp8FaDRl4AYs4g4fLomD8pzBpFRQ2ezLanSM+Skc47Qc4gcMag8VGLlj87PAqgzhagYSqAbn4FYQy7pTanTQ2npx87+8NM4L89L78p+l4BL6ze4AzB+IygmS8Bp8qDzFaLP98Lzn4AQQzLEAL7bFJBEVL7pwyS8Fag868nTl4e+0n04ApfuF8FSbL7SQyrLUaeSl4LShyBEl20YdanTQ8fRl49TQc7bgz9uAq9zV/9pnLoqAag8m8/mf89pD8S+tanDMqA++GfGU4gzmanSNq9SD4fp3nDESpbmF+BEm/9pgLo4bag832diIcnpDpd4i8ASwq98c4MkQP9TUagYb+LlM474Yqgq3qfp3qSmQ+nprJURSpSm7JLSiad+/JDbSy9MM4DSkcg+faaRA2rQk8rS34fLALo4VanYzyAQn4obOqgclagYSqMzn4BkQyAmAygb78rSiN7+x4gcA47pFJd+c4ApQc9+Va/+V+o4m+Bk7aLbApM87+rSb+obQyrRAL7bFnDQjpoc6c/mSyfkIPLS3zMmo4gzcndb7zLSea9LAqgzgtFMD8/bVcgPA/bzyaLLIq9TD/d+L4gzcGMmFcDS9J9pxqg4kanD78nTA4gSQcFTA8B8O8Lzn4b+Q2B4A2op74/QfpFQQzpqFaL+dqM8++d+ncLRA8rDA8Lzn4ApQyeYiaL+BJrkM4omQ2rpVaLpNq7YgGDMCqFkSp7pFJLSbzLRy4g4Q8LM68p+yad+xqgzUJdpFJLDAqfzQznTPnSmFLpml4MmQ40pAzbqROaHVHdWEH0iTP/LU+eZEP0HVHdWlPsHCPsIj2erlH0ijJfRUJnbVHdF=',
        'x-xray-traceid': List.generate(
          32,
          (index) =>
              '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
        ).join(''),
        'Cookie': cookieStr,
      };

      LoggerUtil.i(
        '【小红书】搜索内容请求头: x-s=${(signatures['X-s']?.length ?? 0) > 20 ? (signatures['X-s']?.substring(0, 20) ?? '') + '...' : signatures['X-s'] ?? ''}，x-t=${signatures['X-t'] ?? ''}，traceid=$traceId',
      );

      // 发送请求
      final response = await _dio.post(
        'https://edith.xiaohongshu.com$apiPath',
        data: requestData,
        options: Options(headers: headers),
      );

      LoggerUtil.i('【小红书】搜索内容响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        // 打印部分响应数据以便调试
        final responseStr = jsonEncode(response.data);
        LoggerUtil.i(
          '【小红书】搜索内容成功，响应数据: ${responseStr.length > 100 ? responseStr.substring(0, 100) + '...' : responseStr}',
        );

        try {
          // 检查响应数据是否为有效的JSON对象
          if (response.data is! Map<String, dynamic>) {
            LoggerUtil.e('【小红书】响应数据不是有效的JSON对象: ${response.data.runtimeType}');
            return XhsSearchContentResponse(
              code: -1,
              success: false,
              msg: '响应数据格式错误',
              data: null,
            );
          }

          final result = XhsSearchContentResponse.fromJson(response.data);
          LoggerUtil.i(
            '【小红书】解析响应成功，状态码: ${result.code}, 消息: ${result.msg}, 数据: ${result.data != null ? '有数据' : '无数据'}',
          );
          return result;
        } catch (e) {
          LoggerUtil.e('【小红书】解析搜索内容响应失败: $e');
          return XhsSearchContentResponse(
            code: -1,
            success: false,
            msg: '解析响应失败: $e',
            data: null,
          );
        }
      } else {
        LoggerUtil.e('【小红书】搜索内容失败: ${response.statusCode}');
        return XhsSearchContentResponse(
          code: response.statusCode ?? -1,
          success: false,
          msg: response.statusMessage ?? '请求失败',
          data: null,
        );
      }
    } catch (e) {
      LoggerUtil.e('【小红书】搜索内容异常: $e');
      return XhsSearchContentResponse(
        code: -1,
        success: false,
        msg: '请求异常: $e',
        data: null,
      );
    }
  }

  /// 逆向获取XsXt签名
  ///
  /// [cookieStr] 用户Cookie
  /// [data] 请求参数
  /// 返回包含X-s和X-t的签名Map
  static Future<Map<String, String>> _getReverseResult(
    String cookieStr,
    Map<dynamic, dynamic> data,
  ) async {
    try {
      LoggerUtil.i('获取XsXt签名，参数: ${jsonEncode(data)}');

      // 从cookie中获取a1值
      String? a1;
      final cookies = cookieStr.split(';');
      for (var cookie in cookies) {
        cookie = cookie.trim();
        if (cookie.startsWith('a1=')) {
          a1 = cookie.substring('a1='.length);
          break;
        }
      }

      if (a1 == null) {
        throw Exception('获取签名失败：Cookie中未找到a1值');
      }

      var reverseRes = await _dio.post(
        'http://116.62.154.231:7879',
        data: {...data, 'a1': a1},
        options: Options(
          headers: {'Content-Type': 'application/json;charset=UTF-8'},
        ),
      );

      if (reverseRes.data == null) {
        throw Exception('获取签名失败：请求返回为空');
      }

      // 确保响应数据是字符串，并进行解析
      String responseData;
      if (reverseRes.data is String) {
        responseData = reverseRes.data;
      } else if (reverseRes.data is Map) {
        responseData = jsonEncode(reverseRes.data);
      } else {
        throw Exception('获取签名失败：响应数据格式不正确');
      }

      var reverse = jsonDecode(responseData);
      LoggerUtil.i('获取XsXt签名结果: $reverse');

      // 验证签名数据
      if (reverse == null ||
          !reverse.containsKey('X-s') ||
          !reverse.containsKey('X-t')) {
        throw Exception('获取签名失败，签名数据不完整');
      }

      return {
        'X-s': reverse['X-s'].toString(),
        'X-t': reverse['X-t'].toString(),
      };
    } catch (e) {
      LoggerUtil.e('获取XsXt签名异常: $e');
      throw Exception('获取签名失败: $e');
    }
  }

  /// 获取小红书笔记详情
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [noteId] 笔记ID
  /// [xsecToken] 安全令牌
  /// 返回笔记详情
  static Future<XhsNoteDetailResponse> getNoteDetail({
    required String cookieStr,
    required String noteId,
    required String xsecToken,
  }) async {
    try {
      LoggerUtil.i('【小红书】获取笔记详情，笔记ID: $noteId');
      // cookieStr =
      //     'abRequestId=3b3f1172-e515-5a2b-83f8-9dde179c4f3a; a1=196a9ae613fn49uwy6e6yzk98aj0z6ukt1m5nnaxq50000317016; webId=b4ee448513192325799b22d43a6437aa; gid=yjW2Y0Yf8D9fyjK0j0dKyyI0qiI4j7EvKdKv6CAuTjY0V2288uK7TI888qyW8yK80dfJdfK4; customerClientId=484541279664605; customer-sso-sid=68c517521743575565529647ruy6lxdf4eldeqaj; x-user-id-creator.xiaohongshu.com=5bb0576b4eacab3854b2886c; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517521743575561056695sq3lmyv3jvox6x7o; galaxy_creator_session_id=0gWhrqSYdTiLvCp1yeggkYPl8YGbea85DpV9; galaxy.creator.beaker.session.id=1751292398233027416373; xsecappid=xhs-pc-web; webBuild=4.71.0; acw_tc=0ad6fb2017518761111043262e0e5e5bb2e327bf3041ec0dcb98ad47a825a0; websectiga=59d3ef1e60c4aa37a7df3c23467bd46d7f1da0b1918cf335ee7f2e9e52ac04cf; sec_poison_id=510d03c5-972d-4bcd-b707-3969c03ad4c6; web_session=0400697d88569f39d83a64cb5e3a4b84935b66; loadts=1751876415715; unread={%22ub%22:%22686b7df500000000170345d8%22%2C%22ue%22:%22686b487500000000120210d9%22%2C%22uc%22:12}';
      // 设置请求体参数
      Map<String, dynamic> requestBody = {
        'source_note_id': noteId,
        'image_formats': ['jpg', 'webp', 'avif'],
        'extra': {'need_body_topic': '1'},
        'xsec_source': 'pc_search',
        'xsec_token': xsecToken,
      };

      // 将请求体序列化为JSON字符串
      String jsonBody = jsonEncode(requestBody);
      LoggerUtil.i('【小红书】请求体JSON: $jsonBody');

      // 获取动态签名
      String apiPath = '/api/sns/web/v1/feed';
      Map<String, String> signatures;
      try {
        signatures = await _getReverseResult(cookieStr, {
          'url': apiPath,
          'data': requestBody,
        });
        LoggerUtil.i(
          '【小红书】获取到动态签名: X-s=${(signatures['X-s']?.substring(0, 20) ?? '')}..., X-t=${signatures['X-t'] ?? ''}',
        );
      } catch (e) {
        LoggerUtil.e('【小红书】获取动态签名失败，将使用固定签名: $e');
        signatures = {
          'X-s':
              'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjI4NTNlZmZjYzBiNGU1OTFmMDg3NzhhNWIyZDNhMjQ3NjhmOWY1MGEzZGJmY2M3NWZlMTQxYTg1Y2FhMDYzN2Y5YjFmNjlkZTU5ZmMzMDllNDc1NzE3ZmJhMDZjODdmOWE4N2ZlMTk3MjJmYWRmMmI0Y2E1NzAxYzAxYmQyODc4ZjA3YjgzZDhhODc4MTBhMzY2NjU0ZmI4MGMwMzQ1ZWUzOWYyYzM5OGY1MmViOTM0MjRkNTNlMmRlMTgxNDBiY2Q1ODllYzc0OTRlZjRkMjdkMzliZjA0ODgyNzMxNjQ0OTNjZjQxY2FkMmNjMDAyNGJlMDE1ZGVjMWY0MTg3MTk4OTc0NjA0MzA1NmNiYTczYzc0MGVlZjJkMzUxOTcyOGRkZGE1NmUxYzE5ZTFhZTBhZDNmZWQ5ZjUyZGZhNmQ2Y2ViMmUxYmJkNDI3ZGFiODI4MWFmNzcxNmE1ZDQ3YWUzNjk5ZmEwODFlNWZjOWI1ZWUxNjkwNWU1NzQyYzQ4NmViZGMzMmY0MDE2MDY2NzVhYmMyNGYwOTRmOGYyM2YzIn0=',
          'X-t': DateTime.now().millisecondsSinceEpoch.toString(),
        };
      }

      final response = await _dio.post(
        'https://edith.xiaohongshu.com$apiPath',
        data: requestBody, // Dio会自动将Map转换为JSON
        options: Options(
          headers: {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'content-type': 'application/json;charset=UTF-8', // 确保这个头设置正确
            'origin': 'https://www.xiaohongshu.com',
            'priority': 'u=1, i',
            'referer': 'https://www.xiaohongshu.com/',
            'sec-ch-ua':
                '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-b3-traceid': 'fc503bdf956a1f76',
            'x-mns': 'unload',
            'x-s': signatures['X-s'] ?? '',
            'x-t': signatures['X-t'] ?? '',
            'x-s-common':
                '2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahIHjIj2eHjwjQgynEDJ74A'
                'HjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0qIN0HjNsQh+aHCH0rE+frEGnL9P/+f'
                'J0cE4g4E+fL92gk3w/YYy0mC+dp34ebT+nE1GgYl+/ZIPeZAP/qIP/GjNsQh+jHCHjHV'
                'HdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP'
                '8F8LagY/P9Ql4FpUzfpS2BcI8nT1GFbC/L88JdbFyrSiafp/cDMra7pFLDDAa7+8J7Qg',
            'x-xray-traceid': 'cbe2385814a4d0d44505d6a3d1080184',
            'Cookie': cookieStr,
          },
          contentType: 'application/json', // 再次确保contentType正确设置
          responseType: ResponseType.json, // 确保响应以JSON格式解析
          validateStatus: (status) => true, // 允许任何状态码，以便我们可以自己处理
        ),
      );

      // 打印响应信息以便调试
      LoggerUtil.i('【小红书】获取笔记详情响应状态码: ${response.statusCode}');
      if (response.data != null) {
        try {
          LoggerUtil.i(
            '【小红书】获取笔记详情响应数据: ${jsonEncode(response.data).substring(0, min(100, jsonEncode(response.data).length))}...',
          );
        } catch (e) {
          LoggerUtil.i('【小红书】获取笔记详情响应数据(无法序列化): ${response.data}');
        }
      }

      if (response.statusCode == 200) {
        LoggerUtil.i('【小红书】获取笔记详情成功');
        return XhsNoteDetailResponse.fromJson(response.data);
      } else {
        LoggerUtil.e(
          '【小红书】获取笔记详情失败: ${response.statusCode}, ${response.statusMessage}, ${response.data}',
        );
        throw Exception(
          '获取笔记详情失败: ${response.statusCode}, ${response.statusMessage}',
        );
      }
    } catch (e) {
      LoggerUtil.e('【小红书】获取笔记详情异常: $e');
      throw Exception('获取笔记详情异常: $e');
    }
  }

  /// 获取小红书笔记评论
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [noteId] 笔记ID
  /// [xsecToken] 安全令牌
  /// [cursor] 分页游标，首次请求可为空
  /// [topCommentId] 顶级评论ID，可选
  /// 返回评论列表
  static Future<XhsCommentResponse> getNoteComments({
    required String cookieStr,
    required String noteId,
    required String xsecToken,
    String? cursor,
    String? topCommentId,
  }) async {
    try {
      LoggerUtil.i('【小红书】获取笔记评论，笔记ID: $noteId, 游标: $cursor');
      // cookieStr =
      //     'abRequestId=3b3f1172-e515-5a2b-83f8-9dde179c4f3a; a1=196a9ae613fn49uwy6e6yzk98aj0z6ukt1m5nnaxq50000317016; webId=b4ee448513192325799b22d43a6437aa; gid=yjW2Y0Yf8D9fyjK0j0dKyyI0qiI4j7EvKdKv6CAuTjY0V2288uK7TI888qyW8yK80dfJdfK4; customerClientId=484541279664605; customer-sso-sid=68c517521743575565529647ruy6lxdf4eldeqaj; x-user-id-creator.xiaohongshu.com=5bb0576b4eacab3854b2886c; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517521743575561056695sq3lmyv3jvox6x7o; galaxy_creator_session_id=0gWhrqSYdTiLvCp1yeggkYPl8YGbea85DpV9; galaxy.creator.beaker.session.id=1751292398233027416373; xsecappid=xhs-pc-web; webBuild=4.71.0; acw_tc=0ad6fb2017518761111043262e0e5e5bb2e327bf3041ec0dcb98ad47a825a0; websectiga=59d3ef1e60c4aa37a7df3c23467bd46d7f1da0b1918cf335ee7f2e9e52ac04cf; sec_poison_id=510d03c5-972d-4bcd-b707-3969c03ad4c6; web_session=0400697d88569f39d83a64cb5e3a4b84935b66; loadts=1751876415715; unread={%22ub%22:%22686b7df500000000170345d8%22%2C%22ue%22:%22686b487500000000120210d9%22%2C%22uc%22:12}';
      // 构建URL参数
      final queryParams = {
        'note_id': noteId,
        'cursor': cursor ?? '',
        'top_comment_id': topCommentId ?? '',
        'image_formats': 'jpg,webp,avif',
        'xsec_token': xsecToken,
      };

      // 构建URL
      final uri = Uri.parse(
        'https://edith.xiaohongshu.com/api/sns/web/v2/comment/page',
      ).replace(queryParameters: queryParams);

      // 获取动态签名
      String apiPath = '/api/sns/web/v2/comment/page';
      Map<String, String> signatures;
      try {
        signatures = await _getReverseResult(cookieStr, {
          'url': apiPath,
          'data': queryParams,
        });
        LoggerUtil.i(
          '【小红书】获取评论动态签名: X-s=${(signatures['X-s']?.substring(0, 20) ?? '')}..., X-t=${signatures['X-t'] ?? ''}',
        );
      } catch (e) {
        LoggerUtil.e('【小红书】获取评论动态签名失败，将使用固定签名: $e');
        signatures = {
          'X-s':
              'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjZhMzRhOTBkNjYxODI3ODgyODQwMDQ3ZWI0YjEwY2ZhNjdjMmZkZTdhZTc5YjM1YTk0YjYzOWYxOWE0YWZiMGY5ZDA5OTJhYTA0Nzc3YzFhMjFlNjUwNjU4Y2FjODJiNDAzMTdkMWExZmZmMjdkNjdiNmI1NDhjMDdlNTNhZTgwNzExMjM2NjVlNGFiMTJkNTk1YTZlOGQ5ODQ5Y2U3NWU3MThhOTQwNjZkNjZmMjY1NTU4YjI0YWJkMDQ2MjA5NzI5NDZkNGVlYTI2NmYxNjYxNjRkOWE2OWYzYTliYzM2MTI4NDM1YWI3YTg0NDBkMmRmNWVjZjY4OTcxNmY4ZGUyNzQ3NzE4ZTk1MzYzMWMyNzRjNmEzN2UyNTZjYjRjNGY2MmFkZWIwNDFjMjUwMTRlZGNhYzM0NGZlZTViYTJkZDc2ZWQ5ZTZiYWE0YmQ3N2M3MDI0MTE0Zjc3YWZmNzhiZmEzOGRlNWEyMzdjODA1OTI2NjQyZjAwMzI5OTk4YzY4MGMxZGE1MDYzMGZhN2QzNmNjNzk1YTgyMTc3YmU1In0=',
          'X-t': DateTime.now().millisecondsSinceEpoch.toString(),
        };
      }

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      // 构建请求头
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'origin': 'https://www.xiaohongshu.com',
        'priority': 'u=1, i',
        'referer': 'https://www.xiaohongshu.com/',
        'sec-ch-ua':
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-b3-traceid': traceId,
        'x-mns': 'unload',
        'x-s': signatures['X-s'] ?? '',
        'x-t': signatures['X-t'] ?? '',
        'x-s-common':
            '2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahIHjIj2eHjwjQgynEDJ74A'
            'HjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0qIN0HjNsQh+aHCH0rE+frEGnL9P/+f'
            'J0cE4g4E+fL92gk3w/YYy0mC+dp34ebT+nE1GgYl+/ZIPeZAP/qIP/GjNsQh+jHCHjHV'
            'HdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP'
            '8F8LagY/P9Ql4FpUzfpS2BcI8nT1GFbC/L88JdbFyrSiafp/cDMra7pFLDDAa7+8J7Qg',
        'x-xray-traceid': 'cbe84c98ebb619f25a731741bd7471df',
        'Cookie': cookieStr,
      };

      // 发送请求
      final response = await _dio.get(
        uri.toString(),
        options: Options(headers: headers),
      );

      LoggerUtil.i('【小红书】获取评论响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        // 打印部分响应数据以便调试
        final responseStr = jsonEncode(response.data);
        LoggerUtil.i(
          '【小红书】获取评论成功，响应数据: ${responseStr.length > 100 ? responseStr.substring(0, 100) + '...' : responseStr}',
        );

        return XhsCommentResponse.fromJson(response.data);
      } else {
        LoggerUtil.e(
          '【小红书】获取评论失败: ${response.statusCode}, ${response.statusMessage}, ${response.data}',
        );
        throw Exception(
          '获取评论失败: ${response.statusCode}, ${response.statusMessage}',
        );
      }
    } catch (e) {
      LoggerUtil.e('【小红书】获取评论异常: $e');
      throw Exception('获取评论异常: $e');
    }
  }

  /// 点赞小红书笔记
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [noteId] 笔记ID
  /// 返回点赞结果
  static Future<Map<String, dynamic>> likeNote({
    required String cookieStr,
    required String noteId,
  }) async {
    try {
      LoggerUtil.i('【小红书】点赞笔记，笔记ID: $noteId');
      cookieStr =
          'abRequestId=3b3f1172-e515-5a2b-83f8-9dde179c4f3a; a1=196a9ae613fn49uwy6e6yzk98aj0z6ukt1m5nnaxq50000317016; webId=b4ee448513192325799b22d43a6437aa; gid=yjW2Y0Yf8D9fyjK0j0dKyyI0qiI4j7EvKdKv6CAuTjY0V2288uK7TI888qyW8yK80dfJdfK4; customerClientId=484541279664605; webBuild=4.70.2; customer-sso-sid=68c517521743575565529647ruy6lxdf4eldeqaj; x-user-id-creator.xiaohongshu.com=5bb0576b4eacab3854b2886c; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517521743575561056695sq3lmyv3jvox6x7o; galaxy_creator_session_id=0gWhrqSYdTiLvCp1yeggkYPl8YGbea85DpV9; galaxy.creator.beaker.session.id=1751292398233027416373; web_session=0400697d88569f39d83aaa2f563a4be70a6f04; xsecappid=xhs-pc-web; acw_tc=0a4a9a7a17515513540757158e554a9e678a6aa33f7e210173461271223ff9; websectiga=f3d8eaee8a8c63016320d94a1bd00562d516a5417bc43a032a80cbf70f07d5c0; sec_poison_id=9dbf8d8f-ed18-498e-8782-8392064bd51b; unread={%22ub%22:%22685cfc46000000001202f443%22%2C%22ue%22:%226865e5660000000015020e7b%22%2C%22uc%22:12}';

      // 构建请求数据
      final requestData = {'note_oid': noteId};

      // 获取当前时间戳
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 获取动态签名
      String apiPath = '/api/sns/web/v1/note/like';
      Map<String, String> signatures;
      try {
        signatures = await _getReverseResult(cookieStr, {
          'url': apiPath,
          'data': requestData,
        });
        LoggerUtil.i(
          '【小红书】获取点赞动态签名: X-s=${(signatures['X-s']?.substring(0, 20) ?? '')}..., X-t=${signatures['X-t'] ?? ''}',
        );
      } catch (e) {
        LoggerUtil.e('【小红书】获取点赞动态签名失败，将使用固定签名: $e');
        signatures = {
          'X-s':
              'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjFhMTkwYjY0Y2QwODhmODU1M2ZiYjE1ZTI2NzY0MTEwOGQ4YjYxZWQ5NGRiMmUyYTQ5NzQ5NGNjNjc2MWQyOTk3OWE4OGRmNmIwYmI0ZmQzZjJmZmQ1M2EyOTk0ZTNlNWI3MWY0NTA1ZDc2ODg0OGI0NzNiNjljY2RkZWMyZDljYzRkMjZmM2FiNDFmZDliNzhkODBlZDY4YzQ3OWI2NzIxNjRlOWFmMjY4MGJkODNlZGU5NDYzODlmYTZkYjNlMjQyOGEwYTY1MzZkMThjODY1YzQ3M2FhYzdiMzViYzk3ODM5OWYxZDhhNzBmMTIwNjg3MDA4ZmU5MTEzYWI3ZDgwYTZkMmUyYWI2ZjAxODU4OWQwY2U3Y2QwMTM3MTEyN2NhYWVlODk3NmQyZDUyZDIxZjkyMjdjMjI2ZWY3NWM5ZTQwNzA1ZWYxZTlmNDE4MDFkMDNjNTNjN2Q2NmI3MzQ2ZjliN2U0ZWNhOGIzNmUwYzljNzA1YmQzMzM4M2IwZDY5NmZlMDQxYWYxZTczODk5M2M0MmViZTI0ZjhhZDZmIn0=',
          'X-t': timestamp.toString(),
        };
      }

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      // 发送请求
      final response = await _dio.post(
        'https://edith.xiaohongshu.com/api/sns/web/v1/note/like',
        data: requestData,
        options: Options(
          headers: {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://www.xiaohongshu.com',
            'priority': 'u=1, i',
            'referer': 'https://www.xiaohongshu.com/',
            'sec-ch-ua':
                '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-b3-traceid': traceId,
            'x-mns': 'unload',
            'x-s': signatures['X-s'] ?? '',
            'x-t': signatures['X-t'] ?? timestamp.toString(),
            'x-s-common':
                '2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahIHjIj2eHjwjQgynEDJ74A'
                'HjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0qIN0HjNsQh+aHCH0rE+frEGnL9P/+f'
                'J0cE4g4E+fL92gk3w/YYy0mC+dp34ebT+nE1GgYl+/ZIPeZAP/qIP/GjNsQh+jHCHjHV'
                'HdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP'
                '8F8LagY/P9Ql4FpUzfpS2BcI8nT1GFbC/L88JdbFyrSiafp/cDMra7pFLDDAa7+8J7Qg',
            'x-xray-traceid': 'cbe852e60ab61a0d3a5ad105f6ff1991',
            'Cookie': cookieStr,
          },
        ),
      );

      if (response.statusCode == 200) {
        LoggerUtil.i('【小红书】点赞笔记成功: ${response.data}');
        return {'success': true, 'data': response.data, 'message': '点赞成功'};
      } else {
        LoggerUtil.e('【小红书】点赞笔记失败: ${response.statusCode}, ${response.data}');
        return {'success': false, 'message': '点赞失败: ${response.statusCode}'};
      }
    } catch (e) {
      LoggerUtil.e('【小红书】点赞笔记异常: $e');
      return {'success': false, 'message': '点赞异常: $e'};
    }
  }

  /// 取消点赞小红书笔记
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [noteId] 笔记ID
  /// 返回取消点赞结果
  static Future<Map<String, dynamic>> dislikeNote({
    required String cookieStr,
    required String noteId,
  }) async {
    try {
      LoggerUtil.i('【小红书】取消点赞笔记，笔记ID: $noteId');
      // cookieStr = 'abRequestId=3b3f1172-e515-5a2b-83f8-9dde179c4f3a; a1=196a9ae613fn49uwy6e6yzk98aj0z6ukt1m5nnaxq50000317016; webId=b4ee448513192325799b22d43a6437aa; gid=yjW2Y0Yf8D9fyjK0j0dKyyI0qiI4j7EvKdKv6CAuTjY0V2288uK7TI888qyW8yK80dfJdfK4; customerClientId=484541279664605; webBuild=4.70.2; customer-sso-sid=68c517521743575565529647ruy6lxdf4eldeqaj; x-user-id-creator.xiaohongshu.com=5bb0576b4eacab3854b2886c; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517521743575561056695sq3lmyv3jvox6x7o; galaxy_creator_session_id=0gWhrqSYdTiLvCp1yeggkYPl8YGbea85DpV9; galaxy.creator.beaker.session.id=1751292398233027416373; web_session=0400697d88569f39d83aaa2f563a4be70a6f04; xsecappid=xhs-pc-web; acw_tc=0a4a9a7a17515513540757158e554a9e678a6aa33f7e210173461271223ff9; websectiga=cffd9dcea65962b05ab048ac76962acee933d26157113bb213105a116241fa6c; sec_poison_id=789086c1-fdc5-42ad-8614-e978e0abeaf3';

      // 构建请求数据
      final requestData = {'note_oid': noteId};

      // 获取当前时间戳
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 获取动态签名
      String apiPath = '/api/sns/web/v1/note/dislike';
      Map<String, String> signatures;
      try {
        signatures = await _getReverseResult(cookieStr, {
          'url': apiPath,
          'data': requestData,
        });
        LoggerUtil.i(
          '【小红书】获取取消点赞动态签名: X-s=${(signatures['X-s']?.substring(0, 20) ?? '')}..., X-t=${signatures['X-t'] ?? ''}',
        );
      } catch (e) {
        LoggerUtil.e('【小红书】获取取消点赞动态签名失败，将使用固定签名: $e');
        signatures = {
          'X-s':
              'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6ImJkZTNhYTcxZmNmZGMwZjM3ZTIxNjUwYThiNzUzMDQ0ZTljODQ3ZjYwNzk3OGNkYTRmNjQ5M2M1OGJiZjdkM2JlMDg1MTk1Y2VlYTIyNDY1YmU4NzI0OTY2MTI5NDY5MDZlZjM1NmQ4YzEwOWNmY2QxYjNjNjQ3ODQ1OWU2M2FmMzgzYjRkZWE5MjVhZTcyNjk5MDUyOTFlNGQ0YTg0ODZlNmYxMmU3NGFkNGU2NzJhNGI0ZjcwOWIzZTE0ZDZmNTA2ODY4MDMwNGFlZmNhMjU0MzhkOWNlOGRlODg4ZjFlMDVhNzcyMDYzODVlMjJlYWFiODM0MTc3NTU4MmUwM2JkZmY3YmIxMTFmMjNhYmM4Y2MyNTUwMGQ3NzAyZDA3YmY0YmU1MjJhMmIyNTRiZDk1NTdmZTE5MmY2ZmM2YTY5NWIwZWIwYTk3ODUxMDM0ZDVkNmU0Njc5MzM0MWExMjViNWM5NzUwOGU5MGM2M2IzNDdjNTZkNjU0ZGE2ZWY2YWExYmZkNWNmMmEzZGIyNTU5ZmQ0MzMzNTBiZjBlNTg0In0=',
          'X-t': timestamp.toString(),
        };
      }

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      // 发送请求
      final response = await _dio.post(
        'https://edith.xiaohongshu.com/api/sns/web/v1/note/dislike',
        data: requestData,
        options: Options(
          headers: {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'content-type': 'application/json;charset=UTF-8',
            'origin': 'https://www.xiaohongshu.com',
            'priority': 'u=1, i',
            'referer': 'https://www.xiaohongshu.com/',
            'sec-ch-ua':
                '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-b3-traceid': traceId,
            'x-mns': 'unload',
            'x-s': signatures['X-s'] ?? '',
            'x-t': signatures['X-t'] ?? timestamp.toString(),
            'x-s-common':
                '2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahIHjIj2eHjwjQgynEDJ74A'
                'HjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0qIN0HjNsQh+aHCH0rE+frEGnL9P/+f'
                'J0cE4g4E+fL92gk3w/YYy0mC+dp34ebT+nE1GgYl+/ZIPeZAP/qIP/GjNsQh+jHCHjHV'
                'HdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP'
                '8F8LagY/P9Ql4FpUzfpS2BcI8nT1GFbC/L88JdbFyrSiafp/cDMra7pFLDDAa7+8J7Qg',
            'x-xray-traceid': 'cbe852e60ab61a0d3a5ad105f6ff1991',
            'Cookie': cookieStr,
          },
        ),
      );

      if (response.statusCode == 200) {
        LoggerUtil.i('【小红书】取消点赞笔记成功: ${response.data}');
        return {'success': true, 'data': response.data, 'message': '取消点赞成功'};
      } else {
        LoggerUtil.e('【小红书】取消点赞笔记失败: ${response.statusCode}, ${response.data}');
        return {'success': false, 'message': '取消点赞失败: ${response.statusCode}'};
      }
    } catch (e) {
      LoggerUtil.e('【小红书】取消点赞笔记异常: $e');
      return {'success': false, 'message': '取消点赞异常: $e'};
    }
  }

  /// 发送评论的实例方法
  Future<Map<String, dynamic>> postCommentInstance({
    required String noteId,
    required String content,
    String? rootCommentId,
    String? targetCommentId,
  }) async {
    try {
      return await XhsService.postComment(
        cookieStr: cookieString,
        noteId: noteId,
        content: content,
        rootCommentId: rootCommentId,
        targetCommentId: targetCommentId,
      );
    } catch (e) {
      LoggerUtil.e('实例发送评论异常: $e');
      return {'success': false, 'message': '发送评论失败: $e'};
    }
  }

  /// 点赞评论的实例方法
  Future<Map<String, dynamic>> likeCommentInstance({
    required String noteId,
    required String commentId,
  }) async {
    try {
      return await XhsService.likeComment(
        cookieStr: cookieString,
        noteId: noteId,
        commentId: commentId,
      );
    } catch (e) {
      LoggerUtil.e('实例点赞评论异常: $e');
      return {'success': false, 'message': '点赞评论失败: $e'};
    }
  }

  /// 取消点赞评论的实例方法
  Future<Map<String, dynamic>> dislikeCommentInstance({
    required String noteId,
    required String commentId,
  }) async {
    try {
      return await XhsService.dislikeComment(
        cookieStr: cookieString,
        noteId: noteId,
        commentId: commentId,
      );
    } catch (e) {
      LoggerUtil.e('实例取消点赞评论异常: $e');
      return {'success': false, 'message': '取消点赞评论失败: $e'};
    }
  }

  /// 获取评论的实例方法
  Future<Map<String, dynamic>> getNoteCommentsInstance({
    required String noteId,
    required String xsecToken,
    required String cookieStrl,
    String? cursor,

  }) async {
    try {
      try {
        final result = await XhsService.getNoteComments(
          cookieStr: cookieStrl,
          noteId: noteId,
          xsecToken: xsecToken,
          cursor: cursor,
        );

        if (result.success && result.data != null) {
          return {
            'success': true,
            'data': {
              'cursor': result.data!.cursor,
              'has_more': result.data!.hasMore,
              'comments': result.data!.comments,
            },
          };
        } else {
          return {'success': false, 'message': result.msg};
        }
      } catch (e) {
        LoggerUtil.e('获取评论失败: $e');
        return {'success': false, 'message': '获取评论失败: $e'};
      }
    } catch (e) {
      LoggerUtil.e('实例获取笔记评论异常: $e');
      return {'success': false, 'message': '获取评论失败: $e'};
    }
  }

  /// 发送小红书评论
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [noteId] 笔记ID
  /// [content] 评论内容
  /// [rootCommentId] 根评论ID，回复评论时使用
  /// [targetCommentId] 目标评论ID，回复子评论时使用
  /// 返回发送评论结果
  static Future<Map<String, dynamic>> postComment({
    required String cookieStr,
    required String noteId,
    required String content,
    String? rootCommentId,
    String? targetCommentId,
  }) async {
    try {
      LoggerUtil.i('【小红书】发送评论，笔记ID: $noteId，内容: $content');
      // cookieStr =
      //     'abRequestId=3b3f1172-e515-5a2b-83f8-9dde179c4f3a; a1=196a9ae613fn49uwy6e6yzk98aj0z6ukt1m5nnaxq50000317016; webId=b4ee448513192325799b22d43a6437aa; gid=yjW2Y0Yf8D9fyjK0j0dKyyI0qiI4j7EvKdKv6CAuTjY0V2288uK7TI888qyW8yK80dfJdfK4; customerClientId=484541279664605; webBuild=4.70.2; customer-sso-sid=68c517521743575565529647ruy6lxdf4eldeqaj; x-user-id-creator.xiaohongshu.com=5bb0576b4eacab3854b2886c; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517521743575561056695sq3lmyv3jvox6x7o; galaxy_creator_session_id=0gWhrqSYdTiLvCp1yeggkYPl8YGbea85DpV9; galaxy.creator.beaker.session.id=1751292398233027416373; web_session=0400697d88569f39d83aaa2f563a4be70a6f04; xsecappid=xhs-pc-web; acw_tc=0a4a9a7a17515513540757158e554a9e678a6aa33f7e210173461271223ff9; websectiga=cffd9dcea65962b05ab048ac76962acee933d26157113bb213105a116241fa6c; sec_poison_id=789086c1-fdc5-42ad-8614-e978e0abeaf3';

      // 构建请求数据
      final Map<String, dynamic> requestBody = {
        "note_id": noteId,
        "content": content,
        "at_users": [],
      };

      // 如果是回复评论
      if (rootCommentId != null) {
        requestBody["root_comment_id"] = rootCommentId;

        // 如果是回复子评论
        if (targetCommentId != null) {
          requestBody["target_comment_id"] = targetCommentId;
        }
      }

      // 获取动态签名
      String apiPath = '/api/sns/web/v1/comment/post';
      Map<String, String> signatures;
      try {
        signatures = await _getReverseResult(cookieStr, {
          'url': apiPath,
          'data': requestBody,
        });
        LoggerUtil.i(
          '【小红书】获取评论发送动态签名: X-s=${(signatures['X-s']?.substring(0, 20) ?? '')}..., X-t=${signatures['X-t'] ?? ''}',
        );
      } catch (e) {
        LoggerUtil.e('【小红书】获取评论发送动态签名失败，将使用固定签名: $e');
        signatures = {
          'X-s':
              'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjQ1YzQzZTI1MjQyOTVjZjRjYzc4MTcxZmU2OWE4YTQyNTQ4ZWNlMmNkMmU0NWNiZTJkZjEzMTY2ZTAzYTRiZWFlNGZmNmEyMGVlYTcwYmQ3N2Q0NDdmYzY5M2E4YmQzMmJjYjY5NWFlMDNhYzg2MDlkNmMxNDY5ZjAzNmQxNGRkNDgxNDk0MmFiZGE1ODU3ZTRmMGRjYjc1MThhYWQ4ZWQ4Zjk1ZDhiNzM3Mzg3MTdhNTYwOTAwZGU0YmVmYWYwY2NkYWQ1YzdlM2Y1YjZiOWRiODhjMGFhZDdiN2NlN2Q2Y2YyYzU4YmY0NWRlZDg5MDc0ZDVjOWY4MTlhMWI0Mjg3NmY5OTFjZTRmYWUwOGE5Njc5OWE0ZmE4Mjg4YzI5ZjRiYzkyOTA1YzEwMzAwNjQ1NDRiYmYxNWQ2ZGY1OGEyMjhmNDg2MzU4MmZjMDA1NzYyMzNlZjA3YjRkY2ZjNjMyZGYxZGY0NTk2Mzk0Nzg2MjE3NzA5YzYwNzEyZTU5M2I3MmIxNTA4NjM3OTE5YzNhZWM4ZDU2OTM4Njk4ZmE5In0=',
          'X-t': DateTime.now().millisecondsSinceEpoch.toString(),
        };
      }

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      // 构建请求头
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://www.xiaohongshu.com',
        'priority': 'u=1, i',
        'referer': 'https://www.xiaohongshu.com/',
        'sec-ch-ua':
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-b3-traceid': traceId,
        'x-mns': 'unload',
        'x-s': signatures['X-s'] ?? '',
        'x-t': signatures['X-t'] ?? '',
        'x-s-common':
            '2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PahIHjIj2eHjwjQgynEDJ74A'
            'HjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0qIN0HjNsQh+aHCH0rE+frEGnL9P/+f'
            'J0cE4g4E+fL92gk3w/YYy0mC+dp34ebT+nE1GgYl+/ZIPeZAP/qIP/GjNsQh+jHCHjHV'
            'HdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP'
            '8F8LagY/P9Ql4FpUzfpS2BcI8nT1GFbC/L88JdbFyrSiafpDwLMra7pFLDDAa7+8J7Qg',
        'x-xray-traceid': traceId,
        'Cookie': cookieStr,
      };

      // 发送请求
      final response = await _dio.post(
        'https://edith.xiaohongshu.com$apiPath',
        data: jsonEncode(requestBody),
        options: Options(
          headers: headers,
          contentType: 'application/json',
          responseType: ResponseType.json,
          validateStatus: (status) => true,
        ),
      );

      LoggerUtil.i('【小红书】发送评论响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        // 打印部分响应数据以便调试
        final responseStr = jsonEncode(response.data);
        LoggerUtil.i(
          '【小红书】发送评论成功，响应数据: ${responseStr.length > 100 ? responseStr.substring(0, 100) + '...' : responseStr}',
        );

        return {
          'success': response.data['success'] ?? false,
          'message': response.data['msg'] ?? '',
          'data': response.data['data'],
        };
      } else {
        LoggerUtil.e(
          '【小红书】发送评论失败: ${response.statusCode}, ${response.statusMessage}, ${response.data}',
        );
        return {
          'success': false,
          'message':
              '发送评论失败: ${response.statusCode}, ${response.statusMessage}',
        };
      }
    } catch (e) {
      LoggerUtil.e('【小红书】发送评论异常: $e');
      return {'success': false, 'message': '发送评论异常: $e'};
    }
  }

  /// 点赞小红书评论
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [noteId] 笔记ID
  /// [commentId] 评论ID
  /// 返回点赞评论结果
  static Future<Map<String, dynamic>> likeComment({
    required String cookieStr,
    required String noteId,
    required String commentId,
  }) async {
    try {
      LoggerUtil.i('【小红书】点赞评论，笔记ID: $noteId, 评论ID: $commentId');

      // 获取当前时间戳
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 构建请求数据
      final requestData = {
        'note_id': noteId,
        'comment_id': commentId,
        'status': true, // true表示点赞
      };

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      // 构建请求头
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://creator.xiaohongshu.com',
        'referer': 'https://creator.xiaohongshu.com/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-b3-traceid': traceId,
        'Cookie': cookieStr,
      };

      // 发送请求
      final response = await _dio.post(
        'https://edith.xiaohongshu.com/web_api/sns/v1/comment/like',
        data: requestData,
        options: Options(headers: headers),
      );

      if (response.statusCode == 200) {
        LoggerUtil.i('【小红书】点赞评论成功: ${response.data}');
        return {'success': true, 'data': response.data, 'message': '点赞评论成功'};
      } else {
        LoggerUtil.e('【小红书】点赞评论失败: ${response.statusCode}, ${response.data}');
        return {'success': false, 'message': '点赞评论失败: ${response.statusCode}'};
      }
    } catch (e) {
      LoggerUtil.e('【小红书】点赞评论异常: $e');
      return {'success': false, 'message': '点赞评论异常: $e'};
    }
  }

  /// 取消点赞小红书评论
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [noteId] 笔记ID
  /// [commentId] 评论ID
  /// 返回取消点赞评论结果
  static Future<Map<String, dynamic>> dislikeComment({
    required String cookieStr,
    required String noteId,
    required String commentId,
  }) async {
    try {
      LoggerUtil.i('【小红书】取消点赞评论，笔记ID: $noteId, 评论ID: $commentId');

      // 获取当前时间戳
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 构建请求数据
      final requestData = {
        'note_id': noteId,
        'comment_id': commentId,
        'status': false, // false表示取消点赞
      };

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      // 构建请求头
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://creator.xiaohongshu.com',
        'referer': 'https://creator.xiaohongshu.com/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-b3-traceid': traceId,
        'Cookie': cookieStr,
      };

      // 发送请求
      final response = await _dio.post(
        'https://edith.xiaohongshu.com/web_api/sns/v1/comment/like',
        data: requestData,
        options: Options(headers: headers),
      );

      if (response.statusCode == 200) {
        LoggerUtil.i('【小红书】取消点赞评论成功: ${response.data}');
        return {'success': true, 'data': response.data, 'message': '取消点赞评论成功'};
      } else {
        LoggerUtil.e('【小红书】取消点赞评论失败: ${response.statusCode}, ${response.data}');
        return {'success': false, 'message': '取消点赞评论失败: ${response.statusCode}'};
      }
    } catch (e) {
      LoggerUtil.e('【小红书】取消点赞评论异常: $e');
      return {'success': false, 'message': '取消点赞评论异常: $e'};
    }
  }

  /// 获取小红书首页热门作品列表
  ///
  /// [cookieStr] 用户的完整Cookie字符串
  /// [request] 请求参数
  /// 返回首页热门作品响应
  static Future<XhsHomeFeedResponse> getHomeFeed({
    required String cookieStr,
    required XhsHomeFeedRequest request,
  }) async {
    try {
      LoggerUtil.i('【小红书】获取首页热门作品，页数: ${request.num}');
      // cookieStr =
      //     'abRequestId=3b3f1172-e515-5a2b-83f8-9dde179c4f3a; a1=196a9ae613fn49uwy6e6yzk98aj0z6ukt1m5nnaxq50000317016; webId=b4ee448513192325799b22d43a6437aa; gid=yjW2Y0Yf8D9fyjK0j0dKyyI0qiI4j7EvKdKv6CAuTjY0V2288uK7TI888qyW8yK80dfJdfK4; customerClientId=484541279664605; customer-sso-sid=68c517521743575565529647ruy6lxdf4eldeqaj; x-user-id-creator.xiaohongshu.com=5bb0576b4eacab3854b2886c; access-token-creator.xiaohongshu.com=customer.creator.AT-68c517521743575561056695sq3lmyv3jvox6x7o; galaxy_creator_session_id=0gWhrqSYdTiLvCp1yeggkYPl8YGbea85DpV9; galaxy.creator.beaker.session.id=1751292398233027416373; xsecappid=xhs-pc-web; webBuild=4.71.0; acw_tc=0ad6fb2017518761111043262e0e5e5bb2e327bf3041ec0dcb98ad47a825a0; websectiga=59d3ef1e60c4aa37a7df3c23467bd46d7f1da0b1918cf335ee7f2e9e52ac04cf; sec_poison_id=510d03c5-972d-4bcd-b707-3969c03ad4c6; web_session=0400697d88569f39d83a64cb5e3a4b84935b66; loadts=1751876415715; unread={%22ub%22:%22686b7df500000000170345d8%22%2C%22ue%22:%22686b487500000000120210d9%22%2C%22uc%22:12}';
      // 检查Cookie是否为空
      if (cookieStr.isEmpty) {
        LoggerUtil.e('【小红书】Cookie为空，无法获取首页热门作品');
        return XhsHomeFeedResponse(
          code: -1,
          success: false,
          msg: 'Cookie为空，请先登录账号',
          data: null,
        );
      }

      // 打印部分Cookie信息，方便调试
      LoggerUtil.i(
        '【小红书】Cookie长度: ${cookieStr.length}，前100字符: ${cookieStr.length > 100 ? cookieStr.substring(0, 100) + '...' : cookieStr}',
      );

      // 构建请求数据
      final requestData = request.toJson();

      LoggerUtil.i('【小红书】获取首页热门作品请求参数: ${jsonEncode(requestData)}');

      // 获取动态签名
      String apiPath = '/api/sns/web/v1/homefeed';
      Map<String, String> signatures;
      try {
        signatures = await _getReverseResult(cookieStr, {
          'url': apiPath,
          'data': requestData,
        });
        LoggerUtil.i(
          '【小红书】获取到动态签名: X-s=${(signatures['X-s']?.substring(0, 20) ?? '')}..., X-t=${signatures['X-t'] ?? ''}',
        );
      } catch (e) {
        LoggerUtil.e('【小红书】获取动态签名失败，将使用固定签名: $e');
        // 获取当前时间戳
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        signatures = {
          'X-s':
              'XYW_eyJzaWduU3ZuIjoiNTYiLCJzaWduVHlwZSI6IngyIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjM2NGY2MzljNDlmMzQ3NmRhMmZmNjVmODJmNDA3OTgxZjdjMTRlYzNhNTFmYWZhNGQ5ZDhmYWQ0MzEzNDNhZTM4ZTBhNGFhNDczZGYyYzdlZWExYjIwMWNlODBkNDJlMTViZWQyMjAyYjQzMmUwM2VjNGY3OGM1NTZlMDU1MzIyNTVkYWU1NmY2ZDNkOWM4N2RjZTZmOWIyYjk0MjI2YTkxMTQ3YmNjYjdlOWNlZTk2NTY5NmE2YjRlNjYzMGE5NDZiMzcyYzdjMThjMjkyYWE2NjE3MTkzYjJhYjBmYTA1ZjVlOTA1MzcwNWVhNGZmZGEyZWQxOWVlNGRiMmFkOTI3YzQwYWM4YWVmNTc0MDc3ODJjODlmNzZiNTMwNzI1ZWYxZjA5NDY5N2QwM2JhMTk2MzYzY2U5ODQ1MjdiOTUyYzMwMzQ3YzI4MzY5M2M2NTJjZjNhNzRmYTFkNTRmYjM4NjliNjVlOGU3NDk1ZWJiMTA5MjA3NWJhYjllODliZDExNmQxODQ2YjM2ZWYyZGFkMWVmNzY0YzRlZDA2ODQ3In0=',
          'X-t': timestamp.toString(),
        };
      }

      // 生成x-b3-traceid (随机16位十六进制字符串)
      final traceId = List.generate(
        16,
        (index) =>
            '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
      ).join('');

      // 构建请求头
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://www.xiaohongshu.com',
        'priority': 'u=1, i',
        'referer': 'https://www.xiaohongshu.com/',
        'sec-ch-ua':
            '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-b3-traceid': traceId,
        'x-mns': 'unload',
        'x-s': signatures['X-s'] ?? '',
        'x-t': signatures['X-t'] ?? '',
        'x-s-common':
            '2UQAPsHC+aIjqArjwjHjNsQhPsHCH0rjNsQhPaHCH0c1PjhlHjIj2eHjwjQgynEDJ74AHjIj2ePjwjQhyoPTqBPT49pjHjIj2ecjwjHFN0qlN0ZjNsQh+aHCH0rE+frEGnL9P/+fJ0cE4g4E+fL92gk3w/YYy0mC+dp34ebT+nE1GgYl+/ZIPeZAP/qIP/GjNsQh+jHCHjHVHdW7H0ijHjIj2eWjwjQQPAYUaBzdq9k6qB4Q4fpA8b878FSet9RQzLlTcSiM8/+n4MYP8F8LagY/P9Ql4FpUzfpS2BcI8nT1GFbC/L88JdbFyrSiafp/cDMra7pFLDDAa7+8J7QgabmFz7Qjp0mcwp4fanD68p40+fp8qgzELLbILrDA+9p3JpH9LLI3+LSk+d+DJfpSL98lnLYl49IUqgcMc0mrcDShtUTozBD6qM8FyFSh8o+h4g4U+obFyLSi4nbQz/+SPFlnPrDApSzQcA4SPopFJeQmzBMA/o8Szb+NqM+c4ApQzg8Ayp8FaDRl4AYs4g4fLomD8pzBpFRQ2ezLanSM+Skc47Qc4gcMag8VGLlj87PAqgzhagYSqAbn4FYQy7pTanTQ2npx87+8NM4L89L78p+l4BL6ze4AzB+IygmS8Bp8qDzFaLP98Lzn4AQQzLEAL7bFJBEVL7pwyS8Fag868nTl4e+0n04ApfuF8FSbL7SQyrplLnEl4LShyBEl20YdanTQ8fRl49TQc7bgz9qAq9zV/9pnLoqAag8m8/mf89pD8S+tanDMqA++GfGU4gzmanSNq9SD4fp3nDESpbmF+BEm/9pgLo4bag832fbm4fpDLo4MqB8wq98l4FQQPAYUagYb+LlM474Yqgq3qfp3qSmQ+nprJURSpSm7JLSiad+/JDbSy9MM4DSkcg+faaRA2rQk8rS34fLALo4VanYzyAQn4obOqgclagYSqMzn4BkQyAmAygb78rSiN7+x4gcA47pFJd+c4ApQc9+Va/+V+o4m+Bk7aLbApM87+rSb+obQyrRAL7bFnDQjpoc6c/mSyfkIPLS3zMmo4gzcnSm74DSe8BpnLozpaF8S8p8gcgPl/LpPaLLIqA8S8o+kLozYGMm7qDSeafpfpd4fanTdqAGI/oQQcFTS8Bu6qM+c49SQ4d8SPp8F2rQxLozQygZ6anVIqAb0J7+3NAmSyfF78n8n4M8QyMQBa/+Vc9Mn49pQ4f+aHjIj2eDjwjFEweH7PAcEw/LVHdWlPsHCPsIj2erlH0ijJfRUJnbVHdF=',
        'x-xray-traceid': List.generate(
          32,
          (index) =>
              '0123456789abcdef'[DateTime.now().microsecondsSinceEpoch % 16],
        ).join(''),
        'Cookie': cookieStr,
      };

      LoggerUtil.i(
        '【小红书】获取首页热门作品请求头: x-s=${(signatures['X-s']?.length ?? 0) > 20 ? (signatures['X-s']?.substring(0, 20) ?? '') + '...' : signatures['X-s'] ?? ''}，x-t=${signatures['X-t'] ?? ''}，traceid=$traceId',
      );

      // 发送请求
      final response = await _dio.post(
        'https://edith.xiaohongshu.com$apiPath',
        data: requestData,
        options: Options(headers: headers),
      );

      LoggerUtil.i('【小红书】获取首页热门作品响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        // 打印部分响应数据以便调试
        final responseStr = jsonEncode(response.data);
        LoggerUtil.i(
          '【小红书】获取首页热门作品成功，响应数据: ${responseStr.length > 100 ? responseStr.substring(0, 100) + '...' : responseStr}',
        );

        try {
          // 检查响应数据是否为有效的JSON对象
          if (response.data is! Map<String, dynamic>) {
            LoggerUtil.e('【小红书】响应数据不是有效的JSON对象: ${response.data.runtimeType}');
            return XhsHomeFeedResponse(
              code: -1,
              success: false,
              msg: '响应数据格式错误',
              data: null,
            );
          }

          final result = XhsHomeFeedResponse.fromJson(response.data);
          LoggerUtil.i(
            '【小红书】解析响应成功，状态码: ${result.code}, 消息: ${result.msg}, 数据: ${result.data != null ? '有数据' : '无数据'}',
          );
          return result;
        } catch (e) {
          LoggerUtil.e('【小红书】解析首页热门作品响应失败: $e');
          return XhsHomeFeedResponse(
            code: -1,
            success: false,
            msg: '解析响应失败: $e',
            data: null,
          );
        }
      } else {
        LoggerUtil.e('【小红书】获取首页热门作品失败: ${response.statusCode}');
        return XhsHomeFeedResponse(
          code: response.statusCode ?? -1,
          success: false,
          msg: response.statusMessage ?? '请求失败',
          data: null,
        );
      }
    } catch (e) {
      LoggerUtil.e('【小红书】获取首页热门作品异常: $e');
      return XhsHomeFeedResponse(
        code: -1,
        success: false,
        msg: '请求异常: $e',
        data: null,
      );
    }
  }
}
