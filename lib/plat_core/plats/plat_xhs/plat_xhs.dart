import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_base.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_note_detail_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_poi_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/plat_xhs_publish.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/xhs_service.dart';

class PlatXhs extends PlatBase {
  PlatXhs(super.platformModel);

  @override
  Future<PublishResponseModel> publishVideoWithPlatformConfig(
    PublishParamsModel publishParamsModel,
    Map<String, dynamic> platformConfig,
  ) async {
    LoggerUtil.i('小红书发布参数: ${publishParamsModel.toString()}');
    LoggerUtil.i('小红书平台特定配置: $platformConfig');

    final PlatXhsPublish xhsPublish = PlatXhsPublish(
      this,
      publishParamsModel,
      (p, {msg}) {
        publishParamsModel.progressCallback?.call(p, msg);
        LoggerUtil.i('小红书上传进度: $p%, 消息: $msg');
      },
      platformConfig: platformConfig, // 传递平台特定配置
    );
    return await xhsPublish.publishVideoWorkApi();
  }

  @override
  publishVideo(publishParamsModel) async {
    final PlatXhsPublish xhsPublish = PlatXhsPublish(this, publishParamsModel, (
      p, {
      msg,
    }) {
      publishParamsModel.progressCallback?.call(p, msg);
    });
    return await xhsPublish.publishVideoWorkApi();
  }

  @override
  publishImgText(publishParamsModel) async {
    final PlatXhsPublish xhsPublish = PlatXhsPublish(
      this,
      publishParamsModel,
      (p, {msg}) {},
    );
    return await xhsPublish.publishImagesWorkApi();
  }

  /// 搜索POI位置
  ///
  /// [latitude] 纬度
  /// [longitude] 经度
  /// [keyword] 搜索关键词
  /// 返回POI搜索结果
  Future<XhsPoiSearchResponse> searchPoi({
    required double latitude,
    required double longitude,
    String keyword = '',
  }) async {
    // 获取平台账号的Cookie，使用父类中的cookieStr变量
    final String cookieStr = this.cookieStr;

    // 调用XhsService的搜索POI方法
    return await XhsService.searchPoi(
      cookieStr: cookieStr,
      latitude: latitude,
      longitude: longitude,
      keyword: keyword,
    );
  }

  /// 搜索小红书内容
  ///
  /// [keyword] 搜索关键词
  /// [page] 页码，从1开始
  /// [pageSize] 每页数量
  /// [sort] 排序方式，默认为general
  /// [filters] 过滤条件列表
  /// 返回内容搜索结果
  Future<XhsSearchContentResponse> searchContent({
    required String keyword,
    int page = 1,
    int pageSize = 20,
    String sort = 'general',
    List<XhsSearchFilter>? filters,
  }) async {
    // 获取平台账号的Cookie
    final String cookieStr = this.cookieStr;

    // 构建搜索请求
    final request = XhsSearchContentRequest(
      keyword: keyword,
      page: page,
      pageSize: pageSize,
      sort: sort,
      filters: filters ?? [],
    );

    // 调用XhsService的搜索内容方法
    return await XhsService.searchContent(
      cookieStr: cookieStr,
      request: request,
    );
  }

  /// 获取小红书笔记详情
  ///
  /// [noteId] 笔记ID
  /// [xsecToken] 安全令牌
  /// 返回笔记详情
  Future<XhsNoteDetailResponse> getNoteDetail({
    required String noteId,
    required String xsecToken,
  }) async {
    // 获取平台账号的Cookie
    final String cookieStr = this.cookieStr;

    // 调用XhsService的获取笔记详情方法
    return await XhsService.getNoteDetail(
      cookieStr: cookieStr,
      noteId: noteId,
      xsecToken: xsecToken,
    );
  }
}
