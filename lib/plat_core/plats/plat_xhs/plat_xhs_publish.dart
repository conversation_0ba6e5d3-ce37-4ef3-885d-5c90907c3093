import 'dart:convert';
import 'dart:typed_data' as typed_data;

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/enum/pub_params_enum.dart';
import 'package:aitoearn_app/plat_core/models/publish_parmas_model.dart';
import 'package:aitoearn_app/plat_core/models/publish_response_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/plat_xhs.dart';
import 'package:aitoearn_app/plat_core/utils/plat_common_utils.dart';
import 'package:aitoearn_app/plat_core/utils/video_info_util.dart';
import 'package:dio/dio.dart';
import 'package:mime/mime.dart';

/// 小红书发布
class PlatXhsPublish {
  /// 发布进度回调函数（进度，消息）
  late void Function(int, {String? msg}) publishProgressCallback;

  late PlatXhs xhs;

  /// 视频发布参数
  late PublishParamsModel publishParamsModel;

  /// 平台特定配置
  Map<String, dynamic>? platformConfig;

  get cookieA1 =>
      xhs.cookieList.firstWhere((cookie) => cookie.name == 'a1').value;

  PlatXhsPublish(
    this.xhs,
    this.publishParamsModel,
    this.publishProgressCallback, {
    this.platformConfig,
  });

  /// 获取上传许可证
  _getUploadPermit(String scene) async {
    var permitRes = await xhs.makeRequest(
      'https://creator.xiaohongshu.com/api/media/v1/upload/web/permit?biz_name=spectrum&scene=$scene&file_count=1&version=1&source=web',
    );
    if (permitRes == null || permitRes.data['code'] != 0) {
      throw Exception(
        "获取上传许可证失败,失败原因：${permitRes?.data['msg'] ?? 'permitRes为Null'}",
      );
    }
    return permitRes.data['data']['uploadTempPermits'];
  }

  /// 上传视频文件
  _uploadVideoFile() async {
    try {
      var uploadPermit = await _getUploadPermit('video');
      LoggerUtil.i('上传视频许可证: ${jsonEncode(uploadPermit)}');

      var fileIds = uploadPermit[0]['fileIds'];
      LoggerUtil.i('fileIds类型: ${fileIds.runtimeType}, 值: $fileIds');

      // 处理fileIds可能是列表的情况
      var uploadFileId = fileIds is List ? fileIds[0] : fileIds;
      LoggerUtil.i('处理后的uploadFileId: $uploadFileId');

      var uploadAddr = uploadPermit[0]['uploadAddr'];
      var uploadToken = uploadPermit[0]['token'];
      var uploadBaseUrl = 'https://$uploadAddr/$uploadFileId';
      LoggerUtil.i('上传URL: $uploadBaseUrl');

      // 获取分片上传ID
      var uploadIdRes = await xhs.makeRequest(
        '$uploadBaseUrl?uploads',
        options: Options(
          method: 'POST',
          headers: {
            'X-Cos-Security-Token': uploadToken,
            'Content-Type': lookupMimeType(publishParamsModel.video!.path),
          },
        ),
      );

      if (uploadIdRes == null ||
          PlatCommonUtils.isJsonString(uploadIdRes.data)) {
        final dynamic parsedRes = jsonDecode(uploadIdRes?.data);
        throw Exception('上传视频失败,失败原因:获取上传id失败: ${parsedRes['msg']}');
      }

      var parsedXml = await PlatCommonUtils.xml2json(uploadIdRes.data);
      var uploadId =
          parsedXml['InitiateMultipartUploadResult']['UploadId'] ?? '';
      if (uploadId == '') {
        throw Exception('上传视频失败,失败原因:获取上传id失败: 获取上传id失败');
      }

      // 分片上传文件
      var uploadPartInfo = [];
      var parts = publishParamsModel.filePartInfo!.parts;
      for (var i = 0; i < parts.length; i++) {
        var part = parts[i];
        publishProgressCallback(50, msg: '上传视频（$i/${parts.length}）');
        var uploadIdRes = await xhs.makeRequest(
          '$uploadBaseUrl?uploadId=$uploadId&partNumber=${i + 1}',
          options: Options(
            method: 'PUT',
            headers: {
              'X-Cos-Security-Token': uploadToken,
              'Content-Type': 'application/octet-stream',
            },
          ),
          data: part,
        );
        if (uploadIdRes == null ||
            uploadIdRes.headers['etag'] == null ||
            uploadIdRes.headers['etag']!.isEmpty) {
          throw Exception('上传视频失败,失败原因:上传分片失败');
        }
        uploadPartInfo.add({
          'Part': {'PartNumber': i + 1, 'ETag': uploadIdRes.headers['etag']},
        });
      }

      // 合并分片
      var completeXml = PlatCommonUtils.jsonToXml(
        uploadPartInfo,
        rootName: 'CompleteMultipartUpload',
      );
      var completeRes = await xhs.makeRequest(
        '$uploadBaseUrl?uploadId=$uploadId',
        options: Options(
          method: 'POST',
          headers: {
            'X-Cos-Security-Token': uploadToken,
            'Content-Type': 'application/xml',
          },
        ),
        data: completeXml,
      );

      if (completeRes == null ||
          PlatCommonUtils.isJsonString(completeRes.data)) {
        final dynamic parsedRes = jsonDecode(completeRes?.data);
        throw Exception('上传视频失败,失败原因:合并分片失败: ${parsedRes['msg']}');
      }
      var headers = completeRes.headers;
      LoggerUtil.i('合并分片响应头: ${headers.map}');

      if (headers['x-ros-preview-url'] == null ||
          headers['x-ros-video-id'] == null) {
        throw Exception('上传视频失败,失败原因: 未获取到videoId');
      }

      var previewUrl = headers['x-ros-preview-url'];
      var videoId = headers['x-ros-video-id'];

      // 检查并处理可能的List类型
      String? remotePreviewUrl;
      String? remoteVideoId;

      // 安全地处理可能的List类型
      if (previewUrl != null) {
        if (previewUrl.isNotEmpty) {
          remotePreviewUrl = previewUrl.first.toString();
        }
      }

      if (videoId != null) {
        if (videoId.isNotEmpty) {
          remoteVideoId = videoId.first.toString();
        }
      }

      LoggerUtil.i(
        '上传视频成功: previewUrl=$remotePreviewUrl, videoId=$remoteVideoId',
      );

      return {
        'uploadFileId': uploadFileId,
        'remotePreviewUrl': remotePreviewUrl,
        'remoteVideoId': remoteVideoId,
      };
    } catch (e) {
      LoggerUtil.e('上传视频文件异常: $e');
      rethrow;
    }
  }

  /// 上传图片文件
  _uploadCoverFile(typed_data.Uint8List bytes) async {
    try {
      var uploadPermit = await _getUploadPermit('image');
      LoggerUtil.i('上传图片许可证: ${jsonEncode(uploadPermit)}');

      var fileIds = uploadPermit[0]['fileIds'];
      LoggerUtil.i('图片fileIds类型: ${fileIds.runtimeType}, 值: $fileIds');

      // 处理fileIds可能是列表的情况
      var coverUploadFileId = fileIds is List ? fileIds[0] : fileIds;
      LoggerUtil.i('处理后的coverUploadFileId: $coverUploadFileId');

      var uploadAddr = uploadPermit[0]['uploadAddr'];
      var uploadToken = uploadPermit[0]['token'];
      var uploadBaseUrl = 'https://$uploadAddr/$coverUploadFileId';

      var uploadIdRes = await xhs.makeRequest(
        uploadBaseUrl,
        options: Options(
          method: 'PUT',
          headers: {
            'X-Cos-Security-Token': uploadToken,
            'Content-Type': 'application/octet-stream',
          },
        ),
        data: bytes,
      );
      var uploadRes = uploadIdRes!.headers;
      LoggerUtil.i('上传图片响应头: ${uploadRes.map}');

      if (uploadRes['x-ros-preview-url'] == null) {
        throw Exception('上传封面失败,失败原因:未获取到previewUrl');
      }

      var previewUrl = uploadRes['x-ros-preview-url'];

      // 检查并处理可能的List类型
      String? remotePreviewUrl;

      // 安全地处理可能的List类型
      if (previewUrl != null) {
        if (previewUrl.isNotEmpty) {
          remotePreviewUrl = previewUrl.first.toString();
        }
      }

      LoggerUtil.i('上传图片成功: previewUrl=$remotePreviewUrl');

      // 获取图片宽高
      var coverDimensions = await PlatCommonUtils.getImageSizeFromUint8ListSync(
        bytes,
      );

      return {
        'coverUploadFileId': coverUploadFileId,
        'remotePreviewUrl': remotePreviewUrl,
        'coverDimensions': coverDimensions,
      };
    } catch (e) {
      LoggerUtil.e('上传图片文件异常: $e');
      rethrow;
    }
  }

  /// 视频发布
  Future<PublishResponseModel> publishVideoWorkApi() async {
    try {
      LoggerUtil.i('小红书发布初始参数：\n ${publishParamsModel.toString()}');
      // 上传视频
      publishProgressCallback(10, msg: '正在上传视频');
      LoggerUtil.i('开始上传视频 --------------------------');
      // 获取视频信息
      var videoInfo = await VideoInfoUtil.getVideoInfo(
        publishParamsModel.video!,
      );
      // 上传视频
      var uploadVideoRes = await _uploadVideoFile();

      LoggerUtil.i('开始上传封面 --------------------------');
      publishProgressCallback(60, msg: '正在上传封面...');
      // 上传封面,获取远程Url
      var coverBytes = await publishParamsModel.cover.readAsBytes();
      var uploadCoverRes = await _uploadCoverFile(coverBytes);

      var originalMetadata = {
        'video': {
          'bitrate': videoInfo.videoStream.getBitrate(),
          'colour_primaries': videoInfo.videoStream.getStringProperty(
            'color_primaries',
          ),
          'duration': _getDurationInMilliseconds(
            videoInfo.videoStream.getStringProperty('duration'),
          ),
          'format': videoInfo.videoStream.getStringProperty('format'),
          'frame_rate':
              videoInfo.videoStream.getRealFrameRate()?.split('/').first ?? '',
          'height': videoInfo.videoStream.getHeight(),
          'matrix_coefficients': videoInfo.videoStream.getStringProperty(
            'color_primaries',
          ),
          'rotation': 0,
          'transfer_characteristics': videoInfo.videoStream.getStringProperty(
            'color_primaries',
          ),
          'width': videoInfo.videoStream.getWidth(),
        },
        'audio': {
          'bitrate': videoInfo.audioStream.getBitrate(),
          'channels': videoInfo.audioStream.getNumberProperty('channels'),
          'duration': _getDurationInMilliseconds(
            videoInfo.audioStream.getStringProperty('duration'),
          ),
          'format': videoInfo.audioStream.getCodec()!.toUpperCase(),
          'sampling_rate': videoInfo.audioStream.getSampleRate(),
        },
      };
      var xhsVideoInfo = {
        'fileid': uploadVideoRes['uploadFileId'],
        'file_id': uploadVideoRes['uploadFileId'],
        'format_width': videoInfo.videoStream.getWidth(),
        'format_height': videoInfo.videoStream.getHeight(),
        'video_preview_type':
            videoInfo.videoStream.getHeight()! >
                    videoInfo.videoStream.getWidth()!
                ? 'full_vertical_screen'
                : '',
        'composite_metadata': originalMetadata,
        'timelines': [],
        'cover': {
          'fileid': uploadCoverRes['coverUploadFileId'],
          'file_id': uploadCoverRes['coverUploadFileId'],
          'height':
              uploadCoverRes['coverDimensions'] is Map
                  ? uploadCoverRes['coverDimensions']['height']
                  : uploadCoverRes['coverDimensions'].height,
          'width':
              uploadCoverRes['coverDimensions'] is Map
                  ? uploadCoverRes['coverDimensions']['width']
                  : uploadCoverRes['coverDimensions'].width,
          'frame': {'ts': 0, 'is_user_select': false, 'is_upload': false},
          'stickers': {'version': 2, 'neptune': []},
          'fonts': [],
          'extra_info_json': '{}',
        },
        'chapters': [],
        'chapter_sync_text': false,
        'segments': {
          'count': 1,
          'need_slice': false,
          'items': [
            {
              'mute': 0,
              'speed': 1,
              'start': 0,
              'duration': videoInfo.videoStream.getStringProperty('duration'),
              'transcoded': 0,
              'media_source': 1,
              'original_metadata': originalMetadata,
            },
          ],
        },
        'entrance': 'web',
      };

      return _postCreateWorks(videoInfo: xhsVideoInfo);
    } catch (e) {
      return PublishResponseModel(false, failMsg: '$e');
    }
  }

  /// 图文发布
  Future<PublishResponseModel> publishImagesWorkApi() async {
    try {
      var images = [];
      publishProgressCallback(10, msg: '正在上传图片');
      var len = publishParamsModel.images!.length;

      for (var i = 0; i < len; i++) {
        publishProgressCallback(50, msg: '上传图片（${i + 1}/$len）');

        var image = publishParamsModel.images![i];
        var bytes = await image.readAsBytes();
        var imgInfo = await _uploadCoverFile(bytes);

        var type = lookupMimeType(image.path);
        images.add({
          'file_id': imgInfo['coverUploadFileId'],
          'width':
              imgInfo['coverDimensions'] is Map
                  ? imgInfo['coverDimensions']['width']
                  : imgInfo['coverDimensions'].width,
          'height':
              imgInfo['coverDimensions'] is Map
                  ? imgInfo['coverDimensions']['height']
                  : imgInfo['coverDimensions'].height,
          'metadata': {'source': -1},
          'stickers': {'version': 2, 'floating': []},
          'extra_info_json': jsonEncode({
            'mimeType': 'image/${type!}' == 'jpg' ? 'jpeg' : type,
          }),
        });
      }

      return _postCreateWorks(imageInfo: images);
    } catch (e) {
      LoggerUtil.i('图文发布失败：$e');
      return PublishResponseModel(false, failMsg: '$e');
    }
  }

  /// 逆向获取XsXt
  _getReverseResult(Map<dynamic, dynamic> data) async {
    try {
      LoggerUtil.i('获取XsXt签名，参数: ${jsonEncode(data)}');
      var reverseRes = await xhs.makeRequest(
        'http://116.62.154.231:7879',
        options: Options(
          method: 'POST',
          headers: {'Content-Type': 'application/json;charset=UTF-8'},
        ),
        data: {...data, 'a1': cookieA1},
      );

      if (reverseRes == null) {
        throw Exception('获取签名失败：请求返回为空');
      }

      // 确保响应数据是字符串，并进行解析
      String responseData;
      if (reverseRes.data is String) {
        responseData = reverseRes.data;
      } else if (reverseRes.data is Map) {
        responseData = jsonEncode(reverseRes.data);
      } else {
        throw Exception('获取签名失败：响应数据格式不正确');
      }

      var reverse = jsonDecode(responseData);
      LoggerUtil.i('获取XsXt签名结果: $reverse');
      return reverse;
    } catch (e) {
      LoggerUtil.e('获取XsXt签名异常: $e');
      throw Exception('获取签名失败: $e');
    }
  }

  /// 平台可见性处理
  _getVisibleType() {
    // 优先使用平台特定配置
    if (platformConfig != null && platformConfig!.containsKey('is_private')) {
      final isPrivate = platformConfig!['is_private'] as bool? ?? false;
      return isPrivate ? 1 : 0; // 1: 私密, 0: 公开
    }

    // 回退到通用配置
    switch (publishParamsModel.visibleType) {
      case VisibleTypeEnum.public:
        return 0;
      case VisibleTypeEnum.private:
        return 1;
      case VisibleTypeEnum.priend:
        return 4;
    }
  }

  /// 参数处理
  Map<dynamic, dynamic> _getPubPlatParams() {
    var desc = '';
    const hashTag = [];
    const ats = [];

    // 处理标题、@好友、话题
    if (publishParamsModel.topics != null) {
      for (var topic in publishParamsModel.topics!) {
        desc += ' #$topic[话题]# ';
        hashTag.add({'id': null, 'name': topic, 'link': null, 'type': null});
      }
    }
    if (publishParamsModel.mentionedUserInfo != null) {
      for (var userInfo in publishParamsModel.mentionedUserInfo!) {
        desc += ' @${userInfo.name} ';
        ats.add({
          'nickname': userInfo.name,
          'name': userInfo.name,
          'user_id': userInfo.value,
        });
      }
    }

    // 处理位置
    var postLoc = {};
    if (publishParamsModel.location != null) {
      var location = publishParamsModel.location!;
      postLoc = {
        'poi_id': location.id,
        'poi_type': location.poiType,
        'subname': location.simpleAddress,
        'name': location.name,
      };
    }

    var pubPlatParams = {
      'title': publishParamsModel.title,
      'note_id': '',
      'source':
          '{"type":"web","ids":"","extraInfo":"{\\"systemId\\":\\"web\\"}"}',
      'desc': desc,
      'ats': ats,
      'hash_tag': hashTag,
      'business_binds': jsonEncode({
        'version': 1,
        'noteId': 0,
        'bizType': 0,
        'noteOrderBind': {},
        'notePostTiming': {'postTime': 0},
        'noteCollectionBind': {'id': ''},
      }),
      'privacy_info': {'op_type': 1, 'type': _getVisibleType(), 'user_ids': []},
      'goods_info': postLoc,
      'biz_relations': [],
    };
    return pubPlatParams;
  }

  /// 发布作品
  Future<PublishResponseModel> _postCreateWorks({
    dynamic imageInfo,
    dynamic videoInfo,
  }) async {
    try {
      dynamic capaTraceInfo = {'contextJson': ''};
      // 图文处理
      // if (imageInfo != null) {
      //   Map<String, dynamic> longTextToImage = {'imageFileIds': []};
      //   for (var item in imageInfo) {
      //     longTextToImage['imageFileIds']!.add(item['file_id']);
      //   }
      //   LoggerUtil.d(longTextToImage);
      //   capaTraceInfo['contextJson'] = jsonEncode({
      //     'longTextToImage': longTextToImage,
      //   });
      // }

      var requestData = {
        'common': {
          ..._getPubPlatParams(),
          'type': imageInfo != null ? 'normal' : 'video',
          'capa_trace_info': capaTraceInfo,
        },
        'image_info': imageInfo != null ? {'images': imageInfo} : null,
        'video_info': videoInfo,
      };

      publishProgressCallback(60);

      // 获取签名
      var reverse = await _getReverseResult({
        'url': '/web_api/sns/v2/note',
        'data': requestData,
      });

      // 验证签名数据
      if (reverse == null ||
          !reverse.containsKey('X-s') ||
          !reverse.containsKey('X-t')) {
        throw Exception('获取签名失败，签名数据不完整');
      }

      String xsSign = reverse['X-s'].toString();
      String xtSign = reverse['X-t'].toString();

      LoggerUtil.i('获取到签名: X-s=$xsSign, X-t=$xtSign');

      LoggerUtil.i(jsonEncode(requestData));
      // 发布
      publishProgressCallback(65, msg: '正在发布...');
      var createReq = await xhs.makeRequest(
        'https://edith.xiaohongshu.com/web_api/sns/v2/note',
        options: Options(
          method: 'POST',
          headers: {
            // 'Content-Type': 'application/json;charset=UTF-8',
            // 'url': '/web_api/sns/v2/note',
            // 'a1': cookieA1,
            'X-S': xsSign,
            // 'X-T': xtSign,
          },
        ),
        data: jsonEncode(requestData),
      );
      publishProgressCallback(75, msg: '发布完成，正在查询结果...');
      if (createReq == null) {
        throw Exception('创建作品失败：失败原因：网络繁忙');
      }
      var createRes = createReq.data;

      // 处理结果
      LoggerUtil.i('小红书发布响应: ${jsonEncode(createRes)}');

      // 检查是否有错误码
      if (createRes.containsKey('code') && createRes['code'] == -1) {
        throw Exception('创建作品失败,失败原因:验签未通过');
      }

      // 根据新的响应格式判断成功状态
      // success: true 且 result: 0 表示成功
      bool isSuccess = false;
      if (createRes.containsKey('success') && createRes.containsKey('result')) {
        isSuccess = createRes['success'] == true && createRes['result'] == 0;
      }

      if (!isSuccess) {
        String errorMsg = createRes['msg'] ?? '未知错误';
        if (createRes.containsKey('result') && createRes['result'] != 0) {
          errorMsg = '错误码: ${createRes['result']}, $errorMsg';
        }
        throw Exception('创建作品失败,失败原因: $errorMsg');
      }

      // 获取作品ID和分享链接
      String? workId;
      String? shareLink;

      if (createRes.containsKey('data') && createRes['data'] != null) {
        workId = createRes['data']['id'];
      }

      if (createRes.containsKey('share_link')) {
        shareLink = createRes['share_link'];
      }

      if (workId == null) {
        throw Exception('创建作品失败,失败原因: 未获取到作品ID');
      }

      publishProgressCallback(100);

      LoggerUtil.i('小红书发布成功: workId=$workId, shareLink=$shareLink');

      return PublishResponseModel(
        true,
        workLink: shareLink ?? "https://www.xiaohongshu.com/discovery/item/$workId",
        dataId: workId,
      );
    } catch (e) {
      return PublishResponseModel(false, failMsg: '$e');
    }
  }

  /// 获取作品列表
  getWorks({int page = 0}) async {
    try {
      var url = '/web_api/sns/v5/creator/note/user/posted?tab=0&page=$page';

      // 获取签名
      var reverse = await _getReverseResult({'url': url});

      // 验证签名数据
      if (reverse == null ||
          !reverse.containsKey('X-s') ||
          !reverse.containsKey('X-t')) {
        throw Exception('获取签名失败，签名数据不完整');
      }

      String xsSign = reverse['X-s'].toString();
      String xtSign = reverse['X-t'].toString();

      var createReq = await xhs.makeRequest(
        'https://edith.xiaohongshu.com$url',
        options: Options(
          method: 'GET',
          headers: {'X-S': xsSign, 'X-T': xtSign},
        ),
      );

      if (createReq == null) {
        throw Exception('获取作品列表失败：请求返回为空');
      }

      return createReq.data;
    } catch (e) {
      LoggerUtil.e('获取作品列表异常: $e');
      throw Exception('获取作品列表失败: $e');
    }
  }

  int _getDurationInMilliseconds(String? duration) {
    if (duration == null || duration.isEmpty) return 0;
    try {
      return (double.parse(duration) * 1000).toInt();
    } catch (e) {
      return 0;
    }
  }
}
