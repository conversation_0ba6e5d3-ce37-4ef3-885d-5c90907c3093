import 'package:json_annotation/json_annotation.dart';

part 'xhs_note_detail_model.g.dart';

@JsonSerializable()
class XhsNoteDetailResponse {
  final int code;
  final bool success;
  final String msg;
  final XhsNoteDetailData data;

  XhsNoteDetailResponse({
    required this.code,
    required this.success,
    required this.msg,
    required this.data,
  });

  factory XhsNoteDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$XhsNoteDetailResponseFromJson(json);

  Map<String, dynamic> toJson() => _$XhsNoteDetailResponseToJson(this);
}

@JsonSerializable()
class XhsNoteDetailData {
  @JsonKey(name: 'items')
  final List<NoteDetailItem> items;

  XhsNoteDetailData({required this.items});

  factory XhsNoteDetailData.fromJson(Map<String, dynamic> json) =>
      _$XhsNoteDetailDataFromJson(json);

  Map<String, dynamic> toJson() => _$XhsNoteDetailDataToJson(this);
}

@JsonSerializable()
class NoteDetailItem {
  final String id;
  @JsonKey(name: 'model_type')
  final String modelType;
  @JsonKey(name: 'note_card')
  final NoteCardDetail noteCard;

  NoteDetailItem({
    required this.id,
    required this.modelType,
    required this.noteCard,
  });

  factory NoteDetailItem.fromJson(Map<String, dynamic> json) =>
      _$NoteDetailItemFromJson(json);

  Map<String, dynamic> toJson() => _$NoteDetailItemToJson(this);
}

@JsonSerializable()
class NoteCardDetail {
  final String title;
  final String desc;
  @JsonKey(name: 'image_list')
  final List<NoteImage> imageList;
  @JsonKey(name: 'interact_info')
  final InteractInfo interactInfo;
  final User user;
  final String type;
  @JsonKey(name: 'tag_list')
  final List<Tag> tagList;
  final Video? video;
  @JsonKey(name: 'time', fromJson: _timeFromJson)
  final String? timeStr;
  @JsonKey(name: 'last_update_time')
  final int? lastUpdateTime;
  @JsonKey(defaultValue: false)
  final bool liked;
  @JsonKey(defaultValue: false)
  final bool followed;
  @JsonKey(defaultValue: 'none')
  final String relation;

  NoteCardDetail({
    required this.title,
    required this.desc,
    required this.imageList,
    required this.interactInfo,
    required this.user,
    required this.type,
    required this.tagList,
    this.video,
    this.timeStr,
    this.lastUpdateTime,
    this.liked = false,
    this.followed = false,
    this.relation = 'none',
  });

  // 处理time字段可能是int或String的情况
  static String? _timeFromJson(dynamic time) {
    if (time == null) return null;
    if (time is String) return time;
    if (time is int) return time.toString();
    return null;
  }

  factory NoteCardDetail.fromJson(Map<String, dynamic> json) =>
      _$NoteCardDetailFromJson(json);

  Map<String, dynamic> toJson() => _$NoteCardDetailToJson(this);
}

@JsonSerializable()
class NoteImage {
  @JsonKey(name: 'info_list')
  final List<ImageInfo> infoList;
  final int height;
  final int width;

  NoteImage({
    required this.infoList,
    required this.height,
    required this.width,
  });

  String get url => infoList.firstWhere((info) => info.imageScene == 'WB_DFT', orElse: () => infoList.first).url;

  factory NoteImage.fromJson(Map<String, dynamic> json) =>
      _$NoteImageFromJson(json);

  Map<String, dynamic> toJson() => _$NoteImageToJson(this);
}

@JsonSerializable()
class ImageInfo {
  @JsonKey(name: 'image_scene')
  final String imageScene;
  final String url;

  ImageInfo({required this.imageScene, required this.url});

  factory ImageInfo.fromJson(Map<String, dynamic> json) =>
      _$ImageInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ImageInfoToJson(this);
}

@JsonSerializable()
class InteractInfo {
  @JsonKey(name: 'liked_count', fromJson: _countFromJson)
  final String likedCount;
  @JsonKey(name: 'collected_count', fromJson: _countFromJson)
  final String collectedCount;
  @JsonKey(name: 'comment_count', fromJson: _countFromJson)
  final String commentCount;
  @JsonKey(name: 'share_count', fromJson: _countFromJson)
  final String shareCount;
  @JsonKey(defaultValue: false)
  final bool liked;
  @JsonKey(name: 'time')
  final int? time;

  InteractInfo({
    required this.likedCount,
    required this.collectedCount,
    required this.commentCount,
    required this.shareCount,
    this.liked = false,
    this.time,
  });

  // 处理计数字段可能是int或String的情况
  static String _countFromJson(dynamic count) {
    if (count == null) return '0';
    if (count is String) return count;
    if (count is int) return count.toString();
    return '0';
  }

  factory InteractInfo.fromJson(Map<String, dynamic> json) =>
      _$InteractInfoFromJson(json);

  Map<String, dynamic> toJson() => _$InteractInfoToJson(this);
}

@JsonSerializable()
class User {
  @JsonKey(name: 'user_id')
  final String userId;
  final String nickname;
  final String avatar;

  User({required this.userId, required this.nickname, required this.avatar});

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);
}

@JsonSerializable()
class Tag {
  final String type;
  final String id;
  final String name;

  Tag({required this.type, required this.id, required this.name});

  factory Tag.fromJson(Map<String, dynamic> json) => _$TagFromJson(json);

  Map<String, dynamic> toJson() => _$TagToJson(this);
}

@JsonSerializable()
class Video {
  final Media media;

  Video({required this.media});

  factory Video.fromJson(Map<String, dynamic> json) => _$VideoFromJson(json);

  Map<String, dynamic> toJson() => _$VideoToJson(this);
}

@JsonSerializable()
class Media {
  final VideoStream stream;

  Media({required this.stream});

  factory Media.fromJson(Map<String, dynamic> json) => _$MediaFromJson(json);

  Map<String, dynamic> toJson() => _$MediaToJson(this);
}

@JsonSerializable()
class VideoStream {
  final List<StreamData> h264;

  VideoStream({required this.h264});

  String? get url => h264.isNotEmpty ? h264.first.masterUrl : null;

  factory VideoStream.fromJson(Map<String, dynamic> json) =>
      _$VideoStreamFromJson(json);

  Map<String, dynamic> toJson() => _$VideoStreamToJson(this);
}

@JsonSerializable()
class StreamData {
  @JsonKey(name: 'master_url')
  final String masterUrl;

  StreamData({required this.masterUrl});

  factory StreamData.fromJson(Map<String, dynamic> json) =>
      _$StreamDataFromJson(json);

  Map<String, dynamic> toJson() => _$StreamDataToJson(this);
}

/// 小红书评论响应模型
class XhsCommentResponse {
  final bool success;
  final String msg;
  final XhsCommentData? data;
  final int code;

  XhsCommentResponse({
    required this.success,
    required this.msg,
    this.data,
    required this.code,
  });

  factory XhsCommentResponse.fromJson(Map<String, dynamic> json) {
    return XhsCommentResponse(
      success: json['success'] ?? false,
      msg: json['msg'] ?? '',
      data: json['data'] != null ? XhsCommentData.fromJson(json['data']) : null,
      code: json['code'] ?? -1,
    );
  }
}

/// 小红书评论数据模型
class XhsCommentData {
  final String cursor;
  final bool hasMore;
  final int time;
  final String xsecToken;
  final String userId;
  final List<XhsComment> comments;

  XhsCommentData({
    required this.cursor,
    required this.hasMore,
    required this.time,
    required this.xsecToken,
    required this.userId,
    required this.comments,
  });

  factory XhsCommentData.fromJson(Map<String, dynamic> json) {
    List<XhsComment> commentsList = [];
    if (json['comments'] != null) {
      commentsList = List<XhsComment>.from(
        (json['comments'] as List).map((x) => XhsComment.fromJson(x)),
      );
    }

    return XhsCommentData(
      cursor: json['cursor'] ?? '',
      hasMore: json['has_more'] ?? false,
      time: json['time'] ?? 0,
      xsecToken: json['xsec_token'] ?? '',
      userId: json['user_id'] ?? '',
      comments: commentsList,
    );
  }
}

/// 小红书评论模型
class XhsComment {
  final String id;
  final String content;
  final List<dynamic> atUsers;
  final int createTime;
  final bool subCommentHasMore;
  final int status;
  final bool liked;
  final List<String> showTags;
  final List<XhsSubComment> subComments;
  final String noteId;
  final String ipLocation;
  final String likeCount;
  final XhsCommentUserInfo userInfo;
  final String subCommentCount;
  final String subCommentCursor;

  XhsComment({
    required this.id,
    required this.content,
    required this.atUsers,
    required this.createTime,
    required this.subCommentHasMore,
    required this.status,
    required this.liked,
    required this.showTags,
    required this.subComments,
    required this.noteId,
    required this.ipLocation,
    required this.likeCount,
    required this.userInfo,
    required this.subCommentCount,
    required this.subCommentCursor,
  });

  factory XhsComment.fromJson(Map<String, dynamic> json) {
    List<XhsSubComment> subCommentsList = [];
    if (json['sub_comments'] != null) {
      subCommentsList = List<XhsSubComment>.from(
        (json['sub_comments'] as List).map((x) => XhsSubComment.fromJson(x)),
      );
    }

    List<String> tags = [];
    if (json['show_tags'] != null) {
      tags = List<String>.from(json['show_tags']);
    }

    return XhsComment(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      atUsers: json['at_users'] ?? [],
      createTime: json['create_time'] ?? 0,
      subCommentHasMore: json['sub_comment_has_more'] ?? false,
      status: json['status'] ?? 0,
      liked: json['liked'] ?? false,
      showTags: tags,
      subComments: subCommentsList,
      noteId: json['note_id'] ?? '',
      ipLocation: json['ip_location'] ?? '',
      likeCount: json['like_count'] ?? '0',
      userInfo: XhsCommentUserInfo.fromJson(json['user_info'] ?? {}),
      subCommentCount: json['sub_comment_count'] ?? '0',
      subCommentCursor: json['sub_comment_cursor'] ?? '',
    );
  }
}

/// 小红书子评论模型
class XhsSubComment {
  final String id;
  final String content;
  final bool liked;
  final String likeCount;
  final int createTime;
  final String ipLocation;
  final String noteId;
  final int status;
  final List<dynamic> atUsers;
  final XhsCommentUserInfo userInfo;
  final List<String> showTags;
  final XhsTargetComment? targetComment;

  XhsSubComment({
    required this.id,
    required this.content,
    required this.liked,
    required this.likeCount,
    required this.createTime,
    required this.ipLocation,
    required this.noteId,
    required this.status,
    required this.atUsers,
    required this.userInfo,
    required this.showTags,
    this.targetComment,
  });

  factory XhsSubComment.fromJson(Map<String, dynamic> json) {
    List<String> tags = [];
    if (json['show_tags'] != null) {
      tags = List<String>.from(json['show_tags']);
    }

    return XhsSubComment(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      liked: json['liked'] ?? false,
      likeCount: json['like_count'] ?? '0',
      createTime: json['create_time'] ?? 0,
      ipLocation: json['ip_location'] ?? '',
      noteId: json['note_id'] ?? '',
      status: json['status'] ?? 0,
      atUsers: json['at_users'] ?? [],
      userInfo: XhsCommentUserInfo.fromJson(json['user_info'] ?? {}),
      showTags: tags,
      targetComment: json['target_comment'] != null
          ? XhsTargetComment.fromJson(json['target_comment'])
          : null,
    );
  }
}

/// 评论目标评论模型
class XhsTargetComment {
  final String id;
  final XhsCommentUserInfo userInfo;

  XhsTargetComment({
    required this.id,
    required this.userInfo,
  });

  factory XhsTargetComment.fromJson(Map<String, dynamic> json) {
    return XhsTargetComment(
      id: json['id'] ?? '',
      userInfo: XhsCommentUserInfo.fromJson(json['user_info'] ?? {}),
    );
  }
}

/// 评论用户信息模型
class XhsCommentUserInfo {
  final String xsecToken;
  final String userId;
  final String nickname;
  final String image;

  XhsCommentUserInfo({
    required this.xsecToken,
    required this.userId,
    required this.nickname,
    required this.image,
  });

  factory XhsCommentUserInfo.fromJson(Map<String, dynamic> json) {
    return XhsCommentUserInfo(
      xsecToken: json['xsec_token'] ?? '',
      userId: json['user_id'] ?? '',
      nickname: json['nickname'] ?? '',
      image: json['image'] ?? '',
    );
  }
} 