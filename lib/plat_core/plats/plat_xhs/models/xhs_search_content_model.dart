import 'package:json_annotation/json_annotation.dart';

part 'xhs_search_content_model.g.dart';

/// 小红书搜索笔记请求参数
class XhsSearchContentRequest {
  /// 搜索关键词
  final String keyword;
  
  /// 页码，从1开始
  final int page;
  
  /// 每页数量
  final int pageSize;
  
  /// 搜索ID
  final String? searchId;
  
  /// 排序方式，默认为general
  final String sort;
  
  /// 笔记类型，0表示不限
  final int noteType;
  
  /// 扩展标志
  final List<String> extFlags;
  
  /// 过滤条件
  final List<XhsSearchFilter> filters;
  
  /// 地理位置
  final String geo;
  
  /// 图片格式
  final List<String> imageFormats;

  XhsSearchContentRequest({
    required this.keyword,
    this.page = 1,
    this.pageSize = 20,
    this.searchId,
    this.sort = 'general',
    this.noteType = 0,
    this.extFlags = const [],
    this.filters = const [],
    this.geo = '',
    this.imageFormats = const ['jpg', 'webp', 'avif'],
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'keyword': keyword,
      'page': page,
      'page_size': pageSize,
      'sort': sort,
      'note_type': noteType,
      'ext_flags': extFlags,
      'geo': geo,
      'image_formats': imageFormats,
    };
    
    // 仅在有搜索ID时添加
    if (searchId != null && searchId!.isNotEmpty) {
      json['search_id'] = searchId;
    }
    
    // 仅在有过滤条件时添加
    if (filters.isNotEmpty) {
      json['filters'] = filters.map((filter) => filter.toJson()).toList();
    }
    
    return json;
  }
}

/// 小红书搜索过滤条件
class XhsSearchFilter {
  /// 过滤标签
  final List<String> tags;
  
  /// 过滤类型
  final String type;

  XhsSearchFilter({
    required this.tags,
    required this.type,
  });

  Map<String, dynamic> toJson() {
    return {
      'tags': tags,
      'type': type,
    };
  }

  factory XhsSearchFilter.fromJson(Map<String, dynamic> json) {
    return XhsSearchFilter(
      tags: List<String>.from(json['tags']),
      type: json['type'],
    );
  }
}

/// 小红书搜索笔记响应
@JsonSerializable()
class XhsSearchContentResponse {
  /// 状态码，0表示成功
  @JsonKey(name: 'code')
  final int code;
  
  /// 是否成功
  @JsonKey(name: 'success')
  final bool success;
  
  /// 消息
  @JsonKey(name: 'msg')
  final String msg;
  
  /// 数据
  @JsonKey(name: 'data')
  final XhsSearchContentData? data;

  XhsSearchContentResponse({
    required this.code,
    required this.success,
    required this.msg,
    this.data,
  });

  factory XhsSearchContentResponse.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsSearchContentResponseFromJson(json);
    } catch (e) {
      // 处理解析错误
      print('解析XhsSearchContentResponse失败: $e');
      return XhsSearchContentResponse(
        code: json['code'] as int? ?? -1,
        success: json['success'] as bool? ?? false,
        msg: json['msg'] as String? ?? '解析失败',
        data: null,
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsSearchContentResponseToJson(this);
}

/// 小红书搜索笔记数据
@JsonSerializable()
class XhsSearchContentData {
  /// 是否有更多数据
  @JsonKey(name: 'has_more')
  final bool hasMore;
  
  /// 笔记列表
  @JsonKey(name: 'items')
  final List<XhsNoteItem> items;

  XhsSearchContentData({
    required this.hasMore,
    required this.items,
  });

  factory XhsSearchContentData.fromJson(Map<String, dynamic> json) {
    try {
      // 检查items是否为null或非数组
      if (json['items'] == null) {
        return XhsSearchContentData(
          hasMore: json['has_more'] as bool? ?? false,
          items: [],
        );
      }
      
      // 如果items不是List，尝试转换
      if (json['items'] is! List) {
        print('items不是List类型: ${json['items'].runtimeType}');
        return XhsSearchContentData(
          hasMore: json['has_more'] as bool? ?? false,
          items: [],
        );
      }
      
      // 安全地处理items列表，跳过无法解析的项
      final itemsList = json['items'] as List;
      final parsedItems = <XhsNoteItem>[];
      
      for (var i = 0; i < itemsList.length; i++) {
        try {
          final item = itemsList[i];
          if (item is Map<String, dynamic>) {
            parsedItems.add(XhsNoteItem.fromJson(item));
          } else {
            print('跳过非Map类型的item: ${item.runtimeType}');
          }
        } catch (e) {
          print('解析item[$i]失败: $e');
        }
      }
      
      return XhsSearchContentData(
        hasMore: json['has_more'] as bool? ?? false,
        items: parsedItems,
      );
    } catch (e) {
      print('解析XhsSearchContentData失败: $e');
      return XhsSearchContentData(
        hasMore: json['has_more'] as bool? ?? false,
        items: [],
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsSearchContentDataToJson(this);
}

/// 小红书笔记项
@JsonSerializable()
class XhsNoteItem {
  /// 笔记ID
  @JsonKey(name: 'id')
  final String id;
  
  /// 模型类型
  @JsonKey(name: 'model_type')
  final String modelType;
  
  /// 笔记卡片信息
  @JsonKey(name: 'note_card')
  final XhsNoteCard noteCard;
  
  /// 安全令牌
  @JsonKey(name: 'xsec_token')
  final String xsecToken;

  XhsNoteItem({
    required this.id,
    required this.modelType,
    required this.noteCard,
    required this.xsecToken,
  });

  factory XhsNoteItem.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsNoteItemFromJson(json);
    } catch (e) {
      print('解析XhsNoteItem失败: $e，JSON: $json');
      
      // 尝试提取关键字段，即使部分字段缺失也能创建对象
      return XhsNoteItem(
        id: json['id'] as String? ?? '',
        modelType: json['model_type'] as String? ?? '',
        noteCard: json['note_card'] is Map<String, dynamic>
            ? XhsNoteCard.fromJson(json['note_card'] as Map<String, dynamic>)
            : XhsNoteCard.createEmpty(),
        xsecToken: json['xsec_token'] as String? ?? '',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsNoteItemToJson(this);
}

/// 小红书笔记卡片
@JsonSerializable()
class XhsNoteCard {
  /// 类型
  @JsonKey(name: 'type')
  final String type;
  
  /// 显示标题
  @JsonKey(name: 'display_title', defaultValue: '')
  final String displayTitle;
  
  /// 用户信息
  @JsonKey(name: 'user')
  final XhsUser user;
  
  /// 互动信息
  @JsonKey(name: 'interact_info')
  final XhsInteractInfo interactInfo;
  
  /// 封面
  @JsonKey(name: 'cover')
  final XhsCover cover;
  
  /// 图片列表
  @JsonKey(name: 'image_list')
  final List<XhsImage> imageList;
  
  /// 角标信息
  @JsonKey(name: 'corner_tag_info')
  final List<XhsCornerTag> cornerTagInfo;

  XhsNoteCard({
    required this.type,
    required this.displayTitle,
    required this.user,
    required this.interactInfo,
    required this.cover,
    required this.imageList,
    required this.cornerTagInfo,
  });

  factory XhsNoteCard.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsNoteCardFromJson(json);
    } catch (e) {
      print('解析XhsNoteCard失败: $e');
      return XhsNoteCard.createEmpty();
    }
  }
  
  /// 创建空的笔记卡片对象
  static XhsNoteCard createEmpty() {
    return XhsNoteCard(
      type: '',
      displayTitle: '',
      user: XhsUser(
        nickName: '',
        avatar: '',
        userId: '',
        nickname: '',
        xsecToken: '',
      ),
      interactInfo: XhsInteractInfo(
        liked: false,
        likedCount: '0',
        collected: false,
        collectedCount: '0',
        commentCount: '0',
        sharedCount: '0',
      ),
      cover: XhsCover(
        height: 0,
        width: 0,
        urlDefault: '',
        urlPre: '',
      ),
      imageList: [],
      cornerTagInfo: [],
    );
  }
  
  Map<String, dynamic> toJson() => _$XhsNoteCardToJson(this);
}

/// 小红书用户
@JsonSerializable()
class XhsUser {
  /// 昵称
  @JsonKey(name: 'nick_name')
  final String nickName;
  
  /// 头像
  @JsonKey(name: 'avatar')
  final String avatar;
  
  /// 用户ID
  @JsonKey(name: 'user_id')
  final String userId;
  
  /// 昵称（别名）
  @JsonKey(name: 'nickname')
  final String nickname;
  
  /// 安全令牌
  @JsonKey(name: 'xsec_token')
  final String xsecToken;

  XhsUser({
    required this.nickName,
    required this.avatar,
    required this.userId,
    required this.nickname,
    required this.xsecToken,
  });

  factory XhsUser.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsUserFromJson(json);
    } catch (e) {
      print('解析XhsUser失败: $e');
      return XhsUser(
        nickName: json['nick_name'] as String? ?? '',
        avatar: json['avatar'] as String? ?? '',
        userId: json['user_id'] as String? ?? '',
        nickname: json['nickname'] as String? ?? '',
        xsecToken: json['xsec_token'] as String? ?? '',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsUserToJson(this);
}

/// 小红书互动信息
@JsonSerializable()
class XhsInteractInfo {
  /// 是否点赞
  @JsonKey(name: 'liked')
  final bool liked;
  
  /// 点赞数
  @JsonKey(name: 'liked_count')
  final String likedCount;
  
  /// 是否收藏
  @JsonKey(name: 'collected')
  final bool collected;
  
  /// 收藏数
  @JsonKey(name: 'collected_count')
  final String collectedCount;
  
  /// 评论数
  @JsonKey(name: 'comment_count')
  final String commentCount;
  
  /// 分享数
  @JsonKey(name: 'shared_count')
  final String sharedCount;

  XhsInteractInfo({
    required this.liked,
    required this.likedCount,
    required this.collected,
    required this.collectedCount,
    required this.commentCount,
    required this.sharedCount,
  });

  factory XhsInteractInfo.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsInteractInfoFromJson(json);
    } catch (e) {
      print('解析XhsInteractInfo失败: $e');
      return XhsInteractInfo(
        liked: json['liked'] as bool? ?? false,
        likedCount: json['liked_count']?.toString() ?? '0',
        collected: json['collected'] as bool? ?? false,
        collectedCount: json['collected_count']?.toString() ?? '0',
        commentCount: json['comment_count']?.toString() ?? '0',
        sharedCount: json['shared_count']?.toString() ?? '0',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsInteractInfoToJson(this);
}

/// 小红书封面
@JsonSerializable()
class XhsCover {
  /// 高度
  @JsonKey(name: 'height')
  final int height;
  
  /// 宽度
  @JsonKey(name: 'width')
  final int width;
  
  /// 默认URL
  @JsonKey(name: 'url_default')
  final String urlDefault;
  
  /// 预览URL
  @JsonKey(name: 'url_pre')
  final String urlPre;

  XhsCover({
    required this.height,
    required this.width,
    required this.urlDefault,
    required this.urlPre,
  });

  factory XhsCover.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsCoverFromJson(json);
    } catch (e) {
      print('解析XhsCover失败: $e');
      return XhsCover(
        height: json['height'] as int? ?? 0,
        width: json['width'] as int? ?? 0,
        urlDefault: json['url_default'] as String? ?? '',
        urlPre: json['url_pre'] as String? ?? '',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsCoverToJson(this);
}

/// 小红书图片
@JsonSerializable()
class XhsImage {
  /// 高度
  @JsonKey(name: 'height')
  final int height;
  
  /// 宽度
  @JsonKey(name: 'width')
  final int width;
  
  /// 信息列表
  @JsonKey(name: 'info_list')
  final List<XhsImageInfo> infoList;

  XhsImage({
    required this.height,
    required this.width,
    required this.infoList,
  });

  factory XhsImage.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsImageFromJson(json);
    } catch (e) {
      print('解析XhsImage失败: $e');
      return XhsImage(
        height: json['height'] as int? ?? 0,
        width: json['width'] as int? ?? 0,
        infoList: [],
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsImageToJson(this);
}

/// 小红书图片信息
@JsonSerializable()
class XhsImageInfo {
  /// 图片场景
  @JsonKey(name: 'image_scene')
  final String imageScene;
  
  /// URL
  @JsonKey(name: 'url')
  final String url;

  XhsImageInfo({
    required this.imageScene,
    required this.url,
  });

  factory XhsImageInfo.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsImageInfoFromJson(json);
    } catch (e) {
      print('解析XhsImageInfo失败: $e');
      return XhsImageInfo(
        imageScene: json['image_scene'] as String? ?? '',
        url: json['url'] as String? ?? '',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsImageInfoToJson(this);
}

/// 小红书角标标签
@JsonSerializable()
class XhsCornerTag {
  /// 类型
  @JsonKey(name: 'type')
  final String type;
  
  /// 文本
  @JsonKey(name: 'text')
  final String text;

  XhsCornerTag({
    required this.type,
    required this.text,
  });

  factory XhsCornerTag.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsCornerTagFromJson(json);
    } catch (e) {
      print('解析XhsCornerTag失败: $e');
      return XhsCornerTag(
        type: json['type'] as String? ?? '',
        text: json['text'] as String? ?? '',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsCornerTagToJson(this);
} 