import 'package:aitoearn_app/config/logger.dart';

/// 小红书POI搜索响应模型
class XhsPoiSearchResponse {
  final int statusCode;
  final String statusMsg;
  final List<XhsPoiItem> poiList;
  final bool hasMore;
  final int cursor;
  
  XhsPoiSearchResponse({
    required this.statusCode,
    required this.statusMsg,
    required this.poiList,
    required this.hasMore,
    required this.cursor,
  });
  
  /// 从JSON创建响应
  factory XhsPoiSearchResponse.fromJson(Map<String, dynamic> json) {
    final poiList = <XhsPoiItem>[];
    
    try {
      // 检查返回码和成功状态
      final int code = json['code'] ?? -1;
      final bool success = json['success'] ?? false;
      final String msg = json['msg'] ?? '';
      
      // 如果请求成功并且有数据
      if (success && code == 0 && json['data'] != null) {
        // 处理poi_list字段
        if (json['data']['poi_list'] != null && json['data']['poi_list'] is List) {
          final List<dynamic> poiItems = json['data']['poi_list'];
          for (var item in poiItems) {
            if (item is Map<String, dynamic>) {
              poiList.add(XhsPoiItem.fromJson(item));
            }
          }
        }
      }
      
      return XhsPoiSearchResponse(
        statusCode: code,
        statusMsg: msg,
        poiList: poiList,
        hasMore: false, // 该接口没有分页信息，设为默认值
        cursor: 0, // 该接口没有分页信息，设为默认值
      );
    } catch (e) {
      LoggerUtil.i('处理小红书POI数据出错: $e');
      return XhsPoiSearchResponse(
        statusCode: -1,
        statusMsg: '解析数据出错: $e',
        poiList: [],
        hasMore: false,
        cursor: 0,
      );
    }
  }
  
  /// 从原始API响应创建POI响应对象
  factory XhsPoiSearchResponse.fromRawApiResponse(Map<String, dynamic> json) {
    final poiList = <XhsPoiItem>[];
    
    try {
      // 首先尝试处理标准的接口返回格式
      if (json['data'] != null && json['data']['poi_list'] != null) {
        final List<dynamic> poiItems = json['data']['poi_list'];
        for (var item in poiItems) {
          if (item is Map<String, dynamic>) {
            poiList.add(XhsPoiItem.fromJson(item));
          }
        }
      } 
      // 直接处理poi字段(可能在某些响应中)
      else if (json['data'] != null && json['data']['poi'] != null) {
        final List<dynamic> poiItems = json['data']['poi'];
        for (var item in poiItems) {
          if (item is Map<String, dynamic>) {
            poiList.add(XhsPoiItem.fromJson(item));
          }
        }
      }
      // 处理直接提供的单个POI项
      else if (json['poi_id'] != null || json['name'] != null) {
        poiList.add(XhsPoiItem.fromJson(json));
      }
      // 处理缓存的POI对象
      else if (json['poi_object'] != null) {
        poiList.add(XhsPoiItem.fromJson(json['poi_object']));
      }
    } catch (e) {
      LoggerUtil.i('解析小红书POI数据失败: $e');
    }
    
    // 获取状态码和消息，如果有的话
    final int code = json['code'] ?? 0;
    final String msg = json['msg'] ?? '';
    
    return XhsPoiSearchResponse(
      statusCode: code,
      statusMsg: msg,
      poiList: poiList,
      hasMore: false,
      cursor: 0,
    );
  }
}

/// 小红书POI位置项模型
class XhsPoiItem {
  final String poiId;
  final String poiName;
  final String address;
  final double latitude;
  final double longitude;
  final String cityName;
  final String districtName;
  final int distance; // 单位：米
  
  XhsPoiItem({
    required this.poiId,
    required this.poiName,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.cityName,
    this.districtName = '',
    required this.distance,
  });
  
  /// 创建一个新的XhsPoiItem实例，同时更新部分字段
  XhsPoiItem copyWith({
    String? poiId,
    String? poiName,
    String? address,
    double? latitude,
    double? longitude,
    String? cityName,
    String? districtName,
    int? distance,
  }) {
    return XhsPoiItem(
      poiId: poiId ?? this.poiId,
      poiName: poiName ?? this.poiName,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      cityName: cityName ?? this.cityName,
      districtName: districtName ?? this.districtName,
      distance: distance ?? this.distance,
    );
  }
  
  /// 从JSON创建POI项
  factory XhsPoiItem.fromJson(Map<String, dynamic> json) {
    // 处理位置ID
    String poiId = '';
    if (json['poi_id'] != null) {
      poiId = json['poi_id'].toString();
    } else if (json['id'] != null) {
      poiId = json['id'].toString();
    }
    
    // 处理位置名称
    String poiName = json['name'] ?? '';
    
    // 处理地址
    String address = json['address'] ?? '';
    if (address.isEmpty && json['full_address'] != null) {
      address = json['full_address'];
    }
    
    // 处理城市和区域
    String cityName = json['city_name'] ?? '';
    String districtName = '';
    
    // 处理经纬度
    double latitude = 0.0;
    double longitude = 0.0;
    
    if (json['latitude'] != null && json['longitude'] != null) {
      latitude = _parseDoubleValue(json['latitude']);
      longitude = _parseDoubleValue(json['longitude']);
    }
    
    // 处理距离（小红书API不返回距离信息，默认为0）
    int distance = 0;
    
    return XhsPoiItem(
      poiId: poiId,
      poiName: poiName,
      address: address,
      latitude: latitude,
      longitude: longitude,
      cityName: cityName,
      districtName: districtName,
      distance: distance,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'poi_id': poiId,
      'name': poiName,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'city_name': cityName,
      'full_address': address,
      'type': 'location',
    };
  }
  
  /// 辅助方法：解析double值
  static double _parseDoubleValue(dynamic value) {
    if (value == null) return 0.0;
    
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    
    return 0.0;
  }
} 