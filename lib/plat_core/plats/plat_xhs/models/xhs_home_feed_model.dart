import 'package:json_annotation/json_annotation.dart';

part 'xhs_home_feed_model.g.dart';

/// 小红书首页Feed请求参数
class XhsHomeFeedRequest {
  /// 光标分数，用于分页
  final String cursorScore;
  
  /// 每页数量
  final int num;
  
  /// 刷新类型，1为下拉刷新
  final int refreshType;
  
  /// 笔记索引
  final int noteIndex;
  
  /// 未读开始笔记ID
  final String unreadBeginNoteId;
  
  /// 未读结束笔记ID
  final String unreadEndNoteId;
  
  /// 未读笔记数量
  final int unreadNoteCount;
  
  /// 分类，默认为推荐
  final String category;
  
  /// 搜索关键词
  final String searchKey;
  
  /// 需要的数量
  final int needNum;
  
  /// 图片格式
  final List<String> imageFormats;
  
  /// 是否需要过滤图片
  final bool needFilterImage;

  XhsHomeFeedRequest({
    this.cursorScore = '',
    this.num = 18,
    this.refreshType = 1,
    this.noteIndex = 0,
    this.unreadBeginNoteId = '',
    this.unreadEndNoteId = '',
    this.unreadNoteCount = 0,
    this.category = 'homefeed_recommend',
    this.searchKey = '',
    this.needNum = 8,
    this.imageFormats = const ['jpg', 'webp', 'avif'],
    this.needFilterImage = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'cursor_score': cursorScore,
      'num': num,
      'refresh_type': refreshType,
      'note_index': noteIndex,
      'unread_begin_note_id': unreadBeginNoteId,
      'unread_end_note_id': unreadEndNoteId,
      'unread_note_count': unreadNoteCount,
      'category': category,
      'search_key': searchKey,
      'need_num': needNum,
      'image_formats': imageFormats,
      'need_filter_image': needFilterImage,
    };
  }
}

/// 小红书首页Feed响应
@JsonSerializable()
class XhsHomeFeedResponse {
  /// 状态码，0表示成功
  @JsonKey(name: 'code')
  final int code;
  
  /// 是否成功
  @JsonKey(name: 'success')
  final bool success;
  
  /// 消息
  @JsonKey(name: 'msg')
  final String msg;
  
  /// 数据
  @JsonKey(name: 'data')
  final XhsHomeFeedData? data;

  XhsHomeFeedResponse({
    required this.code,
    required this.success,
    required this.msg,
    this.data,
  });

  factory XhsHomeFeedResponse.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedResponseFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedResponse失败: $e');
      return XhsHomeFeedResponse(
        code: json['code'] as int? ?? -1,
        success: json['success'] as bool? ?? false,
        msg: json['msg'] as String? ?? '解析失败',
        data: null,
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedResponseToJson(this);
}

/// 小红书首页Feed数据
@JsonSerializable()
class XhsHomeFeedData {
  /// 光标分数，用于分页
  @JsonKey(name: 'cursor_score')
  final String cursorScore;
  
  /// 笔记列表
  @JsonKey(name: 'items')
  final List<XhsHomeFeedItem> items;

  XhsHomeFeedData({
    required this.cursorScore,
    required this.items,
  });

  factory XhsHomeFeedData.fromJson(Map<String, dynamic> json) {
    try {
      if (json['items'] == null) {
        return XhsHomeFeedData(
          cursorScore: json['cursor_score'] as String? ?? '',
          items: [],
        );
      }
      
      if (json['items'] is! List) {
        print('items不是List类型: ${json['items'].runtimeType}');
        return XhsHomeFeedData(
          cursorScore: json['cursor_score'] as String? ?? '',
          items: [],
        );
      }
      
      final itemsList = json['items'] as List;
      final parsedItems = <XhsHomeFeedItem>[];
      
      for (var i = 0; i < itemsList.length; i++) {
        try {
          final item = itemsList[i];
          if (item is Map<String, dynamic>) {
            parsedItems.add(XhsHomeFeedItem.fromJson(item));
          } else {
            print('跳过非Map类型的item: ${item.runtimeType}');
          }
        } catch (e) {
          print('解析item[$i]失败: $e');
        }
      }
      
      return XhsHomeFeedData(
        cursorScore: json['cursor_score'] as String? ?? '',
        items: parsedItems,
      );
    } catch (e) {
      print('解析XhsHomeFeedData失败: $e');
      return XhsHomeFeedData(
        cursorScore: json['cursor_score'] as String? ?? '',
        items: [],
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedDataToJson(this);
}

/// 小红书首页Feed项
@JsonSerializable()
class XhsHomeFeedItem {
  /// 笔记ID
  @JsonKey(name: 'id')
  final String id;
  
  /// 模型类型，通常是"note"
  @JsonKey(name: 'model_type')
  final String modelType;
  
  /// 轨迹ID
  @JsonKey(name: 'track_id')
  final String trackId;
  
  /// 是否忽略
  @JsonKey(name: 'ignore')
  final bool ignore;
  
  /// 安全令牌
  @JsonKey(name: 'xsec_token')
  final String xsecToken;
  
  /// 笔记卡片信息
  @JsonKey(name: 'note_card')
  final XhsHomeFeedNoteCard noteCard;

  XhsHomeFeedItem({
    required this.id,
    required this.modelType,
    required this.trackId,
    required this.ignore,
    required this.xsecToken,
    required this.noteCard,
  });

  factory XhsHomeFeedItem.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedItemFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedItem失败: $e，JSON: $json');
      
      return XhsHomeFeedItem(
        id: json['id'] as String? ?? '',
        modelType: json['model_type'] as String? ?? 'note',
        trackId: json['track_id'] as String? ?? '',
        ignore: json['ignore'] as bool? ?? false,
        xsecToken: json['xsec_token'] as String? ?? '',
        noteCard: json['note_card'] != null
            ? XhsHomeFeedNoteCard.fromJson(json['note_card'] as Map<String, dynamic>)
            : XhsHomeFeedNoteCard(
                type: 'normal',
                displayTitle: '',
                user: XhsHomeFeedUser(
                  nickname: '',
                  nickName: '',
                  avatar: '',
                  userId: '',
                  xsecToken: '',
                ),
                interactInfo: XhsHomeFeedInteractInfo(
                  liked: false,
                  likedCount: '0',
                ),
                cover: XhsHomeFeedCover(
                  urlDefault: '',
                  fileId: '',
                  height: 0,
                  width: 0,
                  url: '',
                  infoList: [],
                  urlPre: '',
                ),
                video: null,
              ),
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedItemToJson(this);
}

/// 小红书首页Feed笔记卡片
@JsonSerializable()
class XhsHomeFeedNoteCard {
  /// 类型，可能是"video"或"normal"
  @JsonKey(name: 'type')
  final String type;
  
  /// 显示标题
  @JsonKey(name: 'display_title')
  final String displayTitle;
  
  /// 用户信息
  @JsonKey(name: 'user')
  final XhsHomeFeedUser user;
  
  /// 互动信息
  @JsonKey(name: 'interact_info')
  final XhsHomeFeedInteractInfo interactInfo;
  
  /// 封面图片
  @JsonKey(name: 'cover')
  final XhsHomeFeedCover cover;
  
  /// 视频信息，只有视频类型的笔记才有
  @JsonKey(name: 'video')
  final XhsHomeFeedVideo? video;

  XhsHomeFeedNoteCard({
    required this.type,
    required this.displayTitle,
    required this.user,
    required this.interactInfo,
    required this.cover,
    this.video,
  });

  factory XhsHomeFeedNoteCard.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedNoteCardFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedNoteCard失败: $e, JSON: $json');
      
      return XhsHomeFeedNoteCard(
        type: json['type'] as String? ?? 'normal',
        displayTitle: json['display_title'] as String? ?? '',
        user: json['user'] != null
            ? XhsHomeFeedUser.fromJson(json['user'] as Map<String, dynamic>)
            : XhsHomeFeedUser(
                nickname: '',
                nickName: '',
                avatar: '',
                userId: '',
                xsecToken: '',
              ),
        interactInfo: json['interact_info'] != null
            ? XhsHomeFeedInteractInfo.fromJson(json['interact_info'] as Map<String, dynamic>)
            : XhsHomeFeedInteractInfo(
                liked: false,
                likedCount: '0',
              ),
        cover: json['cover'] != null
            ? XhsHomeFeedCover.fromJson(json['cover'] as Map<String, dynamic>)
            : XhsHomeFeedCover(
                urlDefault: '',
                fileId: '',
                height: 0,
                width: 0,
                url: '',
                infoList: [],
                urlPre: '',
              ),
        video: json['video'] != null
            ? XhsHomeFeedVideo.fromJson(json['video'] as Map<String, dynamic>)
            : null,
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedNoteCardToJson(this);
}

/// 小红书首页Feed用户信息
@JsonSerializable()
class XhsHomeFeedUser {
  /// 昵称
  @JsonKey(name: 'nickname')
  final String nickname;
  
  /// 昵称（另一个字段）
  @JsonKey(name: 'nick_name')
  final String nickName;
  
  /// 头像URL
  @JsonKey(name: 'avatar')
  final String avatar;
  
  /// 用户ID
  @JsonKey(name: 'user_id')
  final String userId;
  
  /// 安全令牌
  @JsonKey(name: 'xsec_token')
  final String xsecToken;

  XhsHomeFeedUser({
    required this.nickname,
    required this.nickName,
    required this.avatar,
    required this.userId,
    required this.xsecToken,
  });

  factory XhsHomeFeedUser.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedUserFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedUser失败: $e');
      
      return XhsHomeFeedUser(
        nickname: json['nickname'] as String? ?? json['nick_name'] as String? ?? '',
        nickName: json['nick_name'] as String? ?? json['nickname'] as String? ?? '',
        avatar: json['avatar'] as String? ?? '',
        userId: json['user_id'] as String? ?? '',
        xsecToken: json['xsec_token'] as String? ?? '',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedUserToJson(this);
}

/// 小红书首页Feed互动信息
@JsonSerializable()
class XhsHomeFeedInteractInfo {
  /// 是否已点赞
  @JsonKey(name: 'liked')
  final bool liked;
  
  /// 点赞数量
  @JsonKey(name: 'liked_count')
  final String likedCount;

  XhsHomeFeedInteractInfo({
    required this.liked,
    required this.likedCount,
  });

  factory XhsHomeFeedInteractInfo.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedInteractInfoFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedInteractInfo失败: $e');
      
      return XhsHomeFeedInteractInfo(
        liked: json['liked'] as bool? ?? false,
        likedCount: json['liked_count'] as String? ?? '0',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedInteractInfoToJson(this);
}

/// 小红书首页Feed封面图片
@JsonSerializable()
class XhsHomeFeedCover {
  /// 默认URL
  @JsonKey(name: 'url_default')
  final String urlDefault;
  
  /// 文件ID
  @JsonKey(name: 'file_id')
  final String fileId;
  
  /// 高度
  @JsonKey(name: 'height')
  final int height;
  
  /// 宽度
  @JsonKey(name: 'width')
  final int width;
  
  /// URL
  @JsonKey(name: 'url')
  final String url;
  
  /// 图片信息列表
  @JsonKey(name: 'info_list')
  final List<XhsHomeFeedImageInfo> infoList;
  
  /// 预览URL
  @JsonKey(name: 'url_pre')
  final String urlPre;

  XhsHomeFeedCover({
    required this.urlDefault,
    required this.fileId,
    required this.height,
    required this.width,
    required this.url,
    required this.infoList,
    required this.urlPre,
  });

  factory XhsHomeFeedCover.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedCoverFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedCover失败: $e');
      
      List<XhsHomeFeedImageInfo> infoList = [];
      if (json['info_list'] != null && json['info_list'] is List) {
        for (var info in json['info_list']) {
          if (info is Map<String, dynamic>) {
            try {
              infoList.add(XhsHomeFeedImageInfo.fromJson(info));
            } catch (e) {
              print('解析图片信息失败: $e');
            }
          }
        }
      }
      
      return XhsHomeFeedCover(
        urlDefault: json['url_default'] as String? ?? '',
        fileId: json['file_id'] as String? ?? '',
        height: json['height'] as int? ?? 0,
        width: json['width'] as int? ?? 0,
        url: json['url'] as String? ?? '',
        infoList: infoList,
        urlPre: json['url_pre'] as String? ?? '',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedCoverToJson(this);
}

/// 小红书首页Feed图片信息
@JsonSerializable()
class XhsHomeFeedImageInfo {
  /// 图片场景
  @JsonKey(name: 'image_scene')
  final String imageScene;
  
  /// URL
  @JsonKey(name: 'url')
  final String url;

  XhsHomeFeedImageInfo({
    required this.imageScene,
    required this.url,
  });

  factory XhsHomeFeedImageInfo.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedImageInfoFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedImageInfo失败: $e');
      
      return XhsHomeFeedImageInfo(
        imageScene: json['image_scene'] as String? ?? '',
        url: json['url'] as String? ?? '',
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedImageInfoToJson(this);
}

/// 小红书首页Feed视频信息
@JsonSerializable()
class XhsHomeFeedVideo {
  /// 能力
  @JsonKey(name: 'capa')
  final XhsHomeFeedVideoCapa capa;

  XhsHomeFeedVideo({
    required this.capa,
  });

  factory XhsHomeFeedVideo.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedVideoFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedVideo失败: $e');
      
      return XhsHomeFeedVideo(
        capa: json['capa'] != null
            ? XhsHomeFeedVideoCapa.fromJson(json['capa'] as Map<String, dynamic>)
            : XhsHomeFeedVideoCapa(duration: 0),
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedVideoToJson(this);
}

/// 小红书首页Feed视频能力
@JsonSerializable()
class XhsHomeFeedVideoCapa {
  /// 时长（秒）
  @JsonKey(name: 'duration')
  final int duration;

  XhsHomeFeedVideoCapa({
    required this.duration,
  });

  factory XhsHomeFeedVideoCapa.fromJson(Map<String, dynamic> json) {
    try {
      return _$XhsHomeFeedVideoCapaFromJson(json);
    } catch (e) {
      print('解析XhsHomeFeedVideoCapa失败: $e');
      
      return XhsHomeFeedVideoCapa(
        duration: json['duration'] as int? ?? 0,
      );
    }
  }
  
  Map<String, dynamic> toJson() => _$XhsHomeFeedVideoCapaToJson(this);
} 