// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'xhs_home_feed_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

XhsHomeFeedResponse _$XhsHomeFeedResponseFromJson(Map<String, dynamic> json) =>
    XhsHomeFeedResponse(
      code: (json['code'] as num).toInt(),
      success: json['success'] as bool,
      msg: json['msg'] as String,
      data:
          json['data'] == null
              ? null
              : XhsHomeFeedData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$XhsHomeFeedResponseToJson(
  XhsHomeFeedResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'success': instance.success,
  'msg': instance.msg,
  'data': instance.data,
};

XhsHomeFeedData _$XhsHomeFeedDataFromJson(Map<String, dynamic> json) =>
    XhsHomeFeedData(
      cursorScore: json['cursor_score'] as String,
      items:
          (json['items'] as List<dynamic>)
              .map((e) => XhsHomeFeedItem.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$XhsHomeFeedDataToJson(XhsHomeFeedData instance) =>
    <String, dynamic>{
      'cursor_score': instance.cursorScore,
      'items': instance.items,
    };

XhsHomeFeedItem _$XhsHomeFeedItemFromJson(Map<String, dynamic> json) =>
    XhsHomeFeedItem(
      id: json['id'] as String,
      modelType: json['model_type'] as String,
      trackId: json['track_id'] as String,
      ignore: json['ignore'] as bool,
      xsecToken: json['xsec_token'] as String,
      noteCard: XhsHomeFeedNoteCard.fromJson(
        json['note_card'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$XhsHomeFeedItemToJson(XhsHomeFeedItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'model_type': instance.modelType,
      'track_id': instance.trackId,
      'ignore': instance.ignore,
      'xsec_token': instance.xsecToken,
      'note_card': instance.noteCard,
    };

XhsHomeFeedNoteCard _$XhsHomeFeedNoteCardFromJson(Map<String, dynamic> json) =>
    XhsHomeFeedNoteCard(
      type: json['type'] as String,
      displayTitle: json['display_title'] as String,
      user: XhsHomeFeedUser.fromJson(json['user'] as Map<String, dynamic>),
      interactInfo: XhsHomeFeedInteractInfo.fromJson(
        json['interact_info'] as Map<String, dynamic>,
      ),
      cover: XhsHomeFeedCover.fromJson(json['cover'] as Map<String, dynamic>),
      video:
          json['video'] == null
              ? null
              : XhsHomeFeedVideo.fromJson(
                json['video'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$XhsHomeFeedNoteCardToJson(
  XhsHomeFeedNoteCard instance,
) => <String, dynamic>{
  'type': instance.type,
  'display_title': instance.displayTitle,
  'user': instance.user,
  'interact_info': instance.interactInfo,
  'cover': instance.cover,
  'video': instance.video,
};

XhsHomeFeedUser _$XhsHomeFeedUserFromJson(Map<String, dynamic> json) =>
    XhsHomeFeedUser(
      nickname: json['nickname'] as String,
      nickName: json['nick_name'] as String,
      avatar: json['avatar'] as String,
      userId: json['user_id'] as String,
      xsecToken: json['xsec_token'] as String,
    );

Map<String, dynamic> _$XhsHomeFeedUserToJson(XhsHomeFeedUser instance) =>
    <String, dynamic>{
      'nickname': instance.nickname,
      'nick_name': instance.nickName,
      'avatar': instance.avatar,
      'user_id': instance.userId,
      'xsec_token': instance.xsecToken,
    };

XhsHomeFeedInteractInfo _$XhsHomeFeedInteractInfoFromJson(
  Map<String, dynamic> json,
) => XhsHomeFeedInteractInfo(
  liked: json['liked'] as bool,
  likedCount: json['liked_count'] as String,
);

Map<String, dynamic> _$XhsHomeFeedInteractInfoToJson(
  XhsHomeFeedInteractInfo instance,
) => <String, dynamic>{
  'liked': instance.liked,
  'liked_count': instance.likedCount,
};

XhsHomeFeedCover _$XhsHomeFeedCoverFromJson(Map<String, dynamic> json) =>
    XhsHomeFeedCover(
      urlDefault: json['url_default'] as String,
      fileId: json['file_id'] as String,
      height: (json['height'] as num).toInt(),
      width: (json['width'] as num).toInt(),
      url: json['url'] as String,
      infoList:
          (json['info_list'] as List<dynamic>)
              .map(
                (e) => XhsHomeFeedImageInfo.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      urlPre: json['url_pre'] as String,
    );

Map<String, dynamic> _$XhsHomeFeedCoverToJson(XhsHomeFeedCover instance) =>
    <String, dynamic>{
      'url_default': instance.urlDefault,
      'file_id': instance.fileId,
      'height': instance.height,
      'width': instance.width,
      'url': instance.url,
      'info_list': instance.infoList,
      'url_pre': instance.urlPre,
    };

XhsHomeFeedImageInfo _$XhsHomeFeedImageInfoFromJson(
  Map<String, dynamic> json,
) => XhsHomeFeedImageInfo(
  imageScene: json['image_scene'] as String,
  url: json['url'] as String,
);

Map<String, dynamic> _$XhsHomeFeedImageInfoToJson(
  XhsHomeFeedImageInfo instance,
) => <String, dynamic>{'image_scene': instance.imageScene, 'url': instance.url};

XhsHomeFeedVideo _$XhsHomeFeedVideoFromJson(Map<String, dynamic> json) =>
    XhsHomeFeedVideo(
      capa: XhsHomeFeedVideoCapa.fromJson(json['capa'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$XhsHomeFeedVideoToJson(XhsHomeFeedVideo instance) =>
    <String, dynamic>{'capa': instance.capa};

XhsHomeFeedVideoCapa _$XhsHomeFeedVideoCapaFromJson(
  Map<String, dynamic> json,
) => XhsHomeFeedVideoCapa(duration: (json['duration'] as num).toInt());

Map<String, dynamic> _$XhsHomeFeedVideoCapaToJson(
  XhsHomeFeedVideoCapa instance,
) => <String, dynamic>{'duration': instance.duration};
