// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'xhs_note_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

XhsNoteDetailResponse _$XhsNoteDetailResponseFromJson(
  Map<String, dynamic> json,
) => XhsNoteDetailResponse(
  code: (json['code'] as num).toInt(),
  success: json['success'] as bool,
  msg: json['msg'] as String,
  data: XhsNoteDetailData.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$XhsNoteDetailResponseToJson(
  XhsNoteDetailResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'success': instance.success,
  'msg': instance.msg,
  'data': instance.data,
};

XhsNoteDetailData _$XhsNoteDetailDataFromJson(Map<String, dynamic> json) =>
    XhsNoteDetailData(
      items:
          (json['items'] as List<dynamic>)
              .map((e) => NoteDetailItem.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$XhsNoteDetailDataToJson(XhsNoteDetailData instance) =>
    <String, dynamic>{'items': instance.items};

NoteDetailItem _$NoteDetailItemFromJson(Map<String, dynamic> json) =>
    NoteDetailItem(
      id: json['id'] as String,
      modelType: json['model_type'] as String,
      noteCard: NoteCardDetail.fromJson(
        json['note_card'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$NoteDetailItemToJson(NoteDetailItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'model_type': instance.modelType,
      'note_card': instance.noteCard,
    };

NoteCardDetail _$NoteCardDetailFromJson(Map<String, dynamic> json) =>
    NoteCardDetail(
      title: json['title'] as String,
      desc: json['desc'] as String,
      imageList:
          (json['image_list'] as List<dynamic>)
              .map((e) => NoteImage.fromJson(e as Map<String, dynamic>))
              .toList(),
      interactInfo: InteractInfo.fromJson(
        json['interact_info'] as Map<String, dynamic>,
      ),
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      type: json['type'] as String,
      tagList:
          (json['tag_list'] as List<dynamic>)
              .map((e) => Tag.fromJson(e as Map<String, dynamic>))
              .toList(),
      video:
          json['video'] == null
              ? null
              : Video.fromJson(json['video'] as Map<String, dynamic>),
      timeStr: NoteCardDetail._timeFromJson(json['time']),
      lastUpdateTime: (json['last_update_time'] as num?)?.toInt(),
      liked: json['liked'] as bool? ?? false,
      followed: json['followed'] as bool? ?? false,
      relation: json['relation'] as String? ?? 'none',
    );

Map<String, dynamic> _$NoteCardDetailToJson(NoteCardDetail instance) =>
    <String, dynamic>{
      'title': instance.title,
      'desc': instance.desc,
      'image_list': instance.imageList,
      'interact_info': instance.interactInfo,
      'user': instance.user,
      'type': instance.type,
      'tag_list': instance.tagList,
      'video': instance.video,
      'time': instance.timeStr,
      'last_update_time': instance.lastUpdateTime,
      'liked': instance.liked,
      'followed': instance.followed,
      'relation': instance.relation,
    };

NoteImage _$NoteImageFromJson(Map<String, dynamic> json) => NoteImage(
  infoList:
      (json['info_list'] as List<dynamic>)
          .map((e) => ImageInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
  height: (json['height'] as num).toInt(),
  width: (json['width'] as num).toInt(),
);

Map<String, dynamic> _$NoteImageToJson(NoteImage instance) => <String, dynamic>{
  'info_list': instance.infoList,
  'height': instance.height,
  'width': instance.width,
};

ImageInfo _$ImageInfoFromJson(Map<String, dynamic> json) => ImageInfo(
  imageScene: json['image_scene'] as String,
  url: json['url'] as String,
);

Map<String, dynamic> _$ImageInfoToJson(ImageInfo instance) => <String, dynamic>{
  'image_scene': instance.imageScene,
  'url': instance.url,
};

InteractInfo _$InteractInfoFromJson(Map<String, dynamic> json) => InteractInfo(
  likedCount: InteractInfo._countFromJson(json['liked_count']),
  collectedCount: InteractInfo._countFromJson(json['collected_count']),
  commentCount: InteractInfo._countFromJson(json['comment_count']),
  shareCount: InteractInfo._countFromJson(json['share_count']),
  liked: json['liked'] as bool? ?? false,
  time: (json['time'] as num?)?.toInt(),
);

Map<String, dynamic> _$InteractInfoToJson(InteractInfo instance) =>
    <String, dynamic>{
      'liked_count': instance.likedCount,
      'collected_count': instance.collectedCount,
      'comment_count': instance.commentCount,
      'share_count': instance.shareCount,
      'liked': instance.liked,
      'time': instance.time,
    };

User _$UserFromJson(Map<String, dynamic> json) => User(
  userId: json['user_id'] as String,
  nickname: json['nickname'] as String,
  avatar: json['avatar'] as String,
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'user_id': instance.userId,
  'nickname': instance.nickname,
  'avatar': instance.avatar,
};

Tag _$TagFromJson(Map<String, dynamic> json) => Tag(
  type: json['type'] as String,
  id: json['id'] as String,
  name: json['name'] as String,
);

Map<String, dynamic> _$TagToJson(Tag instance) => <String, dynamic>{
  'type': instance.type,
  'id': instance.id,
  'name': instance.name,
};

Video _$VideoFromJson(Map<String, dynamic> json) =>
    Video(media: Media.fromJson(json['media'] as Map<String, dynamic>));

Map<String, dynamic> _$VideoToJson(Video instance) => <String, dynamic>{
  'media': instance.media,
};

Media _$MediaFromJson(Map<String, dynamic> json) =>
    Media(stream: VideoStream.fromJson(json['stream'] as Map<String, dynamic>));

Map<String, dynamic> _$MediaToJson(Media instance) => <String, dynamic>{
  'stream': instance.stream,
};

VideoStream _$VideoStreamFromJson(Map<String, dynamic> json) => VideoStream(
  h264:
      (json['h264'] as List<dynamic>)
          .map((e) => StreamData.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$VideoStreamToJson(VideoStream instance) =>
    <String, dynamic>{'h264': instance.h264};

StreamData _$StreamDataFromJson(Map<String, dynamic> json) =>
    StreamData(masterUrl: json['master_url'] as String);

Map<String, dynamic> _$StreamDataToJson(StreamData instance) =>
    <String, dynamic>{'master_url': instance.masterUrl};
