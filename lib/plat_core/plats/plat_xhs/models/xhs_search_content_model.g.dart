// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'xhs_search_content_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

XhsSearchContentResponse _$XhsSearchContentResponseFromJson(
  Map<String, dynamic> json,
) => XhsSearchContentResponse(
  code: (json['code'] as num).toInt(),
  success: json['success'] as bool,
  msg: json['msg'] as String,
  data:
      json['data'] == null
          ? null
          : XhsSearchContentData.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$XhsSearchContentResponseToJson(
  XhsSearchContentResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'success': instance.success,
  'msg': instance.msg,
  'data': instance.data,
};

XhsSearchContentData _$XhsSearchContentDataFromJson(
  Map<String, dynamic> json,
) => XhsSearchContentData(
  hasMore: json['has_more'] as bool,
  items:
      (json['items'] as List<dynamic>)
          .map((e) => XhsNoteItem.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$XhsSearchContentDataToJson(
  XhsSearchContentData instance,
) => <String, dynamic>{'has_more': instance.hasMore, 'items': instance.items};

XhsNoteItem _$XhsNoteItemFromJson(Map<String, dynamic> json) => XhsNoteItem(
  id: json['id'] as String,
  modelType: json['model_type'] as String,
  noteCard: XhsNoteCard.fromJson(json['note_card'] as Map<String, dynamic>),
  xsecToken: json['xsec_token'] as String,
);

Map<String, dynamic> _$XhsNoteItemToJson(XhsNoteItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'model_type': instance.modelType,
      'note_card': instance.noteCard,
      'xsec_token': instance.xsecToken,
    };

XhsNoteCard _$XhsNoteCardFromJson(Map<String, dynamic> json) => XhsNoteCard(
  type: json['type'] as String,
  displayTitle: json['display_title'] as String? ?? '',
  user: XhsUser.fromJson(json['user'] as Map<String, dynamic>),
  interactInfo: XhsInteractInfo.fromJson(
    json['interact_info'] as Map<String, dynamic>,
  ),
  cover: XhsCover.fromJson(json['cover'] as Map<String, dynamic>),
  imageList:
      (json['image_list'] as List<dynamic>)
          .map((e) => XhsImage.fromJson(e as Map<String, dynamic>))
          .toList(),
  cornerTagInfo:
      (json['corner_tag_info'] as List<dynamic>)
          .map((e) => XhsCornerTag.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$XhsNoteCardToJson(XhsNoteCard instance) =>
    <String, dynamic>{
      'type': instance.type,
      'display_title': instance.displayTitle,
      'user': instance.user,
      'interact_info': instance.interactInfo,
      'cover': instance.cover,
      'image_list': instance.imageList,
      'corner_tag_info': instance.cornerTagInfo,
    };

XhsUser _$XhsUserFromJson(Map<String, dynamic> json) => XhsUser(
  nickName: json['nick_name'] as String,
  avatar: json['avatar'] as String,
  userId: json['user_id'] as String,
  nickname: json['nickname'] as String,
  xsecToken: json['xsec_token'] as String,
);

Map<String, dynamic> _$XhsUserToJson(XhsUser instance) => <String, dynamic>{
  'nick_name': instance.nickName,
  'avatar': instance.avatar,
  'user_id': instance.userId,
  'nickname': instance.nickname,
  'xsec_token': instance.xsecToken,
};

XhsInteractInfo _$XhsInteractInfoFromJson(Map<String, dynamic> json) =>
    XhsInteractInfo(
      liked: json['liked'] as bool,
      likedCount: json['liked_count'] as String,
      collected: json['collected'] as bool,
      collectedCount: json['collected_count'] as String,
      commentCount: json['comment_count'] as String,
      sharedCount: json['shared_count'] as String,
    );

Map<String, dynamic> _$XhsInteractInfoToJson(XhsInteractInfo instance) =>
    <String, dynamic>{
      'liked': instance.liked,
      'liked_count': instance.likedCount,
      'collected': instance.collected,
      'collected_count': instance.collectedCount,
      'comment_count': instance.commentCount,
      'shared_count': instance.sharedCount,
    };

XhsCover _$XhsCoverFromJson(Map<String, dynamic> json) => XhsCover(
  height: (json['height'] as num).toInt(),
  width: (json['width'] as num).toInt(),
  urlDefault: json['url_default'] as String,
  urlPre: json['url_pre'] as String,
);

Map<String, dynamic> _$XhsCoverToJson(XhsCover instance) => <String, dynamic>{
  'height': instance.height,
  'width': instance.width,
  'url_default': instance.urlDefault,
  'url_pre': instance.urlPre,
};

XhsImage _$XhsImageFromJson(Map<String, dynamic> json) => XhsImage(
  height: (json['height'] as num).toInt(),
  width: (json['width'] as num).toInt(),
  infoList:
      (json['info_list'] as List<dynamic>)
          .map((e) => XhsImageInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$XhsImageToJson(XhsImage instance) => <String, dynamic>{
  'height': instance.height,
  'width': instance.width,
  'info_list': instance.infoList,
};

XhsImageInfo _$XhsImageInfoFromJson(Map<String, dynamic> json) => XhsImageInfo(
  imageScene: json['image_scene'] as String,
  url: json['url'] as String,
);

Map<String, dynamic> _$XhsImageInfoToJson(XhsImageInfo instance) =>
    <String, dynamic>{'image_scene': instance.imageScene, 'url': instance.url};

XhsCornerTag _$XhsCornerTagFromJson(Map<String, dynamic> json) =>
    XhsCornerTag(type: json['type'] as String, text: json['text'] as String);

Map<String, dynamic> _$XhsCornerTagToJson(XhsCornerTag instance) =>
    <String, dynamic>{'type': instance.type, 'text': instance.text};
