import 'package:get/get.dart';

/// 简单的事件总线，用于组件间通信
class RxBus extends GetxController {
  final _eventSubject = RxMap<String, dynamic>();

  /// 发送事件
  void post(String event, {dynamic data}) {
    _eventSubject[event] = data ?? DateTime.now().toString();
  }

  /// 监听事件
  Worker listen(String event, Function(dynamic) callback) {
    return ever(_eventSubject, (map) {
      if (map.containsKey(event)) {
        callback(map[event]);
      }
    });
  }
} 