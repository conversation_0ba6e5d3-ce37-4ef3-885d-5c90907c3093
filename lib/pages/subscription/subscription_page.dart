import 'dart:math';

import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/base/nav_back_button.dart';
import 'package:aitoearn_app/pages/subscription/subscription_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SubscriptionPage extends GetView<SubscriptionController> {
  const SubscriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      statusBarIconBrightness: Brightness.light,
      useDefaultBackground: false,
      useSafeArea: false,
      title: null,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF5C4E3F),
              Color(0xFF3C3D47),
              Color(0xFF232429),
              Color(0xFF232429),
            ],
          ),
        ),
        child: SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [getTipsCard(), Expanded(child: getSelectCard())],
                ),
              ),
              SafeArea(
                child: NavBackButton(
                  color: Colors.white,
                  onPressed: () {
                    Get.back();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getTipsCard() {
    // 原图尺寸
    const double imageWidth = 1372;
    const double imageHeight = 1012;
    const double aspectRatio = imageWidth / imageHeight;

    // 计算卡片实际宽度（最多不超过 350）
    const double screenWidth = 350;

    // 等比缩放后的高度
    const double scaledHeight = screenWidth / aspectRatio;

    return Transform.translate(
      offset: const Offset(0, scaledHeight * 0.15), // 向下偏移
      child: Container(
        width: screenWidth,
        height: scaledHeight,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/account/bg_vip_card.png'),
            fit: BoxFit.fill,
          ),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: const EdgeInsets.only(top: 70, left: 20, right: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            'subscription.plusMember'.tr.toNormalText(
              fontSize: 22,
              color: AppColors.vipTextColor,
              fontWeight: FontWeight.bold,
            ),
            Obx(() {
              final isVip = controller.userInfo.value?.vipInfo == null;
              return isVip
                  ? 'subscription.unlockAllFeatures'.tr.toNormalText(
                    fontSize: 12,
                    color: AppColors.vipTextColor,
                  )
                  : 'subscription.membershipExpireTime'
                      .trParams({
                        'expireTime':
                            controller.userInfo.value?.vipInfo?.expireTime ??
                            '',
                      })
                      .toNormalText(
                        fontSize: 12,
                        color: AppColors.vipTextColor,
                      );
            }),
            Gaps.vGap24,
            GridView.count(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 4,
              childAspectRatio: 4.0,
              crossAxisSpacing: 10,
              mainAxisSpacing: 16,
              // 使用控制器中的权益列表动态生成网格
              children:
                  controller.memberBenefits
                      .map(
                        (benefit) => Center(
                          child: benefit.toNormalText(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: AppColors.vipTextColor,
                          ),
                        ),
                      )
                      .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget getSelectCard() {
    // 固定整个选择框的高度为420
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            offset: Offset(0, -2),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Obx(
            () => (controller.userInfo.value?.vipInfo == null
                    ? 'subscription.selectPlan'.tr
                    : 'subscription.renewPlan'.tr)
                .toNormalText(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textColor,
                )
                .marginOnly(left: 16, right: 16, top: 16, bottom: 10),
          ),
          Obx(
            () => SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  Gaps.hGap16,
                  _buildSubscriptionCard(
                    title: 'subscription.monthlyPlan'.tr,
                    originalPrice: '\$20',
                    discountPrice: '\$15',
                    discountLabel: '25% ${'subscription.priceOff'.tr}',
                    tagText: 'subscription.mostPopular'.tr,
                    // 根据控制器状态判断是否选中
                    isSelected: controller.selectedIndex.value == 0,
                    onTap: () => controller.changeSelectedIndex(0),
                  ),
                  Gaps.hGap12,
                  _buildSubscriptionCard(
                    title: 'subscription.annualPlan'.tr,
                    originalPrice: '\$240',
                    discountPrice: '\$120',
                    discountLabel: '50% ${'subscription.priceOff'.tr}',
                    tagText: 'subscription.bestValue'.tr,
                    isSelected: controller.selectedIndex.value == 1,
                    onTap: () => controller.changeSelectedIndex(1),
                  ),
                  Gaps.hGap12,
                  _buildSubscriptionCard(
                    title: 'onceMonthlyPlan'.tr,
                    originalPrice: '',
                    discountPrice: '\$20',
                    discountLabel: '',
                    tagText: 'subscription.flexibleChoice'.tr,
                    isSelected: controller.selectedIndex.value == 2,
                    onTap: () => controller.changeSelectedIndex(2),
                  ),
                  Gaps.hGap16,
                ],
              ),
            ),
          ),
          Obx(
            () => Opacity(
              opacity: controller.selectedIndex.value == 2 ? 0 : 1,
              child: 'subscription.autoRenewCancelAnytime'.tr
                  .toNormalText(fontSize: 12, color: AppColors.textSecondColor)
                  .marginOnly(left: 16, right: 16),
            ),
          ),
          Gaps.vGap16,
          ResponsiveLayout(
            mobile: _payButton().marginOnly(left: 16, right: 16, top: 16),
            desktop: Center(
              child: SizedBox(width: 400, child: _payButton()),
            ).marginOnly(left: 16, right: 16, top: 16),
          ),
        ],
      ),
    );
  }

  Widget _payButton() {
    return Obx(
      () => CustomButton(
        gradient: AppColors.blueAndPurple,
        text:
            controller.userInfo.value?.vipInfo == null
                ? 'subscription.activateNow'.tr
                : 'subscription.renewNow'.tr,
        textColor: AppColors.white,
        onPressed: () {
          controller.startPay();
        },
      ),
    );
  }

  // 修改套餐卡片组件，添加点击事件
  Widget _buildSubscriptionCard({
    required String title,
    required String originalPrice,
    required String discountPrice,
    required String discountLabel,
    required String tagText,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    // 固定套餐卡片高度为180
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 150,
        height: 135,
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 10, top: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.primaryColor : Colors.grey[200]!,
            width: 2,
          ),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              offset: Offset(0, 2),
              blurRadius: 5,
            ),
          ],
        ),
        child: Stack(
          children: [
            if (tagText.isNotEmpty)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primaryColor : Colors.orange,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: tagText.toNormalText(
                    fontSize: 10,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Gaps.vGap12,
                title.toNormalText(fontSize: 16, fontWeight: FontWeight.w600),
                Gaps.vGap4,
                if (originalPrice.isNotEmpty)
                  Row(
                    children: [
                      originalPrice.toNormalText(
                        fontSize: 12,
                        color: AppColors.textHintColor,
                        decoration: TextDecoration.lineThrough,
                      ),
                      Gaps.hGap4,
                      discountLabel.toNormalText(
                        fontSize: 12,
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ],
                  ),
                Gaps.vGap4,
              ],
            ),
            Positioned(
              bottom: 0,
              child: discountPrice.toNormalText(
              fontSize: 26,
              fontWeight: FontWeight.w600,
              color: AppColors.textColor,
            ),)
          ],
        ),
      ),
    );
  }
}
