import 'package:aitoearn_app/api/user/payment_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/store/user/model/user_info.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/dialog/dialog_helper.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class SubscriptionController extends GetxController {
  final selectedIndex = 0.obs;
  final payments = ['month', 'year', 'onceMonth'];
  final userInfo = Rxn<UserInfo>();

  final List<String> memberBenefits = [
    'subscription.benefit1'.tr,
    'subscription.benefit2'.tr,
    'subscription.benefit3'.tr,
    'subscription.benefit4'.tr,
    'subscription.benefit5'.tr,
    'subscription.benefit6'.tr,
    'subscription.benefit7'.tr,
    'subscription.benefit8'.tr,
  ];

  CancelFunc? payCancel;

  @override
  void onInit() {
    super.onInit();
    userInfo.value = Get.find<UserStoreService>().userInfo.value;
  }

  void changeSelectedIndex(int index) {
    selectedIndex.value = index;
  }

  Future<void> startPay() async {
    String? userId = userInfo.value?.id;
    if (userId == null) {
      showError('subscription.userInfoError'.tr);
      return;
    }

    final cancel = showProgress();
    try {
      final result = await createOrder(
        mode: selectedIndex.value == 2 ? 'payment' : 'subscription',
        payment: payments[selectedIndex.value],
        userId: userId,
      );
      cancel();

      if (result == null) {
        showError('subscription.createOrderFailed'.tr);
        return;
      }

      final data = result['data'];
      final url = data['url'];
      final orderId = data['id'];

      if (url == null || url.isEmpty) {
        showError('subscription.getPaymentUrlFailed'.tr);
        return;
      }

      if (orderId == null || orderId.isEmpty) {
        showError('subscription.getOrderIdFailed'.tr);
        return;
      }

      // 跳转支付页面
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        DialogHelper.showSimpleDialog(
          title: 'subscription.paymentTitle'.tr,
          content: 'subscription.paymentMessage'.tr,
          confirmText: 'subscription.completePayment'.tr,
          barrierDismissible: false,
        ).then((isSure) async {
          if (isSure == true) {
            // 开始轮询订单状态
            payCancel = showProgress();
            await pollOrderStatus(orderId);
            payCancel?.call();
            await Get.find<UserStoreService>().getUserInfo();
            userInfo.value = Get.find<UserStoreService>().userInfo.value;
          }
        });
      } else {
        showError('subscription.cannotOpenPaymentPage'.tr);
      }
    } catch (e) {
      cancel();
      showError('subscription.paymentFailed@error'.trParams({'error': e.toString()}));
    }
  }

  Future<void> pollOrderStatus(
      String orderId, {
        int maxAttempts = 6,
        int delaySeconds = 5,
        int currentAttempt = 1,
      }) async {
    try {
      final result = await queryOrder(id: orderId);
      if (result == null) {
        showError('subscription.orderQueryFailed'.tr);
        return;
      }

      final data = result['data'];
      if (data == null || data.length == 0) {
        showError('subscription.orderQueryFailed'.tr);
        return;
      }

      final status = data[0]['status'];
      LoggerUtil.d('=== 订单状态：$status');

      // 状态码说明：
      // 1 = 支付成功，2 = 等待支付，3 = 退款成功，4 = 已取消 / 过期
      switch (status) {
        case 1:
          showSuccess('subscription.success'.tr);
          return;
        case 3:
          showError('subscription.orderRefunded'.tr);
          return;
        case 4:
          showError('subscription.orderCanceled'.tr);
          return;
        case 2:
          if (currentAttempt < maxAttempts) {
            LoggerUtil.d('等待支付中...第 ${currentAttempt.toString()} 次轮询');
            await Future.delayed(Duration(seconds: delaySeconds));
            await pollOrderStatus(
              orderId,
              maxAttempts: maxAttempts,
              delaySeconds: delaySeconds,
              currentAttempt: currentAttempt + 1,
            );
          } else {
            showError('subscription.paymentTimeout'.tr);
          }
          return;
        default:
          showError('subscription.unknownOrderStatus@status'.trParams({'status': status.toString()}));
          return;
      }
    } catch (e) {
      e.printError();
      showError('subscription.queryOrderError@error'.trParams({'error': e.toString()}));
    }
  }

}
