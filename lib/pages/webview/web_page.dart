import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/webview/web_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_option.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_pop_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// WebView页面组件
/// 用于加载网页内容，支持自定义标题和全屏模式
class WebPage extends GetView<WebController> {
  const WebPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final bool fullScreen = controller.fullScreen.value;
      final String? title = controller.displayTitle;
      final double progress = controller.progress.value;
      final bool isLoading = controller.isLoading.value;

      return BasePage(
        useDefaultBackground: false,
        backgroundColor: AppColors.white,
        // 根据全屏参数决定是否显示标题栏
        title: fullScreen ? null : title,
        showBackBtnWhenNoLeading: !fullScreen,
        useSafeArea: !fullScreen,
        action: _buildMoreAction(context),
        body: Stack(
          children: [
            Container(
              color: AppColors.cardColor,
              child: controller.inAppWebView.value ?? const SizedBox(),
            ),

            // 顶部加载进度条
            if (isLoading)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.primaryColor,
                  ),
                  minHeight: 2,
                  // 根据进度值显示不同样式
                  value: progress > 0 && progress < 100 ? progress / 100 : null,
                ),
              ),
          ],
        ),
      );
    });
  }

  _buildMoreAction(BuildContext context) {
    final menuKey = GlobalKey();
    return IconButton(
      key: menuKey,
      onPressed: () {
        MenuPopHelper.showMenuByKey(
          context: context,
          key: menuKey,
          options: [
            MenuOption(
              icon: Icons.refresh,
              name: '刷新',
              onTap: () => controller.refreshLink(),
            ),
            MenuOption(
              icon: Icons.copy,
              name: '复制链接',
              onTap: () => controller.copyLink(),
            ),
          ],
        );
      },
      icon: const Icon(Icons.more_horiz),
    ).marginOnly(right: 8);
  }
}
