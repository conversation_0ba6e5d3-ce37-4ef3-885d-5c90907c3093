import 'dart:io';

import 'package:aitoearn_app/config/app_config.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';

/// WebView控制器
/// 管理WebView的加载状态、进度和标题等信息
class WebController extends GetxController {
  final RxnString currentUrl = RxnString();
  final RxnString pageTitle = RxnString();
  final RxBool isLoading = true.obs;
  final RxBool fullScreen = false.obs;
  final RxnString initialTitle = RxnString();
  final RxDouble progress = 0.0.obs; // 新增：加载进度(0-100)

  late InAppWebViewController webViewController;
  late Rxn<InAppWebView?> inAppWebView = Rxn<InAppWebView?>(null);

  @override
  void onInit() {
    super.onInit();
    // 获取路由参数
    final Map<String, dynamic> args = Get.arguments ?? {};
    currentUrl.value = args['url'];
    // 短路由补充host
    if (currentUrl.value != null && !currentUrl.value!.startsWith('http')) {
      currentUrl.value = '${AppConfig.appHost}${currentUrl.value}';
    }

    initialTitle.value = args['title'];
    fullScreen.value = args['fullScreen'] ?? false;

    _initializeWebView();
  }

  void _initializeWebView() {
    inAppWebView.value = InAppWebView(
      onProgressChanged: (controller, progressValue) {
        progress.value = progressValue.toDouble();
        // 根据进度更新加载状态
        if (progressValue >= 100) {
          isLoading.value = false;
        } else {
          isLoading.value = true;
        }
      },
      onWebViewCreated: (controller) {
        webViewController = controller;
        controller.loadUrl(
          urlRequest: URLRequest(url: WebUri(currentUrl.value!)),
        );
        try {
          if (Platform.isAndroid || Platform.isIOS) {
            controller.setSettings(
              settings: InAppWebViewSettings(
                transparentBackground: true,
                userAgent:
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0',
              ),
            );
          }
        } catch (e) {
          e.printError();
        }
      },
      onLoadStart: (controller, url) {
        isLoading.value = true;
        progress.value = 0; // 开始加载时重置进度
      },
      onLoadStop: (controller, url) async {
        isLoading.value = false;
        progress.value = 100; // 加载完成时进度设为100
        // 如果没有提供初始标题，则使用网页标题
        if (initialTitle.value == null) {
          pageTitle.value = await webViewController.getTitle();
        }
      },
      onReceivedHttpError: (controller, request, error) {
        isLoading.value = false;
        progress.value = 0;
      },
      initialSettings: InAppWebViewSettings(
        javaScriptEnabled: true,
        useShouldOverrideUrlLoading: true,
        mediaPlaybackRequiresUserGesture: false,
        useHybridComposition: false,
        isInspectable: true,
      ),
    );
  }

  // 获取显示的标题
  String? get displayTitle => initialTitle.value ?? pageTitle.value;

  refreshLink() async {
    final url = await webViewController.getUrl();
    if (url != null) {
      await webViewController.loadUrl(urlRequest: URLRequest(url: url));
    }
  }

  copyLink() async {
    final url = await webViewController.getUrl();
    if (url != null) {
      Clipboard.setData(ClipboardData(text: url.toString()));
    }
  }
}
