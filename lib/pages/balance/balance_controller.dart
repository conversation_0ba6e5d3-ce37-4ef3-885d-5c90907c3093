import 'package:aitoearn_app/store/user/model/user_info.dart';
import 'package:get/get.dart';

/// 个人余额控制器
class BalanceController extends GetxController {
  final userInfo = Rxn<UserInfo>();
  final balance = 0.0.obs;
  
  @override
  void onInit() {
    super.onInit();
    // 获取传递的用户信息
    final UserInfo? info = Get.arguments;
    if (info != null) {
      userInfo.value = info;
      // 模拟获取余额数据
      balance.value = info.balance ?? 0.0;
    }
  }
  
  /// 刷新余额数据
  void refreshBalance() {
    // 这里添加余额刷新逻辑
    balance.value += 50.0; // 模拟增加余额
  }
}