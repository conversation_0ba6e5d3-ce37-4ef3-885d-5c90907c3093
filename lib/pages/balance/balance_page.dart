import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/balance/balance_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 个人余额页面
class BalancePage extends GetView<BalanceController> {
  const BalancePage({super.key});
  
  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: '我的余额',
      body: Obx(
        () => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              '${controller.userInfo.value?.balance ?? "用户"}的余额'.toNormalText(fontSize: 20),
              const SizedBox(height: 20),
              '¥${controller.balance.value.toStringAsFixed(2)}'.toNormalText(
                fontSize: 40, 
                color: AppColors.primaryColor,
                fontWeight: FontWeight.bold
              ),
              const Sized<PERSON><PERSON>(height: 40),
              ElevatedButton(
                onPressed: controller.refreshBalance,
                child: '刷新余额'.toNormalText(),
              )
            ],
          ),
        ),
      ),
    );
  }
}