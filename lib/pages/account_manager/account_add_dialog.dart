import 'package:aitoearn_app/config/plat_config/plat_config.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/models/account_get_cookie_params.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 添加账号弹窗组件
class AccountAddDialog extends StatefulWidget {
  /// 空间ID
  final String? spaceId;

  const AccountAddDialog({super.key, this.spaceId});

  /// 显示添加账号弹窗的静态方法
  static void show(BuildContext context, {String? spaceId}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return AccountAddDialog(spaceId: spaceId ?? '');
      },
    );
  }

  @override
  State<AccountAddDialog> createState() => _AccountAddDialogState();
}

class _AccountAddDialogState extends State<AccountAddDialog> {
  // 创建用于输入的控制器
  final phoneController = TextEditingController();
  final verifyCodeController = TextEditingController();

  // 当前选中的平台类型
  PlatTypeEnum? selectedPlatType;

  // 当前UI模式 (0: 完整模式, 1: 简化模式)
  int uiMode = 1;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildPlatformSelector(),

          // 根据UI模式选择不同的内容
          uiMode == 0 ? _buildFullModeContent() : _buildSimplifiedModeContent(),

          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  // 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.black12, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              'account.cancel'.tr,
              style: const TextStyle(fontSize: 16, color: Colors.black54),
            ),
          ),
          Row(
            children: [
              Text(
                'account.add'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 10),
              // 模式切换按钮
              // InkWell(
              //   onTap: () {
              //     setState(() {
              //       uiMode = uiMode == 0 ? 1 : 0;
              //     });
              //   },
              //   child: Container(
              //     padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              //     decoration: BoxDecoration(
              //       color: Colors.grey[200],
              //       borderRadius: BorderRadius.circular(10),
              //     ),
              //     child: Row(
              //       children: [
              //         Icon(
              //           uiMode == 0 ? Icons.view_list : Icons.qr_code_scanner,
              //           size: 16,
              //           color: Colors.grey[700],
              //         ),
              //         const SizedBox(width: 4),
              //         Text(
              //           uiMode == 0 ? '切换简洁模式' : '切换完整模式',
              //           style: TextStyle(
              //             fontSize: 12,
              //             color: Colors.grey[700],
              //           ),
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
            ],
          ),
          TextButton(
            onPressed: () {
              if (uiMode == 0 &&
                  phoneController.text.isNotEmpty &&
                  verifyCodeController.text.isNotEmpty) {
                // 添加账号逻辑
                _addNewAccount(selectedPlatType!.val, phoneController.text);
                Navigator.pop(context);
              } else if (uiMode == 1) {
                // 简化模式直接关闭弹窗
                Navigator.pop(context);
              }
            },
            child: Text(
              'account.confirm'.tr,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  // 构建平台选择器
  Widget _buildPlatformSelector() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children:
            platConfigList.map((option) {
              return GestureDetector(
                onTap: () {
                  setState(() {
                    selectedPlatType = option.platType;
                  });
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 10),
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          selectedPlatType == option.platType
                              ? AppColors.primaryColor
                              : Colors.transparent,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(option.icon, width: 24, height: 24),
                      const SizedBox(width: 8),
                      Text(
                        option.name,
                        style: TextStyle(
                          color:
                              selectedPlatType == option.platType
                                  ? AppColors.primaryColor
                                  : Colors.black87,
                          fontWeight:
                              selectedPlatType == option.platType
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  // 构建完整模式内容
  Widget _buildFullModeContent() {
    return Column(
      children: [
        // 手机号输入
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: TextField(
            controller: phoneController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              hintText: 'account.add.phone'.tr,
              filled: true,
              fillColor: Colors.grey[100],
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 14,
              ),
            ),
          ),
        ),

        // 验证码输入
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: verifyCodeController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'account.add.verification'.tr,
                    filled: true,
                    fillColor: Colors.grey[100],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              TextButton(
                onPressed: () {
                  // 获取验证码逻辑
                },
                style: TextButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text('account.add.get.verification'.tr),
              ),
            ],
          ),
        ),

        // 其他登录方式
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton(
                onPressed: () {
                  // 二维码登录逻辑
                  _navigateToQrLogin();
                },
                child: Text(
                  'account.add.qr.login'.tr,
                  style: const TextStyle(color: Colors.purple),
                ),
              ),
              Container(
                width: 1,
                height: 16,
                color: Colors.grey[300],
                margin: const EdgeInsets.symmetric(horizontal: 16),
              ),
              TextButton(
                onPressed: () {
                  // Cookie登录逻辑
                },
                child: Text(
                  'account.add.cookie.login'.tr,
                  style: const TextStyle(color: Colors.purple),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建简化模式内容
  Widget _buildSimplifiedModeContent() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 24),
      child: Column(
        children: [
          // 统一登录卡片
          InkWell(
            onTap: () {
              _onLoginButtonPressed();
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.purple.withOpacity(0.05),
                    Colors.blue.withOpacity(0.1),
                  ],
                ),
                border: Border.all(
                  color: Colors.purple.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  // 图标区域
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.purple.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.qr_code_scanner,
                          size: 28,
                          color: Colors.purple,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.phone_android,
                          size: 28,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // 标题
                  Text(
                    'account.add.oneclick'.tr,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // 描述
                  Text(
                    'account.add.support'.trParams({
                      'platform': selectedPlatType?.val ?? '',
                    }),
                    style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 20),

                  // 登录按钮
                  CustomButton(
                    gradient: AppColors.blueAndPurple,
                    text: 'account.add.login.now'.tr,
                    textColor: AppColors.white,
                    onPressed: _onLoginButtonPressed,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 提示信息
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.amber.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 20, color: Colors.amber[700]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'account.add.notice'.trParams({
                      'platform': selectedPlatType?.val ?? '',
                    }),
                    style: TextStyle(fontSize: 12, color: Colors.amber[800]),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 登录按钮点击
  void _onLoginButtonPressed() {
    // 检查是否选择了平台
    if (selectedPlatType == null) {
      showToast('account.add.select.platform'.tr);
      return;
    }
    Navigator.pop(context);

    if (localLoginPlatSet.contains(selectedPlatType)) {
      _navigateToQrLogin();
    } else {
      // 跳转到官方授权登录
      Get.toNamed(
        AppRouter.accountOfficialAuthPath,
        arguments: AccountGetCookieParams(
          selectedPlatType!,
          widget.spaceId ?? '',
        ),
      );
    }
  }

  // 跳转到二维码登录
  void _navigateToQrLogin() {
    // 直接跳转到登录页面
    Get.toNamed(
      AppRouter.accountGetCookiePath,
      arguments: AccountGetCookieParams(
        selectedPlatType!,
        widget.spaceId ?? '',
      ),
    );
  }

  // 添加新账号
  void _addNewAccount(String platType, String phone) {
    final controller = Get.find<AccountListController>();

    // 创建新账号
    final newAccount = AccountInfo(
      name: '官方账号',
      nickname: '新添加账号',
      avatar: null,
      online: true,
      ip: '***********',
      platType: platType,
      uid:
          'temp_${DateTime.now().millisecondsSinceEpoch}', // 临时UID，实际应用中应使用真实UID
    );

    // 添加到第一个空间
    if (controller.groups.isNotEmpty) {
      controller.groups[0].accounts.add(newAccount);
      controller.update();
      showSuccess('account.add.success.message'.tr);
    }
  }
}
