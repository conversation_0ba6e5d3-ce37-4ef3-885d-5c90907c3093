import 'package:aitoearn_app/api/account/account_api.dart';
import 'package:aitoearn_app/api/account/models/account_group_model.dart'
    as api_model;
import 'package:aitoearn_app/api/account/models/account_user_info_modle.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 账号信息模型
class AccountInfo {
  String name;
  String? nickname;
  String? avatar;
  bool online;
  String? ip;
  bool isSelected;
  String platType; // 平台类型：douyin, ks, wx-sph, xhs
  String uid; // 保存原始UID，用于唯一标识账号

  AccountInfo({
    required this.name,
    required this.online,
    required this.uid,
    this.nickname,
    this.avatar,
    this.ip,
    this.isSelected = false,
    this.platType = 'douyin', // 默认为抖音
  });
}

/// 账号空间模型
class AccountGroup {
  Map<String, dynamic>? browserConfig;
  String name;
  String id;
  bool isDefault;
  RxBool expanded;
  RxList<AccountInfo> accounts;

  AccountGroup({
    required this.name,
    required this.id,
    required List<AccountInfo> accounts,
    this.isDefault = false,
    this.browserConfig,
    bool expanded = true,
  }) : expanded = expanded.obs,
       accounts = accounts.obs;
}

/// 账号列表控制器
class AccountListController extends GetxController {
  // 账号空间数据
  final RxList<AccountGroup> groups = <AccountGroup>[].obs;

  // 是否处于批量管理模式
  final RxBool isBatchMode = false.obs;

  // API服务
  final AccountApi _accountApi = AccountApi();

  // 当前选中的账号数量
  int get selectedCount =>
      groups
          .expand((group) => group.accounts)
          .where((account) => account.isSelected)
          .length;

  // 是否全选
  bool get isAllSelected {
    final allAccounts = groups.expand((group) => group.accounts).toList();
    return allAccounts.isNotEmpty &&
        allAccounts.every((account) => account.isSelected);
  }

  // 账号持久化服务
  late final AccountPersistentService accountService;

  // 初始化数据
  @override
  void onInit() {
    super.onInit();

    // 初始化账号服务
    accountService = Get.find<AccountPersistentService>();

    // 不再在控制器初始化时自动加载数据，而是由页面控制
    // 这样可以避免重复加载和提供更好的用户体验
    LoggerUtil.i('账号列表控制器初始化完成');
  }

  // 从API加载空间数据
  Future<bool> loadGroupsFromApi() async {
    try {
      LoggerUtil.i('尝试从API加载空间数据');

      // 清空当前空间列表，准备加载新数据
      groups.clear();

      final response = await _accountApi.getAccountGroups();

      if (response != null && response.code == 0) {
        List<api_model.AccountGroupModel> groupData = [];

        // 确保我们有空间数据可以处理
        if (response.data.isNotEmpty) {
          groupData = response.data;
          LoggerUtil.i('API返回空间数据成功，共 ${groupData.length} 个空间');
        } else {
          LoggerUtil.w('API返回的空间数据为空，尝试使用本地空间');
        }

        // 处理空间数据
        for (var groupModel in groupData) {
          // 创建空间对象
          final group = AccountGroup(
            id: groupModel.id,
            name: groupModel.name,
            isDefault: groupModel.isDefault,
            expanded: true,
            browserConfig: groupModel.browserConfig,
            accounts: [],
          );

          // 添加到空间列表
          groups.add(group);
        }

        // 加载账号数据并分配到对应空间
        await loadAccountsToGroups();

        return true;
      } else {
        LoggerUtil.w('API请求空间数据失败：${response?.message ?? '未知错误'}');
        return false;
      }
    } catch (e) {
      LoggerUtil.e('获取空间数据出错：$e');
      return false;
    }
  }

  // 加载账号并分配到对应空间
  Future<void> loadAccountsToGroups() async {
    try {
      // 如果没有保存的账号，直接返回
      if (accountService.accounts.isEmpty) {
        return;
      }

      // 如果已经从API获取了空间信息，则将账号分配到API空间中
      if (groups.isNotEmpty) {
        LoggerUtil.i('将账号分配到API空间中，空间数量：${groups.length}');

        // 找到或创建默认空间
        AccountGroup defaultGroup =
            groups.firstWhereOrNull(
              (group) => group.isDefault || group.name == '默认空间',
            ) ??
            groups.first;

        LoggerUtil.i('默认空间: ${defaultGroup.name}, ID: ${defaultGroup.id}');

        // 将账号分配到对应的空间
        for (var account in accountService.accounts) {
          final accountInfo = _convertToAccountInfo(account);
          final groupId = account.groupId;

          if (groupId.isEmpty) {
            // 没有空间ID，放入默认空间
            defaultGroup.accounts.add(accountInfo);
            LoggerUtil.i('账号 ${account.account} 没有空间ID，放入默认空间');
            continue;
          }

          // 尝试找到对应的空间
          final targetGroup = groups.firstWhereOrNull((g) => g.id == groupId);

          if (targetGroup != null) {
            // 找到对应空间，添加账号
            targetGroup.accounts.add(accountInfo);
            LoggerUtil.i('账号 ${account.account} 添加到空间 ${targetGroup.name}');
          } else {
            // 找不到对应空间，放入默认空间
            defaultGroup.accounts.add(accountInfo);
            LoggerUtil.i('账号 ${account.account} 的空间ID $groupId 找不到对应空间，放入默认空间');
          }
        }
      }
      update();
    } catch (e) {
      LoggerUtil.e('加载账号到空间失败: $e');
    }
  }

  // 同步本地空间与API空间
  Future<void> syncLocalGroupsWithApi() async {
    try {
      LoggerUtil.i('开始同步本地空间与API空间');

      // 从API获取空间数据
      final response = await _accountApi.getAccountGroups();

      if (response != null && response.code == 0 && response.data.isNotEmpty) {
        LoggerUtil.i('API返回空间数据成功，共 ${response.data.length} 个空间');

        // 创建一个新的本地空间列表
        final newLocalGroups = <LocalAccountGroupModel>[];

        // 遍历API返回的空间
        for (var apiGroup in response.data) {
          // 查找对应的本地空间
          final localGroup = accountService.groups.firstWhereOrNull(
            (group) => group.name == apiGroup.name || group.id == apiGroup.id,
          );

          if (localGroup != null) {
            // 如果找到匹配的本地空间，更新其ID
            localGroup.id = apiGroup.id;
            newLocalGroups.add(localGroup);
            LoggerUtil.i('更新本地空间ID: ${localGroup.name}, 新ID: ${apiGroup.id}');
          } else {
            // 如果没有找到匹配的本地空间，创建新的本地空间
            final newGroup = LocalAccountGroupModel(
              apiGroup.id,
              apiGroup.name,
              [],
              expanded: true,
            );
            newLocalGroups.add(newGroup);
            LoggerUtil.i('创建新的本地空间: ${apiGroup.name}, ID: ${apiGroup.id}');
          }
        }

        // 检查是否有本地空间不在API返回的空间中
        for (var localGroup in accountService.groups) {
          final apiGroup = response.data.firstWhereOrNull(
            (group) =>
                group.name == localGroup.name || group.id == localGroup.id,
          );

          if (apiGroup == null) {
            // 如果本地空间不在API返回的空间中，添加到新的本地空间列表
            newLocalGroups.add(localGroup);
            LoggerUtil.i('保留本地空间: ${localGroup.name}, ID: ${localGroup.id}');
          }
        }

        // 更新本地空间列表
        accountService.groups.clear();
        accountService.groups.addAll(newLocalGroups);

        // 保存更改
        accountService.saveGroups();

        LoggerUtil.i('本地空间与API空间同步完成，共 ${accountService.groups.length} 个空间');
        return;
      } else {
        LoggerUtil.w('API返回空间数据为空或请求失败，无法同步');
      }
    } catch (e) {
      LoggerUtil.e('同步本地空间与API空间失败: $e');
    }
  }

  // 刷新账号状态
  Future<void> refreshAccountsStatus() async {
    try {
      // 首先尝试从API获取最新账号列表
      final success = await accountService.fetchAccountsFromApi();

      // if (success) {
      //   // 如果API获取成功，重新加载空间数据
      //   loadGroupsFromApi();
      // } else {
      //   // 如果API获取失败，则使用本地数据更新账号状态
      //获取完成后刷新账号状态
      await accountService.updateAccountsUserInfo();
      // 重新加载空间数据
      loadGroupsFromApi();
      // }
    } catch (e) {
      // 关闭加载对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // 显示错误提示
      showError('获取账号状态时出错: $e');
      LoggerUtil.e('刷新账号状态失败: $e');
    }
  }

  // 将保存的账号信息转换为AccountInfo
  AccountInfo _convertToAccountInfo(AccountUserInfoModle savedAccount) {
    return AccountInfo(
      name: savedAccount.account,
      nickname: savedAccount.nickname,
      avatar: savedAccount.avatar,
      online: savedAccount.online, // 使用账号的在线状态
      ip: null, // 可能需要从其他地方获取IP
      platType: savedAccount.type.val,
      uid: savedAccount.uid, // 保存原始UID，用于唯一标识账号
    );
  }

  // 切换空间展开/折叠状态
  void toggleGroupExpanded(AccountGroup group) {
    group.expanded.value = !group.expanded.value;
  }

  // 切换账号选中状态
  void toggleAccountSelected(AccountInfo account) {
    account.isSelected = !account.isSelected;
    update(); // 通知UI更新
  }

  // 切换批量管理模式
  void toggleBatchMode() {
    isBatchMode.value = !isBatchMode.value;

    // 退出批量管理模式时，清除所有选中状态
    if (!isBatchMode.value) {
      clearAllSelection();
    }
  }

  // 全选/取消全选
  void toggleSelectAll() {
    final newValue = !isAllSelected;

    for (var group in groups) {
      for (var account in group.accounts) {
        account.isSelected = newValue;
      }
    }

    update(); // 通知UI更新
  }

  // 清除所有选中状态
  void clearAllSelection() {
    for (var group in groups) {
      for (var account in group.accounts) {
        account.isSelected = false;
      }
    }
    update();
  }

  // 获取所有空间名称
  List<String> getAllGroupNames() {
    return groups.map((group) => group.name).toList();
  }

  // 添加空间
  Future<bool> addGroup(String name) async {
    if (name.isEmpty) {
      return false;
    }

    try {
      LoggerUtil.i('开始创建空间: $name');

      final cancelFunc = showProgress();
      // 调用API创建空间
      final response = await _accountApi.createAccountGroup(name, 2);
      cancelFunc();

      if (response != null && response['data'] != null) {
        // 获取创建成功的空间数据
        final groupData = response['data'];

        if (groupData != null) {
          LoggerUtil.i('空间创建成功: ${groupData['name']}, ID: ${groupData['id']}');

          // 从API重新加载空间数据，确保显示最新数据
          await loadGroupsFromApi();

          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // 编辑空间
  Future<bool> editGroup(int index, String newName) async {
    if (index < 0 || index >= groups.length || newName.isEmpty) {
      return false;
    }

    try {
      // 获取要编辑的空间
      final group = groups[index];
      LoggerUtil.i('开始编辑空间: ${group.name} -> $newName, ID: ${group.id}');

      // 调用API更新空间
      final cancelFunc = showProgress();
      final success = await _accountApi.updateAccountGroup(
        group.id,
        newName,
        0,
      );
      cancelFunc();

      if (success) {
        LoggerUtil.i('空间编辑成功: $newName');

        // 从API重新加载空间数据，确保显示最新数据
        await loadGroupsFromApi();

        return true;
      } else {
        // 如果API更新失败，回退到本地更新
        LoggerUtil.w('API更新空间失败，回退到本地更新');
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // 删除空间
  Future<bool> deleteGroup(int index) async {
    if (index < 0 || index >= groups.length) {
      return false;
    }

    try {
      // 获取要删除的空间
      final group = groups[index];
      LoggerUtil.i('开始删除空间: ${group.name}, ID: ${group.id}');

      // 调用API删除空间
      final cancelFunc = showProgress();
      final success = await _accountApi.deleteAccountGroup([group.id]);
      cancelFunc();
      LoggerUtil.e(success);

      if (success) {
        LoggerUtil.i('空间删除成功');

        // 从API重新加载空间数据，确保显示最新数据
        await loadGroupsFromApi();

        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// 将选中的账号移动到指定空间
  Future<void> moveSelectedAccountsToGroup(String targetGroupId) async {
    try {
      LoggerUtil.i('开始将选中的账号移动到空间: $targetGroupId');

      // 获取选中的账号
      final selectedAccounts = getSelectedAccounts();
      if (selectedAccounts.isEmpty) {
        LoggerUtil.w('没有选中任何账号');
        return;
      }

      // 显示加载提示
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      // 获取所有选中账号的UID
      final uids = selectedAccounts.map((account) => account.uid).toList();

      // 调用账号服务移动账号
      bool success = await accountService.moveAccountsToGroup(
        uids,
        targetGroupId,
      );

      // 关闭加载对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      if (success) {
        // 重新加载空间数据
        await loadGroupsFromApi();

        // 清除选中状态
        clearAllSelection();

        // 显示成功提示
        showSuccess('已将选中账号移动到指定空间');

        LoggerUtil.i('移动账号到空间操作完成');
      } else {
        // 显示失败提示
        showError('API移动账号操作失败，请稍后重试');

        LoggerUtil.e('API移动账号到空间失败');
      }
    } catch (e) {
      // 关闭加载对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // 显示错误提示
      showError('移动账号时出错: $e');

      LoggerUtil.e('移动选中账号失败: $e');
    }
  }

  // 删除选中的账号
  Future<void> deleteSelectedAccounts() async {
    try {
      LoggerUtil.i('开始删除选中的账号');

      // 获取选中的账号
      final selectedAccounts = getSelectedAccounts();
      if (selectedAccounts.isEmpty) {
        LoggerUtil.w('没有选中任何账号');
        return;
      }

      // 显示加载提示
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      // 获取账号服务
      final accountService = Get.find<AccountPersistentService>();

      // 逐个删除选中的账号
      int successCount = 0;
      int failCount = 0;

      for (var account in selectedAccounts) {
        try {
          // 调用API删除账号
          final success = await accountService.deleteAccount(account);

          if (success) {
            successCount++;
          } else {
            failCount++;
          }
        } catch (e) {
          LoggerUtil.e('删除账号失败: ${account.uid}, 错误: $e');
          failCount++;
        }
      }

      // 关闭加载对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // 重新加载空间数据
      loadGroupsFromApi();

      // 显示结果提示
      if (failCount == 0) {
        showSuccess('已删除 $successCount 个账号');
      } else {
        showWarning('成功: $successCount, 失败: $failCount');
      }

      LoggerUtil.i('账号删除完成，成功: $successCount, 失败: $failCount');
    } catch (e) {
      // 关闭加载对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      // 显示错误提示
      showError('删除账号时出错: $e');

      LoggerUtil.e('删除选中账号失败: $e');
      throw Exception('删除账号失败: $e');
    }
  }

  // 刷新账号列表
  void refreshAccountList() {}

  // 更新选中账号的昵称
  void updateSelectedAccountsNickname(List<String> uids, String newNickname) {
    try {
      // 更新持久化存储中的账号昵称
      for (var uid in uids) {
        final accountIndex = accountService.accounts.indexWhere(
          (account) => account.uid == uid,
        );
        if (accountIndex >= 0) {
          accountService.accounts[accountIndex].nickname = newNickname;
        }
      }

      // 保存更改
      accountService.saveAccounts();

      LoggerUtil.i('已更新 ${uids.length} 个账号的昵称为: $newNickname');
    } catch (e) {
      LoggerUtil.e('更新账号昵称失败: $e');
    }
  }

  /// 更新选中账号的名称
  Future<bool> updateSelectedAccountsName(
    List<String> uids,
    String newName,
  ) async {
    try {
      LoggerUtil.i('开始更新账号名称，账号数: ${uids.length}, 新名称: $newName');

      // 获取账号服务
      final accountService = Get.find<AccountPersistentService>();

      int successCount = 0;
      int failCount = 0;

      // 遍历所有需要更新的账号
      for (var uid in uids) {
        try {
          // 查找账号
          final account = accountService.accounts.firstWhereOrNull(
            (account) => account.uid == uid,
          );
          if (account != null) {
            // 更新账号名称
            account.account = newName;

            // 调用API更新账号
            final success = await accountService.updateAccountInfo(account);

            if (success) {
              successCount++;

              // 更新UI中的账号名称
              for (var group in groups) {
                final accountInfo = group.accounts.firstWhereOrNull(
                  (a) => a.uid == uid,
                );
                if (accountInfo != null) {
                  accountInfo.name = newName;
                }
              }
            } else {
              failCount++;
            }
          } else {
            LoggerUtil.e('找不到UID为 $uid 的账号');
            failCount++;
          }
        } catch (e) {
          LoggerUtil.e('更新账号名称失败: $e');
          failCount++;
        }
      }

      // 更新UI
      update();

      LoggerUtil.i('更新账号名称完成，成功: $successCount, 失败: $failCount');

      // 如果全部成功或部分成功，返回true
      return successCount > 0;
    } catch (e) {
      LoggerUtil.e('更新账号名称失败: $e');
      return false;
    }
  }

  // 批量删除空间
  Future<bool> deleteGroups(List<int> indexes) async {
    if (indexes.isEmpty) {
      return false;
    }

    try {
      // 收集要删除的空间ID
      final List<String> groupIds = [];
      final List<String> groupNames = [];

      for (int index in indexes) {
        if (index >= 0 && index < groups.length) {
          groupIds.add(groups[index].id);
          groupNames.add(groups[index].name);
        }
      }

      if (groupIds.isEmpty) {
        return false;
      }

      LoggerUtil.i('开始批量删除空间: ${groupNames.join(", ")}');

      // 调用API批量删除空间
      final success = await _accountApi.deleteAccountGroups(groupIds);

      if (success) {
        LoggerUtil.i('空间批量删除成功');

        // 从API重新加载空间数据，确保显示最新数据
        await loadGroupsFromApi();

        // 显示成功提示
        showSuccess('已删除空间: ${groupNames.join(", ")}');

        return true;
      } else {
        // 如果API删除失败，回退到本地删除
        LoggerUtil.w('API批量删除空间失败，回退到本地删除');
        return _deleteLocalGroups(indexes);
      }
    } catch (e) {
      LoggerUtil.e('批量删除空间失败: $e');
      // 出错时回退到本地删除
      return _deleteLocalGroups(indexes);
    }
  }

  // 本地批量删除空间（作为备选方案）
  bool _deleteLocalGroups(List<int> indexes) {
    try {
      if (indexes.isEmpty) {
        return false;
      }

      // 按照索引从大到小排序，避免删除时索引变化
      indexes.sort((a, b) => b.compareTo(a));

      final List<String> groupNames = [];

      for (int index in indexes) {
        if (index >= 0 && index < groups.length) {
          // 记录空间名称
          groupNames.add(groups[index].name);

          // 获取对应的持久化空间ID
          final groupId = accountService.groups[index].id;

          // 在持久化存储中删除空间
          accountService.deleteGroup(groupId);

          // 在UI中删除空间
          groups.removeAt(index);
        }
      }

      if (groupNames.isEmpty) {
        return false;
      }

      // 显示成功提示
      showSuccess('已删除本地空间: ${groupNames.join(", ")}');

      return true;
    } catch (e) {
      LoggerUtil.e('本地批量删除空间失败: $e');
      return false;
    }
  }

  /// 获取选中的账号
  List<AccountUserInfoModle> getSelectedAccounts() {
    final List<AccountUserInfoModle> selectedAccounts = [];

    // 获取账号服务
    final accountService = Get.find<AccountPersistentService>();

    // 遍历所有空间
    for (var group in groups) {
      // 遍历空间中的账号
      for (var accountInfo in group.accounts) {
        // 如果账号被选中
        if (accountInfo.isSelected) {
          // 使用UID查找原始账号对象
          final originalAccount = accountService.accounts.firstWhereOrNull(
            (account) => account.uid == accountInfo.uid,
          );

          // 如果找到原始账号，添加到选中列表
          if (originalAccount != null) {
            selectedAccounts.add(originalAccount);
          }
        }
      }
    }

    LoggerUtil.i('获取到 ${selectedAccounts.length} 个选中的账号');
    return selectedAccounts;
  }

  /// 根据空间名称获取空间ID
  String? getGroupIdByName(String groupName) {
    final group = groups.firstWhereOrNull((group) => group.name == groupName);
    return group?.id;
  }
}
