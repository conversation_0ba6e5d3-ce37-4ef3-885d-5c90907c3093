import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 账号批量管理底部弹窗
class AccountBatchManagerSheet extends StatelessWidget {
  final AccountListController controller;

  AccountBatchManagerSheet({super.key})
    : controller = Get.find<AccountListController>();

  @override
  Widget build(BuildContext context) {
    // 批量管理模式自动开启
    controller.isBatchMode.value = true;

    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // 顶部标题栏
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: GetBuilder<AccountListController>(
              builder:
                  (controller) => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () {
                          controller.isBatchMode.value = false; // 关闭批量管理模式
                          controller.clearAllSelection(); // 清除所有选择
                          Get.back(); // 关闭底部弹窗
                        },
                      ),
                      Text(
                        '已选择${controller.selectedCount}项',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.playlist_add_check),
                        onPressed: () {
                          // 全选/取消全选切换
                          controller.toggleSelectAll();
                        },
                      ),
                    ],
                  ),
            ),
          ),

          // 账号列表
          Expanded(
            child: GetBuilder<AccountListController>(
              builder:
                  (controller) => ListView.builder(
                    itemCount: controller.groups.length,
                    itemBuilder: (context, groupIndex) {
                      final group = controller.groups[groupIndex];

                      if (group.accounts.isEmpty) {
                        return const SizedBox.shrink();
                      }

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 空间标题
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16.0,
                              vertical: 8.0,
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  group.expanded.value
                                      ? Icons.keyboard_arrow_down
                                      : Icons.keyboard_arrow_right,
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                GestureDetector(
                                  onTap:
                                      () =>
                                          controller.toggleGroupExpanded(group),
                                  child: Text(
                                    group.name,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // 账号列表
                          if (group.expanded.value)
                            Column(
                              children:
                                  group.accounts
                                      .map(
                                        (account) => _buildAccountItem(account),
                                      )
                                      .toList(),
                            ),
                        ],
                      );
                    },
                  ),
            ),
          ),

          // 底部操作栏
          GetBuilder<AccountListController>(
            builder: (controller) {
              if (controller.selectedCount <= 0) {
                return const SizedBox.shrink();
              }

              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SafeArea(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildActionButton(
                        icon: Icons.folder_open,
                        label: '移动空间',
                        onPressed: _showMoveToGroupDialog,
                      ),
                      _buildActionButton(
                        icon: Icons.note_add,
                        label: '添加备注',
                        onPressed: _showBatchEditNicknameDialog,
                      ),
                      _buildActionButton(
                        icon: Icons.delete_outline,
                        label: '删除',
                        onPressed: _showDeleteConfirmDialog,
                      ),
                      _buildActionButton(
                        icon: Icons.settings,
                        label: '设置代理',
                        onPressed: () {
                          // 在实际应用中实现设置代理功能
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 24),
          const SizedBox(height: 4),
          Text(label, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  // 构建账号项
  Widget _buildAccountItem(AccountInfo account) {
    // 设置离线状态的颜色
    final Color textColor = account.online ? Colors.black : Colors.grey;
    final Color avatarColor = account.online ? Colors.grey : Colors.grey[400]!;

    return ListTile(
      leading: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 选择框
          SizedBox(
            width: 24,
            height: 24,
            child: Checkbox(
              value: account.isSelected,
              onChanged: (value) {
                controller.toggleAccountSelected(account);
              },
            ),
          ),
          const SizedBox(width: 12),
          // 头像和平台图标
          Stack(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundColor: Colors.grey[300],
                backgroundImage:
                    account.avatar != null
                        ? NetworkImage(account.avatar!)
                        : null,
                child:
                    account.avatar == null
                        ? Icon(Icons.person, color: avatarColor)
                        : null,
              ),
              Positioned(
                right: 0,
                bottom: 0,
                child: Container(
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white, width: 1.5),
                  ),
                  child: Opacity(
                    opacity: account.online ? 1.0 : 0.6,
                    child: Image.asset(
                      'assets/images/account/plat_icon/${account.platType}.png',
                      width: 14,
                      height: 14,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      title: Row(
        children: [
          Text(
            account.nickname ?? '',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: textColor,
            ),
          ),
          // if (account.nickname != null) ...[
          //   const SizedBox(width: 4),
          //   Text(
          //     '(${account.nickname})',
          //     style: TextStyle(
          //       fontSize: 14,
          //       color: account.online ? Colors.grey[600] : Colors.grey[400],
          //       overflow: TextOverflow.ellipsis,
          //     ),
          //   ),
          // ],
        ],
      ),
      subtitle: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: account.online ? Colors.green : Colors.grey,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            '[${account.online ? '在线' : '离线'}]',
            style: TextStyle(
              fontSize: 12,
              color: account.online ? Colors.grey[800] : Colors.grey,
            ),
          ),
          if (account.ip != null) ...[
            const SizedBox(width: 4),
            Text(
              account.ip!,
              style: TextStyle(
                fontSize: 12,
                color: account.online ? Colors.grey[800] : Colors.grey,
              ),
            ),
          ],
        ],
      ),
      onTap: () {
        controller.toggleAccountSelected(account);
      },
    );
  }

  // 显示移动到空间对话框
  void _showMoveToGroupDialog() {
    if (controller.selectedCount <= 0) return;

    final groupNames = controller.getAllGroupNames();
    String? selectedGroup;

    showDialog(
      context: Get.context!,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('选择目标空间'),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: groupNames.length,
                  itemBuilder: (context, index) {
                    return RadioListTile<String>(
                      title: Text(groupNames[index]),
                      value: groupNames[index],
                      groupValue: selectedGroup,
                      onChanged: (value) {
                        setState(() {
                          selectedGroup = value;
                        });
                      },
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () {
                    if (selectedGroup != null) {
                      controller.moveSelectedAccountsToGroup(selectedGroup!);
                      Navigator.pop(context);

                      // 显示提示
                      showSuccess('已将选中账号移动到 $selectedGroup');
                    }
                  },
                  child: const Text('确定'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 显示批量编辑备注对话框
  void _showBatchEditNicknameDialog() {
    if (controller.selectedCount <= 0) return;

    final TextEditingController nicknameController = TextEditingController();

    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          title: const Text('批量添加修改备注'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('将为选中的 ${controller.selectedCount} 个账号修改备注'),
              const SizedBox(height: 16),
              TextField(
                controller: nicknameController,
                decoration: const InputDecoration(
                  hintText: '请输入备注',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                final String newName = nicknameController.text.trim();

                if (newName.isNotEmpty) {
                  // 收集所有选中账号的UID
                  final selectedUids = <String>[];

                  // 更新所有选中账号的名称
                  for (var group in controller.groups) {
                    for (var account in group.accounts) {
                      if (account.isSelected) {
                        account.name = newName;
                        // 收集UID用于更新持久化存储
                        if (account.uid.isNotEmpty) {
                          selectedUids.add(account.uid);
                        }
                      }
                    }
                  }

                  // 更新持久化存储中的账号昵称
                  controller.updateSelectedAccountsName(selectedUids, newName);

                  controller.update();
                  Navigator.pop(context);

                  // 显示提示
                  showSuccess('已为选中账号添加备注');
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog() {
    if (controller.selectedCount <= 0) return;

    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          title: const Text('删除账号'),
          content: Text('确定要删除选中的 ${controller.selectedCount} 个账号吗？此操作不可恢复。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                final selectedCount = controller.selectedCount;
                controller.deleteSelectedAccounts();
                Navigator.pop(context);
                // 关闭批量管理弹窗
                Get.back();

                // 显示提示
                showSuccess('已删除 $selectedCount 个账号');
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}

// 显示批量管理底部弹窗的辅助方法
void showBatchManagerSheet() {
  Get.bottomSheet(
    AccountBatchManagerSheet(),
    isScrollControlled: true,
    enableDrag: true,
  );
}
