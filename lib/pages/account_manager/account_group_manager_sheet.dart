import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 账号空间管理底部弹窗
class AccountGroupManagerSheet extends StatelessWidget {
  final AccountListController controller;
  final TextEditingController _groupNameController = TextEditingController();
  final AccountPersistentService _accountService =
      Get.find<AccountPersistentService>();

  AccountGroupManagerSheet({super.key})
    : controller = Get.find<AccountListController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.75,
      ),
      padding: const EdgeInsets.only(top: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部标题栏
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    Get.back(); // 关闭底部弹窗
                  },
                  child: const Text('取消'),
                ),
                const Text(
                  '空间管理',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                ),
                TextButton(
                  onPressed: () {
                    Get.back(); // 关闭底部弹窗
                  },
                  child: const Text('确定'),
                ),
              ],
            ),
          ),

          // 空间列表
          Flexible(
            child: Obx(
              () => ListView.builder(
                shrinkWrap: true,
                itemCount: controller.groups.length,
                itemBuilder: (context, index) {
                  return _buildGroupItem(
                    context,
                    controller.groups[index],
                    index,
                  );
                },
              ),
            ),
          ),

          // 添加空间按钮
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _showAddGroupDialog(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black87,
                  elevation: 1,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey[300]!),
                  ),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.add_circle_outline, size: 20),
                    SizedBox(width: 8),
                    Text('添加空间', style: TextStyle(fontSize: 16)),
                  ],
                ),
              ),
            ),
          ),

          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  // 构建空间项
  Widget _buildGroupItem(BuildContext context, AccountGroup group, int index) {
    return ListTile(
      title: Text(group.name, style: const TextStyle(fontSize: 16)),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 编辑按钮
          // IconButton(
          //   icon: const Icon(Icons.people, color: Colors.blue),
          //   onPressed: () {
          //     // 可以添加查看空间成员的功能
          //   },
          // ),
          // 编辑按钮
          IconButton(
            icon: const Icon(Icons.edit_outlined, color: Colors.black54),
            onPressed: () => _showEditGroupDialog(context, index, group.name),
          ),
          // 删除按钮
          IconButton(
            icon: const Icon(Icons.delete_outline, color: Colors.black54),
            onPressed: () => _showDeleteConfirmDialog(context, index),
          ),
        ],
      ),
    );
  }

  // 显示添加空间对话框
  void _showAddGroupDialog(BuildContext context) {
    final TextEditingController nameController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('添加空间'),
          content: TextField(
            controller: nameController,
            decoration: const InputDecoration(
              hintText: '请输入空间名称',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                final String name = nameController.text.trim();
                if (name.isEmpty) return;

                // 关闭对话框
                Navigator.pop(context);

                // 添加空间（现在是异步方法）
                var success = await Get.find<AccountListController>().addGroup(
                  name,
                );
                if (!success) {
                  showError('networkError'.tr);
                } else {
                  showSuccess('空间创建成功！');
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 显示编辑空间对话框
  void _showEditGroupDialog(
    BuildContext context,
    int index,
    String currentName,
  ) {
    final TextEditingController nameController = TextEditingController(
      text: currentName,
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('编辑空间'),
          content: TextField(
            controller: nameController,
            decoration: const InputDecoration(
              hintText: '请输入空间名称',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                final String newName = nameController.text.trim();
                if (newName.isEmpty) return;

                // 关闭对话框
                Navigator.pop(context);

                // 编辑空间（现在是异步方法）
                final success = await Get.find<AccountListController>()
                    .editGroup(index, newName);

                if (!success) {
                  showError('networkError'.tr);
                } else {
                  showSuccess('已更新空间名称: $newName');
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('删除空间'),
          content: Text(
            '确定要删除"${controller.groups[index].name}"空间吗？该空间下的账号将移至默认空间。',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () async {
                // 关闭对话框
                Navigator.pop(context);

                // 删除空间（现在是异步方法）
                final success = await controller.deleteGroup(index);

                if (!success) {
                  showError('networkError'.tr);
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}

// 显示空间管理底部弹窗的辅助方法
void showGroupManagerSheet() {
  Get.bottomSheet(
    AccountGroupManagerSheet(),
    isScrollControlled: true,
    enableDrag: true,
  );
}
