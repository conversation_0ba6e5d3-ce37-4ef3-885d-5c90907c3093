import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/pages/account_manager/subpages/fingerprint_details/state.dart';
import 'package:get/get.dart';

class FingerprintDetailsLogic extends GetxController {
  final FingerprintDetailsState state = FingerprintDetailsState();

  var accountController = Get.put(AccountListController());

  @override
  void onReady() {
    super.onReady();
    state.spaceId = Get.arguments['spaceId'] ?? '';

    accountController.groups.value
        .where((group) => group.id == state.spaceId)
        .forEach((group) {
          state.spaceData.value = group;
        });
  }
}
