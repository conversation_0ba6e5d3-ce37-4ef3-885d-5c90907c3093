import 'dart:convert';

import 'package:aitoearn_app/pages/account_manager/subpages/fingerprint_details/logic.dart';
import 'package:aitoearn_app/pages/account_manager/subpages/fingerprint_details/state.dart';
import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_json_view/flutter_json_view.dart';
import 'package:get/get.dart';

class FingerprintDetailsPage extends StatefulWidget {
  const FingerprintDetailsPage({super.key});

  @override
  State<FingerprintDetailsPage> createState() => _FingerprintDetailsPageState();
}

class _FingerprintDetailsPageState extends State<FingerprintDetailsPage> {
  final FingerprintDetailsLogic logic = Get.put(FingerprintDetailsLogic());
  final FingerprintDetailsState state =
      Get.find<FingerprintDetailsLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => BasePage(
        body:
            state.spaceData.value?.browserConfig == null
                ? const SizedBox()
                : JsonView.string(
                  jsonEncode(state.spaceData.value?.browserConfig),
                ),
        title: '${state.spaceData.value?.name} - 指纹详情',
        useDefaultBackground: false,
        backgroundColor: Colors.white,
      ),
    );
  }

  @override
  void dispose() {
    Get.delete<FingerprintDetailsLogic>();
    super.dispose();
  }
}
