import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/account_manager/account_add_dialog.dart';
import 'package:aitoearn_app/pages/account_manager/account_batch_manager_sheet.dart';
import 'package:aitoearn_app/pages/account_manager/account_group_manager_sheet.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/pages/publish/widgets/tabs_calendar.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_option.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_pop_helper.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 账号列表页面
class AccountListPage extends StatefulWidget {
  const AccountListPage({super.key});

  @override
  State<AccountListPage> createState() => _AccountListPageState();
}

class _AccountListPageState extends State<AccountListPage> {
  late AccountListController controller;
  final RxBool _isLoading = true.obs; // 默认为加载状态

  @override
  void initState() {
    super.initState();
    controller = Get.put(AccountListController());

    // 延迟执行，确保页面已经构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAccountData();
    });
  }

  // 加载账号数据
  Future<void> _loadAccountData() async {
    try {
      LoggerUtil.i('开始加载账号数据');

      // 显示加载状态
      _isLoading.value = true;

      // 从API获取账号列表
      final accountService = Get.find<AccountPersistentService>();
      final success = await accountService.fetchAccountsFromApi();
      await accountService.updateAccountsUserInfo();
      if (success) {
        // 重新加载空间数据
        await controller.loadGroupsFromApi();
        LoggerUtil.i('加载账号数据成功');
      } else {
        showError('networkError'.tr);
      }
    } catch (e) {
      LoggerUtil.e('加载账号数据出错: $e');
    } finally {
      // 隐藏加载状态
      _isLoading.value = false;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.person_add_alt),
                  onPressed: () {
                    // 显示添加账号弹窗
                    AccountAddDialog.show(context);
                  },
                ),
                _showMoreOptions(context),
              ],
            ),
            TabsCalendar(selectedIndex: 2),
          ],
        ),
        centerTitle: true,
      ),
      body: Obx(
        () => Stack(
          children: [
            // 当加载完成后显示列表内容
            !_isLoading.value
                ? RefreshIndicator(
                  onRefresh: () async {
                    return controller.refreshAccountsStatus();
                  },
                  child: Column(
                    children: [
                      // 账号列表
                      Expanded(
                        child: Obx(() {
                          if (controller.groups.isEmpty) {
                            return _buildEmptyView();
                          } else {
                            return ListView.builder(
                              itemCount: controller.groups.length,
                              itemBuilder: (context, index) {
                                return _buildGroupItem(
                                  context,
                                  controller.groups[index],
                                );
                              },
                            );
                          }
                        }),
                      ),
                    ],
                  ),
                )
                : const SizedBox.shrink(),

            // 全屏加载指示器
            if (_isLoading.value)
              Container(
                color: Colors.white,
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(height: 16),
                      // Text('正在加载账号数据...', style: TextStyle(fontSize: 16)),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 构建空视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'account.empty'.tr,
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'account.empty.hint'.tr,
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  // 构建空间项
  Widget _buildGroupItem(BuildContext context, AccountGroup group) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 空间标题
        InkWell(
          onTap: () {
            Get.find<AccountListController>().toggleGroupExpanded(group);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: Row(
              children: [
                Obx(
                  () => Icon(
                    group.expanded.value
                        ? Icons.keyboard_arrow_down
                        : Icons.keyboard_arrow_right,
                    size: 24,
                    color: Colors.black54,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  group.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 8),
                if (group.isDefault)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue[100],
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      'account.default'.tr,
                      style: const TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                  ),
                const Spacer(),

                SizedBox(
                  height: 30,
                  width: 30,
                  child: IconButton(
                    padding: const EdgeInsets.all(0.0),
                    icon: const Icon(Icons.add, size: 22),
                    tooltip: '添加账号',
                    onPressed: () {
                      AccountAddDialog.show(context, spaceId: group.id);
                    },
                  ),
                ),
                Gaps.hGap4,
                SizedBox(
                  height: 30,
                  width: 30,
                  child: IconButton(
                    padding: const EdgeInsets.all(0.0),
                    tooltip: '查看空间详情',
                    icon: const Icon(Icons.visibility, size: 22),
                    onPressed: () {
                      Get.toNamed(
                        AppRouter.fingerprintDetailsPath,
                        arguments: {'spaceId': group.id},
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),

        // 账号列表（展开时显示）
        Obx(() {
          if (!group.expanded.value) {
            return const SizedBox.shrink();
          }

          return Column(
            children: [
              ...group.accounts.map((account) => _buildAccountItem(account)),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                child: CustomButton(
                  onPressed: () {
                    AccountAddDialog.show(context, spaceId: group.id);
                  },
                  text: '关联账号',
                  icon: Transform.rotate(
                    angle: 1.5708, // 90度，单位是弧度
                    child: const Icon(
                      Icons.link,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  textColor: AppColors.primaryColor,
                  color: const Color.fromARGB(255, 243, 234, 255),
                ),
              ),
            ],
          );
        }),
      ],
    );
  }

  // 构建账号项
  Widget _buildAccountItem(AccountInfo account) {
    // 设置离线状态的颜色
    final Color textColor = account.online ? Colors.black : Colors.grey;
    final Color avatarColor = account.online ? Colors.grey : Colors.grey[400]!;

    return ListTile(
      leading: Stack(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.grey[300],
            backgroundImage:
                account.avatar != '' && account.avatar != null
                    ? NetworkImage(account.avatar!)
                    : null,
            child:
                account.avatar == '' || account.avatar == null
                    ? Icon(Icons.person, color: avatarColor)
                    : null,
          ),
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(1),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.white, width: 1.5),
              ),
              child: Opacity(
                opacity: account.online ? 1.0 : 0.6,
                child: Image.asset(
                  'assets/images/account/plat_icon/${account.platType}.png',
                  width: 16,
                  height: 16,
                ),
              ),
            ),
          ),
        ],
      ),
      title: Row(
        children: [
          Text(
            account.nickname ?? '',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: textColor,
            ),
          ),
          // if (account.nickname != null) ...[
          //   const SizedBox(width: 4),
          //   Text(
          //     '(${account.nickname})',
          //     style: TextStyle(
          //       fontSize: 14,
          //       color: account.online ? Colors.grey[600] : Colors.grey[400],
          //       overflow: TextOverflow.ellipsis,
          //     ),
          //   ),
          // ],
        ],
      ),
      subtitle:
          account.ip != null
              ? Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: account.online ? Colors.green : Colors.grey,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '[${account.online ? 'account.online'.tr : 'account.offline'.tr}]',
                    style: TextStyle(
                      fontSize: 12,
                      color: account.online ? Colors.grey[800] : Colors.grey,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    account.ip!,
                    style: TextStyle(
                      fontSize: 12,
                      color: account.online ? Colors.grey[800] : Colors.grey,
                    ),
                  ),
                ],
              )
              : Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: account.online ? Colors.green : Colors.grey,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '[${account.online ? 'account.online'.tr : 'account.offline'.tr}]',
                    style: TextStyle(
                      fontSize: 12,
                      color: account.online ? Colors.grey[800] : Colors.grey,
                    ),
                  ),
                ],
              ),
      trailing: IconButton(
        icon: const Icon(Icons.more_vert, color: Colors.grey),
        onPressed: () {
          // 账号操作菜单
          _showAccountOptions(account);
        },
      ),
      onTap: () {
        // 点击账号进入详情
      },
    );
  }

  // 显示更多选项菜单
  _showMoreOptions(BuildContext context) {
    final menuKey = GlobalKey();

    return (IconButton(
      icon: const Icon(Icons.more_horiz),
      key: menuKey,
      onPressed: () {
        MenuPopHelper.showMenuByKey(
          context: context,
          key: menuKey,
          options: [
            MenuOption(
              icon: Icons.folder_copy_outlined,
              name: 'account.group.manage'.tr,
              onTap:
                  () => // 显示空间管理底部弹窗
                      Future.delayed(const Duration(milliseconds: 10), () {
                    showGroupManagerSheet();
                  }),
            ),
            MenuOption(
              icon: Icons.playlist_add_check_outlined,
              name: 'account.batch.manage'.tr,
              onTap:
                  () => Future.delayed(const Duration(milliseconds: 10), () {
                    showBatchManagerSheet();
                  }),
            ),
          ],
        );
      },
    ));
  }

  // 显示账号操作选项
  void _showAccountOptions(AccountInfo account) {
    showModalBottomSheet(
      context: Get.context!,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.edit_note_outlined),
                title: Text('account.edit.note'.tr),
                onTap: () {
                  Navigator.pop(context);
                  // 添加修改备注
                  _showEditNicknameDialog(account);
                },
              ),
              ListTile(
                leading: const Icon(Icons.swap_horiz),
                title: Text('account.move.group'.tr),
                onTap: () {
                  Navigator.pop(context);
                  // 移动空间
                  _showMoveAccountDialog(account);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete_outline),
                title: Text('account.delete'.tr),
                onTap: () {
                  Navigator.pop(context);
                  // 删除账号
                  _showDeleteAccountDialog(account);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 显示编辑备注对话框
  void _showEditNicknameDialog(AccountInfo account) {
    final TextEditingController nicknameController = TextEditingController(
      text: account.name,
    );

    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          title: Text('account.edit.note'.tr),
          content: TextField(
            controller: nicknameController,
            decoration: InputDecoration(
              hintText: 'account.edit.note'.tr,
              border: const OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text('account.cancel'.tr),
            ),
            TextButton(
              onPressed: () async {
                final String newName = nicknameController.text.trim();
                if (newName.isEmpty) return;

                // 关闭对话框
                Navigator.pop(context);

                try {
                  // 显示加载提示
                  Get.dialog(
                    const Center(child: CircularProgressIndicator()),
                    barrierDismissible: false,
                  );

                  // 获取控制器
                  final controller = Get.find<AccountListController>();

                  // 更新账号名称
                  final success = await controller.updateSelectedAccountsName([
                    account.uid,
                  ], newName);

                  // 关闭加载对话框
                  Get.back();

                  if (success) {
                    // 显示成功提示
                    showSuccess('account.update.note.success'.tr);
                  } else {
                    showError('account.update.note.failed'.tr);
                  }
                } catch (e) {
                  // 关闭加载对话框
                  if (Get.isDialogOpen ?? false) {
                    Get.back();
                  }

                  // 显示错误提示
                  showError('account.update.note.failed'.tr);

                  LoggerUtil.e('更新账号备注失败: $e');
                }
              },
              child: Text('account.confirm'.tr),
            ),
          ],
        );
      },
    );
  }

  // 显示移动账号对话框
  void _showMoveAccountDialog(AccountInfo account) {
    final controller = Get.find<AccountListController>();
    final groupNames = controller.getAllGroupNames();
    String? selectedGroup;

    showDialog(
      context: Get.context!,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('account.select.group'.tr),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: groupNames.length,
                  itemBuilder: (context, index) {
                    return RadioListTile<String>(
                      title: Text(groupNames[index]),
                      value: groupNames[index],
                      groupValue: selectedGroup,
                      onChanged: (value) {
                        setState(() {
                          selectedGroup = value;
                        });
                      },
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text('account.cancel'.tr),
                ),
                TextButton(
                  onPressed: () async {
                    if (selectedGroup != null) {
                      // 关闭对话框
                      Navigator.pop(context);

                      try {
                        // 获取目标空间ID
                        final targetGroupId = controller.getGroupIdByName(
                          selectedGroup!,
                        );
                        if (targetGroupId == null) {
                          // 关闭加载对话框
                          Get.back();

                          // 显示错误提示
                          showError('account.move.group.notfound'.tr);
                          return;
                        }

                        // 临时选中该账号
                        account.isSelected = true;

                        // 移动账号（现在是异步方法）
                        await controller.moveSelectedAccountsToGroup(
                          targetGroupId,
                        );

                        // 清除选中状态
                        account.isSelected = false;
                      } catch (e) {
                        // 关闭加载对话框
                        if (Get.isDialogOpen ?? false) {
                          Get.back();
                        }

                        // 显示错误提示
                        showError(
                          'account.move.failed.retry'.trParams({
                            'error': e.toString(),
                          }),
                        );

                        LoggerUtil.e('移动账号失败: $e');
                      }
                    }
                  },
                  child: Text('account.confirm'.tr),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 显示删除账号确认对话框
  void _showDeleteAccountDialog(AccountInfo account) {
    final controller = Get.find<AccountListController>();

    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          title: Text('account.delete'.tr),
          content: Text('account.delete.confirm'.tr),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text('account.cancel'.tr),
            ),
            TextButton(
              onPressed: () async {
                // 关闭对话框
                Navigator.pop(context);

                // 选中要删除的账号
                account.isSelected = true;

                try {
                  // 删除账号（现在是异步方法）
                  await controller.deleteSelectedAccounts();
                } catch (e) {
                  // 错误处理已在deleteSelectedAccounts方法中完成
                  LoggerUtil.e('删除账号失败: $e');
                } finally {
                  // 清除选中状态
                  account.isSelected = false;
                }
              },
              child: Text('account.confirm'.tr),
            ),
          ],
        );
      },
    );
  }
}
