
import 'package:aitoearn_app/config/app_config.dart';
import 'package:aitoearn_app/i18n/language_service.dart';
import 'package:aitoearn_app/pages/setting/setting_page_controller.dart';
import 'package:aitoearn_app/pages/setting/widgets/setting_item.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingList extends GetView<SettingController> {
  const SettingList({super.key});

  @override
  Widget build(BuildContext context) {
    // 设置选项列表
    List<Widget> settingItems = [
      // 意见反馈
      SettingItem(
        centerWidget: 'setting.feedback'.tr.toNormalText(),
        onTap: () {
          Get.toNamed(AppRouter.webPath, arguments: {
            'url': AppConfig.appHost,
            'title': 'setting.feedback'.tr,
          });
        },
      ),

      // 通知消息开关
      Obx(
        () => SettingItem(
          centerWidget: 'setting.notification'.tr.toNormalText(),
          rightWidget: Transform.scale(
            scale: 0.8, // 缩小到85%大小
            child: Switch(
              padding: EdgeInsets.zero,
              value: controller.notificationEnabled.value,
              onChanged: (value) {
                controller.toggleNotification(value);
              },
              activeColor: AppColors.primaryHighLightColor,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap, // 减小点击区域
            ),
          ),
        ),
      ),

      // 语言切换选项（按要求不做国际化处理）
      SettingItem(
        centerWidget: 'setting.language'.tr.toNormalText(),
        rightWidget: [
          LanguageService.getCurrentLanguageText().toNormalText(color: AppColors.textSecondColor),
          const Icon(Icons.chevron_right, color: AppColors.textHintColor)
        ].toRow(),
        onTap: () {
          controller.showLanguagePicker();
        },
      ),

      // 服务条款
      SettingItem(
        centerWidget: 'setting.terms'.tr.toNormalText(),
        onTap: () {
          Get.toNamed(AppRouter.webPath, arguments: {
            'url': '/en/websit/terms-of-service',
            'title': 'setting.terms'.tr,
          });
        },
      ),

      // 隐私权政策
      SettingItem(
        centerWidget: 'setting.privacy'.tr.toNormalText(),
        onTap: () {
          Get.toNamed(AppRouter.webPath, arguments: {
            'url': '/en/websit/privacy-policy',
            'title': 'setting.privacy'.tr,
          });
        },
      ),

      // 给我们评分
      SettingItem(
        centerWidget: 'setting.rateUs'.tr.toNormalText(),
        onTap: () {
          showToast('评分功能开发中'); // 按要求不做国际化
        },
      ),

      // 关于哎呦赚
      SettingItem(
        centerWidget: 'setting.about'.tr.toNormalText(),
        rightWidget: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(() => Text(
              controller.appVersion.value,
              style: const TextStyle(color: AppColors.textHintColor, fontSize: 14),
            )),
            const SizedBox(width: 8),
            const Icon(Icons.chevron_right, color: AppColors.textHintColor),
          ],
        ),
        onTap: () {
          showToast('关于页面开发中'); // 按要求不做国际化
        },
      ),
    ];

    return settingItems
        .toColumnSpace(space: 1)
        .padding(vertical: 5)
        .decorated(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        );
  }
}
