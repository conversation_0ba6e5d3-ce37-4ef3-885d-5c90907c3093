
import 'package:aitoearn_app/pages/setting/setting_page_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 设置项组件
/// 支持左中右三部分布局、完整点击区域和灵活高度
class SettingItem extends StatelessWidget {
  final Widget? leftWidget;
  final Widget? centerWidget;
  final Widget? rightWidget;
  final VoidCallback? onTap;
  final MainAxisAlignment mainAxisAlignment;
  final double horizontalPadding;
  final double verticalPadding;
  final double minHeight;
  final Color? backgroundColor;

  const SettingItem({
    super.key,
    this.leftWidget,
    this.centerWidget,
    this.rightWidget,
    this.onTap,
    this.mainAxisAlignment = MainAxisAlignment.spaceBetween,
    this.horizontalPadding = 12,
    this.verticalPadding = 4,
    this.minHeight = 46,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final children = <Widget>[];

    // 左侧组件
    if (leftWidget != null) {
      children.add(leftWidget!);
    }

    // 中间组件（占满剩余空间）
    if (centerWidget != null) {
      children.add(Expanded(child: centerWidget!));
    }

    // 右侧组件
    if (rightWidget != null) {
      children.add(rightWidget!);
    } else if (onTap != null) {
      children.add(
        const Icon(Icons.chevron_right, color: AppColors.textHintColor),
      );
    }

    // 构建主容器
    final content = children
        .toRow(
          mainAxisAlignment: mainAxisAlignment,
          crossAxisAlignment: CrossAxisAlignment.center,
        )
        .paddingSymmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding,
        )
        .constrained(minHeight: minHeight);

    // 如果有点击事件，用InkWell包裹以提供点击反馈
    return onTap != null
        ? InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(8),
            splashColor: Colors.grey[200],
            child: content,
          )
        : content;
  }
}
