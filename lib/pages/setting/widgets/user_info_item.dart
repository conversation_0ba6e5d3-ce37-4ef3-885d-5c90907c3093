import 'dart:io';

import 'package:aitoearn_app/pages/setting/widgets/setting_item.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/user_avatar.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserInfoItem extends StatelessWidget {
  const UserInfoItem({super.key});

  @override
  Widget build(BuildContext context) {
    final userStore = Get.find<UserStoreService>();

    return Obx(() {
          final userInfo = userStore.userInfo.value;
          return [
            // 头像设置项
            SettingItem(
              centerWidget: 'userInfo.avatar'.tr.toNormalText(),
              rightWidget: UserAvatar(
                size: 36,
                imageUrl: userInfo?.avatar ?? '',
                errorWidget: Image.asset('assets/images/logo.png').paddingAll(3),
              ),
            ),
            // 昵称设置项
            SettingItem(
              centerWidget: 'userInfo.nickname'.tr.toNormalText(),
              rightWidget: (userInfo?.name ?? 'userInfo.defaultName'.tr).toNormalText(),
            ),
            // 账号设置项
            SettingItem(
              centerWidget: 'userInfo.account'.tr.toNormalText(),
              rightWidget: _formatPhone(userInfo?.phone ?? userInfo?.mail ?? ''),
            ),
          ].toColumnSpace(space: 1)
              .padding(vertical: 5)
              .decorated(color: Colors.white, borderRadius: BorderRadius.circular(8));
        });
  }

  // 格式化手机号，隐藏中间4位
  Widget _formatPhone(String accountId) {
    if (accountId.length >= 11) {
      return '${accountId.substring(0, 3)}****${accountId.substring(7)}'.toNormalText(
        color: AppColors.textSecondColor,
      );
    }
    return accountId.toNormalText(color: AppColors.textSecondColor);
  }
}
