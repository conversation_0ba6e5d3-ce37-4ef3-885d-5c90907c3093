
import 'package:aitoearn_app/i18n/language_service.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/dialog/dialog_helper.dart';
import 'package:aitoearn_app/utils/dialog/widgets/bottom_dialog_widget.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SettingController extends GetxController {
  SettingController();

  final notificationEnabled = true.obs;

  final userStore = Get.find<UserStoreService>();

  final appVersion = ''.obs;

  /// 在 widget 内存中分配后立即调用。
  @override
  void onInit() {
    super.onInit();
    getAppVersionInfo();
  }

  Future<void> getAppVersionInfo() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    appVersion.value = packageInfo.version;
  }

  // 切换通知开关
  void toggleNotification(bool value) {
    notificationEnabled.value = value;
  }

  Future<void> logout() async {
    DialogHelper.showSimpleDialog(content: '确定要退出登录么?').then((result) {
      if (result == true) {
        userStore.logout();
      }
    });
  }

  void showLanguagePicker() {
    DialogHelper.showBottomSheetDialog(items: [
      MenuItem(text: 'followSystem'.tr, onTap: () {
        LanguageService.switchLocale('system');
      }),
      MenuItem(text: '简体中文', onTap: () {
        LanguageService.switchLocale('zh_CN');
      }),
      MenuItem(text: 'English', onTap: () {
        LanguageService.switchLocale('en_US');
      })
    ]);

  }
}
