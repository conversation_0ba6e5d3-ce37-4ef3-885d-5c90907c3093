import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/setting/setting_page_controller.dart';
import 'package:aitoearn_app/pages/setting/widgets/setting_list.dart';
import 'package:aitoearn_app/pages/setting/widgets/user_info_item.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SettingPage extends GetView<SettingController> {
  const SettingPage({super.key});

  // 主视图
  Widget _buildView(BuildContext context) {
    return Column(
      children: [
        const Expanded(child: SingleChildScrollView(
          child: <PERSON>umn(
            children: [
              UserInfoItem(),
              Gaps.vGap16,
              Setting<PERSON>ist(),
              Gaps.vGap16,
            ],
          ),
        ),),
        Gaps.vGap10,
        // 退出登录按钮
        CustomButton(
          onPressed: () => controller.logout(),
          text: 'setting.logout'.tr,
          textColor: AppColors.white,
          gradient: AppColors.blueAndPurple,
        ),
        'setting.cancelAccount'.tr.toNormalText(color: Colors.grey).paddingAll(12),
        Gaps.vGap10,
      ],
    ).marginSymmetric(horizontal: 16).safeArea();
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'setting.title'.tr,
      body: ResponsiveLayout(
        mobile: _buildView(context),
        desktop: Container(
          alignment: Alignment.center,
          child: SizedBox(width: 400, child: _buildView(context)),
        ),
      ),
    );
  }
}
