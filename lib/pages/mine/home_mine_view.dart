import 'package:aitoearn_app/pages/mine/home_mine_controller.dart';
import 'package:aitoearn_app/pages/mine/widgets/user_action.dart';
import 'package:aitoearn_app/pages/mine/widgets/user_buckets.dart';
import 'package:aitoearn_app/pages/mine/widgets/user_header.dart';
import 'package:aitoearn_app/pages/mine/widgets/vip_card.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeMineView extends GetView<HomeMineController> {
  const HomeMineView({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: 280,
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            color: Colors.white,
          ),
        ),
        Positioned.fill(
          child: Column(
            children: [
              // 用户头像和信息区域
              Gaps.vGap4,
              Row(
                children: [const UserHeader().expanded(), const UserAction()],
              ).safeArea(
                top: true,
                bottom: false,
              ),
              Gaps.vGap4,
              Expanded(
                child:
                    const Column(
                      children: [
                        // VIP会员卡片
                        VipCard(),
                        // 各种库
                        UserBuckets(),
                      ],
                    ).scrollable(
                      controller: controller.scrollController,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
