import 'package:aitoearn_app/api/media_material/media_material_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/draft_and_media/model/draft_group_info.dart';
import 'package:aitoearn_app/pages/draft_and_media/model/media_group_info.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/user/model/user_info.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/dialog/dialog_helper.dart';
import 'package:aitoearn_app/utils/dialog/form_dialog_helper.dart';
import 'package:aitoearn_app/utils/dialog/models/form_item_model.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

/// 底部展示类型
enum BucketsTabType { imageBucket, videoBucket, draftBucket }

class HomeMineController extends GetxController {
  final userInfo = Rxn<UserInfo>();
  final hasNotice = false.obs;

  final tabType = BucketsTabType.imageBucket.obs;

  final imageBuckets = <MediaGroupInfoModel>[].obs;
  final videoBuckets = <MediaGroupInfoModel>[].obs;
  final draftBuckets = <DraftGroupInfoModel>[].obs;

  final imageGroupTotal = 0.obs;
  final videoGroupTotal = 0.obs;
  final draftGroupTotal = 0.obs;

  final scrollController = ScrollController();

  final imagePageNo = 1.obs;
  final videoPageNo = 1.obs;
  final draftPageNo = 1.obs;

  final isLoading = false.obs;
  
  final pageSize = 20;

  @override
  void onInit() {
    super.onInit();
    userInfo.value = Get.find<UserStoreService>().userInfo.value;
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
              scrollController.position.maxScrollExtent &&
          isMobileMode()) {
        // 滚动到了底部
        if (hasMoreBucket() && !isLoading.value) {
          loadMoreBuckets();
        }
      }
    });

    getImageBuckets(1, pageSize);
    getVideoBuckets(1, pageSize);
    getDraftBuckets(1, pageSize);
  }

  getImageBuckets(int pageNo, int pageSize) async {
    isLoading.value = true;

    final response = await getMediaGroupListApi(
      pageNo: pageNo,
      pageSize: pageSize,
      type: 'img',
    );

    final rawData = response?.data?['data'];
    final total = rawData?['total'] ?? 0;

    final List<dynamic> list = rawData?['list'] ?? [];

    final List<MediaGroupInfoModel> mediaGroups =
        list.map((e) => MediaGroupInfoModel.fromJson(e)).toList();
    if (pageNo == 1) {
      imageBuckets.clear();
    }
    imageBuckets.addAll(mediaGroups);
    imageGroupTotal.value = total;
    imagePageNo.value += 1;
    isLoading.value = false;
  }

  getVideoBuckets(pageNo, pageSize) async {
    isLoading.value = true;

    final response = await getMediaGroupListApi(
      pageNo: pageNo,
      pageSize: pageSize,
      type: 'video',
    );

    final rawData = response?.data?['data'];
    final total = rawData?['total'] ?? 0;

    final List<dynamic> list = rawData?['list'] ?? [];

    final List<MediaGroupInfoModel> mediaGroups =
        list.map((e) => MediaGroupInfoModel.fromJson(e)).toList();

    // 你可以使用 mediaGroups 和 total 做接下来的逻辑
    print('媒体组总数: $total');
    print('解析到的媒体组数: ${mediaGroups.length}');
    videoBuckets.value = mediaGroups;
    videoGroupTotal.value = total;
    videoPageNo.value += 1;
    isLoading.value = false;
  }

  getDraftBuckets(int pageNo, int pageSize) async {
    isLoading.value = true;
    final response = await getMaterialGroupListApi(
      pageNo: pageNo,
      pageSize: pageSize,
    );

    final rawData = response?.data?['data'];
    final int total = rawData?['total'] ?? 0;
    final List<dynamic> list = rawData?['list'] ?? [];

    final List<DraftGroupInfoModel> draftGroups =
        list.map((e) => DraftGroupInfoModel.fromJson(e)).toList();

    // 使用 draftGroups 和 total 进行后续逻辑
    print('草稿组总数: $total');
    print('解析到草稿组数量: ${draftGroups.length}');
    draftBuckets.value = draftGroups;
    draftGroupTotal.value = total;
    draftPageNo.value += 1;
    isLoading.value = false;
  }

  clickTab(BucketsTabType type) {
    tabType.value = type;
  }

  void createBucket(BucketsTabType type) {
    // 跳转或弹窗等
    if (BucketsTabType.imageBucket == type) {
      // 创建媒体库
      String name = 'mine.form.image_library_title'.tr;
      String desc = 'mine.form.image_library_desc'.tr;
      FormDialogHelper.showFormDialog(
        title: 'mine.dialog.create_image_library'.tr,
        formItems: [
          InputFormItem(name: name, hint: 'mine.form.enter_image_library_name'.tr, required: true),
          InputFormItem(name: desc, hint: 'mine.form.enter_image_library_desc'.tr),
        ],
      ).then((value) async {
        if (value == null) {
          return;
        }
        final cancel = showProgress();
        final res = await createMediaGroupApi(title: value[name], desc: value[desc], type: 'img',);
        if (res?['code'] == 0) {
          showSuccess('mine.toast.create_success'.tr);
          refreshBuckets();
        } else {
          showError(res?['message'] ?? 'mine.toast.create_failed'.tr);
        }
        cancel();
      });

    } else if (BucketsTabType.videoBucket == type) {
      // 创建媒体库
      String name = 'mine.form.video_library_title'.tr;
      String desc = 'mine.form.video_library_desc'.tr;
      FormDialogHelper.showFormDialog(
        title: 'mine.dialog.create_video_library'.tr,
        formItems: [
          InputFormItem(name: name, hint: 'mine.form.enter_video_library_name'.tr, required: true),
          InputFormItem(name: desc, hint: 'mine.form.enter_video_library_desc'.tr),
        ],
      ).then((value) async {
        if (value == null) {
          return;
        }
        final cancel = showProgress();
        final res = await createMediaGroupApi(title: value[name], desc: value[desc], type: 'video',);
        if (res?['code'] == 0) {
          showSuccess('mine.toast.create_success'.tr);
          refreshBuckets();
        } else {
          showError(res?['message'] ?? 'mine.toast.create_failed'.tr);
        }
        cancel();
      });

    } else if (BucketsTabType.draftBucket == type) {
      String name = 'mine.form.draft_box_title'.tr;
      FormDialogHelper.showFormDialog(
        title: 'mine.dialog.create_draft_box'.tr,
        formItems: [
          InputFormItem(name: name, hint: 'mine.form.enter_draft_box_name'.tr, required: true),
        ],
      ).then((value) async {
        if (value == null) {
          return;
        }
        final cancel = showProgress();
        final res = await createMaterialGroupApi(name: value[name], type: 'article', );
        if (res?['code'] == 0) {
          showSuccess('mine.toast.create_success'.tr);
          refreshBuckets();
        } else {
          showError(res?['message'] ?? 'mine.toast.create_failed'.tr);
        }
        cancel();
      });
    }
  }

  bool hasMoreBucket() {
    return switch (tabType.value) {
      BucketsTabType.imageBucket => imageBuckets.length < imageGroupTotal.value,
      BucketsTabType.videoBucket => videoBuckets.length < videoGroupTotal.value,
      BucketsTabType.draftBucket => draftBuckets.length < draftGroupTotal.value,
    };
  }

  // 刷新数据
  Future<void> refreshBuckets() async {
    final cancel = showProgress();
    if (tabType.value == BucketsTabType.imageBucket) {
      imagePageNo.value = 1;
      await getImageBuckets(imagePageNo.value, pageSize);
    } else if (tabType.value == BucketsTabType.videoBucket) {
      videoPageNo.value = 1;
      await getVideoBuckets(videoPageNo.value, pageSize);
    } else if (tabType.value == BucketsTabType.draftBucket) {
      draftPageNo.value = 1;
      await getDraftBuckets(draftPageNo.value, pageSize);
    }
    cancel();
  }

  // 加载更多
  Future<void> loadMoreBuckets() async {
    final cancel = showProgress();
    if (tabType.value == BucketsTabType.imageBucket) {
      await getImageBuckets(imagePageNo.value, pageSize);
    } else if (tabType.value == BucketsTabType.videoBucket) {
      await getVideoBuckets(videoPageNo.value, pageSize);
    } else if (tabType.value == BucketsTabType.draftBucket) {
      await getDraftBuckets(draftPageNo.value, pageSize);
    }
    cancel();
  }

  /// 编辑草稿桶
  Future<void> editDraftBucket(DraftGroupInfoModel model) async {
    String name = 'mine.form.draft_box_title'.tr;
    FormDialogHelper.showFormDialog(
      title: 'mine.dialog.update_draft_box'.tr,
      formItems: [
        InputFormItem(name: name, hint: 'mine.form.enter_draft_box_name'.tr, value: model.name, required: true),
      ],
    ).then((value) async {
      if (value == null) {
        return;
      }
      final cancel = showProgress();
      final res = await updateMaterialGroupInfoApi(id: model.id, name: value[name],);
      if (res?['code'] == 0) {
        showSuccess('mine.toast.update_success'.tr);
        refreshBuckets();
      } else {
        showError(res?['message'] ?? 'mine.toast.update_failed'.tr);
      }
      cancel();
    });
  }

  /// 删除草稿桶
  void deleteDraftBucket(DraftGroupInfoModel model) {
    DialogHelper.showSimpleDialog(title: 'mine.dialog.confirm_delete_draft_box'.tr).then((res) async {
      if (res == true) {
        final cancel = showProgress();
        final res = await deleteMaterialGroupApi(model.id);
        if (res?['code'] == 0) {
          showSuccess('mine.toast.delete_success'.tr);
          refreshBuckets();
        } else {
          showError(res?['message'] ?? 'mine.toast.delete_failed'.tr);
        }
        cancel();
      }
    });

  }

  /// 编辑媒体桶
  void editMediaBucket(MediaGroupInfoModel model) {
    final isVideo = model.type == 'video';
    final name = isVideo ? 'mine.form.video_library_title'.tr : 'mine.form.image_library_title'.tr;
    final desc = isVideo ? 'mine.form.video_library_desc'.tr : 'mine.form.image_library_desc'.tr;
    FormDialogHelper.showFormDialog(
      title: isVideo ? 'mine.dialog.update_video_library'.tr : 'mine.dialog.update_image_library'.tr,
      formItems: [
        InputFormItem(name: name, hint: isVideo ? 'mine.form.enter_video_library_name'.tr : 'mine.form.enter_image_library_name'.tr, value: model.title, required: true),
        InputFormItem(name: desc, hint: isVideo ? 'mine.form.enter_video_library_desc'.tr : 'mine.form.enter_image_library_desc'.tr, value: model.desc),
      ],
    ).then((value) async {
      if (value == null) {
        return;
      }
      final cancel = showProgress();
      final res = await updateMediaGroupInfoApi(id: model.id, title: value[name], desc: value[desc]);
      if (res?['code'] == 0) {
        showSuccess('mine.toast.update_success'.tr);
        refreshBuckets();
      } else {
        showError(res?['message'] ?? 'mine.toast.update_failed'.tr);
      }
      cancel();
    });
  }

  /// 删除媒体桶
  void deleteMediaBucket(MediaGroupInfoModel model) {
    final isVideo = model.type == 'video';
    DialogHelper.showSimpleDialog(title: isVideo ? 'mine.dialog.confirm_delete_video_library'.tr : 'mine.dialog.confirm_delete_image_library'.tr).then((res) async {
      if (res == true) {
        final cancel = showProgress();
        final res = await deleteMediaGroupApi(model.id);
        if (res?['code'] == 0) {
          showSuccess('mine.toast.delete_success'.tr);
          refreshBuckets();
        } else {
          showError(res?['message'] ?? 'mine.toast.delete_failed'.tr);
        }
        cancel();
      }
    });
  }
  
  /// 跳转到积分页面
  void navigateToPoints() {
    Get.toNamed(
      AppRouter.pointsPath,
      arguments: userInfo.value, // 传递用户信息
    );
  }
  
  /// 跳转到余额页面
  void navigateToBalance() {
    Get.toNamed(
      AppRouter.balancePath,
      arguments: userInfo.value, // 传递用户信息
    );
  }
}
