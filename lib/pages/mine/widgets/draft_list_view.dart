import 'package:aitoearn_app/pages/draft_and_media/model/draft_group_info.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_pop_helper.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_option.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../home_mine_controller.dart';

/// 草稿列表视图组件
/// 用于草稿箱的展示，使用列表布局并添加分割线
class DraftListView extends GetView<HomeMineController> {
  const DraftListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final draftGroups = controller.draftBuckets;
      return ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 0),
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: draftGroups.length,
        separatorBuilder:
            (context, index) =>
                const Divider(height: 1, color: AppColors.dividerColor),
        itemBuilder: (context, index) {
          return _buildDraftGroupItem(context, draftGroups[index]);
        },
      );
    });
  }

  /// 构建草稿组列表项
  Widget _buildDraftGroupItem(BuildContext context, DraftGroupInfoModel model) {
    // 添加GlobalKey用于定位气泡菜单
    final GlobalKey draftMenuKey = GlobalKey();

    return GestureDetector(
      onTap: () async {
        final needRefresh = await Get.toNamed(
          AppRouter.draftBucketPath,
          arguments: {
            'bucketId': model.id,
            'title': model.name,
            'type': model.type,
          },
        );
        if (needRefresh == true) {
          controller.refreshBuckets();
        }
      },
      child: Row(
        children: [
          Image.asset(
            'assets/images/account/icon_draft_dir.png',
            width: 36,
            height: 36,
          ),
          Gaps.hGap10,
          Expanded(
            child: model.name.toNormalText(
              fontSize: 14,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (model.id.isNotEmpty)
            IconButton(
              key: draftMenuKey,
              onPressed: () {
                // 显示气泡菜单
                MenuPopHelper.showMenuByKey(
                  context: context,
                  key: draftMenuKey,
                  options: [
                    MenuOption(
                      icon: Icons.edit,
                      name: '编辑',
                      onTap: () => controller.editDraftBucket(model),
                    ),
                    MenuOption(
                      icon: Icons.delete,
                      name: '删除',
                      onTap: () => controller.deleteDraftBucket(model),
                    ),
                  ],
                );
              },
              icon: const Icon(
                Icons.more_horiz,
                color: AppColors.textColor,
                size: 22,
              ),
            ),
        ],
      ).paddingSymmetric(vertical: 8),
    );
  }
}
