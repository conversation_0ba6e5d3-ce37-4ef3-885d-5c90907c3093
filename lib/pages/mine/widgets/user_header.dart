import 'package:aitoearn_app/pages/mine/home_mine_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:aitoearn_app/widgets/user_avatar.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserHeader extends GetView<HomeMineController> {
  const UserHeader({super.key});

  @override
  Widget build(BuildContext context) {
    /// 用户头像
    final Widget userAvatar = Obx(
      () => Container(
        width: isMobileMode() ? 46 : 54,
        height: isMobileMode() ? 46 : 54,
        decoration: controller.userInfo.value?.vipInfo != null ? BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [
              AppColors.white.withOpacity(0.8),
              AppColors.vipColor.withOpacity(0.8),
              AppColors.vipColor,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          )
        ) : const BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.white
        ),
        child: UserAvatar(
          imageUrl: controller.userInfo.value?.avatar,
          // 或者传你的头像地址
          errorWidget: Image.asset('assets/images/logo.png').paddingAll(3),
        ).marginAll(2),
      ),
    ).marginOnly(right: 10);

    /// 用户名 + 认证图标
    final Widget userNameRow = Obx(
      () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          (controller.userInfo.value?.name ?? '用户名').toNormalText(
            color: AppColors.textColor,
            fontSize: isMobileMode() ? 14 : 16,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            fontWeight: FontWeight.w600,
          ),
          Visibility(visible:false,child: Image.asset('assets/images/account/icon_user_vip_tips.png', width: 46).marginOnly(top: 3)),
        ],
      ),
    );

    /// 用户信息模块（头像 + 名称 + 积分）
    return [userAvatar, Expanded(child: userNameRow)]
        .toRow(crossAxisAlignment: CrossAxisAlignment.center)
        .marginOnly(left: 16, right: 6, top: 6, bottom: 6);
  }
}
