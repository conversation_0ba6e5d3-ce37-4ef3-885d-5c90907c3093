import 'package:aitoearn_app/pages/mine/home_mine_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class VipCard extends GetView<HomeMineController> {
  const VipCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: isMobileMode() ? double.infinity : 350,
        ),
        child: getVipCard(),
      ),
    ).marginOnly(left: 24, right: 24, top: 12);
  }

  getVipCard() {
    final isVip = controller.userInfo.value?.vipInfo != null;
    final textColor = isVip ? AppColors.vipColor : AppColors.noVipColor;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        gradient:
            isVip
                ? const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF4C4741),
                    Color(0xFF33353C),
                    Color(0xFF322E2A),
                  ],
                )
                : const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFFD2DFFA),
                    Color(0xFFE0EBFD),
                    Color(0xFFD2DFFA),
                  ],
                ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gaps.vGap8,

          /// 累计赚钱
          Row(
            children: [
              Image.asset(
                isVip
                    ? 'assets/images/account/icon_user_vip.png'.tr
                    : 'assets/images/account/icon_user_vip_no.png'.tr,
                width: 20,
                height: 20,
              ),
              Gaps.hGap4,
              Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '${'mine.vip.total_earning'.tr} ',
                      style: TextStyle(color: textColor, fontSize: 16),
                    ),
                    TextSpan(
                      text: '0',
                      style: TextStyle(
                        color: textColor,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: ' ${'mine.vip.currency_unit'.tr}',
                      style: TextStyle(color: textColor, fontSize: 16),
                    ),
                  ],
                ),
              ),

              const Expanded(child: SizedBox()),

              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(40),
                  gradient:
                      isVip
                          ? const LinearGradient(
                            colors: [Color(0xFFF3D8AB), Color(0xFFE7BE78)],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          )
                          : const LinearGradient(
                            colors: [Color(0xFF7E94A5), Color(0xFF445C70)],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                ),
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                child: Row(
                  children: [
                    Text(
                      isVip ? ' ${'mine.vip.renew'.tr}' : ' ${'mine.vip.upgrade'.tr}',
                      style: TextStyle(
                        color:
                            isVip ? const Color(0xff744E1D) : AppColors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Gaps.hGap4,
                    Image.asset(
                      isVip
                          ? 'assets/images/account/icon_vip_arrow.png'
                          : 'assets/images/account/icon_arrow.png',
                      width: 16,
                      height: 16,
                    ),
                  ],
                ),
              ).gestures(onTap: () => Get.toNamed(AppRouter.subscriptionPath)),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('0', 'mine.vip.balance'.tr, textColor),
              _buildStatItem('0', 'mine.vip.points'.tr, textColor),
              _buildStatItem('0', 'mine.vip.coupon'.tr, textColor),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String value, String label, Color textColor) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
        Gaps.vGap4,
        Text(
          label,
          style: TextStyle(fontSize: 12, color: textColor.withOpacity(0.8)),
        ),
      ],
    );
  }
}
