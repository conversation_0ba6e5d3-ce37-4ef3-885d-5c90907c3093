import 'package:aitoearn_app/pages/mine/home_mine_controller.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_sign_in_all_platforms/google_sign_in_all_platforms.dart';

class UserAction extends GetView<HomeMineController> {
  const UserAction({super.key});

  @override
  Widget build(BuildContext context) {
    return [
      _rippleIconButton(
        onTap: () {},
        assetPath: 'assets/images/account/icon_user_qrcode.png',
        text: 'mine.action.qr_code'.tr,
      ),
      _rippleIconButton(
        onTap: () {},
        assetPath: 'assets/images/account/icon_share.png',
        text: 'mine.action.share'.tr,
      ),
      [
        _rippleIconButton(
          onTap: () {},
          assetPath: 'assets/images/account/icon_notice.png',
          text: 'mine.action.message'.tr,
        ),
        Obx(
              () => Visibility(
            visible: controller.hasNotice.value,
            child: const SizedBox(width: 6, height: 6)
                .decorated(color: Colors.red, shape: BoxShape.circle)
                .positioned(right: 8, top: 2),
          ),
        ),
      ].toStack(),
      _rippleIconButton(
        onTap: () => Get.toNamed<void>(AppRouter.settingPath),
        text: 'mine.action.settings'.tr,
        assetPath: 'assets/images/account/icon_setting.png',
      ),
    ].toRow(
      mainAxisAlignment: MainAxisAlignment.end,
    ).marginOnly(right: 16, left: 2);
  }

  Widget _rippleIconButton({
    required VoidCallback onTap,
    required String assetPath,
    required String text,
    double size = 50,
  }) {
    return Material(
      color: Colors.transparent, // 背景透明
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8), // 点击时水波纹的圆角
        child: SizedBox(
          width: isMobileMode() ? (size * 8 / 9) : size,
          height: size,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                assetPath,
                width: size / 2,
                height: size / 2,
              ),
              Text(
                text,
                style: const TextStyle(fontSize: 10),
              )
            ],
          ),
        ),
      ),
    );
  }
}
