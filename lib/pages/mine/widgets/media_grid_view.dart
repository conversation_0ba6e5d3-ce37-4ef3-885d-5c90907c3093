import 'package:aitoearn_app/pages/draft_and_media/model/media_group_info.dart';
import 'package:aitoearn_app/pages/mine/home_mine_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_option.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_pop_helper.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 媒体网格视图组件
/// 用于图片库和视频库的展示，高度固定，宽度自适应
class MediaGridView extends GetView<HomeMineController> {
  const MediaGridView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final mediaGroups =
          controller.tabType.value == BucketsTabType.imageBucket
              ? controller.imageBuckets
              : controller.videoBuckets;
      return GridView.builder(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 0),
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: 200, // 最大宽度为200，超过后自动换行
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1, // 宽高比1:1，保持高度固定
        ),
        itemCount: mediaGroups.length,
        itemBuilder: (context, index) {
          return _buildMediaGroupCard(context, mediaGroups[index]);
        },
      );
    });
  }

  /// 构建媒体组卡片
  Widget _buildMediaGroupCard(BuildContext context, MediaGroupInfoModel model) {
    // 添加GlobalKey用于定位气泡菜单
    final GlobalKey mediaMenuKey = GlobalKey();

    final List<String> imageUrls =
        model.mediaList
            .map((e) => e.url)
            .where((url) => url.isNotEmpty)
            .toList();

    final firstImage = imageUrls.isNotEmpty ? imageUrls[0] : null;

    Widget imageBox(String url) {
      return NetworkImageWidget(
        url,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
      );
    }

    Widget imageLayout;

    if (imageUrls.length == 1) {
      imageLayout = imageBox(firstImage!);
    } else if (imageUrls.length == 2) {
      imageLayout = Row(
        children: [
          Expanded(child: imageBox(imageUrls[0])),
          const SizedBox(width: 1),
          Expanded(child: imageBox(imageUrls[1])),
        ],
      );
    } else if (imageUrls.length >= 3) {
      imageLayout = Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: imageBox(imageUrls[0])),
          const SizedBox(width: 1),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(child: imageBox(imageUrls[1])),
                const SizedBox(height: 1),
                Expanded(child: imageBox(imageUrls[2])),
              ],
            ),
          ),
        ],
      );
    } else {
      imageLayout = Center(
        child: Image.asset(
          'assets/images/account/icon_no_data.png',
          width: 100,
          height: 100,
        ),
      );
    }

    return Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: imageLayout),
                Center(
                  child: '${model.title}（${model.mediaTotal}）'
                      .toNormalText(
                        fontSize: 12,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )
                      .marginOnly(top: 6, bottom: 8, left: 8, right: 8),
                ),
              ],
            ).onTap(() async {
              final needRefresh = await Get.toNamed(
                AppRouter.mediaBucketPath,
                arguments: {
                  'bucketId': model.id,
                  'bucketType': model.type,
                  'title': model.title,
                  'desc': model.desc,
                },
              );
              if (needRefresh == true) {
                controller.refreshBuckets();
              }
            }),
            if (model.id.isNotEmpty)
              Positioned(
                right: 6,
                top: 6,
                child: Container(
                  padding: const EdgeInsets.all(3),
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.4),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    key: mediaMenuKey,
                    Icons.more_horiz,
                    color: AppColors.textColor,
                    size: 20,
                  ),
                ).gestures(
                  onTap: () {
                    // 显示气泡菜单
                    MenuPopHelper.showMenuByKey(
                      context: context,
                      key: mediaMenuKey,
                      options: [
                        MenuOption(
                          icon: Icons.edit,
                          name: '编辑',
                          onTap: () => controller.editMediaBucket(model),
                        ),
                        MenuOption(
                          icon: Icons.delete,
                          name: '删除',
                          onTap: () => controller.deleteMediaBucket(model),
                        ),
                      ],
                    );
                  },
                ),
              ),
          ],
        )
        .clipRRect(all: 8)
        .decorated(
          color: const Color(0xFFf4f2ff),
          borderRadius: BorderRadius.circular(8),
        );
  }
}
