import 'package:aitoearn_app/pages/mine/home_mine_controller.dart';
import 'package:aitoearn_app/pages/mine/widgets/draft_list_view.dart';
import 'package:aitoearn_app/pages/mine/widgets/media_grid_view.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserBuckets extends GetView<HomeMineController> {
  const UserBuckets({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.mediaBgFrame.withAlpha(20), // 阴影颜色
            blurRadius: 3, // 模糊程度
            offset: const Offset(0, 0), // 阴影偏移 (x, y)
          ),
        ],
      ),
      child: <Widget>[
            _buildTabSelector(),
            Obx(() => _buildCreateItem()),
            Obx(() => controller.tabType.value == BucketsTabType.draftBucket ? Gaps.vGap4 : Gaps.vGap16),
            Obx(() => _buildContentArea()),
            Obx(() {
              Widget infoWidget = const SizedBox();

              if (!controller.hasMoreBucket()) {
                infoWidget = Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: 'mine.buckets.no_more_data'.tr.toNormalText(),
                );
              } else if (!isMobileMode() && controller.hasMoreBucket()) {
                infoWidget = Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: 'mine.buckets.load_more'.tr.toNormalText().gestures(
                    onTap: () => controller.loadMoreBuckets(),
                  ),
                );
              }

              return infoWidget;
            }),
            Gaps.vGap16,
          ]
          .toColumn(crossAxisAlignment: CrossAxisAlignment.start)
          .marginSymmetric(horizontal: 16),
    );
  }

  Widget _buildTabSelector() {
    const tabs = [
      BucketsTabType.imageBucket,
      BucketsTabType.videoBucket,
      BucketsTabType.draftBucket,
    ];

    final labels = {
      BucketsTabType.imageBucket: 'mine.buckets.image_library'.tr,
      BucketsTabType.videoBucket: 'mine.buckets.video_library'.tr,
      BucketsTabType.draftBucket: 'mine.buckets.draft_box'.tr,
    };

    return Obx(() {
      final tabTotals = {
        BucketsTabType.imageBucket: controller.imageGroupTotal.value,
        BucketsTabType.videoBucket: controller.videoGroupTotal.value,
        BucketsTabType.draftBucket: controller.draftGroupTotal.value,
      };

      return Row(
        children: [
          ...tabs.map((type) {
            final isSelected = controller.tabType.value == type;
            final total = tabTotals[type] ?? 0;

            return GestureDetector(
              onTap: () => controller.clickTab(type),
              child: Container(
                margin: const EdgeInsets.only(right: 10, top: 5, bottom: 5),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primaryHighLightColor.withAlpha(30)
                      : AppColors.transparentColor,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: '${labels[type]!} $total'.toNormalText(
                  style: TextStyle(
                    fontSize: 13,
                    color:
                    isSelected
                        ? AppColors.textColor
                        : AppColors.textHintColor,
                    fontWeight:
                    isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }),
          Expanded(
            child: Container(
              alignment: Alignment.centerRight,
              child: IconButton(
                onPressed: controller.refreshBuckets,
                icon: const Icon(Icons.refresh),
              ),
            ),
          ),
        ],
      ).marginOnly(bottom: 6, top: 6);
    });
  }

  Widget _buildCreateItem() {
    final currentTab = controller.tabType.value;

    final label = switch (currentTab) {
      BucketsTabType.imageBucket => 'mine.buckets.create_image_library'.tr,
      BucketsTabType.videoBucket => 'mine.buckets.create_video_library'.tr,
      BucketsTabType.draftBucket => 'mine.buckets.create_draft_box'.tr,
    };

    return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.add,
              color: AppColors.primaryHighLightColor,
              size: 24,
            ),
            Gaps.hGap4,
            label.toNormalText(
              color: AppColors.primaryHighLightColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ],
        )
        .height(45)
        .width(isMobileMode() ? double.infinity : 350)
        .decorated(
          border: Border.all(color: AppColors.primaryHighLightColor, width: 1),
          borderRadius: BorderRadius.circular(12),
          color: AppColors.primaryHighLightColor.withOpacity(0.1),
        )
        .gestures(onTap: () => controller.createBucket(currentTab));
  }


  /// 根据当前标签类型构建不同的内容区域
  Widget _buildContentArea() {
    final tabType = controller.tabType.value;
    switch (tabType) {
      case BucketsTabType.imageBucket:
      case BucketsTabType.videoBucket:
        return const MediaGridView();
      case BucketsTabType.draftBucket:
        return const DraftListView();
    }
  }

}
