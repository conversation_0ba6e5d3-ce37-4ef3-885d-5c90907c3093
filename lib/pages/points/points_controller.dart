import 'package:aitoearn_app/store/user/model/user_info.dart';
import 'package:get/get.dart';

/// 个人积分控制器
class PointsController extends GetxController {
  final userInfo = Rxn<UserInfo>();
  final points = 0.obs;
  
  @override
  void onInit() {
    super.onInit();
    // 获取传递的用户信息
    final UserInfo? info = Get.arguments;
    if (info != null) {
      userInfo.value = info;
      // 模拟获取积分数据
      points.value = info.score ?? 0;
    }
  }
  
  /// 刷新积分数据
  void refreshPoints() {
    // 这里添加积分刷新逻辑
    points.value += 10; // 模拟增加积分
  }
}