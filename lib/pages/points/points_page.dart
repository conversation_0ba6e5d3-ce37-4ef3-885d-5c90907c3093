import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/points/points_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 个人积分页面
class PointsPage extends GetView<PointsController> {
  const PointsPage({super.key});
  
  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: '我的积分',
      body: Obx(
        () => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              '${controller.userInfo.value?.name ?? "用户"}的积分'.toNormalText(fontSize: 20),
              const SizedBox(height: 20),
              controller.points.value.toString().toNormalText(
                fontSize: 40, 
                color: AppColors.primaryColor,
                fontWeight: FontWeight.bold
              ),
              const Si<PERSON><PERSON><PERSON>(height: 40),
              ElevatedButton(
                onPressed: controller.refreshPoints,
                child: '刷新积分'.toNormalText(),
              )
            ],
          ),
        ),
      ),
    );
  }
}