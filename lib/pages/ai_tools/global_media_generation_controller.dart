import 'dart:async';
import 'dart:collection';

import 'package:aitoearn_app/api/ai/ai_api.dart';
import 'package:aitoearn_app/api/media_material/media_material_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:get/get.dart';

/// 全局媒体生成控制器（支持图片和视频）
class GlobalMediaGenerationController extends GetxController {
  static GlobalMediaGenerationController get to => Get.find();

  /// 生成状态记录 Map<BucketId, List<生成状态>>
  final generationStatus = <String, List<GenerationStatus>>{}.obs;

  /// 请求队列
  final Queue<Map<String, dynamic>> _taskQueue = Queue();

  /// 当前是否正在处理队列
  bool _isProcessing = false;

  /// 最大并发数
  final int _maxConcurrentTasks = 3;

  /// 添加图片生成任务
  void addImageGenerationTasks({
    required String prompt,
    required String model,
    required String quality,
    required String size,
    required String style,
    required String bucketId,
    required int imageCount,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    if (!generationStatus.containsKey(bucketId)) {
      generationStatus[bucketId] = <GenerationStatus>[];
    }

    for (int i = 0; i < imageCount; i++) {
      final requestId = 'gen_${timestamp}_$i';

      _addGenerationStatus(
        bucketId,
        GenerationStatus(
          status: GenerationStatusType.generating,
          message: '等待生成...',
          requestId: requestId,
          url: '',
          createdAt: DateTime.now(), // 添加创建时间
        ),
      );

      _taskQueue.add({
        'type': 'generation', // 任务类型：生成
        'prompt': prompt,
        'model': model,
        'quality': quality,
        'size': size,
        'style': style,
        'bucketId': bucketId,
        'requestId': requestId,
        'index': i,
      });
    }

    _processTaskQueue();
  }

  /// 添加图片编辑任务
  void addImageEditTasks({
    required String prompt,
    required String model,
    required String size,
    required String image,
    required String bucketId,
    required int imageCount,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    if (!generationStatus.containsKey(bucketId)) {
      generationStatus[bucketId] = <GenerationStatus>[];
    }

    for (int i = 0; i < imageCount; i++) {
      final requestId = 'edit_${timestamp}_$i';

      _addGenerationStatus(
        bucketId,
        GenerationStatus(
          status: GenerationStatusType.generating,
          message: '等待编辑...',
          requestId: requestId,
          url: '',
          createdAt: DateTime.now(), // 添加创建时间
        ),
      );

      _taskQueue.add({
        'type': 'edit', // 任务类型：编辑
        'prompt': prompt,
        'model': model,
        'size': size,
        'image': image,
        'bucketId': bucketId,
        'requestId': requestId,
        'index': i,
      });
    }

    _processTaskQueue();
  }

  /// 添加视频生成任务
  void addVideoGenerationTask({
    required String prompt,
    required String model,
    required String mode,
    required String size,
    required int duration,
    required String bucketId,
    String? image, // 可选的起始图base64
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final requestId = 'vid_${timestamp}_0'; // 视频不会批量，固定索引为0

    if (!generationStatus.containsKey(bucketId)) {
      generationStatus[bucketId] = <GenerationStatus>[];
    }

    _addGenerationStatus(
      bucketId,
      GenerationStatus(
        status: GenerationStatusType.generating,
        message: '等待生成...',
        requestId: requestId,
        url: '',
        createdAt: DateTime.now(),
        mediaType: MediaType.video,
        taskId: '', // 初始为空，后面会设置
      ),
    );

    _taskQueue.add({
      'type': 'video', // 任务类型：视频
      'prompt': prompt,
      'model': model,
      'mode': mode,
      'size': size,
      'duration': duration,
      'image': image,
      'bucketId': bucketId,
      'requestId': requestId,
    });

    _processTaskQueue();
  }

  /// 处理任务队列（并发控制 + 异常隔离）
  Future<void> _processTaskQueue() async {
    if (_isProcessing || _taskQueue.isEmpty) {
      return;
    }

    _isProcessing = true;

    try {
      while (_taskQueue.isNotEmpty) {
        final List<Map<String, dynamic>> currentTasks = [];

        // 获取当前批次
        while (currentTasks.length < _maxConcurrentTasks &&
            _taskQueue.isNotEmpty) {
          currentTasks.add(_taskQueue.removeFirst());
        }

        // 并发执行每一个任务
        await Future.wait(
          currentTasks.map((task) async {
            try {
              if (task['type'] == 'video') {
                await _processSingleVideoTask(task);
              } else {
                await _processSingleImageTask(task);
              }
            } catch (e, stack) {
              LoggerUtil.e('任务执行失败: $e $stack');
            }
          }),
        );

        // 可选休眠一帧，让主线程喘息
        await Future.delayed(const Duration(milliseconds: 16));
      }
    } finally {
      _isProcessing = false;
    }
  }

  /// 处理单个视频任务
  Future<void> _processSingleVideoTask(Map<String, dynamic> task) async {
    final String bucketId = task['bucketId'];
    final String requestId = task['requestId'];

    try {
      _updateRequestStatus(
        bucketId: bucketId,
        requestId: requestId,
        status: GenerationStatusType.generating,
        message: '视频生成中...',
      );

      // 提交视频生成请求
      final response = await submitAiVideoGenerationApi(
        model: task['model'],
        prompt: task['prompt'],
        image: task['image'],
        mode: task['mode'],
        size: task['size'],
        duration: task['duration'],
      );

      if (response != null && response['code'] == 0) {
        final taskId = response['data']['taskId'];

        // 更新任务ID
        _updateRequestStatus(
          bucketId: bucketId,
          requestId: requestId,
          status: GenerationStatusType.generating,
          message: '视频生成中...',
          taskId: taskId,
        );

        // 开始轮询任务状态
        await _pollVideoTaskStatus(bucketId, requestId, taskId);
      } else {
        _updateRequestStatus(
          bucketId: bucketId,
          requestId: requestId,
          status: GenerationStatusType.error,
          message: response?['message'] ?? '视频生成任务提交失败',
        );
      }
    } catch (e, stack) {
      LoggerUtil.e('视频生成异常: $e $stack');
      _updateRequestStatus(
        bucketId: bucketId,
        requestId: requestId,
        status: GenerationStatusType.error,
        message: '处理失败: ${e.toString().substring(0, 50)}',
      );
    }
  }

  /// 轮询视频任务状态
  Future<void> _pollVideoTaskStatus(
    String bucketId,
    String requestId,
    String taskId,
  ) async {
    int maxAttempts = 120; // 最多轮询120次 (6分钟)
    int attempts = 0;

    while (attempts < maxAttempts) {
      await Future.delayed(const Duration(seconds: 3));
      attempts++;

      try {
        final response = await getAiVideoGenerationStatusApi(taskId);

        if (response != null && response['code'] == 0) {
          final data = response['data'];
          final taskStatus = data['status'];
          final progress = data['progress'] ?? 0;

          // 更新进度
          _updateRequestStatus(
            bucketId: bucketId,
            requestId: requestId,
            status: GenerationStatusType.generating,
            message: '生成进度: $progress%',
          );

          if (taskStatus == 'completed') {
            // 生成完成，保存视频
            final videoUrl = data['video_url'];
            final fileName =
                'ai_video_${DateTime.now().millisecondsSinceEpoch}.mp4';

            _updateRequestStatus(
              bucketId: bucketId,
              requestId: requestId,
              status: GenerationStatusType.saving,
              message: '视频保存中...',
              url: videoUrl,
            );

            await _createMedia(bucketId, fileName, videoUrl, 'video');

            _updateRequestStatus(
              bucketId: bucketId,
              requestId: requestId,
              status: GenerationStatusType.completed,
              message: '视频生成完成',
              url: videoUrl,
            );
            return;
          } else if (taskStatus == 'failed') {
            _updateRequestStatus(
              bucketId: bucketId,
              requestId: requestId,
              status: GenerationStatusType.error,
              message: data['error_message'] ?? '视频生成失败',
            );
            return;
          }
        }
      } catch (e) {
        LoggerUtil.e('查询视频任务状态异常: $e');
        _updateRequestStatus(
          bucketId: bucketId,
          requestId: requestId,
          status: GenerationStatusType.error,
          message: '查询状态异常',
        );
        return;
      }
    }

    // 超时
    _updateRequestStatus(
      bucketId: bucketId,
      requestId: requestId,
      status: GenerationStatusType.error,
      message: '视频生成超时',
    );
  }

  /// 处理单个图片任务（生成或编辑）
  Future<void> _processSingleImageTask(Map<String, dynamic> task) async {
    final String bucketId = task['bucketId'];
    final String requestId = task['requestId'];
    final String taskType = task['type'] ?? 'generation';

    try {
      _updateRequestStatus(
        bucketId: bucketId,
        requestId: requestId,
        status: GenerationStatusType.generating,
        message: taskType == 'edit' ? '图片编辑中...' : '图片生成中...',
      );

      dynamic response;
      if (taskType == 'edit') {
        // 调用编辑接口
        response = await editAiImageApi(
          image: task['image'],
          prompt: task['prompt'],
          model: task['model'],
          size: task['size'],
          responseFormat: 'url',
          user: Get.find<UserStoreService>().userInfo.value?.id ?? '',
        );
      } else {
        // 调用生成接口
        response = await generateAiImageApi(
          prompt: task['prompt'],
          model: task['model'],
          quality: task['quality'] ?? '',
          size: task['size'],
          style: task['style'] ?? '',
          user: Get.find<UserStoreService>().userInfo.value?.id ?? '',
        );
      }

      final data = response?['data'];
      if (response != null &&
          response['code'] == 0 &&
          data is Map &&
          data['data'] is List) {
        final List<dynamic> results = data['data'];
        final List<String> urls =
            results
                .map((item) => item['url'] as String?)
                .where((url) => url != null && url.isNotEmpty)
                .cast<String>()
                .toList();

        if (urls.isEmpty) {
          _updateRequestStatus(
            bucketId: bucketId,
            requestId: requestId,
            status: GenerationStatusType.error,
            message: '${taskType == 'edit' ? '编辑' : '生成'}失败: 未返回有效图片',
          );
          return;
        }

        final url = urls.first;
        final fileName =
            'ai_${taskType}_${DateTime.now().millisecondsSinceEpoch}_${task['index']}.jpg';

        _updateRequestStatus(
          bucketId: bucketId,
          requestId: requestId,
          status: GenerationStatusType.saving,
          message: '图片保存中...',
          url: url,
        );

        await _createMedia(bucketId, fileName, url, 'image');

        _updateRequestStatus(
          bucketId: bucketId,
          requestId: requestId,
          status: GenerationStatusType.completed,
          message: '图片${taskType == 'edit' ? '编辑' : '生成'}完成',
          url: url,
        );
      } else {
        _updateRequestStatus(
          bucketId: bucketId,
          requestId: requestId,
          status: GenerationStatusType.error,
          message:
              response?['message'] ?? '图片${taskType == 'edit' ? '编辑' : '生成'}失败',
        );
      }
    } catch (e, stack) {
      LoggerUtil.e('图片${taskType == 'edit' ? '编辑' : '生成'}异常: $e $stack');
      _updateRequestStatus(
        bucketId: bucketId,
        requestId: requestId,
        status: GenerationStatusType.error,
        message: '处理失败: ${e.toString().substring(0, 50)}',
      );
    }
  }

  /// 添加生成状态
  void _addGenerationStatus(String bucketId, GenerationStatus status) {
    generationStatus.update(bucketId, (list) {
      list.add(status);
      return list;
    });
    generationStatus.refresh(); // 确保UI更新
  }

  /// 更新状态
  void _updateRequestStatus({
    required String bucketId,
    required String requestId,
    required GenerationStatusType status,
    required String message,
    String url = '',
    String taskId = '',
  }) {
    if (!generationStatus.containsKey(bucketId)) return;

    generationStatus.update(bucketId, (list) {
      final index = list.indexWhere((s) => s.requestId == requestId);
      if (index != -1) {
        list[index] = GenerationStatus(
          status: status,
          message: message,
          requestId: requestId,
          url: url,
          createdAt: list[index].createdAt,
          mediaType: list[index].mediaType,
          taskId: taskId.isNotEmpty ? taskId : list[index].taskId,
        );
      }
      return list;
    });
    generationStatus.refresh(); // 确保UI更新
  }

  /// 调用保存接口
  Future<void> _createMedia(
    String bucketId,
    String fileName,
    String url,
    String type,
  ) async {
    await createMediaApi(
      groupId: bucketId,
      title: fileName,
      url: url,
      type: type,
      desc: type == 'video' ? 'AI生成视频' : 'AI生成图片',
    );
  }
}

/// 媒体类型枚举
enum MediaType { image, video }

/// 状态模型
class GenerationStatus {
  final GenerationStatusType status;
  final String message;
  final String requestId;
  final String url;
  final DateTime createdAt;
  final MediaType mediaType; // 新增媒体类型字段
  final String taskId; // 新增任务ID字段

  GenerationStatus({
    required this.status,
    required this.message,
    required this.requestId,
    required this.url,
    required this.createdAt,
    this.mediaType = MediaType.image, // 默认图片类型
    this.taskId = '',
  });
}

enum GenerationStatusType { generating, saving, completed, error }
