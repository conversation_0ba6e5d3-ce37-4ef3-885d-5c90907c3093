import 'package:aitoearn_app/api/ai/ai_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:get/get.dart';

/// 全局AI配置控制器
/// 在应用启动时加载所有AI模型配置，供各页面共享使用
class GlobalAiConfigController extends GetxController {
  // 生成模型配置
  final generationModels = <dynamic>[].obs;
  // 编辑模型配置
  final editModels = <dynamic>[].obs;
  // 视频生成模型配置
  final videoGenerationModels = <dynamic>[].obs;

  // 是否加载完成
  bool isLoaded = false;

  static GlobalAiConfigController get to =>
      Get.find<GlobalAiConfigController>();

  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadAllModelConfigs();
  }

  Future<void> reload() async {
    isLoaded = false;
    await _loadAllModelConfigs();
  }

  /// 加载所有模型配置
  Future<void> _loadAllModelConfigs() async {
    try {
      // 并行请求生成模型和编辑模型配置
      final results = await Future.wait([
        getImageGenerationModelsApi(),
        getImageEditModelsApi(),
        getVideoGenerationModelsApi(),
      ]);

      // 处理生成模型配置
      if (results[0] != null && results[0]?['code'] == 0) {
        generationModels.assignAll(results[0]?['data'] ?? []);
      }

      // 处理编辑模型配置
      if (results[1] != null && results[1]?['code'] == 0) {
        editModels.assignAll(results[1]?['data'] ?? []);
      }

      // 处理视频生成模型配置
      if (results[2] != null && results[2]?['code'] == 0) {
        videoGenerationModels.assignAll(results[2]?['data'] ?? []);
      }

      isLoaded = true;
    } catch (e) {
      LoggerUtil.e('加载全局AI模型配置异常: $e');
      isLoaded = false; // 即使出错也标记为加载完成，避免阻塞UI
    }
  }
}
