import 'package:aitoearn_app/pages/ai_tools/ai_to_video/ai_to_video_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/global_ai_config_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SetParameterPanel extends StatelessWidget {
  final AiToVideoController controller;

  const SetParameterPanel({required this.controller, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Gaps.vGap8,
        // 模型选择
        _buildModelDropdown(),
        Gaps.vGap16,
        // 模式和尺寸
        Row(
          children: [
            Expanded(
              child: _dropdown(
                title: '尺寸',
                value: controller.size,
                items: controller.availableSizes,
              ),
            ),
            if (controller.availableModes.isNotEmpty) ...[
              Gaps.hGap16,
              Expanded(
                child: _dropdown(
                  title: '模式',
                  value: controller.mode,
                  items: controller.availableModes,
                ),
              ),
            ],
          ],
        ),
        Gaps.vGap16,
        // 时长选择
        _buildDurationSlider(),
      ],
    );
  }

  // 模型选择下拉框
  Widget _buildModelDropdown() {
    return Obx(() {
      final models = GlobalAiConfigController.to.videoGenerationModels;
      return DropdownButtonFormField<String>(
        value: controller.model.value,
        decoration: InputDecoration(
          labelText: '模型',
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        ),
        items:
            models
                .map(
                  (model) => DropdownMenuItem(
                    value: model['name'] as String,
                    child: model['name'].toString().toNormalText(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                )
                .toList(),
        onChanged: (v) {
          controller.model.value = v!;
          controller.updateModelParameters();
        },
      );
    });
  }

  // 通用下拉框
  Widget _dropdown({
    required String title,
    required RxString value,
    required List<String> items,
  }) {
    return Obx(() {
      return DropdownButtonFormField<String>(
        value: value.value,
        decoration: InputDecoration(
          labelText: title,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        ),
        items:
            items
                .map((item) => DropdownMenuItem(value: item, child: Text(item)))
                .toList(),
        onChanged: (v) => value.value = v!,
      );
    });
  }

  // 时长滑块
  Widget _buildDurationSlider() {
    return Obx(() {
      // 找到当前值在列表中的索引
      int currentIndex = controller.availableDurations.indexOf(
        controller.duration.value.toInt(),
      );
      if (currentIndex == -1 && controller.availableDurations.isNotEmpty) {
        currentIndex = 0;
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          '视频时长 (${controller.duration.value}秒)'.toNormalText(
            color: AppColors.textSecondColor,
          ),
          Slider(
            min: 0,
            max:
                controller.availableDurations.length - 1 > 0
                    ? controller.availableDurations.length - 1
                    : 0,
            value: currentIndex.toDouble(),
            onChanged: (double newValue) {
              final index = newValue.round();
              if (index >= 0 && index < controller.availableDurations.length) {
                controller.duration.value =
                    controller.availableDurations[index];
              }
            },
            divisions: controller.availableDurations.length - 1,
            label: '${controller.duration.value}秒',
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              controller.availableDurations.isNotEmpty
                  ? '${controller.availableDurations.first}秒'.toNormalText(
                    fontSize: 12,
                  )
                  : ''.toNormalText(fontSize: 12),
              controller.availableDurations.isNotEmpty
                  ? '${controller.availableDurations.last}秒'.toNormalText(
                    fontSize: 12,
                  )
                  : ''.toNormalText(fontSize: 12),
            ],
          ),
        ],
      );
    });
  }
}
