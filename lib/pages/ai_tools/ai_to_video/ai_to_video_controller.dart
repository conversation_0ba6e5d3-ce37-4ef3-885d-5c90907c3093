import 'dart:async';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/ai_tools/global_ai_config_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/global_media_generation_controller.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/image_utils.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

/// AI视频生成控制器
class AiToVideoController extends GetxController {
  // 添加bucketId参数
  final String bucketId = Get.arguments?['bucketId'] ?? '';

  final userId = Get.find<UserStoreService>().userInfo.value?.id ?? '';

  // 添加页面进入时间
  DateTime? startTime;

  // 生成参数
  final promptText = ''.obs; // 描述文本（必填）
  final model = ''.obs; // 模型名称，初始为空
  final image = ''.obs; // 起始图（base64编码，可选）
  final mode = ''.obs; // 模式
  final size = ''.obs; // 视频尺寸
  final duration = 5.obs; // 视频时长（秒）

  // 动态配置
  final availableModes = <String>[].obs; // 当前可用模式选项
  final availableSizes = <String>[].obs; // 当前可用尺寸选项
  final availableDurations = <int>[].obs; // 当前可用时长选项

  final isSetParamsMode = false.obs; // 设置参数模式

  // 状态控制
  final isGenerating = false.obs; // 是否正在生成

  // 本地添加参考的图片
  final localImage = ''.obs;

  // 直接使用GenerationStatus列表替代Batch
  final generationStatusList = <GenerationStatus>[].obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    startTime = DateTime.now();
    ever(localImage, (_) => _updateModelConfigs());
    // 等待全局配置加载完成
    if (!GlobalAiConfigController.to.isLoaded) {
      await GlobalAiConfigController.to.reload();
    }
    _updateModelConfigs();

    // 监听全局生成状态变化
    ever(GlobalMediaGenerationController.to.generationStatus, (_) {
      _updateGenerationStatusList();
    });

    // 初始更新状态列表
    _updateGenerationStatusList();
  }

  /// 更新模型配置
  void _updateModelConfigs() {
    final models = GlobalAiConfigController.to.videoGenerationModels;
    if (models.isEmpty) {
      return;
    }
    if (model.value.isEmpty || !models.any((m) => m['name'] == model.value)) {
      model.value = models.first['name'];
    }
    updateModelParameters();
  }

  /// 更新当前模型的可用参数
  void updateModelParameters() {
    final currentModel = GlobalAiConfigController.to.videoGenerationModels
        .firstWhereOrNull((m) => m['name'] == model.value);

    if (currentModel == null) {
      return;
    }

    // 更新可用模式选项
    availableModes.assignAll(currentModel['modes']?.cast<String>() ?? []);
    // 更新可用尺寸选项
    availableSizes.assignAll(currentModel['sizes']?.cast<String>() ?? []);
    // 更新可用时长选项
    availableDurations.assignAll(currentModel['durations']?.cast<int>() ?? []);

    // 重置参数到第一个选项或空字符串
    mode.value = availableModes.isNotEmpty ? availableModes.first : '';
    size.value = availableSizes.isNotEmpty ? availableSizes.first : '';
    // 设置默认时长
    if (availableDurations.isNotEmpty) {
      duration.value = availableDurations.first;
    }
  }

  /// 生成视频
  Future<void> generateVideo() async {
    if (promptText.value.isEmpty) {
      showError('请输入视频描述内容');
      return;
    }

    isGenerating.value = true;
    try {
      String? base64Image;
      if (localImage.value.isNotEmpty) {
        // 处理图片：缩放并转换为base64
        base64Image = await ImageUtils.processImageForEdit(localImage.value);
        if (base64Image == null) {
          showError('图片处理失败，请重试');
          return;
        }
      }

      // 使用全局控制器添加视频生成任务
      GlobalMediaGenerationController.to.addVideoGenerationTask(
        prompt: promptText.value,
        model: model.value,
        mode: mode.value,
        size: size.value,
        duration: duration.value.toInt(),
        bucketId: bucketId,
        image: base64Image,
      );

      showSuccess('视频生成任务已提交');
    } catch (e) {
      LoggerUtil.e('视频生成请求异常: $e');
      showError('网络异常，请重试');
    } finally {
      isGenerating.value = false;
    }
  }

  /// 更新生成状态列表
  void _updateGenerationStatusList() {
    // 获取当前bucket的所有生成状态
    final statusList =
        GlobalMediaGenerationController.to.generationStatus[bucketId] ?? [];

    // 过滤出视频类型且创建时间晚于startTime的状态
    List<GenerationStatus> filteredStatus =
        statusList
            .where(
              (s) =>
                  s.mediaType == MediaType.video &&
                  s.createdAt.isAfter(startTime ?? DateTime.now()),
            )
            .toList();

    // 按创建时间降序排序
    filteredStatus.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    generationStatusList.assignAll(filteredStatus);
  }

  /// 选择图片并转换为base64
  Future<void> selectImage() async {
    final imagePath = await ImagePicker().pickImage(
      source: ImageSource.gallery,
    );
    localImage.value = imagePath?.path ?? '';
  }

  // 添加清除本地图片的方法
  void clearLocalImage() {
    localImage.value = '';
  }
}
