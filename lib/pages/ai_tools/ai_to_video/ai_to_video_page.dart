import 'dart:io';
import 'package:aitoearn_app/pages/ai_tools/ai_to_video/ai_to_video_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/ai_to_video/widget/set_parameter_panel.dart';
import 'package:aitoearn_app/pages/ai_tools/global_ai_config_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/global_media_generation_controller.dart';
import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiToVideoPage extends GetView<AiToVideoController> {
  const AiToVideoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'AI视频生成',
      body: GestureDetector(
        // 添加点击空白区域收起面板的手势
        onTap: () => controller.isSetParamsMode.value = false,
        behavior: HitTestBehavior.translucent,
        child: Column(
          children: [
            _buildResultArea(),
            Gaps.vGap12,
            // 为参数面板添加防点击穿透
            GestureDetector(
              onTap: () {}, // 空实现，防止点击面板内部触发外部收起
              child: Obx(() {
                final isExpanded = controller.isSetParamsMode.value;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      _buildParameterHeader(),
                      Gaps.vGap12,
                      AnimatedCrossFade(
                        duration: const Duration(milliseconds: 200),
                        crossFadeState:
                            isExpanded
                                ? CrossFadeState.showFirst
                                : CrossFadeState.showSecond,
                        firstChild: SetParameterPanel(controller: controller),
                        secondChild: Column(
                          children: [
                            _buildInputField(),
                            Gaps.vGap12,
                            _buildGenerateButton(),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ],
        ).marginOnly(top: 0, left: 16, right: 16, bottom: 16),
      ),
    );
  }

  Widget _buildInputField() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: TextField(
            decoration: const InputDecoration(
              hintText: '描述你想要创作的视频内容',
              hintStyle: TextStyle(color: AppColors.textHintColor),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(1),
            ),
            maxLines: 4,
            onChanged: (value) => controller.promptText.value = value,
          ),
        ),
        Gaps.hGap10,
        // 参考图片显示区域，添加删除按钮
        Stack(
          children: [
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Obx(() {
                if (controller.localImage.value.isNotEmpty) {
                  return Image.file(
                    File(controller.localImage.value),
                    fit: BoxFit.cover,
                  );
                }
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Gaps.vGap8,
                    const Icon(
                      Icons.add,
                      size: 28,
                      color: AppColors.textHintColor,
                    ),
                    Gaps.vGap4,
                    '添加参考'.toNormalText(
                      color: AppColors.textHintColor,
                      fontSize: 11,
                    ),
                  ],
                );
              }),
            ).gestures(onTap: () => controller.selectImage()),
            // 右上角删除按钮
            Obx(() {
              if (controller.localImage.value.isNotEmpty) {
                return Positioned(
                  top: 3,
                  right: 3,
                  child: Container(
                    width: 22,
                    height: 22,
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 14,
                      color: Colors.white,
                    ),
                  ).gestures(onTap: () => controller.clearLocalImage()),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildGenerateButton() {
    return Obx(
      () => CustomButton(
        text: controller.isGenerating.value ? '生成中...' : '生成视频',
        width: double.infinity,
        height: 50,
        gradient: AppColors.blueAndPurple,
        textColor: Colors.white,
        showLoading: controller.isGenerating.value,
        enable:
            !controller.isGenerating.value && controller.promptText.isNotEmpty,
        onPressed: () => controller.generateVideo(),
      ),
    );
  }

  Widget _buildParameterHeader() {
    return GestureDetector(
      onTap:
          () =>
              controller.isSetParamsMode.value =
                  !controller.isSetParamsMode.value,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Obx(() {
          final isExpanded = controller.isSetParamsMode.value;
          // 获取当前模型信息
          final currentModel = GlobalAiConfigController.to.videoGenerationModels
              .firstWhereOrNull((m) => m['name'] == controller.model.value);

          return Row(
            children: [
              Image.asset(
                'assets/images/ai/icon_model.png',
                width: 20,
                height: 20,
              ),
              Gaps.hGap8,
              // 显示模型名称和描述
              ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 160),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    controller.model.value.toNormalText(
                      color: AppColors.textSecondColor,
                      fontSize: 13,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (currentModel?['description'] != null)
                      currentModel!['description'].toString().toNormalText(
                        color: AppColors.textHintColor,
                        fontSize: 11,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              Gaps.hGap8,
              Container(
                width: 1,
                height: 18,
                color: AppColors.deepDividerColor,
              ),
              Gaps.hGap8,
              // 显示当前选择的参数
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    '${controller.size.value} ${controller.duration.value}秒'
                        .toNormalText(
                          color: AppColors.textSecondColor,
                          fontSize: 14,
                        ),
                    if (controller.mode.value.isNotEmpty)
                      '模式: ${controller.mode.value}'.toNormalText(
                        color: AppColors.textSecondColor,
                        fontSize: 12,
                      ),
                  ],
                ),
              ),
              AnimatedRotation(
                turns: isExpanded ? 0.5 : 0,
                duration: const Duration(milliseconds: 150),
                child: const Icon(
                  Icons.arrow_drop_down,
                  size: 20,
                  color: AppColors.textSecondColor,
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildResultArea() {
    return Expanded(
      child: Obx(() {
        // 直接使用generationStatusList
        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          itemCount: controller.generationStatusList.length,
          itemBuilder: (context, index) {
            final status = controller.generationStatusList[index];
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 使用创建时间作为标题
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 4,
                    horizontal: 0,
                  ),
                  child: Text(
                    // 格式化时间显示
                    '${status.createdAt.year}-${status.createdAt.month.toString().padLeft(2, '0')}-${status.createdAt.day.toString().padLeft(2, '0')} ${status.createdAt.hour.toString().padLeft(2, '0')}:${status.createdAt.minute.toString().padLeft(2, '0')}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textColor,
                    ),
                  ),
                ),
                // 视频项
                SizedBox(
                  width: 160,
                  height: 100,
                  child: _buildGenerationStatusItem(context, status),
                ),
                // 项间隔
                const SizedBox(height: 16),
              ],
            );
          },
        );
      }),
    );
  }

  // 状态展示组件
  Widget _buildGenerationStatusItem(
    BuildContext context,
    GenerationStatus status,
  ) {
    Widget content;

    switch (status.status) {
      case GenerationStatusType.completed:
        content = Stack(
          children: [
            Container(
              color: Colors.black,
              // child: VideoPlayerController.network(status.videoUrl!)
              //   .initialize().then((_) => VideoPlayer(
              //     VideoPlayerController.network(status.videoUrl!),
              //   )),
            ),
            const Center(
              child: Icon(Icons.play_arrow, color: Colors.white, size: 40),
            ),
          ],
        );
        break;
      case GenerationStatusType.error:
        content = Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: AppColors.errorColor, size: 32),
            Gaps.vGap12,
            status.message.toNormalText(
              color: AppColors.textHintColor,
              fontSize: 12,
            ),
          ],
        );
        break;
      default:
        content = Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(),
            ),
            Gaps.vGap12,
            status.message.toNormalText(
              color: AppColors.textHintColor,
              fontSize: 12,
            ),
          ],
        );
    }

    return Stack(
      children: [
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: content,
          ),
        ),
      ],
    );
  }
}
