import 'package:aitoearn_app/pages/ai_tools/global_ai_config_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/global_media_generation_controller.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/image_utils.dart'; // 导入图片工具类
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

/// AI图片生成控制器
/// 对接generateAiImageApi接口，管理图片生成参数和状态
class AiToImageController extends GetxController {
  // 添加bucketId参数
  final String bucketId = Get.arguments?['bucketId'] ?? '';

  final userId = Get.find<UserStoreService>().userInfo.value?.id ?? '';

  // 添加页面进入时间
  DateTime? startTime;

  // 生成参数 - 与接口严格对应
  final promptText = ''.obs; // 描述文本（必填）
  final model = ''.obs; // 模型名称，初始为空
  final imageCount = 3.obs; // 生成数量（1-10）
  final quality = ''.obs; // 图片质量
  final size = ''.obs; // 图片尺寸
  final style = ''.obs; // 图片风格

  // 动态配置
  final availableQualities = <String>[].obs; // 当前可用质量选项
  final availableSizes = <String>[].obs; // 当前可用尺寸选项
  final availableStyles = <String>[].obs; // 当前可用风格选项

  final isSetParamsMode = false.obs; // 设置参数模式

  // 状态控制
  final isGenerating = false.obs; // 是否正在生成
  // 删除generatedImages，添加generationStatusList
  final generationStatusList = <GenerationStatus>[].obs; // 生成状态列表

  // 本地添加参考的图片
  final localImage = ''.obs;

  // 添加批次列表，用于分组展示
  final batches = <Batch>[].obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    startTime = DateTime.now();
    ever(localImage, (_) => _updateModelConfigs());
    ever(
      GlobalMediaGenerationController.to.generationStatus,
      _updateGeneratedImages,
    );
    // 等待全局配置加载完成
    if (!GlobalAiConfigController.to.isLoaded) {
      await GlobalAiConfigController.to.reload();
    }
    _updateModelConfigs();
  }

  /// 根据全局生成状态更新本地图片列表
  void _updateGeneratedImages(Map<String, List<GenerationStatus>> statusMap) {
    // 只处理当前bucketId的状态
    if (!statusMap.containsKey(bucketId)) {
      generationStatusList.clear();
      _updateBatches();
      return;
    }

    // 过滤出当前页面进入后创建的任务
    final filteredTasks =
        statusMap[bucketId]!.where((task) {
          return startTime != null && task.createdAt.isAfter(startTime!);
        }).toList();

    generationStatusList.assignAll(filteredTasks);
    _updateBatches(); // 更新批次分组
  }

  /// 更新批次分组
  void _updateBatches() {
    // 按批次key分组
    Map<String, List<GenerationStatus>> batchesMap = {};

    for (var status in generationStatusList) {
      String batchKey = _extractBatchKey(status.requestId);
      if (!batchesMap.containsKey(batchKey)) {
        batchesMap[batchKey] = [];
      }
      batchesMap[batchKey]!.add(status);
    }

    // 改进排序逻辑：先按时间戳降序，再按类型排序
    List<String> sortedKeys =
        batchesMap.keys.toList()..sort((a, b) {
          // 提取时间戳部分
          int? getTimestamp(String key) {
            List<String> parts = key.split('_');
            if (parts.length >= 2) {
              return int.tryParse(parts[1]);
            }
            return null;
          }

          int? timestampA = getTimestamp(a);
          int? timestampB = getTimestamp(b);

          // 优先按时间戳降序排序
          if (timestampA != null && timestampB != null) {
            int timestampCompare = timestampB.compareTo(timestampA);
            if (timestampCompare != 0) {
              return timestampCompare;
            }
          }

          // 时间戳相同时，按类型排序（编辑在前，生成在后）
          bool isEditA = a.startsWith('edit');
          bool isEditB = b.startsWith('edit');

          if (isEditA && !isEditB) {
            return -1;
          }
          if (!isEditA && isEditB) {
            return 1;
          }

          return a.compareTo(b);
        });

    // 转换为Batch列表
    List<Batch> newBatches =
        sortedKeys.map((key) {
          String title = key.startsWith('gen') ? '生成图片' : '编辑图片';
          return Batch(title: title, items: batchesMap[key]!);
        }).toList();

    batches.assignAll(newBatches);
  }

  /// 从requestId提取批次key
  String _extractBatchKey(String requestId) {
    List<String> parts = requestId.split('_');
    if (parts.length >= 3) {
      return '${parts[0]}_${parts[1]}';
    }
    return requestId;
  }

  /// 生成/编辑图片 - 使用全局控制器处理
  Future<void> generateOrEditImage() async {
    if (promptText.value.isEmpty) {
      showError('请输入描述内容');
      return;
    }

    isGenerating.value = true;
    try {
      if (localImage.value.isNotEmpty) {
        // 图片编辑流程
        // 1. 处理图片：缩放并转换为base64
        final base64Image = await ImageUtils.processImageForEdit(
          localImage.value,
        );
        if (base64Image == null) {
          showError('图片处理失败，请重试');
          return;
        }

        // 2. 调用全局控制器添加编辑任务
        GlobalMediaGenerationController.to.addImageEditTasks(
          prompt: promptText.value,
          model: model.value,
          size: size.value,
          image: base64Image,
          bucketId: bucketId,
          imageCount: imageCount.value.toInt(),
        );
      } else {
        // 图片生成流程
        GlobalMediaGenerationController.to.addImageGenerationTasks(
          prompt: promptText.value,
          model: model.value,
          quality: quality.value,
          size: size.value,
          style: style.value,
          bucketId: bucketId,
          imageCount: imageCount.value.toInt(),
        );
      }
    } finally {
      isGenerating.value = false;
    }
  }

  // 根据localImage状态更新模型配置
  void _updateModelConfigs() {
    final models =
        localImage.value.isEmpty
            ? GlobalAiConfigController.to.generationModels
            : GlobalAiConfigController.to.editModels;
    if (models.isEmpty) {
      return;
    }
    if (model.value.isEmpty || !models.any((m) => m['name'] == model.value)) {
      model.value = models.first['name'];
    }
    updateModelParameters();
  }

  // 更新当前模型的可用参数
  void updateModelParameters() {
    final currentModel = (localImage.value.isEmpty
            ? GlobalAiConfigController.to.generationModels
            : GlobalAiConfigController.to.editModels)
        .firstWhereOrNull((m) => m['name'] == model.value);

    if (currentModel == null) {
      return;
    }

    // 更新可用质量选项
    availableQualities.assignAll(
      currentModel['qualities']?.cast<String>() ?? [],
    );
    // 更新可用尺寸选项
    availableSizes.assignAll(currentModel['sizes']?.cast<String>() ?? []);
    // 更新可用风格选项
    availableStyles.assignAll(currentModel['styles']?.cast<String>() ?? []);

    // 重置参数到第一个选项或空字符串
    quality.value =
        availableQualities.isNotEmpty ? availableQualities.first : '';
    size.value = availableSizes.isNotEmpty ? availableSizes.first : '';
    style.value = availableStyles.isNotEmpty ? availableStyles.first : '';
  }

  /// 调整生成数量
  void adjustCount(int delta) {
    final newCount = imageCount.value + delta;
    if (newCount >= 1 && newCount <= 10) {
      imageCount.value = newCount;
    }
  }

  /// 选择图片并转换为base64
  Future<void> selectImage() async {
    final imagePath = await ImagePicker().pickImage(
      source: ImageSource.gallery,
    );
    localImage.value = imagePath?.path ?? '';
  }

  // 添加清除本地图片的方法
  void clearLocalImage() {
    localImage.value = '';
  }
}

/// 批次模型，包含标题和对应图片列表
class Batch {
  final String title;
  final List<GenerationStatus> items;

  Batch({required this.title, required this.items});
}
