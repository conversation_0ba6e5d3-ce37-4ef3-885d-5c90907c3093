import 'package:aitoearn_app/pages/ai_tools/ai_to_image/ai_to_image_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GenerateButton extends StatelessWidget {
  final AiToImageController controller;

  const GenerateButton({required this.controller, super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final isGenerating = controller.isGenerating.value;
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withOpacity(0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                _countButton(Icons.remove, () => controller.adjustCount(-1)),
                SizedBox(
                  width: 60,
                  child: Center(
                    child: '${controller.imageCount.value}'.toNormalText(
                      fontSize: 16,
                    ),
                  ),
                ),
                _countButton(Icons.add, () => controller.adjustCount(1)),
              ],
            ),
          ),
          SizedBox(
            width: 120,
            child: CustomButton(
              text: isGenerating ? '生成中...' : '生成',
              height: 42,
              color: AppColors.primaryColor,
              borderRadius: 10,
              textColor: Colors.white,
              showLoading: isGenerating,
              enable: !isGenerating,
              onPressed: controller.generateOrEditImage,
            ),
          ),
        ],
      );
    });
  }

  Widget _countButton(IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 30,
        height: 30,
        color: AppColors.primaryColor.withAlpha(60),
        child: Icon(icon, color: AppColors.primaryColor),
      ),
    );
  }
}
