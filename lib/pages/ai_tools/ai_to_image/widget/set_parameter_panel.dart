import 'package:aitoearn_app/pages/ai_tools/ai_to_image/ai_to_image_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/global_ai_config_controller.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SetParameterPanel extends StatelessWidget {
  final AiToImageController controller;

  const SetParameterPanel({required this.controller, super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // 根据localImage状态显示不同的模型列表
      final models =
          controller.localImage.value.isEmpty
              ? GlobalAiConfigController.to.generationModels
              : GlobalAiConfigController.to.editModels;

      return Column(
        children: [
          Gaps.vGap8,
          // 模型选择下拉框
          _dropdown(
            title: '模型',
            value: controller.model,
            items: models.map((m) => m['name'] as String).toList(),
            onChanged: (value) {
              controller.model.value = value!;
              controller.updateModelParameters();
            },
          ),
          Gaps.vGap16,
          Row(
            children: [
              // 尺寸选择下拉框
              Expanded(
                child: _dropdown(
                  title: '尺寸',
                  value: controller.size,
                  items: controller.availableSizes,
                ),
              ), // 质量选择下拉框
              Gaps.hGap16,
              Expanded(
                child: _dropdown(
                  title: '质量',
                  value: controller.quality,
                  items: controller.availableQualities,
                ),
              ),
            ],
          ),
          if (controller.availableStyles.isNotEmpty) Gaps.vGap16,
          // 风格选择下拉框
          _dropdown(
            title: '风格',
            value: controller.style,
            items: controller.availableStyles,
          ),
        ],
      );
    });
  }

  Widget _dropdown({
    required String title,
    required RxString value,
    required List<String> items,
    Function(String?)? onChanged,
  }) {
    if (items.isEmpty) {
      return const SizedBox();
    }

    return DropdownButtonFormField<String>(
      value:
          items.isNotEmpty && !items.contains(value.value)
              ? items.first
              : value.value,
      decoration: InputDecoration(
        labelText: title,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
      ),
      items:
          items
              .map((item) => DropdownMenuItem(value: item, child: Text(item)))
              .toList(),
      onChanged: onChanged ?? (v) => value.value = v!,
      disabledHint: items.isEmpty ? Text('无$title选项') : null,
    );
  }
}
