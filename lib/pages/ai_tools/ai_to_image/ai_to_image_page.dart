import 'dart:io';

import 'package:aitoearn_app/pages/ai_tools/ai_to_image/ai_to_image_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/ai_to_image/widget/generate_button.dart';
import 'package:aitoearn_app/pages/ai_tools/ai_to_image/widget/set_parameter_panel.dart';
import 'package:aitoearn_app/pages/ai_tools/global_ai_config_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/global_media_generation_controller.dart';
import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/utils/image_preview_viewer.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiToImagePage extends GetView<AiToImageController> {
  const AiToImagePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'AI生图',
      body: GestureDetector(
        // 添加点击空白区域收起面板的手势
        onTap: () => controller.isSetParamsMode.value = false,
        behavior: HitTestBehavior.translucent,
        child: Column(
          children: [
            _buildResultArea(),
            Gaps.vGap12,
            // 为参数面板添加防点击穿透
            GestureDetector(
              onTap: () {}, // 空实现，防止点击面板内部触发外部收起
              child: Obx(() {
                final isExpanded = controller.isSetParamsMode.value;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      _buildParameterHeader(),
                      Gaps.vGap12,
                      AnimatedCrossFade(
                        duration: const Duration(milliseconds: 200),
                        crossFadeState:
                            isExpanded
                                ? CrossFadeState.showFirst
                                : CrossFadeState.showSecond,
                        firstChild: SetParameterPanel(controller: controller),
                        secondChild: Column(
                          children: [
                            _buildInputField(),
                            Gaps.vGap12,
                            GenerateButton(controller: controller),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ],
        ).marginOnly(top: 0, left: 16, right: 16, bottom: 16),
      ),
    );
  }

  Widget _buildInputField() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: TextField(
            decoration: const InputDecoration(
              hintText: '描述你想要创作的内容',
              hintStyle: TextStyle(color: AppColors.textHintColor),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(1),
            ),
            maxLines: 4,
            onChanged: (value) => controller.promptText.value = value,
          ),
        ),
        Gaps.hGap10,
        // 修改参考图片显示区域，添加删除按钮
        Stack(
          children: [
            Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Obx(() {
                if (controller.localImage.value.isNotEmpty) {
                  return Image.file(
                    File(controller.localImage.value),
                    fit: BoxFit.cover,
                  );
                }
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Gaps.vGap8,
                    const Icon(
                      Icons.add,
                      size: 28,
                      color: AppColors.textHintColor,
                    ),
                    Gaps.vGap4,
                    '添加参考'.toNormalText(
                      color: AppColors.textHintColor,
                      fontSize: 11,
                    ),
                  ],
                );
              }),
            ).gestures(onTap: () => controller.selectImage()),
            // 右上角删除按钮
            Obx(() {
              if (controller.localImage.value.isNotEmpty) {
                return Positioned(
                  top: 3,
                  right: 3,
                  child: Container(
                    width: 22,
                    height: 22,
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 14,
                      color: Colors.white,
                    ),
                  ).gestures(onTap: () => controller.clearLocalImage()),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildParameterHeader() {
    return GestureDetector(
      onTap:
          () =>
              controller.isSetParamsMode.value =
                  !controller.isSetParamsMode.value,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Obx(() {
          final isExpanded = controller.isSetParamsMode.value;
          // 获取当前模型信息
          final currentModel = (controller.localImage.value.isEmpty
                  ? GlobalAiConfigController.to.generationModels
                  : GlobalAiConfigController.to.editModels)
              .firstWhereOrNull((m) => m['name'] == controller.model.value);

          return Row(
            children: [
              Image.asset(
                'assets/images/ai/icon_model.png',
                width: 20,
                height: 20,
              ),
              Gaps.hGap8,
              // 显示模型名称和描述
              ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 140),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    controller.model.value.toNormalText(
                      color: AppColors.textSecondColor,
                      fontSize: 13,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (currentModel?['description'] != null)
                      currentModel!['description'].toString().toNormalText(
                        color: AppColors.textHintColor,
                        fontSize: 11,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
              Gaps.hGap8,
              Container(
                width: 1,
                height: 18,
                color: AppColors.deepDividerColor,
              ),
              Gaps.hGap8,
              // 显示当前选择的参数
              Expanded(
                child: Row(
                  children: [
                    // 动态矩形 - 根据size计算宽高比
                    Obx(() {
                      if (controller.size.value.isEmpty) {
                        return const SizedBox.shrink();
                      }

                      if (!controller.size.value.contains('x')) {
                        return const SizedBox.shrink();
                      }

                      // 解析尺寸字符串 (格式: WxH)
                      final sizeParts = controller.size.value.split('x');
                      if (sizeParts.length != 2) {
                        return const SizedBox.shrink();
                      }

                      final width = double.tryParse(sizeParts[0]) ?? 0;
                      final height = double.tryParse(sizeParts[1]) ?? 0;

                      if (width == 0 || height == 0) {
                        return const SizedBox.shrink();
                      }

                      // 固定高度为20，按比例计算宽度
                      const fixedHeight = 24.0;
                      final displayWidth = (width / height) * fixedHeight;

                      return Container(
                        width: displayWidth,
                        height: fixedHeight,
                        margin: const EdgeInsets.only(right: 6),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: AppColors.primaryColor,
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(3),
                          gradient: LinearGradient(
                            colors: [
                              AppColors.textColor.withAlpha(200),
                              AppColors.textColor.withAlpha(55),
                              AppColors.textColor.withAlpha(0),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                        ),
                      );
                    }),

                    if (controller.size.value.isNotEmpty)
                      controller.size.value.toNormalText(
                        color: AppColors.textSecondColor,
                        fontSize: 14,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    Gaps.hGap16,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (controller.quality.value.isNotEmpty)
                          '质量: ${controller.quality.value}'.toNormalText(
                            color: AppColors.textSecondColor,
                            fontSize: 12,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        if (controller.style.value.isNotEmpty)
                          '风格: ${controller.style.value}'.toNormalText(
                            color: AppColors.textSecondColor,
                            fontSize: 12,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              AnimatedRotation(
                turns: isExpanded ? 0.5 : 0,
                duration: const Duration(milliseconds: 150),
                child: const Icon(
                  Icons.arrow_drop_down,
                  size: 20,
                  color: AppColors.textSecondColor,
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildResultArea() {
    return Expanded(
      child: Obx(() {
        // 使用ListView展示批次列表
        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          itemCount: controller.batches.length,
          itemBuilder: (context, index) {
            final batch = controller.batches[index];
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 批次标题
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 4,
                    horizontal: 0,
                  ),
                  child: Text(
                    batch.title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textColor,
                    ),
                  ),
                ),
                // 批次图片网格
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      batch.items
                          .map(
                            (status) => SizedBox(
                              width: 100,
                              height: 100,
                              child: _buildGenerationStatusItem(
                                context,
                                status,
                              ),
                            ),
                          )
                          .toList(),
                ),
                // 批次间隔
                const SizedBox(height: 16),
              ],
            );
          },
        );
      }),
    );
  }

  // 添加状态展示组件
  Widget _buildGenerationStatusItem(
    BuildContext context,
    GenerationStatus status,
  ) {
    // 实现与media_bucket_page中相同的展示逻辑
    Widget content;

    switch (status.status) {
      case GenerationStatusType.completed:
        content = NetworkImageWidget(
          status.url,
          heroTag: '${status.url}_ai',
          width: double.infinity,
          height: double.infinity,
          fit: BoxFit.cover,
          borderRadius: BorderRadius.circular(8),
        ).gestures(
          onTap: () {
            ImagePreviewViewer.show(
              context: context,
              imageUrls: [status.url],
              heroTags: ['${status.url}_ai'],
            );
          },
        );
        break;
      case GenerationStatusType.error:
        content = Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: AppColors.errorColor, size: 32),
            Gaps.vGap12,
            status.message.toNormalText(
              color: AppColors.textHintColor,
              fontSize: 12,
            ),
          ],
        );
        break;
      default:
        content = Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(),
            ),
            Gaps.vGap12,
            status.message.toNormalText(
              color: AppColors.textHintColor,
              fontSize: 12,
            ),
          ],
        );
    }

    return Stack(
      children: [
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: content,
          ),
        ),
      ],
    );
  }
}
