import 'package:aitoearn_app/pages/interaction/bindings.dart';
import 'package:aitoearn_app/pages/interaction/controller.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_controller.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomePageController extends GetxController {
  HomePageController();

  final currentIndex = 0.obs;

  late final PublishLogic publishLogic;

  @override
  void onInit() {
    super.onInit();
    _initDependencies();
    _getGlobalPublishService();
  }

  @override
  void onReady() {
    super.onReady();
    checkBackgroundPublishTasks();
  }

  // 初始化依赖
  void _initDependencies() {
    if (!Get.isRegistered<InteractionController>() || !Get.isRegistered<FilterController>()) {
      InteractionBinding().dependencies();
    }
  }

  // 获取全局发布服务
  void _getGlobalPublishService() {
    try {
      publishLogic = Get.find<PublishLogic>(tag: 'global');
    } catch (e) {
      publishLogic = Get.put<PublishLogic>(
        PublishLogic(),
        permanent: true,
        tag: 'global',
      );
    }
  }

  // 页面切换
  void changePage(int index) {
    if (index == currentIndex.value) {
      return;
    }
    currentIndex.value = index;
    _handlePageDependencies(index);
  }

  // 处理页面依赖
  void _handlePageDependencies(int index) {
    if (index == 1) {
      _initDependencies();
    }
  }

  // 检查后台发布任务
  void checkBackgroundPublishTasks() {
    if (publishLogic.state.isBackgroundPublishing.value) {
      Get.snackbar(
        '后台发布中', 
        '内容正在后台发布中，请稍候...',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.blue[100],
        colorText: Colors.blue[800],
        duration: const Duration(seconds: 2),
      );
    } else if (publishLogic.state.isBackgroundPublishingCompleted.value) {
      final result = publishLogic.state.backgroundPublishResult.value;
      if (result.isNotEmpty) {
        final successCount = result['successCount'] ?? 0;
        final failCount = result['failCount'] ?? 0;
        
        Get.snackbar(
          '发布完成', 
          '${successCount}个内容发布成功' + (failCount > 0 ? ', ${failCount}个失败' : ''),
          snackPosition: SnackPosition.TOP,
          backgroundColor: failCount > 0 ? Colors.orange[100] : Colors.green[100],
          colorText: failCount > 0 ? Colors.orange[800] : Colors.green[800],
          duration: const Duration(seconds: 3),
          mainButton: TextButton(
            onPressed: () => publishLogic.showBackgroundPublishResultDialog(result),
            child: const Text('查看详情'),
          ),
        );
        
        publishLogic.state.isBackgroundPublishingCompleted.value = false;
        publishLogic.state.backgroundPublishResult.value = {};
      }
    }
  }
}
