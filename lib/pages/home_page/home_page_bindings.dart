import 'package:aitoearn_app/pages/ai_tools/global_ai_config_controller.dart';
import 'package:aitoearn_app/pages/home_page/home_page_controller.dart';
import 'package:aitoearn_app/pages/interaction/bindings.dart';
import 'package:aitoearn_app/pages/mine/home_mine_controller.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/pages/publish/publish_record/logic.dart';
import 'package:get/get.dart';

class HomePageBinding implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => HomePageController());
    Get.lazyPut(() => HomeMineController());
    Get.lazyPut(() => PublishLogic());
    Get.lazyPut(() => PublishRecordLogic());
    InteractionBinding().dependencies();
    Get.put(GlobalAiConfigController());
  }
}
