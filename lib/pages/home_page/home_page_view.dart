import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/home_page/home_page_controller.dart';
import 'package:aitoearn_app/pages/interaction/index.dart';
import 'package:aitoearn_app/pages/mine/home_mine_view.dart';
import 'package:aitoearn_app/pages/publish/publish_home/view.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomePage extends GetView<HomePageController> {
  const HomePage({super.key});

  final List<Widget> _pages = const [
    PublishHomePage(),
    InteractionPage(),
    // SizedBox(),
    // PublishPage(),
    HomeMineView(),
  ];

  @override
  Widget build(BuildContext context) {
    return BasePage(
      useSafeArea: false,
      body: ResponsiveLayout(
        mobile: Column(
          children: [
            // 页面主体
            Expanded(
              child: Obx(
                () => IndexedStack(
                  index: controller.currentIndex.value,
                  children: _pages,
                ),
              ),
            ),
            // 底部导航栏
            _buildBottomNavBar(),
          ],
        ),
        desktop: Row(
          children: [
            // 左侧导航栏
            _buildSideNavBar(),
            // 页面主体
            Expanded(
              child: Obx(
                () => IndexedStack(
                  index: controller.currentIndex.value,
                  children: _pages,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 移动端底部导航栏
  Widget _buildBottomNavBar() {
    return Obx(
      () => BottomNavigationBar(
        backgroundColor: AppColors.cardColor,
        type: BottomNavigationBarType.fixed,
        currentIndex: controller.currentIndex.value,
        selectedFontSize: 15,
        unselectedFontSize: 14,
        onTap: controller.changePage,
        items: [
          BottomNavigationBarItem(
            icon: const SizedBox.shrink(),
            label: 'homePageTitle'.tr,
          ),
          BottomNavigationBarItem(
            icon: const SizedBox.shrink(),
            label: 'BNBInteraction'.tr,
          ),
          // BottomNavigationBarItem(
          //   icon: const SizedBox.shrink(),
          //   label: 'dataInfo'.tr,
          // ),
          BottomNavigationBarItem(
            icon: const SizedBox.shrink(),
            label: 'BNBMine'.tr,
          ),
        ],
      ),
    );
  }

  /// 桌面端左侧导航栏（可替换为 NavigationRail 或自定义组件）
  Widget _buildSideNavBar() {
    return Obx(
      () => NavigationRail(
        backgroundColor: AppColors.halfTabColor,
        selectedIndex: controller.currentIndex.value,
        onDestinationSelected: controller.changePage,
        labelType: NavigationRailLabelType.all,
        destinations: [
          NavigationRailDestination(
            icon: const Icon(Icons.home),
            padding: const EdgeInsets.all(8),
            label: Text('homePageTitle'.tr),
          ),
          NavigationRailDestination(
            icon: const Icon(Icons.integration_instructions_outlined),
            padding: const EdgeInsets.all(8),
            label: Text('BNBInteraction'.tr),
          ),
          // NavigationRailDestination(
          //   icon: const Icon(Icons.send),
          //   padding: const EdgeInsets.all(8),
          //   label: Text('BNBPub'.tr),
          // ),
          NavigationRailDestination(
            icon: const Icon(Icons.person),
            padding: const EdgeInsets.all(8),
            label: Text('BNBMine'.tr),
          ),
        ],
      ),
    );
  }
}
