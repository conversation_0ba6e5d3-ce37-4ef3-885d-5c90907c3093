import 'package:aitoearn_app/pages/interaction/controller.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_controller.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_panel_controller.dart';
import 'package:aitoearn_app/pages/interaction/widgets/content_list.dart';
import 'package:aitoearn_app/pages/interaction/widgets/filter_panel.dart';
import 'package:aitoearn_app/pages/interaction/widgets/filter_tabs.dart';
import 'package:aitoearn_app/pages/interaction/widgets/platform_selector.dart';
import 'package:aitoearn_app/pages/interaction/widgets/search_bar.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// 互动页面
class InteractionPage extends GetView<InteractionController> {
  const InteractionPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 获取筛选控制器
    final filterController = Get.find<FilterController>();

    // 确保FilterPanelController已经被注册
    if (!Get.isRegistered<FilterPanelController>()) {
      Get.put(FilterPanelController());
    }

    // 设置透明状态栏
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return GestureDetector(
      // 点击页面任何位置关闭键盘
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        // 设置可调整大小，让键盘弹出时能够自动调整视图
        resizeToAvoidBottomInset: true,
        body: Stack(
          children: [
            // 背景渐变
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFFF6EDFA), Color(0xFFFFFFFF)],
                  stops: [0.2, 0.4],
                ),
              ),
            ),

            // 主内容区
            SafeArea(
              child: Column(
                children: [
                  const PlatformSelector(),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      // 搜索栏
                      Expanded(child: VideoSearchBar(controller: controller)),

                      // 抖音搜索按钮 - 仅在选择抖音平台时显示
                      // Obx(() {
                      //   if (controller.selectedPlatform.value.type == PlatformChecker.DOUYIN) {
                      //     return Padding(
                      //       padding: const EdgeInsets.only(right: 16.0),
                      //       child: ElevatedButton(
                      //         onPressed: () {
                      //           // 跳转到抖音搜索示例页面
                      //           Get.toNamed(AppRouter.douyinSearchExamplePage);
                      //         },
                      //         style: ElevatedButton.styleFrom(
                      //           backgroundColor: const Color(0xFF7468E4),
                      //           shape: RoundedRectangleBorder(
                      //             borderRadius: BorderRadius.circular(20),
                      //           ),
                      //           padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      //         ),
                      //         child: Text(
                      //           'interaction.douyinSearch'.tr,
                      //           style: const TextStyle(
                      //             color: Colors.white,
                      //             fontSize: 12,
                      //           ),
                      //         ),
                      //       ),
                      //     );
                      //   } else {
                      //     return const SizedBox.shrink();
                      //   }
                      // }),
                    ],
                  ),
                  const SizedBox(height: 4),
                  FilterTabs(controller: filterController),
                  const SizedBox(height: 4),
                  Obx(
                    () =>
                        filterController.showFilterPanel.value
                            ? FilterPanel()
                            : const SizedBox.shrink(),
                  ),
                  Expanded(
                    child: Obx(
                      () => ContentList(
                        platform: controller.selectedPlatform.value.type,
                        contentList: controller.contentList,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 底部按钮 - 使用Positioned保证不会被遮挡
            Positioned(
              left: 0,
              right: 0,
              bottom: 16,
              child: Container(
                height: 40,
                margin: const EdgeInsets.symmetric(
                  horizontal: Dimens.marginExtraLarge,
                ),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF7468E4),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                    elevation: 2,
                  ),
                  onPressed: () {
                    // 跳转到互动选择页面
                    Get.toNamed(AppRouter.interactionSelectPath)?.then((
                      result,
                    ) {
                      // 处理互动选择页面返回的结果
                      if (result != null && result is Map) {
                        // 获取选中的内容和参数
                        final content = result['content'];
                        final parameters = result['parameters'];

                        if (content != null) {
                          // 这里可以处理互动逻辑
                          Get.snackbar(
                            'interaction.interactionStarted'.tr,
                            'interaction.selectedContent'.tr
                                .replaceAll(
                                  '{frequency}',
                                  parameters['frequency'].toString(),
                                )
                                .replaceAll(
                                  '{duration}',
                                  parameters['duration'].toString(),
                                ),
                            snackPosition: SnackPosition.BOTTOM,
                          );
                        }
                      }
                    });
                  },
                  child: Text(
                    'interaction.startInteraction'.tr,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterPanelOverlay(
    BuildContext context,
    FilterController filterController,
  ) {
    final topPadding = MediaQuery.of(context).padding.top; // 状态栏高度
    const topBarHeight = 60.0; // PlatformSelector高度估计
    const searchBarHeight = 50.0; // SearchBar高度估计
    const filterTabsHeight = 40.0; // FilterTabs高度估计

    final topPosition =
        topPadding + topBarHeight + searchBarHeight + filterTabsHeight;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    // 当键盘显示时，调整筛选面板的位置
    final adjustedHeight =
        keyboardHeight > 0
            ? MediaQuery.of(context).size.height - topPosition - keyboardHeight
            : MediaQuery.of(context).size.height - topPosition;

    return Positioned(
      top: topPosition,
      left: 0,
      right: 0,
      child: Container(
        color: Colors.transparent,
        height: adjustedHeight,
        child: Stack(
          children: [
            // 透明遮罩层 - 用于点击关闭面板
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  FocusScope.of(context).unfocus(); // 关闭键盘
                  filterController.showFilterPanel.value = false;
                  print('interaction.closeFilterPanel'.tr);
                },
                behavior: HitTestBehavior.opaque,
                child: Container(color: Colors.white.withOpacity(0.1)),
              ),
            ),
            // 筛选面板 - 使用Positioned确保在Stack中正确定位
            Positioned(top: 0, left: 0, right: 0, child: FilterPanel()),
          ],
        ),
      ),
    );
  }
}
