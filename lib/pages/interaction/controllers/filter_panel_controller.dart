import 'package:aitoearn_app/pages/interaction/controllers/filter_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 筛选面板控制器
class FilterPanelController extends GetxController {
  // 排序选项
  late final List<String> sortOptions;
  late final RxString selectedSortOption;

  // 笔记类型
  late final List<String> noteTypes;
  late final RxString selectedNoteType;

  // 发布时间
  final startTimeText = ''.obs;
  final endTimeText = ''.obs;
  DateTime? startTime;
  DateTime? endTime;

  // 预设时间范围选项
  late final List<String> timeRangeOptions;
  late final RxString selectedTimeRange;

  // 点赞数量
  final minLikesController = TextEditingController();
  final maxLikesController = TextEditingController();

  // 预设点赞数量范围选项
  late final List<String> likesRangeOptions;
  late final RxString selectedLikesRange;

  @override
  void onInit() {
    super.onInit();

    // 初始化所有翻译选项
    _initTranslatedLists();

    // 监听语言变化，更新列表
    // 由于用户注释掉了FilterController中的语言变化监听，这里也暂时注释掉
    // ever(Get.locale, (_) => _initTranslatedLists());
  }

  /// 初始化翻译后的选项列表
  void _initTranslatedLists() {
    // 初始化排序选项
    sortOptions = [
      'filter.sort.comprehensive'.tr,
      'filter.sort.latest'.tr,
      'filter.sort.mostLikes'.tr,
      'filter.sort.mostComments'.tr,
      'filter.sort.mostFavorites'.tr,
    ];
    selectedSortOption = 'filter.sort.comprehensive'.tr.obs;

    // 初始化笔记类型
    noteTypes = [
      'filter.noteType.unlimited'.tr,
      'filter.noteType.video'.tr,
      'filter.noteType.imageText'.tr,
      'filter.noteType.live'.tr,
    ];
    selectedNoteType = 'filter.noteType.unlimited'.tr.obs;

    // 初始化时间范围选项
    timeRangeOptions = [
      'filter.timeRange.unlimited'.tr,
      'filter.timeRange.today'.tr,
      'filter.timeRange.thisWeek'.tr,
      'filter.timeRange.thisMonth'.tr,
      'filter.timeRange.threeMonths'.tr,
      'filter.timeRange.sixMonths'.tr,
      'filter.timeRange.oneYear'.tr,
    ];
    selectedTimeRange = 'filter.timeRange.unlimited'.tr.obs;

    // 初始化点赞范围选项
    likesRangeOptions = [
      'filter.likesRange.unlimited'.tr,
      'filter.likesRange.zeroToHundred'.tr,
      'filter.likesRange.hundredToThousand'.tr,
      'filter.likesRange.thousandToTenThousand'.tr,
      'filter.likesRange.tenThousandPlus'.tr,
    ];
    selectedLikesRange = 'filter.likesRange.unlimited'.tr.obs;
  }

  @override
  void onClose() {
    minLikesController.dispose();
    maxLikesController.dispose();
    super.onClose();
  }

  // 选择排序选项
  void switchSortOption(String option) {
    selectedSortOption.value = option;
    update();
  }

  // 选择笔记类型
  void switchNoteType(String type) {
    selectedNoteType.value = type;
    update();
  }

  // 选择预设时间范围
  void switchTimeRange(String range) {
    selectedTimeRange.value = range;

    // 根据预设范围设置实际的时间值
    final now = DateTime.now();

    // 使用相等性比较而不是直接比较字符串
    final unlimited = 'filter.timeRange.unlimited'.tr;
    final today = 'filter.timeRange.today'.tr;
    final thisWeek = 'filter.timeRange.thisWeek'.tr;
    final thisMonth = 'filter.timeRange.thisMonth'.tr;
    final threeMonths = 'filter.timeRange.threeMonths'.tr;
    final sixMonths = 'filter.timeRange.sixMonths'.tr;
    final oneYear = 'filter.timeRange.oneYear'.tr;
    final custom = 'filter.timeRange.custom'.tr;

    if (range == unlimited) {
      startTime = null;
      endTime = null;
      startTimeText.value = '';
      endTimeText.value = '';
    } else if (range == today) {
      startTime = DateTime(now.year, now.month, now.day);
      endTime = now;
      startTimeText.value = formatDateTime(startTime!);
      endTimeText.value = formatDateTime(endTime!);
    } else if (range == thisWeek) {
      // 计算本周一
      final weekday = now.weekday;
      final firstDayOfWeek = now.subtract(Duration(days: weekday - 1));
      startTime = DateTime(
        firstDayOfWeek.year,
        firstDayOfWeek.month,
        firstDayOfWeek.day,
      );
      endTime = now;
      startTimeText.value = formatDateTime(startTime!);
      endTimeText.value = formatDateTime(endTime!);
    } else if (range == thisMonth) {
      startTime = DateTime(now.year, now.month, 1);
      endTime = now;
      startTimeText.value = formatDateTime(startTime!);
      endTimeText.value = formatDateTime(endTime!);
    } else if (range == threeMonths) {
      startTime = DateTime(now.year, now.month - 3, now.day);
      endTime = now;
      startTimeText.value = formatDateTime(startTime!);
      endTimeText.value = formatDateTime(endTime!);
    } else if (range == sixMonths) {
      startTime = DateTime(now.year, now.month - 6, now.day);
      endTime = now;
      startTimeText.value = formatDateTime(startTime!);
      endTimeText.value = formatDateTime(endTime!);
    } else if (range == oneYear) {
      startTime = DateTime(now.year - 1, now.month, now.day);
      endTime = now;
      startTimeText.value = formatDateTime(startTime!);
      endTimeText.value = formatDateTime(endTime!);
    }

    update();
  }

  // 选择预设点赞数量范围
  void switchLikesRange(String range) {
    selectedLikesRange.value = range;

    // 使用相等性比较而不是直接比较字符串
    final unlimited = 'filter.likesRange.unlimited'.tr;
    final zeroToHundred = 'filter.likesRange.zeroToHundred'.tr;
    final hundredToThousand = 'filter.likesRange.hundredToThousand'.tr;
    final thousandToTenThousand = 'filter.likesRange.thousandToTenThousand'.tr;
    final tenThousandPlus = 'filter.likesRange.tenThousandPlus'.tr;

    // 根据预设范围设置实际的点赞数值
    if (range == unlimited) {
      minLikesController.clear();
      maxLikesController.clear();
    } else if (range == zeroToHundred) {
      minLikesController.text = '0';
      maxLikesController.text = '100';
    } else if (range == hundredToThousand) {
      minLikesController.text = '100';
      maxLikesController.text = '1000';
    } else if (range == thousandToTenThousand) {
      minLikesController.text = '1000';
      maxLikesController.text = '10000';
    } else if (range == tenThousandPlus) {
      minLikesController.text = '10000';
      maxLikesController.text = '';
    }

    update();
  }

  // 选择开始时间
  void selectStartTime(BuildContext context) async {
    final result = await selectTime(context, startTime);
    if (result != null) {
      startTime = result;
      startTimeText.value = formatDateTime(result);
      // 如果手动选择了时间，设置为自定义
      selectedTimeRange.value = 'filter.timeRange.custom'.tr;
      update();
    }
  }

  // 选择结束时间
  void selectEndTime(BuildContext context) async {
    final result = await selectTime(context, endTime);
    if (result != null) {
      endTime = result;
      endTimeText.value = formatDateTime(result);
      // 如果手动选择了时间，设置为自定义
      selectedTimeRange.value = 'filter.timeRange.custom'.tr;
      update();
    }
  }

  // 格式化日期时间
  String formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
  }

  // 选择时间的通用方法
  Future<DateTime?> selectTime(
    BuildContext context,
    DateTime? initialTime,
  ) async {
    // 这里可以使用现有的日期选择器或默认的MaterialDatePicker
    return await showDatePicker(
      context: context,
      initialDate: initialTime ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF7468E4),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF333333),
            ), dialogTheme: const DialogThemeData(backgroundColor: Colors.white),
          ),
          child: child!,
        );
      },
    );
  }

  // 重置所有筛选条件
  void resetFilters() {
    selectedSortOption.value = 'filter.sort.comprehensive'.tr;
    selectedNoteType.value = 'filter.noteType.unlimited'.tr;
    startTimeText.value = '';
    endTimeText.value = '';
    startTime = null;
    endTime = null;
    minLikesController.clear();
    maxLikesController.clear();
    selectedTimeRange.value = 'filter.timeRange.unlimited'.tr;
    selectedLikesRange.value = 'filter.likesRange.unlimited'.tr;
    update();
  }

  // 应用筛选条件
  void applyFilters() {
    // 收集筛选条件
    Map<String, dynamic> filters = {
      'sortBy': selectedSortOption.value,
      'noteType': selectedNoteType.value,
      'startTime': startTime,
      'endTime': endTime,
      'minLikes':
          minLikesController.text.isNotEmpty
              ? int.parse(minLikesController.text)
              : null,
      'maxLikes':
          maxLikesController.text.isNotEmpty
              ? int.parse(maxLikesController.text)
              : null,
      'timeRange': selectedTimeRange.value,
      'likesRange': selectedLikesRange.value,
    };

    // 关闭筛选面板
    Get.find<FilterController>().showFilterPanel.value = false;

    // 返回筛选结果
    Get.back(result: filters);
  }
}
