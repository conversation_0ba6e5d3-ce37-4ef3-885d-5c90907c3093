import 'package:get/get.dart';

/// 筛选控制器
class FilterController extends GetxController {
  // 是否显示筛选面板
  final showFilterPanel = false.obs;

  // 是否展开分类
  final isCategoryExpanded = false.obs;

  // 选中的分类
  final selectedCategory = 'filter.category.all'.tr.obs;

  // 日期范围选项
  late final List<String> dateRanges;
  final selectedDateRange = 'filter.dateRange.all'.tr.obs;

  // 内容类型选项
  late final List<String> contentTypes;
  final selectedContentType = 'filter.contentType.all'.tr.obs;

  // 互动类型选项
  late final List<String> interactionTypes;
  final selectedInteractionType = 'filter.interactionType.all'.tr.obs;

  @override
  void onInit() {
    super.onInit();

    // 初始化选项列表
    _initTranslatedLists();

    // 监听语言变化，更新列表
    // ever(Get.locale, (_) => _initTranslatedLists());
  }

  /// 初始化翻译后的选项列表
  void _initTranslatedLists() {
    dateRanges = [
      'filter.dateRange.all'.tr,
      'filter.dateRange.today'.tr,
      'filter.dateRange.thisWeek'.tr,
      'filter.dateRange.thisMonth'.tr,
      'filter.dateRange.custom'.tr,
    ];

    contentTypes = [
      'filter.contentType.all'.tr,
      'filter.contentType.image'.tr,
      'filter.contentType.video'.tr,
      'filter.contentType.live'.tr,
    ];

    interactionTypes = [
      'filter.interactionType.all'.tr,
      'filter.interactionType.like'.tr,
      'filter.interactionType.favorite'.tr,
      'filter.interactionType.comment'.tr,
      'filter.interactionType.share'.tr,
    ];

    // 更新选中值，确保语言切换后显示正确
    selectedCategory.value = 'filter.category.all'.tr;
    selectedDateRange.value = 'filter.dateRange.all'.tr;
    selectedContentType.value = 'filter.contentType.all'.tr;
    selectedInteractionType.value = 'filter.interactionType.all'.tr;
  }

  /// 切换分类展开状态
  void toggleCategoryExpanded() {
    // 直接设置状态，不依赖当前值
    isCategoryExpanded.value = !isCategoryExpanded.value;
  }

  /// 显示筛选面板和分类下拉菜单
  void showFilterAndCategory() {
    showFilterPanel.value = true;
    isCategoryExpanded.value = true;
  }

  /// 隐藏筛选面板和分类下拉菜单
  void hideFilterAndCategory() {
    showFilterPanel.value = false;
    isCategoryExpanded.value = false;
  }

  /// 切换筛选面板和分类下拉菜单
  void toggleFilterAndCategory() {
    if (showFilterPanel.value && isCategoryExpanded.value) {
      hideFilterAndCategory();
    } else {
      showFilterAndCategory();
    }
  }

  /// 选择分类
  void selectCategory(String category) {
    selectedCategory.value = category;
    // 选择分类后关闭分类下拉菜单和筛选面板
    hideFilterAndCategory();
  }

  /// 切换筛选面板显示状态
  void toggleFilterPanel() {
    showFilterPanel.value = !showFilterPanel.value;
    // 同步分类展开状态
    isCategoryExpanded.value = showFilterPanel.value;
  }

  /// 重置筛选条件
  void resetFilters() {
    selectedDateRange.value = 'filter.dateRange.all'.tr;
    selectedContentType.value = 'filter.contentType.all'.tr;
    selectedInteractionType.value = 'filter.interactionType.all'.tr;
  }

  /// 应用筛选条件
  void applyFilters() {
    // 关闭筛选面板和分类下拉菜单
    hideFilterAndCategory();

    // 应用筛选条件的逻辑
    // 这里可以通过GetX依赖注入获取主控制器，然后调用其搜索方法
    // 或者通过事件总线发送筛选条件变更事件
  }
}
