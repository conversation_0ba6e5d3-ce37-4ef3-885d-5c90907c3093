import 'package:aitoearn_app/api/account/models/account_user_info_modle.dart';
import 'package:aitoearn_app/api/platform_service.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/models/douyin_models/douyin_hot_feed_model.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_controller.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_panel_controller.dart';
import 'package:aitoearn_app/pages/interaction/models/interaction_platform_model.dart';
import 'package:aitoearn_app/pages/interaction/models/platform_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart';
import 'package:aitoearn_app/plat_core/rx_bus/rx_bus.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:aitoearn_app/viewModels/douyin_hot_feed_view_model.dart';
import 'package:aitoearn_app/viewModels/xhs_content_view_model/xhs_content_view_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class InteractionController extends GetxController {
  // 筛选控制器
  late FilterController filterController;

  // 筛选面板控制器
  final FilterPanelController filterPanelController = FilterPanelController();

  // 小红书内容视图模型
  final XhsContentViewModel xhsContentViewModel = XhsContentViewModel();

  // 抖音热门Feed视图模型
  final DouyinHotFeedViewModel douyinHotFeedViewModel =
  DouyinHotFeedViewModel();

  // 搜索控制器
  final TextEditingController searchController = TextEditingController();

  // 搜索文本 - 用于Obx响应式更新
  final RxString searchText = ''.obs;

  // 选中的平台
  final Rx<InteractionPlatformModel> selectedPlatform =
      InteractionPlatformModel
          .getSupportedPlatforms()
          .first
          .obs;

  // 平台列表
  final RxList<InteractionPlatformModel> platformList =
      InteractionPlatformModel
          .getSupportedPlatforms()
          .obs;

  // 平台列表 - 用于原始UI
  final RxList<PlatformModel> platforms = <PlatformModel>[].obs;

  // 内容列表
  final RxList<dynamic> contentList = <dynamic>[].obs;

  // 账号列表
  final RxList<AccountUserInfoModle> accountList = <AccountUserInfoModle>[].obs;

  // 选中的账号
  final Rx<AccountUserInfoModle?> selectedAccount = Rx<AccountUserInfoModle?>(
    null,
  );

  // 是否正在加载
  final RxBool isLoading = false.obs;

  // 是否正在切换平台
  final RxBool isChangingPlatform = false.obs;

  // 是否显示错误
  final RxBool hasError = false.obs;

  // 错误信息
  final RxString errorMessage = ''.obs;

  // 滚动控制器
  final ScrollController scrollController = ScrollController();

  // 互动参数 - 频率
  final RxDouble interactionFrequency = 5.0.obs;

  // 互动参数 - 时间(分钟)
  final RxDouble interactionDuration = 30.0.obs;

  // 选中的内容列表
  final RxList<dynamic> selectedContents = <dynamic>[].obs;

  // 当前搜索关键词 - 用于在选择账号后继续搜索
  String _currentKeyword = '';

  // RxBus监听器
  Worker? _rxBusWorker;

  @override
  void onInit() {
    super.onInit();

    try {
      // 获取筛选控制器
      filterController = Get.find<FilterController>();

      // 不再需要设置回调，新的FilterController没有这个方法
      print('成功获取FilterController');
    } catch (e) {
      print('获取控制器失败: $e');
    }

    // 监听搜索控制器文本变化
    searchController.addListener(() {
      searchText.value = searchController.text;
    });

    // 初始化平台列表
    _initPlatforms();

    // 直接从API加载账号列表
    _loadAccountsFromApi();

    // 监听滚动事件，实现上拉加载更多
    scrollController.addListener(_scrollListener);

    // 确保RxBus已经注册
    if (!Get.isRegistered<RxBus>()) {
      Get.put(RxBus());
    }

    // 监听账号更新事件
    _rxBusWorker = Get.find<RxBus>().listen("account_updated", (_) {
      print("收到账号更新事件，刷新数据");
      refreshData();
    });

    // 监听平台变化
    ever(selectedPlatform, (_) {
      _onPlatformChanged();
      _updatePlatformSelection();
    });

    // 监听小红书内容变化
    ever(xhsContentViewModel.contentList, (_) {
      _updateContentList();
    });

    // 修改监听小红书错误消息的处理
    ever(xhsContentViewModel.errorMessage, (errorMsg) {
      if (errorMsg.contains('登录已过期') &&
          selectedPlatform.value.type == PlatformChecker.XHS) {
        print('监听到小红书错误消息: 登录已过期');
        hasError.value = true;
        errorMessage.value = 'error.xhsTokenExpired'.tr;
        isLoading.value = false; // 立即停止加载状态
        isChangingPlatform.value = false; // 确保平台切换状态也被重置
      }
    });

    // 监听抖音热门Feed变化
    ever(douyinHotFeedViewModel.hotFeedList, (_) {
      _updateContentList();
    });

    // 监听账号选择变化
    ever(selectedAccount, (account) async {
      // 如果有待处理的搜索关键词，并且选择了账号，则继续搜索
      if (_currentKeyword.isNotEmpty && account != null) {
        searchContent(_currentKeyword);
        _currentKeyword = ''; // 清空待处理的关键词
      }
      // 否则加载首页热门内容
      else if (account != null) {
        if (selectedPlatform.value.type == PlatformChecker.XHS) {
          isLoading.value = true;
          hasError.value = false; // 重置错误状态
          errorMessage.value = ''; // 重置错误消息

          try {
            // 确保账号ID不为空
            final accountId = account.id ?? account.uid;
            final result = await xhsContentViewModel.getHomeFeed(
              accountId: accountId,
              refresh: true,
            );

            // 检查小红书是否返回登录过期错误
            if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
              print('小红书账号登录已过期');
              hasError.value = true;
              errorMessage.value = 'error.xhsTokenExpired'.tr;
              isLoading.value = false; // 立即停止加载状态
              return; // 提前返回，不再执行后续代码
            }
            _updateContentList();
          } catch (e) {
            print('加载首页热门内容失败: $e');
            hasError.value = true;
            errorMessage.value = 'error.loadHomeFeedFailed'.tr.replaceAll(
              '{error}',
              e.toString(),
            );
            isLoading.value = false; // 确保在异常情况下也停止加载状态
            return; // 提前返回
          } finally {
            isLoading.value = false;
            print('小红书加载完成，isLoading设置为false');
          }
        } else if (selectedPlatform.value.type == PlatformChecker.DOUYIN) {
          isLoading.value = true;
          try {
            // 获取Cookie
            final cookie = account.accessToken ?? '';
            if (cookie.isEmpty) {
              print('抖音账号Cookie为空，无法加载热门内容');
              return;
            }

            await douyinHotFeedViewModel.getHotFeed(
              cookie: cookie,
              refresh: true,
            );
            _updateContentList();
          } catch (e) {
            print('加载抖音热门内容失败: $e');
          } finally {
            isLoading.value = false;
          }
        }
      }
    });
  }

  // 初始化平台列表
  void _initPlatforms() {
    platforms.value = [
      PlatformModel(
        type: PlatformType.xiaohongshu,
        name: 'platform.xiaohongshu'.tr,
        isSelected: true,
      ),
      PlatformModel(
        type: PlatformType.douyin,
        name: 'platform.douyin'.tr,
        isSelected: false,
      ),
      // PlatformModel(type: PlatformType.wechat, name: 'platform.wechat'.tr, isSelected: false),
      // PlatformModel(type: PlatformType.kuaishou, name: 'platform.kuaishou'.tr, isSelected: false),
      // PlatformModel(
      //   type: PlatformType.doupinping,
      //   name: 'platform.doupinping'.tr,
      //   isSelected: false,
      // ),
    ];
  }

  // 切换平台
  void switchPlatform(PlatformType type) {
    // 标记开始切换平台
    isChangingPlatform.value = true;

    // 立即进入加载状态并清空列表
    isLoading.value = true;
    contentList.clear();

    // 更新平台选择状态
    final List<PlatformModel> updatedPlatforms = [];
    for (final platform in platforms) {
      updatedPlatforms.add(
        platform.copyWith(isSelected: platform.type == type),
      );
    }
    platforms.value = updatedPlatforms;

    // 找到对应的InteractionPlatformModel
    final selectedPlatformType = _mapPlatformTypeToString(type);
    final matchingPlatform = platformList.firstWhere(
          (p) => p.type == selectedPlatformType,
      orElse: () => platformList.first,
    );

    // 确保找到了匹配的平台
    if (matchingPlatform.type == selectedPlatformType) {
      // 更新选中的平台
      selectedPlatform.value = matchingPlatform;
      print('切换到平台: ${matchingPlatform.name} (${matchingPlatform.type})');
    } else {
      print('未找到匹配的平台类型: $selectedPlatformType');
    }
  }

  // 更新平台选择状态
  void _updatePlatformSelection() {
    final selectedPlatformType = _mapStringToPlatformType(
      selectedPlatform.value.type,
    );
    final List<PlatformModel> updatedPlatforms = [];
    for (final platform in platforms) {
      updatedPlatforms.add(
        platform.copyWith(isSelected: platform.type == selectedPlatformType),
      );
    }
    platforms.value = updatedPlatforms;
  }

  // 将PlatformType映射到字符串类型
  String _mapPlatformTypeToString(PlatformType type) {
    switch (type) {
      case PlatformType.xiaohongshu:
        return PlatformChecker.XHS;
      case PlatformType.douyin:
        return PlatformChecker.DOUYIN;
      case PlatformType.wechat:
        return PlatformChecker.WX_SPH;
      case PlatformType.kuaishou:
        return PlatformChecker.KWAI;
      case PlatformType.doupinping:
        return PlatformChecker.DPP;
    }
  }

  // 将字符串类型映射到PlatformType
  PlatformType _mapStringToPlatformType(String type) {
    switch (type) {
      case PlatformChecker.XHS:
        return PlatformType.xiaohongshu;
      case PlatformChecker.DOUYIN:
        return PlatformType.douyin;
      case PlatformChecker.WX_SPH:
        return PlatformType.wechat;
      case PlatformChecker.KWAI:
        return PlatformType.kuaishou;
      case PlatformChecker.DPP:
        return PlatformType.doupinping;
      default:
        return PlatformType.xiaohongshu;
    }
  }

  @override
  void onClose() {
    searchController.dispose();
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    super.onClose();
  }

  // 从API加载账号列表
  Future<void> _loadAccountsFromApi() async {
    try {
      print('开始从API加载账号列表');
      isLoading.value = true;

      // 使用AccountPersistentService的fetchAccountsFromApi方法获取账号
      final success = await AccountPersistentService.to.fetchAccountsFromApi();

      if (success) {
        print('从API加载账号成功');
        // 获取账号列表
        final allAccounts = AccountPersistentService.to.accounts;
        print('API获取到的账号总数: ${allAccounts.length}');

        // 按平台类型筛选账号
        _filterAccountsByPlatform(allAccounts);

        // 打印筛选后的账号信息
        print('当前平台筛选后的账号数: ${accountList.length}');
        for (var acc in accountList) {
          print(
            '账号: ${acc.nickname}, ID: ${acc.id}, UID: ${acc
                .uid}, Cookie长度: ${acc.cookie?.length ?? 0}',
          );
        }
      } else {
        print('API返回的账号数据为空或格式不正确，尝试从本地加载');
        // 尝试从本地加载
        _loadAccountsFromLocal();
      }
    } catch (e) {
      print('从API加载账号列表失败: $e');
      // 尝试从本地加载
      _loadAccountsFromLocal();
    } finally {
      // 此处不要设置isLoading为false，让调用方决定何时结束加载状态
    }
  }

  // 从本地加载账号列表
  Future<void> _loadAccountsFromLocal() async {
    try {
      print('从本地加载账号列表');
      final accounts = AccountPersistentService.to.accounts;
      _filterAccountsByPlatform(accounts);
    } catch (e) {
      print('从本地加载账号列表失败: $e');
      accountList.value = [];
      selectedAccount.value = null;
    }
  }

  // 按平台类型筛选账号
  void _filterAccountsByPlatform(List<AccountUserInfoModle> accounts) {
    print('所有账号: ${accounts.length}个');
    for (var acc in accounts) {
      print(
        '账号: ${acc.nickname}, 平台类型: ${acc.type
            .toString()}, 选中平台: ${selectedPlatform.value.type}',
      );
    }

    // 按平台类型筛选账号
    final filteredAccounts =
    accounts.where((account) {
      // 将PlatTypeEnum转换为字符串进行比较
      String accountType = '';
      switch (account.type) {
        case PlatTypeEnum.douyin:
          accountType = PlatformChecker.DOUYIN;
          break;
        case PlatTypeEnum.xhs:
          accountType = PlatformChecker.XHS;
          break;
        case PlatTypeEnum.wxWph:
          accountType = PlatformChecker.WX_SPH;
          break;
        case PlatTypeEnum.kwai:
          accountType = PlatformChecker.KWAI;
          break;
        default:
          accountType = '';
          break;
      }

      print(
        '比较: 账号类型=$accountType, 选中平台=${selectedPlatform.value
            .type}, 匹配=${accountType == selectedPlatform.value.type}',
      );
      return accountType == selectedPlatform.value.type;
    }).toList();

    print('筛选后账号: ${filteredAccounts.length}个');
    accountList.value = filteredAccounts;

    // 如果有账号，默认选中第一个
    if (filteredAccounts.isNotEmpty) {
      // 保存当前选中的账号ID，如果有的话
      final currentAccountId =
          selectedAccount.value?.id ?? selectedAccount.value?.uid;

      // 如果有当前账号ID，尝试在新的账号列表中找到它
      if (currentAccountId != null) {
        final matchingAccount = filteredAccounts.firstWhere(
              (acc) =>
          acc.id == currentAccountId || acc.uid == currentAccountId,
          orElse: () => filteredAccounts.first,
        );
        selectedAccount.value = matchingAccount;
        print('选中匹配的账号: ${selectedAccount.value?.nickname}');
      } else {
        // 如果没有当前账号ID，选择第一个账号
        selectedAccount.value = filteredAccounts.first;
        print('选中第一个账号: ${selectedAccount.value?.nickname}');
      }
    } else {
      selectedAccount.value = null;
      print('没有可用账号');

      // 如果没有账号，停止加载状态
      isLoading.value = false;
      isChangingPlatform.value = false;
    }
  }

  // 平台变化处理
  void _onPlatformChanged() async {
    // 清空搜索框
    searchController.clear();
    searchText.value = '';

    // 重置内容列表
    contentList.clear();

    // 重置错误状态
    hasError.value = false;
    errorMessage.value = '';

    // 重置内容视图模型
    if (selectedPlatform.value.type == PlatformChecker.XHS) {
      xhsContentViewModel.resetSearch();
      xhsContentViewModel.resetHomeFeed();
    } else if (selectedPlatform.value.type == PlatformChecker.DOUYIN) {
      douyinHotFeedViewModel.clear();
    }

    // 设置加载状态
    isLoading.value = true;

    try {
      // 加载账号列表
      await _loadAccountsFromApi();

      // 如果没有账号，停止加载状态并提前返回
      if (selectedAccount.value == null) {
        isLoading.value = false;
        isChangingPlatform.value = false;
        return;
      }

      // 如果已选择账号，加载首页热门内容
      print(
        '当前选择的账号: ${selectedAccount.value!
            .nickname}, ID: ${selectedAccount.value!.id ??
            selectedAccount.value!.uid}',
      );
      print('账号Cookie长度: ${selectedAccount.value!.cookie?.length ?? 0}');
      if (selectedAccount.value!.cookie?.isNotEmpty == true) {
        print(
          '账号Cookie前50个字符: ${selectedAccount.value!.cookie!.substring(
              0, 50)}...',
        );
      } else {
        print('账号Cookie为空!');
      }

      if (selectedPlatform.value.type == PlatformChecker.XHS) {
        try {
          // 确保账号ID不为空
          final String accountId =
              selectedAccount.value!.id ?? selectedAccount.value!.uid;
          print('开始加载小红书热门内容，使用账号ID: $accountId');
          final result = await xhsContentViewModel.getHomeFeed(
            accountId: accountId,
            refresh: true,
          );
          print(
            '加载小红书热门内容结果: $result, 列表长度: ${xhsContentViewModel
                .homeFeedList.length}',
          );

          // 检查小红书是否返回登录过期错误
          if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
            print('小红书账号登录已过期');
            hasError.value = true;
            errorMessage.value = 'error.xhsTokenExpired'.tr;
            isLoading.value = false; // 立即停止加载状态
            isChangingPlatform.value = false; // 确保平台切换状态也被重置
            return; // 提前返回，不再执行后续代码
          }
          // 如果加载成功但列表为空，尝试再次加载
          else if (result && xhsContentViewModel.homeFeedList.isEmpty) {
            print('小红书热门内容列表为空，尝试再次加载');
            await xhsContentViewModel.getHomeFeed(
              accountId: accountId,
              refresh: true,
            );

            // 再次检查是否返回登录过期错误
            if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
              hasError.value = true;
              errorMessage.value = 'error.xhsTokenExpired'.tr;
              isLoading.value = false; // 立即停止加载状态
              isChangingPlatform.value = false; // 确保平台切换状态也被重置
              return; // 提前返回，不再执行后续代码
            }
          }
        } catch (e) {
          print('加载首页热门内容失败: $e');
          hasError.value = true;
          errorMessage.value = 'error.loadHomeFeedFailed'.tr.replaceAll(
            '{error}',
            e.toString(),
          );
          isLoading.value = false; // 确保在异常情况下也停止加载状态
          isChangingPlatform.value = false; // 重置平台切换状态
          return; // 提前返回
        }
      } else if (selectedPlatform.value.type == PlatformChecker.DOUYIN) {
        try {
          // 获取Cookie
          final cookie = selectedAccount.value!.accessToken ?? '';
          if (cookie.isEmpty) {
            print('抖音账号Cookie为空，无法加载热门内容');
            // 不要在这里直接return，而是设置错误状态
            hasError.value = true;
            errorMessage.value = 'error.douyinTokenEmpty'.tr;
          } else {
            print('开始加载抖音热门内容');
            final success = await douyinHotFeedViewModel.getHotFeed(
              cookie: cookie,
              refresh: true,
            );
            print(
              '加载抖音热门内容完成，列表长度: ${douyinHotFeedViewModel
                  .hotFeedList.length}',
            );

            // 如果加载成功但列表为空，尝试再次加载
            if (success && douyinHotFeedViewModel.hotFeedList.isEmpty) {
              print('抖音热门内容列表为空，尝试再次加载');
              await douyinHotFeedViewModel.getHotFeed(
                cookie: cookie,
                refresh: true,
              );
            }
          }
        } catch (e) {
          print('加载抖音热门内容失败: $e');
          hasError.value = true;
          errorMessage.value = 'error.douyinHotFeedFailed'.tr.replaceAll(
            '{error}',
            e.toString(),
          );
        }
      }
    } catch (e) {
      print('平台变化处理异常: $e');
    } finally {
      isLoading.value = false;
    }

    // 在加载状态结束后更新内容列表
    _updateContentList();
    print('更新内容列表完成，当前列表长度: ${contentList.length}');

    // 标记平台切换完成
    isChangingPlatform.value = false;
  }

  // 更新内容列表方法修改
  void _updateContentList() {
    print('更新内容列表开始，hasError=${hasError
        .value}, errorMessage=${errorMessage.value}');

    // 修改条件判断，确保只在小红书平台时检查小红书的登录过期错误
    if (selectedPlatform.value.type == PlatformChecker.XHS &&
        hasError.value &&
        (errorMessage.value.contains('xhsTokenExpired') ||
            xhsContentViewModel.errorMessage.value.contains('登录已过期'))) {
      print('检测到小红书登录过期错误，停止更新内容列表');
      isLoading.value = false; // 确保不在loading状态
      return; // 直接返回，不继续处理
    }

    // 根据当前选中的平台和搜索状态，更新内容列表
    if (selectedPlatform.value.type == PlatformChecker.XHS) {
      // 检查小红书视图模型中的错误消息
      if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
        print('_updateContentList: 检测到小红书登录已过期');
        hasError.value = true;
        errorMessage.value = 'error.xhsTokenExpired'.tr;
        isLoading.value = false;
        return;
      }

      // 如果有搜索关键词，使用搜索结果，否则使用热门作品列表
      if (searchText.value.isNotEmpty) {
        contentList.value = xhsContentViewModel.contentList;
      } else {
        contentList.value = xhsContentViewModel.homeFeedList;
      }

      print('更新小红书内容列表，当前列表长度: ${contentList.length}');

      // 如果内容列表为空，但有账号，尝试重新加载
      if (contentList.isEmpty &&
          selectedAccount.value != null &&
          !isLoading.value &&
          !hasError.value) { // 添加错误检查，如果已经有错误就不要再尝试加载
        print('内容列表为空，尝试重新加载小红书内容');
        // 使用延迟执行，避免在当前更新周期内重复加载
        Future.delayed(Duration.zero, () async {
          // 再次检查是否已经有错误或者正在加载
          if (hasError.value || isLoading.value) {
            print('延迟加载前检测到错误或正在加载，取消重新加载');
            return;
          }

          isLoading.value = true;
          final accountId =
              selectedAccount.value!.id ?? selectedAccount.value!.uid;
          final result = await xhsContentViewModel.getHomeFeed(
            accountId: accountId,
            refresh: true,
          );

          // 再次检查是否返回登录过期错误
          if (!result ||
              xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
            print('延迟加载检测到小红书登录已过期或加载失败');
            hasError.value = true;
            errorMessage.value = 'error.xhsTokenExpired'.tr;
          }

          isLoading.value = false; // 确保设置loading状态为false
        });
      }
    } else if (selectedPlatform.value.type == PlatformChecker.DOUYIN) {
      // 目前抖音只支持热门列表，不支持搜索
      contentList.value = douyinHotFeedViewModel.hotFeedList;

      print('更新抖音内容列表，当前列表长度: ${contentList.length}');

      // 如果内容列表为空，但有账号，尝试重新加载
      if (contentList.isEmpty &&
          selectedAccount.value != null &&
          !isLoading.value &&
          !hasError.value) {
        print('内容列表为空，尝试重新加载抖音内容');
        // 使用延迟执行，避免在当前更新周期内重复加载
        Future.delayed(Duration.zero, () async {
          // 避免重复加载
          if (hasError.value || isLoading.value) {
            print('延迟加载前检测到错误或正在加载，取消重新加载');
            return;
          }

          isLoading.value = true;
          final cookie = selectedAccount.value!.accessToken ?? '';
          if (cookie.isNotEmpty) {
            await douyinHotFeedViewModel.getHotFeed(
              cookie: cookie,
              refresh: true,
            );
          } else {
            hasError.value = true;
            errorMessage.value = '抖音账号Cookie为空，无法加载热门内容';
          }
          isLoading.value = false;
        });
      }
    } else {
      // 其他平台暂不支持
      contentList.clear();
    }
  }

  // 滚动监听器，实现上拉加载更多
  void _scrollListener() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200 &&
        !isLoading.value) {
      loadMoreContent();
    }
  }

  // 加载更多内容方法修改
  Future<void> loadMoreContent() async {
    print('尝试加载更多内容');
    if (isLoading.value || selectedAccount.value == null) {
      print('正在加载或账号为空，不进行加载');
      return;
    }

    // 如果已经有错误，特别是登录过期错误，不要尝试加载更多
    if (hasError.value) {
      print('已有错误，不加载更多内容');
      isLoading.value = false;
      return;
    }

    isLoading.value = true;

    try {
      if (selectedPlatform.value.type == PlatformChecker.XHS) {
        // 确保账号ID不为空
        final String accountId =
            selectedAccount.value!.id ?? selectedAccount.value!.uid;
        final result = await xhsContentViewModel.loadMore(accountId);

        // 检查小红书是否返回登录过期错误
        if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
          print('加载更多时检测到小红书账号登录已过期');
          hasError.value = true;
          errorMessage.value = 'error.xhsTokenExpired'.tr;
          isLoading.value = false; // 立即停止加载状态
          return; // 提前返回
        } else if (!result) {
          // 处理其他加载失败情况
          hasError.value = true;
          errorMessage.value = xhsContentViewModel.errorMessage.value;
        }
      } else if (selectedPlatform.value.type == PlatformChecker.DOUYIN) {
        // 获取Cookie
        final cookie = selectedAccount.value!.refreshToken ?? '';
        if (cookie.isEmpty) {
          print('抖音账号Cookie为空，无法加载更多内容');
          return;
        }

        // 抖音暂不支持加载更多，重新获取热门内容
        await douyinHotFeedViewModel.getHotFeed(cookie: cookie);
      }
      // 其他平台的加载更多逻辑...
    } catch (e) {
      print('加载更多内容失败: $e');
    } finally {
      isLoading.value = false;
    }

    // 在加载状态结束后更新内容列表
    _updateContentList();
  }

  // 搜索内容方法修改
  Future<void> searchContent(String keyword) async {
    print('开始搜索内容，关键词: $keyword');

    // 确保任何搜索操作都会重置平台切换状态
    final bool wasChangingPlatform = isChangingPlatform.value;

    if (selectedAccount.value == null) {
      // 如果没有选择账号，直接返回，不显示选择账号对话框
      // 因为UI已经显示了"暂无账号"的提示和"去添加账号"的按钮

      // 如果之前在切换平台，确保重置状态
      if (wasChangingPlatform) {
        isChangingPlatform.value = false;
      }

      // 确保不在加载状态
      isLoading.value = false;
      return;
    }

    // 清除之前的错误状态
    hasError.value = false;
    errorMessage.value = '';

    // 如果关键词为空，加载首页热门作品
    if (keyword
        .trim()
        .isEmpty) {
      // 重置关键词
      searchController.clear();
      searchText.value = '';

      // 设置加载状态
      isLoading.value = true;

      try {
        if (selectedPlatform.value.type == PlatformChecker.XHS) {
          // 确保账号ID不为空
          final String accountId =
              selectedAccount.value!.id ?? selectedAccount.value!.uid;

          // 加载首页热门作品
          final success = await xhsContentViewModel.getHomeFeed(
            accountId: accountId,
            refresh: true,
          );

          // 检查是否登录过期
          if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
            print('搜索时检测到小红书账号登录已过期');
            hasError.value = true;
            errorMessage.value = 'error.xhsTokenExpired'.tr;
            isLoading.value = false; // 立即停止加载状态
            return; // 提前返回
          } else if (!success) {
            hasError.value = true;
            errorMessage.value = xhsContentViewModel.errorMessage.value;
          } else if (xhsContentViewModel.homeFeedList.isEmpty) {
            // 如果加载成功但列表为空，尝试再次加载
            print('小红书热门内容列表为空，尝试再次加载');
            final retryResult = await xhsContentViewModel.getHomeFeed(
              accountId: accountId,
              refresh: true,
            );

            // 再次检查是否登录过期
            if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
              print('重试时检测到小红书账号登录已过期');
              hasError.value = true;
              errorMessage.value = 'error.xhsTokenExpired'.tr;
              isLoading.value = false;
              return;
            }
          }
        } else if (selectedPlatform.value.type == PlatformChecker.DOUYIN) {
          // 获取Cookie
          final cookie = selectedAccount.value!.accessToken ?? '';
          if (cookie.isEmpty) {
            hasError.value = true;
            errorMessage.value = '抖音账号Cookie为空，无法加载热门内容';
          } else {
            // 加载抖音热门内容
            final success = await douyinHotFeedViewModel.getHotFeed(
              cookie: cookie,
              refresh: true,
            );

            if (douyinHotFeedViewModel.errorMessage.value.isNotEmpty) {
              hasError.value = true;
              errorMessage.value = douyinHotFeedViewModel.errorMessage.value;
            } else if (douyinHotFeedViewModel.hotFeedList.isEmpty) {
              // 如果加载成功但列表为空，尝试再次加载
              print('抖音热门内容列表为空，尝试再次加载');
              await douyinHotFeedViewModel.getHotFeed(
                cookie: cookie,
                refresh: true,
              );
            }
          }
        }
      } catch (e) {
        hasError.value = true;
        errorMessage.value = '加载首页热门作品失败: $e';
      } finally {
        isLoading.value = false;
        _updateContentList();

        // 确保重置平台切换状态
        if (wasChangingPlatform) {
          isChangingPlatform.value = false;
        }
      }
      return;
    }

    // 设置加载状态
    isLoading.value = true;

    try {
      // 根据平台类型执行搜索
      if (selectedPlatform.value.type == PlatformChecker.XHS) {
        // 确保账号ID不为空
        final String accountId =
            selectedAccount.value!.id ?? selectedAccount.value!.uid;

        final success = await xhsContentViewModel.searchContent(
          accountId: accountId,
          keyword: keyword,
        );

        if (!success) {
          hasError.value = true;
          // 检查是否是登录过期错误
          if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
            errorMessage.value = 'error.xhsTokenExpired'.tr;
          } else {
            errorMessage.value = xhsContentViewModel.errorMessage.value;
          }
        } else if (xhsContentViewModel.contentList.isEmpty) {
          // 如果搜索成功但结果为空，尝试再次搜索
          print('小红书搜索结果为空，尝试再次搜索');
          await xhsContentViewModel.searchContent(
            accountId: accountId,
            keyword: keyword,
          );

          // 再次检查是否返回登录过期错误
          if (xhsContentViewModel.errorMessage.value.contains('登录已过期')) {
            hasError.value = true;
            errorMessage.value = 'error.xhsTokenExpired'.tr;
          }
        }
      } else if (selectedPlatform.value.type == PlatformChecker.DOUYIN) {
        final account = selectedAccount.value;
        if (account == null || (account.refreshToken?.isEmpty ?? true)) {
          print('抖音账号未登录或Token无效');
          _currentKeyword = keyword; // 缓存关键词，以便登录后重试
          Get.snackbar('notice.title'.tr, 'notice.selectValidDouyinAccount'.tr);
          isLoading.value = false;
          return;
        }

        print('开始执行抖音搜索，关键词: $keyword');
        final platformService = PlatformService();
        final response = await platformService.searchDouyin(
          keyword: keyword,
          cookie: account.refreshToken!, // 使用refreshToken作为Cookie
        );

        if (response.success && response.data != null) {
          final List<dynamic> resultList =
              response.data?['data'] as List? ?? [];
          if (resultList.isNotEmpty) {
            // 将搜索结果的Map列表，转换为DouyinHotFeedItem列表
            final List<DouyinHotFeedItem> searchItems =
            resultList.map((item) {
              // 直接使用 .fromJson 工厂构造函数，传入 aweme_info 这个 map
              return DouyinHotFeedItem.fromJson(item['aweme_info'] ?? {});
            }).toList();

            contentList.assignAll(searchItems);
          } else {
            contentList.clear();
            print('抖音搜索成功，但结果为空');

            // 如果搜索结果为空，尝试再次搜索
            print('抖音搜索结果为空，尝试再次搜索');
            final retryResponse = await platformService.searchDouyin(
              keyword: keyword,
              cookie: account.refreshToken!,
            );

            if (retryResponse.success && retryResponse.data != null) {
              final List<dynamic> retryResultList =
                  retryResponse.data?['data'] as List? ?? [];
              if (retryResultList.isNotEmpty) {
                final List<DouyinHotFeedItem> retrySearchItems =
                retryResultList.map((item) {
                  return DouyinHotFeedItem.fromJson(
                    item['aweme_info'] ?? {},
                  );
                }).toList();
                contentList.assignAll(retrySearchItems);
              }
            }
          }
          hasError.value = false;
        } else {
          hasError.value = true;
          errorMessage.value = response.msg ?? 'error.douyinSearchFailed'.tr;
          print('抖音搜索失败: ${response.msg}');
        }
      }
      // 其他平台的搜索逻辑...
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'error.searchContentFailed'.tr.replaceAll(
        '{error}',
        e.toString(),
      );
      print('搜索内容异常: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // 显示账号选择对话框
  void showAccountSelectDialog() {
    // 如果没有账号，先尝试重新加载账号
    if (accountList.isEmpty) {
      _loadAccountsFromApi().then((_) {
        // 加载完成后再检查是否有账号
        if (accountList.isEmpty) {
          // 获取翻译后的平台名称
          final platformName = selectedPlatform.value.name;

          Get.snackbar(
            'notice.title'.tr,
            'notice.noAccount'.tr.replaceAll('{platform}', platformName),
            snackPosition: SnackPosition.BOTTOM,
          );
          return;
        } else {
          _showAccountSelectDialogUI();
        }
      });
    } else {
      _showAccountSelectDialogUI();
    }
  }

  // 显示账号选择对话框UI
  void _showAccountSelectDialogUI() {
    // 获取翻译后的平台名称
    final platformName = selectedPlatform.value.name;

    // 显示账号选择对话框
    Get.dialog(
      AlertDialog(
        title: Text(
          'dialog.selectAccount'.tr.replaceAll('{platform}', platformName),
          style: const TextStyle(fontSize: 20),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: accountList.length,
            itemBuilder: (context, index) {
              final account = accountList[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundImage:
                  account.avatar != null && account.avatar!.isNotEmpty
                      ? NetworkImage(account.avatar!)
                      : null,
                  child:
                  account.avatar == null || account.avatar!.isEmpty
                      ? const Icon(Icons.person)
                      : null,
                ),
                title: Text(account.nickname ?? 'dialog.unnamedAccount'.tr),
                subtitle: Text(account.id ?? ''),
                onTap: () {
                  // 选择账号
                  selectAccount(account);
                  Get.back(); // 关闭对话框
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: Text('cancel'.tr)),
        ],
      ),
    );
  }

  // 选择账号
  void selectAccount(AccountUserInfoModle account) {
    selectedAccount.value = account;
  }

  // 切换内容选择状态
  void toggleContentSelection(dynamic content, bool isSelected) {
    if (isSelected) {
      if (!selectedContents.contains(content)) {
        selectedContents.add(content);
      }
    } else {
      selectedContents.remove(content);
    }
    // 手动触发更新
    selectedContents.refresh();
  }

  // 检查内容是否被选中
  bool isContentSelected(dynamic content) {
    // 使用 == 操作符比较对象
    for (var item in selectedContents) {
      if (item == content ||
          (content is XhsNoteItem &&
              item is XhsNoteItem &&
              item.id == content.id)) {
        return true;
      }
    }
    return false;
  }

  // 清除所有选中的内容
  void clearSelectedContents() {
    selectedContents.clear();
    // 手动触发更新
    selectedContents.refresh();
  }

  // 刷新数据
  void refreshData() {
    print('刷新数据');
    // 加载最新的账号列表
    _loadAccountsFromApi();

    // 延时刷新内容，确保账号列表已经加载完成
    Future.delayed(Duration(milliseconds: 500), () {
      if (selectedAccount.value != null) {
        if (searchText.value.isNotEmpty) {
          searchContent(searchText.value);
        } else {
          // 如果搜索框为空，加载首页热门作品
          searchContent('');
        }
      } else {
        _updateContentList();
      }
    });
  }
}