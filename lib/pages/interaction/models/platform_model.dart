enum PlatformType { x<PERSON><PERSON><PERSON><PERSON>, douy<PERSON>, we<PERSON>t, kua<PERSON><PERSON>, doupinping }

class PlatformModel {
  final PlatformType type;
  final String name;
  final bool isSelected;

  PlatformModel({
    required this.type,
    required this.name,
    required this.isSelected,
  });

  PlatformModel copyWith({
    PlatformType? type,
    String? name,
    bool? isSelected,
  }) {
    return PlatformModel(
      type: type ?? this.type,
      name: name ?? this.name,
      isSelected: isSelected ?? this.isSelected,
    );
  }
} 