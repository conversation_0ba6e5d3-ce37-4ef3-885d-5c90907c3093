import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 互动平台模型
class InteractionPlatformModel {
  /// 平台类型
  final String type;

  /// 平台名称键 - 用于国际化
  final String nameKey;

  /// 平台图标
  final IconData icon;

  /// 平台颜色
  final Color color;

  /// 是否支持互动功能
  final bool supportInteraction;

  /// 构造函数
  InteractionPlatformModel({
    required this.type,
    required this.nameKey,
    required this.icon,
    required this.color,
    this.supportInteraction = false,
  });

  /// 获取平台名称(已翻译)
  String get name => nameKey.tr;

  /// 获取支持的平台列表
  static List<InteractionPlatformModel> getSupportedPlatforms() {
    return [
      InteractionPlatformModel(
        type: PlatformChecker.XHS,
        nameKey: 'platform.xiaohongshu',
        icon: Icons.favorite,
        color: const Color(0xFFFF2442),
        supportInteraction: true,
      ),
      InteractionPlatformModel(
        type: PlatformChecker.DOUYIN,
        nameKey: 'platform.douyin',
        icon: Icons.music_note,
        color: const Color(0xFF000000),
        supportInteraction: false,
      ),
      InteractionPlatformModel(
        type: PlatformChecker.KWAI,
        nameKey: 'platform.kuaishou',
        icon: Icons.play_circle_fill,
        color: const Color(0xFFFF4906),
        supportInteraction: false,
      ),
      InteractionPlatformModel(
        type: PlatformChecker.WX_SPH,
        nameKey: 'platform.wechat',
        icon: Icons.video_library,
        color: const Color(0xFF07C160),
        supportInteraction: false,
      ),
      InteractionPlatformModel(
        type: PlatformChecker.DPP,
        nameKey: 'platform.doupinping',
        icon: Icons.video_call,
        color: const Color(0xFF9334EA),
        supportInteraction: false,
      ),
    ];
  }

  /// 根据平台类型获取平台模型
  static InteractionPlatformModel? getPlatformByType(String type) {
    final platforms = getSupportedPlatforms();
    try {
      return platforms.firstWhere((platform) => platform.type == type);
    } catch (e) {
      return null;
    }
  }
}
