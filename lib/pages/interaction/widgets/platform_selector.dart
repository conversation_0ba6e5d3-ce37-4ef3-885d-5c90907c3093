import 'package:aitoearn_app/pages/interaction/controller.dart';
import 'package:aitoearn_app/pages/interaction/models/platform_model.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 平台选择器组件
class PlatformSelector extends GetView<InteractionController> {
  const PlatformSelector({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30,
      width: double.infinity,
      margin: const EdgeInsets.only(
        top: Dimens.marginSmall,
      ),
      child: Obx(
        () => ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: controller.platforms.length,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          itemBuilder: (context, index) {
            return _buildPlatformItem(controller.platforms[index]);
          },
        ),
      ),
    );
  }

  Widget _buildPlatformItem(PlatformModel platform) {
    final isSelected = platform.isSelected;
    
    // 根据平台类型获取文本颜色
    Color textColor = Colors.black87;
    
    if (isSelected) {
      textColor = Colors.white;
    }
    
    return GestureDetector(
      onTap: () => controller.switchPlatform(platform.type),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient:
              isSelected && platform.type == PlatformType.xiaohongshu
                  ? const LinearGradient(
                      colors: [Color(0xFF4990FD), Color(0xFFFC3F74)],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    )
                  : null,
          color:
              isSelected && platform.type != PlatformType.xiaohongshu
                  ? _getPlatformColor(platform.type)
                  : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isSelected)
              Padding(
                padding: const EdgeInsets.only(right: 4),
                child: _getPlatformIcon(platform.type, isSelected),
              ),
            Text(
              platform.name,
              style: TextStyle(
                color: textColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _getPlatformIcon(PlatformType type, bool isSelected) {
    String imagePath;
    switch (type) {
      case PlatformType.xiaohongshu:
        imagePath = 'assets/images/account/plat_icon/xhs.png';
        break;
      case PlatformType.douyin:
        imagePath = 'assets/images/account/plat_icon/douyin.png';
        break;
      case PlatformType.wechat:
        imagePath = 'assets/images/account/plat_icon/wx-sph.png';
        break;
      case PlatformType.kuaishou:
        imagePath = 'assets/images/account/plat_icon/ks.png';
        break;
      case PlatformType.doupinping:
        imagePath = 'assets/images/account/plat_icon/douyin.png';
        break;
    }
    
    return Image.asset(imagePath, width: 16, height: 16);
  }
  
  Color _getPlatformColor(PlatformType type) {
    switch (type) {
      case PlatformType.xiaohongshu:
        return const Color(0xFFFF2442);
      case PlatformType.douyin:
        return const Color(0xFF000000);
      case PlatformType.wechat:
        return const Color(0xFF07C160);
      case PlatformType.kuaishou:
        return const Color(0xFFFF7E00);
      case PlatformType.doupinping:
        return const Color(0xFF9334EA);
    }
  }
}
