import 'package:aitoearn_app/pages/interaction/controllers/filter_panel_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 筛选面板组件
class FilterPanel extends GetView<FilterPanelController> {
  FilterPanel({super.key});

  // 创建一个焦点节点，用于管理输入框焦点
  final FocusNode _minLikesFocusNode = FocusNode();
  final FocusNode _maxLikesFocusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    // 获取屏幕高度以调整最大高度
    final screenHeight = MediaQuery.of(context).size.height;
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    // 如果键盘弹出，减小面板最大高度，确保输入框可见
    final maxHeight =
        keyboardHeight > 0 ? screenHeight * 0.45 : screenHeight * 0.50;

    // 使用GestureDetector阻止点击事件传递到下层
    return GestureDetector(
      onTap: () {
        // 点击面板空白区域时，取消所有输入框焦点
        _unfocusAll();
      },
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(maxHeight: maxHeight),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              offset: const Offset(0, 1),
              blurRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            // 筛选内容区域 - 可滚动
            Expanded(
              child: SingleChildScrollView(
                // 添加键盘弹出时自动滚动
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 排序依据
                    _buildSectionTitle('filter.panel.sortBy'.tr),
                    const SizedBox(height: 12),
                    _buildSortOptions(),
                    const SizedBox(height: 24),

                    // 笔记类型
                    _buildSectionTitle('filter.panel.noteType'.tr),
                    const SizedBox(height: 12),
                    _buildNoteTypes(),
                    const SizedBox(height: 24),

                    // 发布时间
                    _buildSectionTitle('filter.panel.publishTime'.tr),
                    const SizedBox(height: 12),
                    _buildTimeRangeButtons(),
                    const SizedBox(height: 12),
                    _buildCustomTimeRangeSelector(context),
                    const SizedBox(height: 24),

                    // 点赞数量
                    _buildSectionTitle('filter.panel.likesCount'.tr),
                    const SizedBox(height: 12),
                    _buildLikesRangeButtons(),
                    const SizedBox(height: 12),
                    _buildCustomLikesRangeSelector(),
                    const SizedBox(height: 24),

                    // 添加额外的底部空间，确保键盘弹出时可以看到输入框
                    SizedBox(height: keyboardHeight > 0 ? 300 : 0),
                  ],
                ),
              ),
            ),

            // 底部按钮区域 - 固定在底部
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: _buildButtonRow(context),
            ),
          ],
        ),
      ),
    );
  }

  // 取消所有焦点
  void _unfocusAll() {
    _minLikesFocusNode.unfocus();
    _maxLikesFocusNode.unfocus();
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: Color(0xFF333333),
      ),
    );
  }

  Widget _buildSortOptions() {
    return GetBuilder<FilterPanelController>(
      builder:
          (_) => Wrap(
            spacing: 12,
            runSpacing: 12,
            children:
                controller.sortOptions
                    .map(
                      (option) => _buildOptionChip(
                        option,
                        controller.selectedSortOption.value == option,
                        () => controller.switchSortOption(option),
                      ),
                    )
                    .toList(),
          ),
    );
  }

  Widget _buildNoteTypes() {
    return GetBuilder<FilterPanelController>(
      builder:
          (_) => Wrap(
            spacing: 12,
            runSpacing: 12,
            children:
                controller.noteTypes
                    .map(
                      (type) => _buildOptionChip(
                        type,
                        controller.selectedNoteType.value == type,
                        () => controller.switchNoteType(type),
                      ),
                    )
                    .toList(),
          ),
    );
  }

  // 发布时间范围按钮
  Widget _buildTimeRangeButtons() {
    return GetBuilder<FilterPanelController>(
      builder:
          (_) => Wrap(
            spacing: 12,
            runSpacing: 12,
            children:
                controller.timeRangeOptions
                    .map(
                      (option) => _buildOptionChip(
                        option,
                        controller.selectedTimeRange.value == option,
                        () => controller.switchTimeRange(option),
                      ),
                    )
                    .toList(),
          ),
    );
  }

  // 点赞数量范围按钮
  Widget _buildLikesRangeButtons() {
    return GetBuilder<FilterPanelController>(
      builder:
          (_) => Wrap(
            spacing: 12,
            runSpacing: 12,
            children:
                controller.likesRangeOptions
                    .map(
                      (option) => _buildOptionChip(
                        option,
                        controller.selectedLikesRange.value == option,
                        () => controller.switchLikesRange(option),
                      ),
                    )
                    .toList(),
          ),
    );
  }

  Widget _buildOptionChip(String text, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFECE6FF) : const Color(0xFFF5F7FA),
          borderRadius: BorderRadius.circular(10),
          border:
              isSelected
                  ? Border.all(color: const Color(0xFF7468E4))
                  : Border.all(color: Colors.transparent),
        ),
        child: Text(
          text,
          style: TextStyle(
            color:
                isSelected ? const Color(0xFF7468E4) : const Color(0xFF666666),
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  // 自定义时间范围选择器（当选择"自定义"时显示）
  Widget _buildCustomTimeRangeSelector(BuildContext context) {
    final customTimeRange = 'filter.timeRange.custom'.tr;
    final unlimitedTimeRange = 'filter.timeRange.unlimited'.tr;

    return Obx(
      () =>
          controller.selectedTimeRange.value == customTimeRange ||
                  (controller.startTimeText.value.isNotEmpty &&
                      controller.selectedTimeRange.value == unlimitedTimeRange)
              ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildTimeTextField(
                          text: controller.startTimeText,
                          hint: 'filter.panel.startTime'.tr,
                          onTap: () => controller.selectStartTime(context),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Text(
                          'filter.panel.to'.tr,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ),
                      Expanded(
                        child: _buildTimeTextField(
                          text: controller.endTimeText,
                          hint: 'filter.panel.endTime'.tr,
                          onTap: () => controller.selectEndTime(context),
                        ),
                      ),
                    ],
                  ),
                ],
              )
              : const SizedBox.shrink(),
    );
  }

  // 自定义点赞数量范围选择器
  Widget _buildCustomLikesRangeSelector() {
    final unlimitedLikesRange = 'filter.likesRange.unlimited'.tr;

    return Obx(
      () =>
          controller.selectedLikesRange.value == unlimitedLikesRange &&
                  (controller.minLikesController.text.isNotEmpty ||
                      controller.maxLikesController.text.isNotEmpty)
              ? Row(
                children: [
                  Expanded(
                    child: _buildLikesTextField(
                      controller: controller.minLikesController,
                      focusNode: _minLikesFocusNode,
                      hint: 'filter.panel.minValue'.tr,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Text(
                      'filter.panel.to'.tr,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF666666),
                      ),
                    ),
                  ),
                  Expanded(
                    child: _buildLikesTextField(
                      controller: controller.maxLikesController,
                      focusNode: _maxLikesFocusNode,
                      hint: 'filter.panel.maxValue'.tr,
                    ),
                  ),
                ],
              )
              : const SizedBox.shrink(),
    );
  }

  // 时间输入框
  Widget _buildTimeTextField({
    required RxString text,
    required String hint,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 40,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: const Color(0xFFF5F7FA),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: const Color(0xFFE6E6E6)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                text.value.isEmpty ? hint : text.value,
                style: TextStyle(
                  fontSize: 14,
                  color:
                      text.value.isEmpty
                          ? const Color(0xFFAAAAAA)
                          : const Color(0xFF333333),
                ),
              ),
            ),
            const Icon(
              Icons.calendar_today_outlined,
              size: 16,
              color: Color(0xFF999999),
            ),
          ],
        ),
      ),
    );
  }

  // 点赞数量输入框
  Widget _buildLikesTextField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String hint,
  }) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: const Color(0xFFF5F7FA),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: const Color(0xFFE6E6E6)),
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
        style: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: const TextStyle(fontSize: 14, color: Color(0xFFAAAAAA)),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 12),
        ),
        onChanged: (_) {
          // 当输入内容变化时，将预设选项设为"不限"
          this.controller.selectedLikesRange.value =
              'filter.likesRange.unlimited'.tr;
          this.controller.update();
        },
      ),
    );
  }

  // 底部按钮行
  Widget _buildButtonRow(BuildContext context) {
    return Row(
      children: [
        // 重置按钮
        Expanded(
          child: TextButton(
            onPressed: () {
              controller.resetFilters();
            },
            style: TextButton.styleFrom(
              backgroundColor: const Color(0xFFF5F7FA),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: Text(
              'filter.panel.reset'.tr,
              style: const TextStyle(
                color: Color(0xFF666666),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        // 确定按钮
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              controller.applyFilters();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF7468E4),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              elevation: 0,
            ),
            child: Text(
              'filter.panel.confirm'.tr,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
