import 'package:aitoearn_app/pages/interaction/controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/user_avatar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 搜索栏组件
class VideoSearchBar extends StatelessWidget {
  final InteractionController controller;

  const VideoSearchBar({required this.controller, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 搜索图标
          const Padding(
            padding: EdgeInsets.only(left: 16, right: 8),
            child: Icon(Icons.search, color: AppColors.primaryColor, size: 24),
          ),

          // 搜索输入框
          Expanded(
            child: Obx(() {
              // 获取翻译后的平台名称
              final platformName = controller.selectedPlatform.value.name;
              // 手动替换占位符
              final hintText = 'search.placeholder'.trParams({
                'platform': platformName,
              });
              return TextField(
                controller: controller.searchController,
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),
                  border: InputBorder.none,
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(vertical: 10),
                ),
                style: const TextStyle(fontSize: 14),
                textInputAction: TextInputAction.search,
                onChanged: (value) {
                  // 更新搜索文本状态
                  controller.searchText.value = value;
                },
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    controller.searchContent(value);
                  }
                },
              );
            }),
          ),

          // 清除按钮
          Obx(
            () =>
                controller.searchText.isNotEmpty
                    ? IconButton(
                      icon: const Icon(
                        Icons.close,
                        color: Colors.grey,
                        size: 20,
                      ),
                      onPressed: () {
                        controller.searchController.clear();
                        controller.searchText.value = '';
                        // 删除搜索内容后加载热门内容
                        controller.searchContent('');
                      },
                    )
                    : const SizedBox.shrink(),
          ),

          // 账号头像按钮
          Obx(
            () => GestureDetector(
              onTap: () {
                controller.showAccountSelectDialog();
              },
              child: Container(
                margin: const EdgeInsets.only(right: 16),
                child:
                    controller.selectedAccount.value != null
                        ? UserAvatar(
                          imageUrl: controller.selectedAccount.value?.avatar,
                          size: 30,
                          backgroundColor: Colors.grey.shade300,
                        )
                        : const Icon(
                          Icons.person,
                          color: Colors.grey,
                          size: 20,
                        ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
