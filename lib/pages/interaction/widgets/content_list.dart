import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/models/douyin_models/douyin_hot_feed_model.dart';
import 'package:aitoearn_app/pages/interaction/controller.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_home_feed_model.dart';

// 导入需要的类
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart'
    show XhsNoteItem, XhsNoteCard, XhsUser, XhsInteractInfo, XhsCover;
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/routers/router.dart';

// 导入设备工具类
import 'package:aitoearn_app/utils/device_utils.dart';
import 'package:aitoearn_app/widgets/common_loading.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 内容列表组件
class ContentList extends StatefulWidget {
  final String platform;
  final RxList contentList;

  const ContentList({
    Key? key,
    required this.platform,
    required this.contentList,
  }) : super(key: key);

  @override
  State<ContentList> createState() => _ContentListState();
}

class _ContentListState extends State<ContentList> {
  // 是否是首次加载
  bool _isFirstLoad = true;

  // 获取控制器
  InteractionController get controller => Get.find<InteractionController>();

  // 监听器ID
  late final List<Worker> _workers;

  @override
  void initState() {
    super.initState();

    // 添加监听器
    _workers = [
      // 监听内容列表变化
      ever(widget.contentList, (_) {
        // 如果内容列表不为空，说明已经加载完成
        if (widget.contentList.isNotEmpty && _isFirstLoad) {
          setState(() {
            _isFirstLoad = false;
          });
        }
      }),

      // 监听平台变化
      ever(controller.selectedPlatform, (_) {
        // 当平台变化时，重置_isFirstLoad状态
        setState(() {
          _isFirstLoad = true;
        });
      }),

      // 监听错误状态
      ever(controller.hasError, (hasError) {
        // 如果有错误，将_isFirstLoad设为false，停止显示加载指示器
        if (hasError && _isFirstLoad) {
          setState(() {
            _isFirstLoad = false;
          });
        }
      }),
    ];
  }

  @override
  void dispose() {
    // 移除监听器
    for (final worker in _workers) {
      worker.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // 如果有错误，优先显示错误信息，不显示loading
      if (controller.hasError.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                controller.errorMessage.value,
                style: const TextStyle(color: Colors.grey, fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  if (controller.searchController.text.isNotEmpty) {
                    controller.searchContent(controller.searchController.text);
                  } else {
                    // 如果搜索框为空，加载首页热门作品
                    controller.searchContent('');
                  }
                },
                child: Text('error.retry'.tr),
              ),
            ],
          ),
        );
      }

      // 如果正在切换平台且有账号，显示加载指示器
      if (controller.isChangingPlatform.value &&
          controller.selectedAccount.value != null) {
        return const Center(child: CommonLoading());
      }

      // 如果没有账号，显示"暂无账号"提示
      if (controller.selectedAccount.value == null) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/account/icon_no_data.png',
                width: 80,
                height: 80,
              ),
              const SizedBox(height: 16),
              Text(
                'content.no_account'.trParams({'platform': _getPlatformName(widget.platform)}),
                style: const TextStyle(color: Colors.grey, fontSize: 16),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  Get.toNamed(AppRouter.accountManagerPath)?.then((_) {
                    // 从账号管理页面返回后刷新数据
                    print('从账号管理页面返回，刷新数据');
                    
                    // 刷新数据
                    controller.refreshData();
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF7468E4),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                child: Text(
                  'content.add_account'.tr,
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        );
      }

      // 如果不是切换平台，但正在加载且列表为空，显示加载指示器
      if (controller.isLoading.value && widget.contentList.isEmpty) {
        return const Center(child: CommonLoading());
      }

      // 如果是首次加载，即使列表为空且不在加载中，也显示加载指示器
      // 但是如果有错误则不显示加载指示器
      if (_isFirstLoad &&
          widget.contentList.isEmpty &&
          !controller.hasError.value) {
        return const Center(child: CommonLoading());
      }

      // 如果列表为空，但没有在加载（例如，初始状态或搜索无结果）
      if (widget.contentList.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.search, size: 48, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                'content.search_hint'.tr,
                style: const TextStyle(color: Colors.grey, fontSize: 16),
              ),
            ],
          ),
        );
      }

      // 根据设备类型确定列数
      final int crossAxisCount = Device.isDesktop ? 3 : 2;

      // 显示内容列表 - 使用网格布局
      return RefreshIndicator(
        onRefresh: () async {
          if (controller.searchController.text.isNotEmpty) {
            await controller.searchContent(controller.searchController.text);
          } else {
            // 如果搜索框为空，刷新首页热门作品
            await controller.searchContent('');
          }
        },
        child: GridView.builder(
          controller: controller.scrollController,
          padding: const EdgeInsets.only(
            left: Dimens.marginMedium,
            right: Dimens.marginMedium,
            top: Dimens.marginMedium,
            bottom: 70, // 添加足够的底部边距，防止溢出
          ),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount, // 根据设备类型动态设置列数
            childAspectRatio: 0.63, // 调整比例使卡片更高一些
            crossAxisSpacing: 8,
            mainAxisSpacing: 15, // 增加垂直间距
          ),
          itemCount:
              widget.contentList.length + (controller.isLoading.value ? 1 : 0),
          itemBuilder: (context, index) {
            // 显示加载更多指示器
            if (index == widget.contentList.length) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: CommonLoading(size: 20),
                ),
              );
            }

            // 根据平台类型和内容类型显示不同的内容项
            if (widget.platform == PlatformChecker.XHS) {
              // 判断是搜索结果还是首页热门作品
              final item = widget.contentList[index];
              if (item is XhsNoteItem) {
                return _buildXhsContentItem(item);
              } else if (item is XhsHomeFeedItem) {
                return _buildXhsHomeFeedItem(item);
              }
            } else if (widget.platform == PlatformChecker.DOUYIN) {
              // 抖音热门Feed
              final item = widget.contentList[index];
              if (item is DouyinHotFeedItem) {
                return _buildDouyinHotFeedItem(item);
              }
            }

            // 默认显示空内容
            return const SizedBox.shrink();
          },
        ),
      );
    });
  }

  /// 构建小红书搜索结果内容项
  Widget _buildXhsContentItem(XhsNoteItem item) {
    return GestureDetector(
      onTap: () {
        // 如果在互动选择页面，则切换选择状态
        if (Get.currentRoute == '/interaction_select') {
          final isSelected = controller.isContentSelected(item);
          controller.toggleContentSelection(item, !isSelected);
        } else {
          // 传递笔记项和当前选择的账号ID
          Get.toNamed(
            AppRouter.contentDetailPath,
            arguments: {
              'noteItem': item,
              'accountId':
                  controller.selectedAccount.value?.id ??
                  controller.selectedAccount.value?.uid,
            },
          );
        }
      },
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            // 使用SizedBox包裹Column以限制最大高度
            child: SizedBox(
              height: 240, // 设置一个固定的高度，避免溢出
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min, // 使用最小尺寸
                children: [
                  // 图片部分 - 使用Expanded包裹，使其能够自适应剩余空间
                  Expanded(
                    child: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(8),
                            topRight: Radius.circular(8),
                          ),
                          child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            child:
                                item.noteCard.cover.urlDefault.isNotEmpty
                                    ? Image.network(
                                      item.noteCard.cover.urlDefault,
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Container(
                                          color: Colors.grey.shade200,
                                          child: const Center(
                                            child: Icon(
                                              Icons.image,
                                              size: 40,
                                              color: Colors.grey,
                                            ),
                                          ),
                                        );
                                      },
                                    )
                                    : Container(
                                      color: Colors.grey.shade200,
                                      child: const Center(
                                        child: Icon(
                                          Icons.image,
                                          size: 40,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ),
                          ),
                        ),
                        // 视频标识
                        if (item.noteCard.type.contains('video'))
                          const Positioned(
                            right: 8,
                            top: 8,
                            child: Icon(
                              Icons.play_circle_outline,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                      ],
                    ),
                  ),
                  // 标题部分 - 使用Padding控制内边距
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题 - 限制最大行数
                        Text(
                          item.noteCard.displayTitle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 6),
                        // 作者信息和点赞数 - 使用Row布局
                        Row(
                          children: [
                            // 头像 - 使用CircleAvatar
                            CircleAvatar(
                              radius: 10,
                              backgroundImage:
                                  item.noteCard.user.avatar.isNotEmpty
                                      ? NetworkImage(item.noteCard.user.avatar)
                                      : null,
                              child:
                                  item.noteCard.user.avatar.isEmpty
                                      ? const Icon(Icons.person, size: 12)
                                      : null,
                            ),
                            const SizedBox(width: 4),
                            // 昵称 - 限制最大宽度
                            Expanded(
                              child: Text(
                                item.noteCard.user.nickname,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ),
                            // 点赞数
                            Row(
                              children: [
                                const Icon(
                                  Icons.favorite,
                                  size: 12,
                                  color: Colors.red,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  _formatLikeCount(
                                    item.noteCard.interactInfo.likedCount,
                                  ),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 选中状态指示器 - 仅在互动选择页面显示
          if (Get.currentRoute == '/interaction_select')
            Obx(
              () => Positioned(
                left: 8,
                top: 8,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color:
                        controller.isContentSelected(item)
                            ? const Color(0xFF2196F3)
                            : Colors.white.withOpacity(0.8),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color:
                          controller.isContentSelected(item)
                              ? Colors.transparent
                              : Colors.grey.shade400,
                      width: 1,
                    ),
                  ),
                  child:
                      controller.isContentSelected(item)
                          ? const Icon(
                            Icons.check,
                            size: 16,
                            color: Colors.white,
                          )
                          : null,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建小红书首页热门作品项
  Widget _buildXhsHomeFeedItem(XhsHomeFeedItem item) {
    // 转换为XhsNoteItem，以便复用现有的UI
    return _buildXhsContentItem(_convertHomeFeedItemToNoteItem(item));
  }

  /// 构建抖音热门Feed项
  Widget _buildDouyinHotFeedItem(DouyinHotFeedItem item) {
    String coverUrl = '';

    if (item.video?.cover?.urlList?.isNotEmpty == true) {
      coverUrl = item.video!.cover!.urlList!.first;
    } else if (item.coverData?.cover?.urlList?.isNotEmpty == true) {
      coverUrl = item.coverData!.cover!.urlList!.first;
    }

    return GestureDetector(
      onTap: () {
        // 如果在互动选择页面，则切换选择状态
        if (Get.currentRoute == '/interaction_select') {
          final isSelected = controller.isContentSelected(item);
          controller.toggleContentSelection(item, !isSelected);
        } else {
          // 跳转到抖音内容详情页
          Get.toNamed(
            AppRouter.douyinContentDetailPath,
            arguments: {'awemeId': item.awemeId},
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 封面图
            if (coverUrl.isNotEmpty)
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                child: Image.network(
                  coverUrl,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: 170, // 调整封面图高度
                ),
              )
            else
              Container(
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: const Center(
                  child: Icon(Icons.videocam_off, color: Colors.grey),
                ),
              ),

            // 标题
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                item.desc,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // 作者信息和点赞数
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 12,
                    backgroundImage:
                        (item.author?.avatarThumb?.urlList?.isNotEmpty == true)
                            ? NetworkImage(
                              item.author!.avatarThumb!.urlList!.first,
                            )
                            : null,
                    child:
                        (item.author?.avatarThumb?.urlList?.isEmpty == true)
                            ? const Icon(Icons.person, size: 12)
                            : null,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      item.author?.nickname ?? 'content.anonymous_user'.tr,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.favorite_border,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatCount(item.statistics?.diggCount ?? 0),
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const Spacer(),
            // 使用Spacer填充剩余空间

            // 统计信息
            // Padding(
            //   padding: const EdgeInsets.all(8.0),
            //   child: Row(
            //     children: [
            //       const Spacer(), // Pushes the following items to the right
            //       Flexible(child: _buildStatItem(Icons.comment_outlined, _formatCount(item.statistics?.commentCount ?? 0))),
            //       const SizedBox(width: 16),
            //       Flexible(child: _buildStatItem(Icons.share_outlined, _formatCount(item.statistics?.shareCount ?? 0))),
            //     ],
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  /// 构建统计信息项
  Widget _buildStatItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(height: 2),
          Text(text, style: TextStyle(fontSize: 10, color: Colors.grey[600])),
        ],
      ),
    );
  }

  /// 格式化数字，超过万时显示为x.x万
  String _formatCount(int count) {
    if (count > 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    }
    return count.toString();
  }

  // 格式化点赞数量
  String _formatLikeCount(dynamic count) {
    // 将输入转换为整数
    int likeCount;
    if (count is String) {
      likeCount = int.tryParse(count) ?? 0;
    } else if (count is int) {
      likeCount = count;
    } else {
      likeCount = 0;
    }

    if (likeCount < 1000) {
      return likeCount.toString();
    } else if (likeCount < 10000) {
      return '${(likeCount / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(likeCount / 10000).toStringAsFixed(1)}W';
    }
  }

  /// 将XhsHomeFeedItem转换为XhsNoteItem
  XhsNoteItem _convertHomeFeedItemToNoteItem(XhsHomeFeedItem item) {
    // 创建XhsNoteCard
    final noteCard = XhsNoteCard(
      type: item.noteCard.type,
      displayTitle: item.noteCard.displayTitle,
      user: XhsUser(
        nickName: item.noteCard.user.nickName,
        avatar: item.noteCard.user.avatar,
        userId: item.noteCard.user.userId,
        nickname: item.noteCard.user.nickname,
        xsecToken: item.noteCard.user.xsecToken,
      ),
      interactInfo: XhsInteractInfo(
        liked: item.noteCard.interactInfo.liked,
        likedCount: item.noteCard.interactInfo.likedCount,
        collected: false,
        // 热门Feed中可能没有这些字段
        collectedCount: '0',
        commentCount: '0',
        sharedCount: '0',
      ),
      cover: XhsCover(
        height: item.noteCard.cover.height,
        width: item.noteCard.cover.width,
        urlDefault: item.noteCard.cover.urlDefault,
        urlPre: item.noteCard.cover.urlPre,
      ),
      imageList: [],
      // 热门Feed中可能没有图片列表
      cornerTagInfo: [], // 热门Feed中可能没有角标信息
    );

    return XhsNoteItem(
      id: item.id,
      modelType: item.modelType,
      noteCard: noteCard,
      xsecToken: item.xsecToken,
    );
  }

  /// 格式化视频时长
  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  // 跳转到详情页面
  void _navigateToDetail(dynamic content, PlatTypeEnum platformType) {
    if (platformType == PlatTypeEnum.xhs) {
      Get.toNamed(AppRouter.contentDetailPath, arguments: content);
    } else if (platformType == PlatTypeEnum.douyin) {
      // 从抖音内容中获取awemeId
      final String awemeId = content.id ?? '';
      if (awemeId.isNotEmpty) {
        Get.toNamed(
          AppRouter.douyinContentDetailPath,
          arguments: {'awemeId': awemeId},
        );
      }
    }
  }

  /// 获取平台名称
  String _getPlatformName(String platformType) {
    switch (platformType) {
      case PlatformChecker.XHS:
        return 'platform.name.xhs'.tr;
      case PlatformChecker.DOUYIN:
        return 'platform.name.douyin'.tr;
      case PlatformChecker.WX_SPH:
        return 'platform.name.wxsph'.tr;
      case PlatformChecker.KWAI:
        return 'platform.name.kwai'.tr;
      default:
        return 'platform.name.default'.tr;
    }
  }
}
