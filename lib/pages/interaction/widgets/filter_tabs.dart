import 'package:aitoearn_app/pages/interaction/controllers/filter_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 筛选选项卡组件
class FilterTabs extends StatelessWidget {
  final FilterController controller;

  const FilterTabs({required this.controller, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Stack(
        children: [
          // 主标签栏
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  // 使用新的方法切换筛选面板和分类下拉菜单
                  controller.toggleFilterAndCategory();
                },
                child: Obx(
                  () => _buildCategoryTab(
                    controller.selectedCategory.value,
                    controller.isCategoryExpanded.value,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              _buildIndicator(),
            ],
          ),

          // 分类下拉菜单
          Obx(
            () =>
                controller.isCategoryExpanded.value
                    ? Positioned(
                      top: 40,
                      left: 0,
                      child: _buildCategoryDropdown(),
                    )
                    : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  /// 构建分类标签
  Widget _buildCategoryTab(String title, bool isExpanded) {
    return Row(
      children: [
        Text(
          title,
          style: const TextStyle(
            color: Color(0xFF333333),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 4),
        Icon(
          isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
          color: const Color(0xFF333333),
          size: 20,
        ),
      ],
    );
  }

  /// 构建底部指示器
  Widget _buildIndicator() {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      width: 16,
      height: 3,
      decoration: BoxDecoration(
        color: const Color(0xFF7468E4),
        borderRadius: BorderRadius.circular(1.5),
      ),
    );
  }

  /// 构建分类下拉菜单
  Widget _buildCategoryDropdown() {
    final categories = [
      'filter.category.all'.tr,
      'filter.category.hot'.tr,
      'filter.category.latest'.tr,
    ];

    return Container(
      width: 120,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            categories.map((category) => _buildCategoryItem(category)).toList(),
      ),
    );
  }

  /// 构建分类项
  Widget _buildCategoryItem(String category) {
    return GestureDetector(
      onTap: () => controller.selectCategory(category),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Text(
          category,
          style: TextStyle(
            color:
                controller.selectedCategory.value == category
                    ? const Color(0xFF7468E4)
                    : const Color(0xFF666666),
            fontSize: 14,
            fontWeight:
                controller.selectedCategory.value == category
                    ? FontWeight.w500
                    : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
