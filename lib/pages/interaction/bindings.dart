import 'package:aitoearn_app/pages/interaction/controller.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_controller.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_panel_controller.dart';
import 'package:get/get.dart';

/// 互动页面绑定
/// 负责注册交互页面所需的所有控制器
class InteractionBinding extends Bindings {
  @override
  void dependencies() {
    // 注册互动页面控制器
    Get.lazyPut<InteractionController>(() => InteractionController());
    
    // 注册筛选控制器
    Get.lazyPut<FilterController>(() => FilterController());
    
    // 注册筛选面板控制器
    Get.lazyPut<FilterPanelController>(() => FilterPanelController());
  }
} 