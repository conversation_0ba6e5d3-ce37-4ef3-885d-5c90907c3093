import 'package:aitoearn_app/pages/interaction/controller.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_controller.dart';
import 'package:aitoearn_app/pages/interaction/controllers/filter_panel_controller.dart';
import 'package:aitoearn_app/pages/interaction/widgets/content_list.dart';
import 'package:aitoearn_app/pages/interaction/widgets/filter_panel.dart';
import 'package:aitoearn_app/pages/interaction/widgets/filter_tabs.dart';
import 'package:aitoearn_app/pages/interaction/widgets/search_bar.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// 互动选择页面 - 用于选择作品和调整互动参数
class InteractionSelectPage extends GetView<InteractionController> {
  const InteractionSelectPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取筛选控制器
    final filterController = Get.find<FilterController>();

    // 确保FilterPanelController已经被注册
    if (!Get.isRegistered<FilterPanelController>()) {
      Get.put(FilterPanelController());
    }

    // 设置透明状态栏
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return GestureDetector(
      // 点击页面任何位置关闭键盘
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
            onPressed: () => Get.back(),
          ),
          title: Obx(() => Text(
            '已选择 ${controller.selectedContents.length} 项',
            style: const TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          )),
          centerTitle: true,
          actions: [
            // 全选/反选按钮
            Obx(() {
              final bool hasSelectedAll = controller.selectedContents.isNotEmpty && 
                  controller.selectedContents.length == controller.contentList.length;
              
              return TextButton(
                onPressed: () {
                  if (hasSelectedAll) {
                    // 如果已全选，则取消全选
                    controller.clearSelectedContents();
                  } else {
                    // 否则全选
                    for (var content in controller.contentList) {
                      if (!controller.isContentSelected(content)) {
                        controller.toggleContentSelection(content, true);
                      }
                    }
                  }
                },
                child: Text(
                  hasSelectedAll ? '取消全选' : '全选',
                  style: const TextStyle(
                    color: Color(0xFF2196F3),
                    fontSize: 14,
                  ),
                ),
              );
            }),
          ],
        ),
        body: Column(
          children: [
            // 搜索栏
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: VideoSearchBar(controller: controller),
            ),
            
            // 筛选标签
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: FilterTabs(controller: filterController),
            ),
            
            const SizedBox(height: 4),
            
            // 筛选面板
            Obx(
              () => filterController.showFilterPanel.value
                  ? FilterPanel()
                  : const SizedBox.shrink(),
            ),
            
            // 内容列表
            Expanded(
              child: Obx(
                () => ContentList(
                  platform: controller.selectedPlatform.value.type,
                  contentList: controller.contentList,
                ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: _buildBottomBar(),
      ),
    );
  }

  // 底部栏
  Widget _buildBottomBar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, -1),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          // 参数按钮
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: IconButton(
              icon: const Icon(Icons.tune, size: 20),
              onPressed: () {
                // 显示互动参数设置弹窗
                _showInteractionParamsDialog(Get.context!);
              },
              padding: EdgeInsets.zero,
            ),
          ),
          const SizedBox(width: 16),
          // 开始互动按钮
          Expanded(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2196F3),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onPressed: () {
                _startInteraction();
              },
              child: const Text(
                '开始互动',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 显示互动参数设置弹窗
  void _showInteractionParamsDialog(BuildContext context) {
    if (controller.selectedContents.isEmpty) {
      Get.snackbar(
        '提示',
        '请先选择要互动的内容',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildInteractionParamsBottomSheet(context),
    );
  }

  // 构建互动参数底部弹窗
  Widget _buildInteractionParamsBottomSheet(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              const Text(
                '互动参数',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _startInteraction();
                },
                child: const Text('确定'),
              ),
            ],
          ),
          const Divider(),
          // 选择账号
          _buildParamItem(
            title: '选择账号',
            trailing: Row(
              children: [
                const Text('请选择', style: TextStyle(color: Colors.grey)),
                const SizedBox(width: 4),
                Icon(Icons.chevron_right, color: Colors.grey.shade400, size: 20),
              ],
            ),
            onTap: () {
              // 处理选择账号
            },
          ),
          // 互动概率
          _buildParamItem(
            title: '互动概率',
            trailing: Row(
              children: [
                const Text('输入内容', style: TextStyle(color: Colors.grey)),
                const SizedBox(width: 4),
                Icon(Icons.edit, color: Colors.grey.shade400, size: 16),
              ],
            ),
            onTap: () {
              // 处理互动概率
            },
          ),
          // 互动间隔
          _buildParamItem(
            title: '互动间隔',
            trailing: Row(
              children: [
                const Text('输入内容', style: TextStyle(color: Colors.grey)),
                const SizedBox(width: 4),
                Icon(Icons.edit, color: Colors.grey.shade400, size: 16),
              ],
            ),
            onTap: () {
              // 处理互动间隔
            },
          ),
          // 互动开始时间
          _buildParamItem(
            title: '互动开始时间',
            trailing: Row(
              children: [
                const Text('请选择', style: TextStyle(color: Colors.grey)),
                const SizedBox(width: 4),
                Icon(Icons.chevron_right, color: Colors.grey.shade400, size: 20),
              ],
            ),
            onTap: () {
              // 处理互动开始时间
            },
          ),
          // 重复任务
          _buildParamItem(
            title: '重复任务',
            trailing: Row(
              children: [
                const Text('不重复', style: TextStyle(color: Colors.grey)),
                const SizedBox(width: 4),
                Icon(Icons.chevron_right, color: Colors.grey.shade400, size: 20),
              ],
            ),
            onTap: () {
              // 处理重复任务
            },
          ),
          const SizedBox(height: 16),
          // 进度指示器
          Container(
            width: 80,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
            alignment: Alignment.center,
            margin: const EdgeInsets.symmetric(vertical: 8),
          ),
        ],
      ),
    );
  }

  // 构建参数项
  Widget _buildParamItem({
    required String title,
    required Widget trailing,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 16),
            ),
            trailing,
          ],
        ),
      ),
    );
  }

  // 开始互动
  void _startInteraction() {
    // 获取选中的内容和参数
    final selectedContents = controller.selectedContents;

    if (selectedContents.isEmpty) {
      Get.snackbar(
        '提示',
        '请先选择要互动的内容',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // 返回上一页并传递选中的内容和参数
    Get.back(result: {
      'content': selectedContents,
      'parameters': {
        'frequency': controller.interactionFrequency.value,
        'duration': controller.interactionDuration.value,
      },
    });
  }
} 