import 'package:aitoearn_app/pages/content_detail/douyin_content_detail_controller.dart';
import 'package:aitoearn_app/widgets/comment_date_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DouyinCommentFilterSheet extends StatelessWidget {
  final DouyinContentDetailController controller;

  const DouyinCommentFilterSheet({required this.controller, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 上次同步时间
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Text(
              'content.douyin.filter.last_sync_time'.tr,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black87,
              ),
            ),
          ),

          // 同步全部评论/只看未回复 选项
          Row(
            children: [
              _buildFilterOptionButton('content.douyin.filter.sync_all'.tr, false),
              const SizedBox(width: 12),
              _buildFilterOptionButton('content.douyin.filter.only_unreplied'.tr, true),
            ],
          ),

          const SizedBox(height: 24),

          // 评论时间
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Text(
              'content.douyin.filter.comment_time'.tr,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black87,
              ),
            ),
          ),

          // 时间范围选择
          Row(
            children: [
              // 上下箭头指示器
              Stack(
                children: [
                  Icon(Icons.arrow_drop_up, color: Colors.grey[400]),
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Icon(Icons.arrow_drop_down, color: Colors.grey[400]),
                  ),
                ],
              ),
              const SizedBox(width: 8),
              // 开始时间输入框
              Expanded(
                child: _buildDateInputField(
                  'content.douyin.filter.start_time'.tr,
                  controller.commentStartTime.value,
                  isStartDate: true,
                ),
              ),
              // 至
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text('content.douyin.filter.to'.tr),
              ),
              // 结束时间输入框
              Expanded(
                child: _buildDateInputField(
                  'content.douyin.filter.end_time'.tr,
                  controller.commentEndTime.value,
                  isStartDate: false,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 点赞数量
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Text(
              'content.douyin.filter.likes_count'.tr,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black87,
              ),
            ),
          ),

          // 点赞数范围选择
          Row(
            children: [
              // 上下箭头指示器
              Stack(
                children: [
                  Icon(Icons.arrow_drop_up, color: Colors.grey[400]),
                  Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Icon(Icons.arrow_drop_down, color: Colors.grey[400]),
                  ),
                ],
              ),
              const SizedBox(width: 8),
              // 最小值输入框
              Expanded(
                child: _buildNumberInputField(
                  'content.douyin.filter.min_value'.tr,
                  controller.minLikes.value.toString(),
                ),
              ),
              // 至
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text('content.douyin.filter.to'.tr),
              ),
              // 最大值输入框
              Expanded(
                child: _buildNumberInputField(
                  'content.douyin.filter.max_value'.tr,
                  controller.maxLikes.value.toString(),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 关键字匹配
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Text(
              'content.douyin.filter.keywords'.tr,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black87,
              ),
            ),
          ),

          // 关键词标签
          _buildKeywordsSection(),

          const SizedBox(height: 40),

          // 底部按钮
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: controller.resetCommentFilters,
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.indigo,
                    backgroundColor: Colors.grey[100],
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text('content.douyin.filter.reset'.tr),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: controller.applyCommentFilters,
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: const Color(0xFF3B82F6),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text('content.douyin.filter.confirm'.tr),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建筛选选项按钮
  Widget _buildFilterOptionButton(String title, bool isUnrepliedOption) {
    return Obx(() {
      final bool isSelected = isUnrepliedOption
          ? controller.showUnrepliedOnly.value
          : !controller.showUnrepliedOnly.value;

      return GestureDetector(
        onTap: () {
          if (isUnrepliedOption) {
            controller.showUnrepliedOnly.value = true;
          } else {
            controller.showUnrepliedOnly.value = false;
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFFF0F4FF) : Colors.grey[100],
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? const Color(0xFF3B82F6) : Colors.grey[600],
              fontSize: 13,
            ),
          ),
        ),
      );
    });
  }

  // 构建日期输入字段
  Widget _buildDateInputField(String hint, DateTime? value, {required bool isStartDate}) {
    return Obx(() {
      // 使用Obx监听日期变化，确保UI更新
      final dateValue = isStartDate ? controller.commentStartTime.value : controller.commentEndTime.value;
      final displayText =
          dateValue != null
              ? '${dateValue.year}-${dateValue.month.toString().padLeft(2, '0')}-${dateValue.day.toString().padLeft(2, '0')}'
              : hint;

      return GestureDetector(
        onTap: () async {
          // 使用CommentDatePicker选择日期时间
          CommentDatePicker.show(
            context: Get.context!,
            initialDateTime: dateValue,
            onTimeSelected: (selectedTime) {
              if (isStartDate) {
                controller.setCommentTimeRange(
                  selectedTime,
                  controller.commentEndTime.value,
                );
              } else {
                controller.setCommentTimeRange(
                  controller.commentStartTime.value,
                  selectedTime,
                );
              }
            },
          );
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          decoration: BoxDecoration(
            color: const Color(0xFFF3F4F9),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            displayText,
            style: TextStyle(
              color: dateValue != null ? Colors.black87 : Colors.grey[500],
              fontSize: 13,
            ),
          ),
        ),
      );
    });
  }

  // 构建数字输入字段
  Widget _buildNumberInputField(String hint, String value) {
    final textController = TextEditingController(text: value == '0' ? '' : value);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        color: const Color(0xFFF3F4F9),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: textController,
        keyboardType: TextInputType.number,
        decoration: InputDecoration.collapsed(
          hintText: hint,
          hintStyle: TextStyle(color: Colors.grey[500], fontSize: 13),
        ),
        style: const TextStyle(fontSize: 13),
        onChanged: (value) {
          if (hint == '最小值') {
            controller.minLikes.value = int.tryParse(value) ?? 0;
          } else {
            controller.maxLikes.value = int.tryParse(value) ?? 0;
          }
        },
      ),
    );
  }

  // 构建关键词部分
  Widget _buildKeywordsSection() {
    return Obx(() {
      // 显示已添加的关键词
      final keywords = controller.commentKeywords;
      
      return Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          // 已添加的关键词
          ...keywords.map((keyword) => _buildKeywordTag(keyword)),
          
          // 添加关键词按钮
          GestureDetector(
            onTap: () => _showAddKeywordDialog(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(10),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.add, size: 16, color: Colors.indigo[300]),
                  const SizedBox(width: 4),
                  Text(
                    'content.douyin.filter.add_keyword'.tr,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.indigo[300],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    });
  }

  // 构建关键词标签
  Widget _buildKeywordTag(String keyword) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF3F4F9),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            keyword,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: () => controller.removeKeyword(keyword),
            child: const Icon(
              Icons.close,
              size: 14,
              color: Colors.black54,
            ),
          ),
        ],
      ),
    );
  }

  // 显示添加关键词对话框
  void _showAddKeywordDialog() {
    final TextEditingController keywordController = TextEditingController();
    
    Get.dialog(
      AlertDialog(
        title: Text('content.douyin.filter.add_keyword_title'.tr),
        content: TextField(
          controller: keywordController,
          decoration: InputDecoration(
            hintText: 'content.douyin.filter.enter_keyword'.tr,
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('content.douyin.filter.cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              if (keywordController.text.isNotEmpty) {
                controller.addKeyword(keywordController.text);
                Get.back();
              }
            },
            child: Text('content.douyin.filter.add'.tr),
          ),
        ],
      ),
    );
  }
} 