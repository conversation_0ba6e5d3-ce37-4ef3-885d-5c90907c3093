import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../content_detail_controller.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_note_detail_model.dart';

// 创建一个控制器来管理响应式状态
class QuickReplyController extends GetxController {
  final ContentDetailController contentDetailController;
  
  QuickReplyController({required this.contentDetailController});
  
  // 存储选中的评论ID
  final selectedCommentIds = <String>[].obs;
  
  // 存储自定义回复内容
  final replyContent = '感谢分享！'.obs;
  
  // 存储选择的时间间隔（秒）
  final timeIntervalSeconds = 5.obs;
  
  // 当前回复的索引
  final currentReplyIndex = 0.obs;
  
  // 获取时间间隔的标签
  String getTimeIntervalLabel() {
    switch (timeIntervalSeconds.value) {
      case 5:
        return '5秒';
      case 10:
        return '10秒';
      case 30:
        return '30秒';
      case 60:
        return '1分钟';
      case 300:
        return '5分钟';
      case 600:
        return '10分钟';
      case 1800:
        return '30分钟';
      case 3600:
        return '1小时';
      default:
        return '${timeIntervalSeconds.value}秒';
    }
  }
  
  // 批量回复评论
  void startBatchReply() {
    // 重置索引
    currentReplyIndex.value = 0;
    
    // 显示进度对话框
    Get.dialog(
      AlertDialog(
        title: const Text('批量回复中'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const LinearProgressIndicator(),
            const SizedBox(height: 16),
            Obx(() => Text('正在回复 (${currentReplyIndex.value}/${selectedCommentIds.length})')),
          ],
        ),
      ),
      barrierDismissible: false,
    );
    
    // 开始批量回复
    replyNextComment();
  }
  
  // 回复下一条评论
  void replyNextComment() {
    if (currentReplyIndex.value >= selectedCommentIds.length) {
      // 所有评论已回复完毕
      Get.back(); // 关闭进度对话框
      Get.snackbar(
        '批量回复完成',
        '成功回复了 ${selectedCommentIds.length} 条评论',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }
    
    // 获取当前需要回复的评论ID
    final commentId = selectedCommentIds[currentReplyIndex.value];
    
    // 查找对应的评论对象
    XhsComment? comment;
    try {
      comment = contentDetailController.comments.firstWhere((c) => c.id == commentId);
    } catch (e) {
      // 找不到评论，comment 保持为 null
    }
    
    if (comment != null) {
      // 设置回复目标
      contentDetailController.setReplyTarget(comment: comment);
      
      // 设置回复内容
      contentDetailController.commentText.value = replyContent.value;
      
      // 发送评论
      contentDetailController.sendComment().then((_) {
        // 回复下一条评论（添加延时，避免频率限制）
        currentReplyIndex.value++;
        Future.delayed(Duration(seconds: timeIntervalSeconds.value), () {
          replyNextComment();
        });
      }).catchError((error) {
        // 回复失败，继续下一条
        currentReplyIndex.value++;
        Future.delayed(Duration(seconds: timeIntervalSeconds.value), () {
          replyNextComment();
        });
      });
    } else {
      // 找不到评论，跳过
      currentReplyIndex.value++;
      replyNextComment();
    }
  }
}

class QuickReplySheet extends StatelessWidget {
  final ContentDetailController controller;
  late final QuickReplyController quickReplyController;

  QuickReplySheet({required this.controller, Key? key}) : super(key: key) {
    // 初始化控制器
    quickReplyController = Get.put(QuickReplyController(contentDetailController: controller));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          _buildTitleBar(),
          
          const SizedBox(height: 16),
          
          // 回复时间间隔
          Obx(() => _buildSettingItem(
            '回复时间间隔',
            trailing: Row(
              children: [
                Text(
                  quickReplyController.getTimeIntervalLabel(),
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showTimeIntervalPicker(),
          )),
          
          // 回复评论选择
          Obx(() => _buildSettingItem(
            '回复评论选择',
            trailing: Row(
              children: [
                Text(
                  quickReplyController.selectedCommentIds.isEmpty 
                      ? '选择评论' 
                      : '已选择 ${quickReplyController.selectedCommentIds.length} 条评论',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showCommentSelectionPicker(),
          )),
          
          // 回复内容
          Obx(() => _buildSettingItem(
            '回复内容',
            trailing: Row(
              children: [
                Text(
                  quickReplyController.replyContent.value.length > 10
                      ? '${quickReplyController.replyContent.value.substring(0, 10)}...'
                      : quickReplyController.replyContent.value,
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showReplyContentEditor(),
          )),
          
          const SizedBox(height: 24),
          
          // 底部按钮
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Get.back(),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.black54,
                    backgroundColor: Colors.grey[200],
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('取消'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _confirmQuickReply(),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: Colors.blue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('确定'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建标题栏
  Widget _buildTitleBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消', style: TextStyle(color: Colors.black54)),
        ),
        const Text(
          '一键回复',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        TextButton(
          onPressed: () => _confirmQuickReply(),
          child: const Text('确定', style: TextStyle(color: Colors.blue)),
        ),
      ],
    );
  }

  // 构建设置项
  Widget _buildSettingItem(String title, {Widget? trailing, VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade200),
          ),
        ),
        child: Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 15,
              ),
            ),
            const Spacer(),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }

  // 显示时间间隔选择器
  void _showTimeIntervalPicker() {
    // 时间间隔选项及其对应的秒数
    final timeIntervalOptions = [
      {'label': '5秒', 'seconds': 5},
      {'label': '10秒', 'seconds': 10},
      {'label': '30秒', 'seconds': 30},
      {'label': '1分钟', 'seconds': 60},
      {'label': '5分钟', 'seconds': 300},
      {'label': '10分钟', 'seconds': 600},
      {'label': '30分钟', 'seconds': 1800},
      {'label': '1小时', 'seconds': 3600},
    ];

    // 当前选中的时间间隔标签
    String currentLabel = '5秒';
    for (var option in timeIntervalOptions) {
      if (option['seconds'] == quickReplyController.timeIntervalSeconds.value) {
        currentLabel = option['label'] as String;
        break;
      }
    }

    Get.bottomSheet(
      Container(
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: const Text(
                '选择回复时间间隔',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: timeIntervalOptions.length,
                itemBuilder: (context, index) {
                  final option = timeIntervalOptions[index];
                  final label = option['label'] as String;
                  final seconds = option['seconds'] as int;
                  
                  return ListTile(
                    title: Text(label),
                    trailing: label == currentLabel ? Icon(Icons.check, color: Colors.blue) : null,
                    onTap: () {
                      // 更新时间间隔
                      quickReplyController.timeIntervalSeconds.value = seconds;
                      Get.back();
                    },
                  );
                },
              ),
            ),
            SafeArea(
              child: TextButton(
                onPressed: () => Get.back(),
                child: const Text('取消'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示评论选择器
  void _showCommentSelectionPicker() {
    // 用于跟踪选中的评论
    final selectedComments = <String>[].obs;
    
    // 初始化已选择的评论
    if (quickReplyController.selectedCommentIds.isNotEmpty) {
      selectedComments.addAll(quickReplyController.selectedCommentIds);
    }
    
    Get.bottomSheet(
      Container(
        color: Colors.white,
        height: Get.height * 0.7, // 弹窗高度为屏幕的70%
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('取消'),
                  ),
                  const Text(
                    '选择要回复的评论',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Obx(() => TextButton(
                    onPressed: selectedComments.isNotEmpty ? () {
                      // 确认选择的评论
                      quickReplyController.selectedCommentIds.clear();
                      quickReplyController.selectedCommentIds.addAll(selectedComments);
                      Get.back();
                    } : null,
                    child: Text(
                      '确定(${selectedComments.length})',
                      style: TextStyle(
                        color: selectedComments.isNotEmpty ? Colors.blue : Colors.grey,
                      ),
                    ),
                  )),
                ],
              ),
            ),
            
            // 选择全部/取消全部
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() => TextButton(
                    onPressed: () {
                      if (selectedComments.isEmpty || selectedComments.length < controller.comments.length) {
                        // 全选
                        selectedComments.clear();
                        selectedComments.addAll(controller.comments.map((comment) => comment.id));
                      } else {
                        // 取消全选
                        selectedComments.clear();
                      }
                    },
                    child: Text(
                      selectedComments.isEmpty || selectedComments.length < controller.comments.length ? '全选' : '取消全选',
                    ),
                  )),
                  Obx(() => Text(
                    '已选择 ${selectedComments.length}/${controller.comments.length}',
                    style: TextStyle(color: Colors.grey[600]),
                  )),
                ],
              ),
            ),
            
            // 评论列表
            Expanded(
              child: Obx(() {
                if (controller.comments.isEmpty) {
                  return const Center(child: Text('暂无评论可选择'));
                }
                
                return ListView.builder(
                  itemCount: controller.comments.length,
                  itemBuilder: (context, index) {
                    final comment = controller.comments[index];
                    return Obx(() => CheckboxListTile(
                      value: selectedComments.contains(comment.id),
                      onChanged: (value) {
                        if (value == true) {
                          selectedComments.add(comment.id);
                        } else {
                          selectedComments.remove(comment.id);
                        }
                      },
                      title: Text(
                        comment.userInfo.nickname,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      subtitle: Text(
                        comment.content,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 13),
                      ),
                      secondary: Text(
                        controller.formatCommentTime(comment.createTime),
                        style: TextStyle(
                          color: Colors.grey[500],
                          fontSize: 12,
                        ),
                      ),
                      dense: true,
                      controlAffinity: ListTileControlAffinity.leading,
                    ));
                  },
                );
              }),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
      enableDrag: true,
    );
  }

  // 显示回复内容编辑器
  void _showReplyContentEditor() {
    final replyTemplates = [
      '感谢分享！',
      '非常感谢您的分享！',
      '很有价值的内容，谢谢分享！',
      '学习了，感谢分享！',
      '太棒了，感谢分享！',
    ];

    final textController = TextEditingController(text: quickReplyController.replyContent.value);

    Get.bottomSheet(
      Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '编辑回复内容',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: textController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: '请输入回复内容',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '快速模板',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: replyTemplates.map((template) {
                return GestureDetector(
                  onTap: () {
                    textController.text = template;
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: template == quickReplyController.replyContent.value ? Colors.blue[100] : Colors.grey[100],
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: template == quickReplyController.replyContent.value ? Colors.blue : Colors.transparent,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      template,
                      style: TextStyle(
                        color: template == quickReplyController.replyContent.value ? Colors.blue : Colors.black87,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // 保存回复内容
                    if (textController.text.trim().isNotEmpty) {
                      quickReplyController.replyContent.value = textController.text.trim();
                    }
                    Get.back();
                  },
                  child: const Text('保存'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  // 确认一键回复
  void _confirmQuickReply() {
    // 这里实现确认一键回复的逻辑
    Get.back(); // 关闭弹窗
    
    if (quickReplyController.selectedCommentIds.isEmpty) {
      // 如果没有选择评论，默认回复主贴
      controller.commentController.text = quickReplyController.replyContent.value;
      controller.commentText.value = quickReplyController.replyContent.value;
      controller.commentFocusNode.requestFocus();
    } else {
      // 如果选择了评论，依次回复
      quickReplyController.startBatchReply();
    }
  }
} 