import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../douyin_content_detail_controller.dart';
import 'package:dio/dio.dart';
import 'package:aitoearn_app/config/logger.dart';

class DouyinQuickReplyController extends GetxController {
  final DouyinContentDetailController contentDetailController;
  final dio = Dio();
  
  DouyinQuickReplyController({required this.contentDetailController});
  
  // 存储选中的评论ID
  final selectedCommentIds = <String>[].obs;
  
  // 存储自定义回复内容
  final replyContent = 'content.douyin.quick_reply.default_reply'.tr.obs;
  
  // 存储选择的时间间隔（秒）
  final timeIntervalSeconds = 5.obs;
  
  // 当前回复的索引
  final currentReplyIndex = 0.obs;
  
  // 批量回复状态
  final isReplying = false.obs;
  final successCount = 0.obs;
  final failCount = 0.obs;
  
  // AI回复相关
  final isGeneratingAIReply = false.obs;
  final aiReplyError = ''.obs;
  
  // 存储每个评论ID对应的AI生成内容
  final commentAIReplies = <String, String>{}.obs;
  
  // 获取时间间隔的标签
  String getTimeIntervalLabel() {
    switch (timeIntervalSeconds.value) {
      case 5:
        return 'content.douyin.quick_reply.time.seconds'.trParams({'count': '5'});
      case 10:
        return 'content.douyin.quick_reply.time.seconds'.trParams({'count': '10'});
      case 30:
        return 'content.douyin.quick_reply.time.seconds'.trParams({'count': '30'});
      case 60:
        return 'content.douyin.quick_reply.time.minute'.tr;
      case 300:
        return 'content.douyin.quick_reply.time.minutes'.trParams({'count': '5'});
      case 600:
        return 'content.douyin.quick_reply.time.minutes'.trParams({'count': '10'});
      case 1800:
        return 'content.douyin.quick_reply.time.minutes'.trParams({'count': '30'});
      case 3600:
        return 'content.douyin.quick_reply.time.hour'.tr;
      default:
        return 'content.douyin.quick_reply.time.seconds'.trParams({'count': timeIntervalSeconds.value.toString()});
    }
  }
  
  // 生成AI回复
  Future<String?> generateAIReply(String commentText, {String? commentId}) async {
    isGeneratingAIReply.value = true;
    aiReplyError.value = '';
    
    print('开始生成AI回复 - commentId: $commentId, text: ${commentText.substring(0, commentText.length > 20 ? 20 : commentText.length)}...');
    
    try {
      print('发送请求到AI接口...');
      final response = await dio.post(
        'https://apitest.aiearn.ai/api/tools/ai/review',
        data: {
          'title': '',
          'desc': commentText,
          'max': 0
        },
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json'
          }
        )
      );
      
      print('收到API响应: statusCode=${response.statusCode}');
      print('响应数据: ${response.data}');
      
      isGeneratingAIReply.value = false;
      
      if ((response.statusCode == 200 || response.statusCode == 201) && response.data['code'] == 0) {
        String aiReply = response.data['data'];
        print('AI回复生成成功: ${aiReply.substring(0, aiReply.length > 20 ? 20 : aiReply.length)}...');
        
        // 如果提供了评论ID，则保存该评论的AI回复
        if (commentId != null) {
          commentAIReplies[commentId] = aiReply;
          print('已保存评论ID $commentId 的AI回复');
          
          // 立即更新评论对象，添加AI回复内容标记
          final commentIndex = contentDetailController.comments.indexWhere((comment) => comment['cid'] == commentId);
          if (commentIndex != -1) {
            final updatedComment = Map<String, dynamic>.from(contentDetailController.comments[commentIndex]);
            updatedComment['has_ai_reply'] = true; // 标记有AI回复内容
            updatedComment['ai_reply_content'] = aiReply; // 保存AI回复内容
            contentDetailController.comments[commentIndex] = updatedComment;
            contentDetailController.comments.refresh(); // 立即刷新UI
            print('已更新评论对象，设置has_ai_reply=true，保存AI回复内容');
          }
        }
        
        return aiReply;
      } else {
        aiReplyError.value = response.data['message'] ?? 'content.douyin.quick_reply.ai_generate_failed'.tr;
        print('AI回复生成失败: ${aiReplyError.value}');
        return null;
      }
    } catch (e) {
      isGeneratingAIReply.value = false;
      aiReplyError.value = '${'content.operation.failed'.trParams({'message': e.toString()})}';
      print('AI回复请求异常: $e');
      return null;
    }
  }
  
  // 为单个评论生成AI回复
  Future<String?> generateSingleCommentAIReply(String commentId, String commentText) async {
    // 设置特定评论的生成状态
    final commentGeneratingKey = 'generating_$commentId';
    
    // 使用临时Map来存储正在生成AI回复的评论ID
    if (!Get.isRegistered<RxMap<String, bool>>(tag: 'commentGeneratingMap')) {
      Get.put(RxMap<String, bool>(), tag: 'commentGeneratingMap');
    }
    
    final commentGeneratingMap = Get.find<RxMap<String, bool>>(tag: 'commentGeneratingMap');
    commentGeneratingMap[commentGeneratingKey] = true;
    
    try {
      final aiReply = await generateAIReply(commentText, commentId: commentId);
      
      // 生成完成后更新状态
      commentGeneratingMap[commentGeneratingKey] = false;
      
      if (aiReply != null) {
        // 评论对象的更新已经在generateAIReply方法中处理了
        
        // 使用try-catch包裹SnackBar显示，防止_controller未初始化导致错误
        try {
          // 显示成功提示
          Get.snackbar(
            'content.douyin.quick_reply.generate_complete'.tr, 
            'content.douyin.quick_reply.generate_success_single'.tr,
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 1),
          );
        } catch (e) {
          print('显示SnackBar时发生错误: $e');
        }
      }
      
      return aiReply;
    } catch (e) {
      // 生成失败后更新状态
      commentGeneratingMap[commentGeneratingKey] = false;
      aiReplyError.value = 'content.douyin.quick_reply.generate_failed'.trParams({'message': e.toString()});
      
      // 使用try-catch包裹SnackBar显示，防止_controller未初始化导致错误
      try {
        // 显示错误提示
        Get.snackbar(
          'content.douyin.quick_reply.generate_failed'.tr, 
          aiReplyError.value,
          snackPosition: SnackPosition.BOTTOM,
        );
      } catch (e) {
        print('显示SnackBar时发生错误: $e');
      }
      
      return null;
    }
  }
  
  // 批量生成AI回复
  Future<void> batchGenerateAIReply({Function? onComplete}) async {
    if (selectedCommentIds.isEmpty) {
      LoggerUtil.i('【DouyinQuickReply】没有选择要回复的评论');
      return;
    }
    
    isGeneratingAIReply.value = true;
    
    // 创建一个RxInt来跟踪当前处理的评论索引
    final currentProcessingIndex = 0.obs;
    
    // 使用临时Map来存储正在生成AI回复的评论ID
    if (!Get.isRegistered<RxMap<String, bool>>(tag: 'commentGeneratingMap')) {
      Get.put(RxMap<String, bool>(), tag: 'commentGeneratingMap');
    }
    final commentGeneratingMap = Get.find<RxMap<String, bool>>(tag: 'commentGeneratingMap');
    
    // 显示进度对话框
    final dialog = AlertDialog(
      title: Text('content.douyin.quick_reply.ai_batch_title'.tr),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Obx(() => LinearProgressIndicator(
            value: selectedCommentIds.isEmpty ? 0 : currentProcessingIndex.value / selectedCommentIds.length,
          )),
          const SizedBox(height: 16),
          Obx(() => Text('content.douyin.quick_reply.generating_progress'.trParams({
            'current': currentProcessingIndex.value.toString(),
            'total': selectedCommentIds.length.toString()
          }))),
        ],
      ),
    );
    
    try {
      await Get.dialog(dialog, barrierDismissible: false);
    } catch (e) {
      LoggerUtil.e('【DouyinQuickReply】显示对话框时发生错误: $e');
    }
    
    try {
      // 为每条评论生成AI回复
      for (int i = 0; i < selectedCommentIds.length; i++) {
        currentProcessingIndex.value = i + 1;
        final commentId = selectedCommentIds[i];
        
        // 设置评论特定的生成状态
        final commentGeneratingKey = 'generating_$commentId';
        commentGeneratingMap[commentGeneratingKey] = true;
        
        // 查找评论内容
        final commentIndex = contentDetailController.comments.indexWhere((comment) => comment['cid'] == commentId);
        if (commentIndex == -1) {
          commentGeneratingMap[commentGeneratingKey] = false;
          continue;
        }
        
        final commentText = contentDetailController.comments[commentIndex]['text'] ?? '';
        if (commentText.isEmpty) {
          commentGeneratingMap[commentGeneratingKey] = false;
          continue;
        }
        
        // 生成AI回复
        final aiReply = await generateAIReply(commentText, commentId: commentId);
        // 评论对象的更新已经在generateAIReply方法中处理了
        
        // 更新评论特定的生成状态
        commentGeneratingMap[commentGeneratingKey] = false;
        
        // 如果是第一条评论，将其设为默认回复内容
        if (i == 0 && commentAIReplies.containsKey(commentId)) {
          replyContent.value = commentAIReplies[commentId]!;
        }
        
        // 添加延迟，避免API请求过于频繁
        if (i < selectedCommentIds.length - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }
      
      // 尝试关闭进度对话框
      try {
        Get.back();
      } catch (e) {
        LoggerUtil.e('【DouyinQuickReply】关闭进度对话框时发生错误: $e');
      }
      
      if (commentAIReplies.isNotEmpty) {
        // 刷新评论列表，确保显示AI回复内容
        contentDetailController.comments.refresh();
        
        // 使用Future.delayed来确保Snackbar显示在正确的上下文中
        await Future.delayed(const Duration(milliseconds: 100));
        try {
          final message = 'content.douyin.quick_reply.generate_success'.trParams({
            'count': commentAIReplies.length.toString()
          });
          LoggerUtil.i('【DouyinQuickReply】$message');
          Get.rawSnackbar(
            message: message,
            title: 'content.douyin.quick_reply.generate_complete'.tr,
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );
        } catch (e) {
          LoggerUtil.e('【DouyinQuickReply】显示Snackbar时发生错误: $e');
        }
      } else {
        throw Exception('content.douyin.quick_reply.ai_generate_failed'.tr);
      }
    } catch (e) {
      // 尝试关闭进度对话框
      try {
        Get.back();
      } catch (e2) {
        LoggerUtil.e('【DouyinQuickReply】关闭进度对话框时发生错误: $e2');
      }
      
      // 使用Future.delayed来确保Snackbar显示在正确的上下文中
      await Future.delayed(const Duration(milliseconds: 100));
      try {
        final message = e.toString();
        LoggerUtil.e('【DouyinQuickReply】生成失败: $message');
        Get.rawSnackbar(
          message: message,
          title: 'content.douyin.quick_reply.generate_failed'.tr,
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 2),
        );
      } catch (e2) {
        LoggerUtil.e('【DouyinQuickReply】显示Snackbar时发生错误: $e2');
      }
    } finally {
      isGeneratingAIReply.value = false;
      // 通知调用者操作已完成
      onComplete?.call();
    }
  }
  
  // 批量回复评论
  void startBatchReply() {
    // 重置索引和状态
    currentReplyIndex.value = 0;
    isReplying.value = true;
    successCount.value = 0;
    failCount.value = 0;
    
    // 显示进度对话框
    try {
      Get.dialog(
        AlertDialog(
          title: Text('content.douyin.quick_reply.batch_replying'.tr),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const LinearProgressIndicator(),
              const SizedBox(height: 16),
              Obx(() => Text('content.douyin.quick_reply.replying_progress'.trParams({
                'current': currentReplyIndex.value.toString(), 
                'total': selectedCommentIds.length.toString()
              }))),
            ],
          ),
        ),
        barrierDismissible: false,
      );
    } catch (e) {
      print('显示对话框时发生错误: $e');
    }
    
    // 开始批量回复
    replyNextComment();
  }
  
  // 回复下一条评论
  void replyNextComment() {
    if (currentReplyIndex.value >= selectedCommentIds.length) {
      // 所有评论已回复完毕
      try {
        Get.back(); // 关闭进度对话框
      } catch (e) {
        print('关闭进度对话框时发生错误: $e');
      }
      
      try {
        Get.snackbar(
          'content.douyin.quick_reply.batch_complete'.tr,
          'content.douyin.quick_reply.success_fail'.trParams({
            'success': successCount.value.toString(), 
            'fail': failCount.value.toString()
          }),
          snackPosition: SnackPosition.BOTTOM,
        );
      } catch (e) {
        print('显示SnackBar时发生错误: $e');
      }
      
      isReplying.value = false;
      return;
    }
    
    // 获取当前需要回复的评论ID
    final commentId = selectedCommentIds[currentReplyIndex.value];
    
    // 获取该评论的AI回复内容（如果有）
    String replyText = commentAIReplies[commentId] ?? replyContent.value;
    
    // 发送回复
    contentDetailController.replyToComment(commentId, replyText)
      .then((success) {
        if (success) {
          successCount.value++;
          
          // 标记评论已回复，并保存回复内容
          final commentIndex = contentDetailController.comments.indexWhere((comment) => comment['cid'] == commentId);
          if (commentIndex != -1) {
            final updatedComment = Map<String, dynamic>.from(contentDetailController.comments[commentIndex]);
            updatedComment['ai_reply_sent'] = true;
            updatedComment['ai_reply_content'] = replyText;
            contentDetailController.comments[commentIndex] = updatedComment;
            contentDetailController.comments.refresh();
            
            LoggerUtil.i('【DouyinQuickReply】已标记评论 $commentId 为已回复，回复内容: ${replyText.substring(0, replyText.length > 20 ? 20 : replyText.length)}...');
          }
        } else {
          failCount.value++;
        }
        
        // 回复下一条评论（添加延时，避免频率限制）
        currentReplyIndex.value++;
        Future.delayed(Duration(seconds: timeIntervalSeconds.value), () {
          replyNextComment();
        });
      })
      .catchError((error) {
        // 回复失败，继续下一条
        failCount.value++;
        currentReplyIndex.value++;
        Future.delayed(Duration(seconds: timeIntervalSeconds.value), () {
          replyNextComment();
        });
      });
  }
}

class DouyinQuickReplySheet extends StatelessWidget {
  final DouyinContentDetailController controller;
  late final DouyinQuickReplyController quickReplyController;

  DouyinQuickReplySheet({required this.controller, Key? key}) : super(key: key) {
    // 初始化控制器
    quickReplyController = Get.put(DouyinQuickReplyController(contentDetailController: controller));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          _buildTitleBar(),
          
          const SizedBox(height: 16),
          
          // 回复时间间隔
          Obx(() => _buildSettingItem(
            'content.douyin.quick_reply.time_interval'.tr,
            trailing: Row(
              children: [
                Text(
                  quickReplyController.getTimeIntervalLabel(),
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showTimeIntervalPicker(),
          )),
          
          // 回复评论选择
          Obx(() => _buildSettingItem(
            'content.douyin.quick_reply.comment_selection'.tr,
            trailing: Row(
              children: [
                Text(
                  quickReplyController.selectedCommentIds.isEmpty 
                      ? 'content.douyin.quick_reply.select_comment'.tr 
                      : 'content.douyin.quick_reply.selected_comments'.trParams({
                          'count': quickReplyController.selectedCommentIds.length.toString()
                        }),
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showCommentSelectionPicker(),
          )),
          
          // 回复内容
          Obx(() => _buildSettingItem(
            'content.douyin.quick_reply.reply_content'.tr,
            trailing: Row(
              children: [
                Text(
                  quickReplyController.replyContent.value.length > 10
                      ? '${quickReplyController.replyContent.value.substring(0, 10)}...'
                      : quickReplyController.replyContent.value,
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showReplyContentEditor(),
          )),
          
          const SizedBox(height: 24),
          
          // 底部按钮
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Get.back(),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.black54,
                    backgroundColor: Colors.grey[200],
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text('content.douyin.quick_reply.cancel'.tr),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _confirmQuickReply(),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: const Color(0xFF7468E4), // 使用抖音紫色
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text('content.douyin.quick_reply.confirm'.tr),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建标题栏
  Widget _buildTitleBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text('content.douyin.quick_reply.cancel'.tr, style: TextStyle(color: Colors.black54)),
        ),
        Text(
          'content.douyin.quick_reply.title'.tr,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        TextButton(
          onPressed: () => _confirmQuickReply(),
          child: Text('content.douyin.quick_reply.confirm'.tr, style: const TextStyle(color: Color(0xFF7468E4))),
        ),
      ],
    );
  }

  // 构建设置项
  Widget _buildSettingItem(String title, {Widget? trailing, VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade200),
          ),
        ),
        child: Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 15,
              ),
            ),
            const Spacer(),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }

  // 显示时间间隔选择器
  void _showTimeIntervalPicker() {
    // 时间间隔选项及其对应的秒数
    final timeIntervalOptions = [
      {'label': 'time.interval.5s'.tr, 'seconds': 5},
      {'label': 'time.interval.10s'.tr, 'seconds': 10},
      {'label': 'time.interval.30s'.tr, 'seconds': 30},
      {'label': 'time.interval.1m'.tr, 'seconds': 60},
      {'label': 'time.interval.5m'.tr, 'seconds': 300},
      {'label': 'time.interval.10m'.tr, 'seconds': 600},
      {'label': 'time.interval.30m'.tr, 'seconds': 1800},
      {'label': 'time.interval.1h'.tr, 'seconds': 3600},
    ];

    // 当前选中的时间间隔标签
    String currentLabel = 'time.interval.5s'.tr;
    for (var option in timeIntervalOptions) {
      if (option['seconds'] == quickReplyController.timeIntervalSeconds.value) {
        currentLabel = option['label'] as String;
        break;
      }
    }

    Get.bottomSheet(
      Container(
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: Text(
                'time.interval.title'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: timeIntervalOptions.length,
                itemBuilder: (context, index) {
                  final option = timeIntervalOptions[index];
                  final label = option['label'] as String;
                  final seconds = option['seconds'] as int;
                  
                  return ListTile(
                    title: Text(label),
                    trailing: label == currentLabel ? const Icon(Icons.check, color: Color(0xFF7468E4)) : null,
                    onTap: () {
                      // 更新时间间隔
                      quickReplyController.timeIntervalSeconds.value = seconds;
                      Get.back();
                    },
                  );
                },
              ),
            ),
            SafeArea(
              child: TextButton(
                onPressed: () => Get.back(),
                child: Text('content.douyin.quick_reply.cancel'.tr),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示评论选择器
  void _showCommentSelectionPicker() {
    // 用于跟踪选中的评论
    final selectedComments = <String>[].obs;
    
    // 初始化已选择的评论
    if (quickReplyController.selectedCommentIds.isNotEmpty) {
      selectedComments.addAll(quickReplyController.selectedCommentIds);
    }
    
    final bottomSheet = Container(
      color: Colors.white,
      height: Get.height * 0.7, // 弹窗高度为屏幕的70%
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    try {
                      Navigator.of(Get.context!).pop();
                    } catch (e) {
                      LoggerUtil.e('【DouyinQuickReply】关闭评论选择器时发生错误: $e');
                    }
                  },
                  child:  Text('content.douyin.quick_reply.cancel'.tr),
                ),
                 Text(
                  'content.douyin.quick_reply.select_comment'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Obx(() => TextButton(
                  onPressed: selectedComments.isNotEmpty ? () {
                    // 确认选择的评论
                    quickReplyController.selectedCommentIds.clear();
                    quickReplyController.selectedCommentIds.addAll(selectedComments);
                    try {
                      Navigator.of(Get.context!).pop();
                    } catch (e) {
                      LoggerUtil.e('【DouyinQuickReply】关闭评论选择器时发生错误: $e');
                    }
                  } : null,
                  child: Text(
                    'content.douyin.quick_reply.confirm'.trParams({
                      'count': selectedComments.length.toString()
                    }),
                    style: TextStyle(
                      color: selectedComments.isNotEmpty ? const Color(0xFF7468E4) : Colors.grey,
                    ),
                  ),
                )),
              ],
            ),
          ),
          
          // 选择全部/取消全部
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() => TextButton(
                  onPressed: () {
                    if (selectedComments.isEmpty || selectedComments.length < controller.comments.length) {
                      // 全选
                      selectedComments.clear();
                      for (var comment in controller.comments) {
                        if (comment['cid'] != null) {
                          selectedComments.add(comment['cid'].toString());
                        }
                      }
                    } else {
                      // 取消全选
                      selectedComments.clear();
                    }
                  },
                  child: Text(
                    selectedComments.isEmpty || selectedComments.length < controller.comments.length ? 'content.douyin.quick_reply.select_all'.tr : 'content.douyin.quick_reply.unselect_all'.tr,
                  ),
                )),
                Obx(() => Text(
                  'content.douyin.quick_reply.selected_count'.trParams({
                    'selected': selectedComments.length.toString(),
                    'total': controller.comments.length.toString()
                  }),
                  style: TextStyle(color: Colors.grey[600]),
                )),
              ],
            ),
          ),
          
          // AI回复按钮
          Obx(() => selectedComments.isNotEmpty ? Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                 Text(
                  'content.douyin.quick_reply.batch_ai_reply'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                 Text(
                  'content.douyin.quick_reply.batch_ai_reply_desc'.tr,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: quickReplyController.isGeneratingAIReply.value 
                    ? null 
                    : () {
                      // 保存选中的评论
                      quickReplyController.selectedCommentIds.clear();
                      quickReplyController.selectedCommentIds.addAll(selectedComments);
                      
                      // 生成AI回复
                      quickReplyController.batchGenerateAIReply();
                      
                      // 直接关闭选择器，不再等待异步操作完成
                      try {
                        Navigator.of(Get.context!).pop();
                      } catch(e) {
                        LoggerUtil.e('【DouyinQuickReply】关闭评论选择器时发生错误: $e');
                      }
                    },
                  icon: quickReplyController.isGeneratingAIReply.value 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.smart_toy),
                  label: Text(quickReplyController.isGeneratingAIReply.value ? 'content.douyin.quick_reply.ai_generating'.tr : 'content.douyin.quick_reply.batch_generate_ai_reply'.tr),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF7468E4),
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ) : const SizedBox.shrink()),
          
          // 评论列表
          Expanded(
            child: Obx(() {
              if (controller.comments.isEmpty) {
                return  Center(child: Text('content.douyin.quick_reply.no_comments_to_select'.tr));
              }
              
              return ListView.builder(
                itemCount: controller.comments.length,
                itemBuilder: (context, index) {
                  final comment = controller.comments[index];
                  final commentId = comment['cid']?.toString() ?? '';
                  if (commentId.isEmpty) return const SizedBox.shrink();
                  
                  final String text = comment['text'] ?? 'content.douyin.quick_reply.comment_content'.tr;
                  final int createTime = comment['create_time'] ?? 0;
                  
                  // 获取用户信息
                  final Map<String, dynamic>? user = comment['user'];
                  final String nickname = user?['nickname'] ?? 'content.douyin.quick_reply.user'.tr;
                  
                  // 获取该评论的AI回复内容（如果有）
                  final bool hasAIReply = quickReplyController.commentAIReplies.containsKey(commentId) || comment['has_ai_reply'] == true;
                  final String aiReplyContent = quickReplyController.commentAIReplies[commentId] ?? comment['ai_reply_content'] ?? '';
                  
                  return Obx(() {
                    final isSelected = selectedComments.contains(commentId);
                    
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CheckboxListTile(
                          value: isSelected,
                          onChanged: (value) {
                            if (value == true) {
                              selectedComments.add(commentId);
                            } else {
                              selectedComments.remove(commentId);
                            }
                          },
                          title: Text(
                            nickname,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                          subtitle: Text(
                            text,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(fontSize: 13),
                          ),
                          secondary: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                controller.formatTimestamp(createTime),
                                style: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(width: 8),
                              // 单个评论的AI生成按钮
                              InkWell(
                                onTap: () async {
                                  // 获取评论特定的生成状态
                                  final commentGeneratingKey = 'generating_$commentId';
                                  RxMap<String, bool> commentGeneratingMap;
                                  try {
                                    commentGeneratingMap = Get.find<RxMap<String, bool>>(tag: 'commentGeneratingMap');
                                  } catch (e) {
                                    commentGeneratingMap = RxMap<String, bool>();
                                    // 确保Map被注册到GetX中
                                    if (!Get.isRegistered<RxMap<String, bool>>(tag: 'commentGeneratingMap')) {
                                      Get.put(commentGeneratingMap, tag: 'commentGeneratingMap');
                                    }
                                  }
                                  
                                  // 如果已经在生成中，则不重复点击
                                  if (commentGeneratingMap[commentGeneratingKey] == true) {
                                    LoggerUtil.i('【DouyinQuickReply】评论 $commentId 正在生成AI回复中，忽略重复点击');
                                    return;
                                  }
                                  
                                  LoggerUtil.i('【DouyinQuickReply】点击了评论 $commentId 的AI生成按钮');
                                  
                                  // 手动设置生成状态，确保UI立即更新
                                  commentGeneratingMap[commentGeneratingKey] = true;
                                  
                                  try {
                                    // 生成单个评论的AI回复
                                    LoggerUtil.i('【DouyinQuickReply】准备为评论 $commentId 生成AI回复');
                                    final result = await quickReplyController.generateSingleCommentAIReply(commentId, text);
                                    
                                    // 如果生成成功，添加到选中的评论列表中（如果还没有选中）
                                    if (result != null && !selectedComments.contains(commentId)) {
                                      selectedComments.add(commentId);
                                      LoggerUtil.i('【DouyinQuickReply】已将评论 $commentId 添加到选中列表');
                                    }
                                  } catch (e) {
                                    LoggerUtil.e('【DouyinQuickReply】生成AI回复时发生异常: $e');
                                    try {
                                      Get.rawSnackbar(
                                        message: 'content.douyin.quick_reply.generate_failed'.trParams({'message': e.toString()}),
                                        title: 'content.douyin.quick_reply.generate_failed'.tr,
                                        snackPosition: SnackPosition.BOTTOM,
                                        duration: const Duration(seconds: 2),
                                      );
                                    } catch (e2) {
                                      LoggerUtil.e('【DouyinQuickReply】显示Snackbar时发生错误: $e2');
                                    }
                                    // 确保状态被重置
                                    commentGeneratingMap[commentGeneratingKey] = false;
                                  }
                                },
                                child: Obx(() {
                                  // 获取评论特定的生成状态
                                  final commentGeneratingKey = 'generating_$commentId';
                                  RxMap<String, bool> commentGeneratingMap;
                                  try {
                                    commentGeneratingMap = Get.find<RxMap<String, bool>>(tag: 'commentGeneratingMap');
                                  } catch (e) {
                                    commentGeneratingMap = RxMap<String, bool>();
                                  }
                                  final isGenerating = commentGeneratingMap[commentGeneratingKey] ?? false;
                                  
                                  return Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFECE6FF),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: isGenerating
                                        ? const SizedBox(
                                            width: 12,
                                            height: 12,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF7468E4)),
                                            ),
                                          )
                                        : const Text(
                                            'AI',
                                            style: TextStyle(
                                              color: Color(0xFF7468E4),
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                  );
                                }),
                              ),
                            ],
                          ),
                          dense: true,
                          controlAffinity: ListTileControlAffinity.leading,
                        ),
                        // 如果有AI回复内容，则显示（无论是否选中）
                        if (hasAIReply && aiReplyContent.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(left: 72, right: 16, bottom: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                 Text(
                                  'content.douyin.quick_reply.ai_reply_content'.tr,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF7468E4),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF5F3FF),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: const Color(0xFFE6E1FF)),
                                  ),
                                  child: Text(
                                    aiReplyContent,
                                    style: const TextStyle(fontSize: 13),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        // 显示已发送的AI回复（如果有）
                        if (comment.containsKey('ai_reply_sent') && comment['ai_reply_sent'] == true)
                          Padding(
                            padding: const EdgeInsets.only(left: 72, right: 16, bottom: 8),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: const Color(0xFFEEFFF0),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: const Color(0xFFD0E8D2)),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.check_circle, color: Colors.green, size: 16),
                                  const SizedBox(width: 4),
                                   Text(
                                    'content.douyin.quick_reply.replied'.tr,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      comment['ai_reply_content'] ?? aiReplyContent,
                                      style: const TextStyle(fontSize: 13),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        const Divider(height: 1),
                      ],
                    );
                  });
                },
              );
            }),
          ),
        ],
      ),
    );
    
    Get.bottomSheet(
      bottomSheet,
      isScrollControlled: true,
      enableDrag: true,
    );
  }

  // 显示回复内容编辑器
  void _showReplyContentEditor() {
    final replyTemplates = [
      'content.douyin.quick_reply.reply_template_1'.tr,
      'content.douyin.quick_reply.reply_template_2'.tr,
      'content.douyin.quick_reply.reply_template_3'.tr,
      'content.douyin.quick_reply.reply_template_4'.tr,
      'content.douyin.quick_reply.reply_template_5'.tr,
      'content.douyin.quick_reply.reply_template_6'.tr,
      'content.douyin.quick_reply.reply_template_7'.tr,
      'content.douyin.quick_reply.reply_template_8'.tr,
      'content.douyin.quick_reply.reply_template_9'.tr,
      'content.douyin.quick_reply.reply_template_10'.tr,
    ];

    final textController = TextEditingController(text: quickReplyController.replyContent.value);
    
    // 获取选中的评论文本，用于AI回复生成
    String selectedCommentText = '';
    String selectedCommentId = '';
    if (quickReplyController.selectedCommentIds.isNotEmpty) {
      selectedCommentId = quickReplyController.selectedCommentIds.first;
      final commentIndex = controller.comments.indexWhere((comment) => comment['cid'] == selectedCommentId);
      if (commentIndex != -1) {
        selectedCommentText = controller.comments[commentIndex]['text'] ?? '';
      }
    }
    
    // 收集所有已生成的AI回复内容
    final allGeneratedReplies = <String>[];
    quickReplyController.commentAIReplies.forEach((_, value) {
      if (!allGeneratedReplies.contains(value)) {
        allGeneratedReplies.add(value);
      }
    });

    Get.bottomSheet(
      Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                   Text(
                    'content.douyin.quick_reply.edit_reply_content'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Get.back(),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextField(
                controller: textController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'content.douyin.quick_reply.input_reply_hint'.tr,
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              
              // AI回复按钮
              if (selectedCommentText.isNotEmpty)
                Obx(() => ElevatedButton.icon(
                  onPressed: quickReplyController.isGeneratingAIReply.value 
                      ? null 
                      : () async {
                          final aiReply = await quickReplyController.generateAIReply(selectedCommentText, commentId: selectedCommentId);
                          if (aiReply != null) {
                            textController.text = aiReply;
                          } else if (quickReplyController.aiReplyError.value.isNotEmpty) {
                            try {
                              Get.snackbar(
                                'content.douyin.quick_reply.ai_generate_failed'.tr, 
                                quickReplyController.aiReplyError.value,
                                snackPosition: SnackPosition.BOTTOM,
                              );
                            } catch (e) {
                              print('显示SnackBar时发生错误: $e');
                            }
                          }
                        },
                  icon: quickReplyController.isGeneratingAIReply.value 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.smart_toy),
                  label: Text(quickReplyController.isGeneratingAIReply.value ? 'content.douyin.quick_reply.generating'.tr : 'content.douyin.quick_reply.ai_smart_reply'.tr),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF7468E4),
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                )),
              
              // 已生成的AI回复内容
              if (allGeneratedReplies.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'content.douyin.quick_reply.ai_generated'.tr,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ...allGeneratedReplies.map((reply) => GestureDetector(
                  onTap: () {
                    textController.text = reply;
                  },
                  child: Container(
                    width: double.infinity,
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F3FF),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFFE6E1FF)),
                    ),
                    child: Text(
                      reply,
                      style: const TextStyle(fontSize: 13),
                    ),
                  ),
                )).toList(),
              ],
              
              const SizedBox(height: 16),
              Text(
                'content.douyin.quick_reply.quick_templates'.tr,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: replyTemplates.map((template) {
                  return GestureDetector(
                    onTap: () {
                      textController.text = template;
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: template == quickReplyController.replyContent.value ? const Color(0xFFECE6FF) : Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: template == quickReplyController.replyContent.value ? const Color(0xFF7468E4) : Colors.transparent,
                          width: 1,
                        ),
                      ),
                      child: Text(
                        template,
                        style: TextStyle(
                          color: template == quickReplyController.replyContent.value ? const Color(0xFF7468E4) : Colors.black87,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: Text('content.douyin.quick_reply.cancel'.tr),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      // 保存回复内容
                      if (textController.text.trim().isNotEmpty) {
                        quickReplyController.replyContent.value = textController.text.trim();
                      }
                      Get.back();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF7468E4),
                    ),
                    child: Text('content.douyin.quick_reply.save'.tr),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      isScrollControlled: true,
      enableDrag: true,
    );
  }
  
  // 确认一键回复
  void _confirmQuickReply() {
    try {
      Get.back();
    } catch (e) {
      print('关闭对话框时发生错误: $e');
    }
    
    // 如果没有选择评论，默认回复主贴
    if (quickReplyController.selectedCommentIds.isEmpty) {
      controller.commentController.text = quickReplyController.replyContent.value;
      controller.commentText.value = quickReplyController.replyContent.value;
      controller.commentFocusNode.requestFocus();
    } else {
      // 如果选择了评论，依次回复
      // 如果回复内容为空，显示提示
      if (quickReplyController.replyContent.value.trim().isEmpty) {
        try {
          Get.snackbar('content.hint'.tr, 'content.douyin.quick_reply.enter_reply_first'.tr, snackPosition: SnackPosition.BOTTOM);
        } catch (e) {
          print('显示SnackBar时发生错误: $e');
        }
        return;
      }
      
      // 确认是否要批量回复
      try {
        Get.dialog(
          AlertDialog(
            title: Text('content.douyin.quick_reply.confirm_batch'.tr),
            content: Text('content.douyin.quick_reply.confirm_batch_message'.trParams({
              'count': quickReplyController.selectedCommentIds.length.toString(),
              'interval': quickReplyController.getTimeIntervalLabel()
            })),
            actions: [
              TextButton(
                onPressed: () {
                  try {
                    Get.back();
                  } catch (e) {
                    print('关闭对话框时发生错误: $e');
                  }
                },
                child: Text('content.douyin.quick_reply.cancel'.tr),
              ),
              TextButton(
                onPressed: () {
                  try {
                    Get.back();
                  } catch (e) {
                    print('关闭对话框时发生错误: $e');
                  }
                  quickReplyController.startBatchReply();
                },
                child: Text('content.douyin.quick_reply.confirm'.tr),
              ),
            ],
          ),
        );
      } catch (e) {
        print('显示对话框时发生错误: $e');
      }
    }
  }
} 