import 'package:aitoearn_app/plat_core/plats/plat_xhs/xhs_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../content_detail_controller.dart';
import 'package:dio/dio.dart';
import 'package:aitoearn_app/config/logger.dart';


class XhsQuickReplyController extends GetxController {
  final ContentDetailController contentDetailController;
  final dio = Dio();
  
  XhsQuickReplyController({required this.contentDetailController});
  
  // 存储选中的评论ID
  final selectedCommentIds = <String>[].obs;
  
  // 存储自定义回复内容
  final replyContent = '感谢分享！'.obs;
  
  // 存储选择的时间间隔（秒）
  final timeIntervalSeconds = 5.obs;
  
  // 当前回复的索引
  final currentReplyIndex = 0.obs;
  
  // 批量回复状态
  final isReplying = false.obs;
  final successCount = 0.obs;
  final failCount = 0.obs;
  
  // AI回复相关
  final isGeneratingAIReply = false.obs;
  final aiReplyError = ''.obs;
  
  // 存储每个评论ID对应的AI生成内容
  final commentAIReplies = <String, String>{}.obs;
  
  // 获取时间间隔的标签
  String getTimeIntervalLabel() {
    switch (timeIntervalSeconds.value) {
      case 5:
        return '5秒';
      case 10:
        return '10秒';
      case 30:
        return '30秒';
      case 60:
        return '1分钟';
      case 300:
        return '5分钟';
      case 600:
        return '10分钟';
      case 1800:
        return '30分钟';
      case 3600:
        return '1小时';
      default:
        return '${timeIntervalSeconds.value}秒';
    }
  }
  
  // 生成AI回复
  Future<String?> generateAIReply(String commentText, {String? commentId}) async {
    isGeneratingAIReply.value = true;
    aiReplyError.value = '';
    
    print('开始生成AI回复 - commentId: $commentId, text: ${commentText.substring(0, commentText.length > 20 ? 20 : commentText.length)}...');
    
    try {
      print('发送请求到AI接口...');
      final response = await dio.post(
        'https://apitest.aiearn.ai/api/tools/ai/review',
        data: {
          'title': '',
          'desc': commentText,
          'max': 0
        },
        options: Options(
          headers: {
            'accept': 'application/json',
            'Content-Type': 'application/json'
          }
        )
      );
      
      print('收到API响应: statusCode=${response.statusCode}');
      print('响应数据: ${response.data}');
      
      isGeneratingAIReply.value = false;
      
      if ((response.statusCode == 200 || response.statusCode == 201) && response.data['code'] == 0) {
        String aiReply = response.data['data'];
        print('AI回复生成成功: ${aiReply.substring(0, aiReply.length > 20 ? 20 : aiReply.length)}...');
        
        // 如果提供了评论ID，则保存该评论的AI回复
        if (commentId != null) {
          commentAIReplies[commentId] = aiReply;
          print('已保存评论ID $commentId 的AI回复');
          
          // 立即更新评论对象，添加AI回复内容标记
          final commentIndex = contentDetailController.comments.indexWhere((comment) => comment.id == commentId);
          if (commentIndex != -1) {
            // 由于XhsComment是不可变的，我们需要创建一个新的评论对象
            final oldComment = contentDetailController.comments[commentIndex];
            
            // 使用临时map存储AI回复内容
            if (!Get.isRegistered<RxMap<String, bool>>(tag: 'commentHasAIReplyMap')) {
              Get.put(RxMap<String, bool>(), tag: 'commentHasAIReplyMap');
            }
            final commentHasAIReplyMap = Get.find<RxMap<String, bool>>(tag: 'commentHasAIReplyMap');
            commentHasAIReplyMap[commentId] = true;
            
            print('已更新评论对象，设置hasAIReply=true，保存AI回复内容');
            contentDetailController.comments.refresh(); // 立即刷新UI
          }
        }
        
        return aiReply;
      } else {
        final errorMsg = response.data['msg'] ?? 'AI回复生成失败';
        aiReplyError.value = errorMsg;
        print('AI回复生成失败: $errorMsg');
        return null;
      }
    } catch (e) {
      print('AI回复生成异常: $e');
      isGeneratingAIReply.value = false;
      aiReplyError.value = e.toString();
      return null;
    }
  }
  
  // 为单个评论生成AI回复
  Future<String?> generateSingleCommentAIReply(String commentId, String commentText) async {
    // 设置特定评论的生成状态
    final commentGeneratingKey = 'generating_$commentId';
    
    // 使用临时Map来存储正在生成AI回复的评论ID
    if (!Get.isRegistered<RxMap<String, bool>>(tag: 'commentGeneratingMap')) {
      Get.put(RxMap<String, bool>(), tag: 'commentGeneratingMap');
    }
    
    final commentGeneratingMap = Get.find<RxMap<String, bool>>(tag: 'commentGeneratingMap');
    commentGeneratingMap[commentGeneratingKey] = true;
    
    try {
      final aiReply = await generateAIReply(commentText, commentId: commentId);
      
      // 生成完成后更新状态
      commentGeneratingMap[commentGeneratingKey] = false;
      
      if (aiReply != null) {
        // 使用try-catch包裹SnackBar显示，防止_controller未初始化导致错误
        try {
          // 显示成功提示
          Get.snackbar(
            '生成完成', 
            '已成功为该评论生成AI回复',
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 1),
          );
        } catch (e) {
          print('显示SnackBar时发生错误: $e');
        }
      }
      
      return aiReply;
    } catch (e) {
      // 生成失败后更新状态
      commentGeneratingMap[commentGeneratingKey] = false;
      aiReplyError.value = '生成失败: ${e.toString()}';
      
      // 使用try-catch包裹SnackBar显示，防止_controller未初始化导致错误
      try {
        // 显示错误提示
        Get.snackbar(
          '生成失败', 
          aiReplyError.value,
          snackPosition: SnackPosition.BOTTOM,
        );
      } catch (e) {
        print('显示SnackBar时发生错误: $e');
      }
      
      return null;
    }
  }
  
  // 批量生成AI回复
  Future<void> batchGenerateAIReply({Function? onComplete}) async {
    if (selectedCommentIds.isEmpty) {
      LoggerUtil.i('【XhsQuickReply】没有选择要回复的评论');
      return;
    }
    
    isGeneratingAIReply.value = true;
    
    // 创建一个RxInt来跟踪当前处理的评论索引
    final currentProcessingIndex = 0.obs;
    
    // 使用临时Map来存储正在生成AI回复的评论ID
    if (!Get.isRegistered<RxMap<String, bool>>(tag: 'commentGeneratingMap')) {
      Get.put(RxMap<String, bool>(), tag: 'commentGeneratingMap');
    }
    final commentGeneratingMap = Get.find<RxMap<String, bool>>(tag: 'commentGeneratingMap');
    
    // 显示进度对话框
    final dialog = AlertDialog(
      title: const Text('AI批量生成回复'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Obx(() => LinearProgressIndicator(
            value: selectedCommentIds.isEmpty ? 0 : currentProcessingIndex.value / selectedCommentIds.length,
          )),
          const SizedBox(height: 16),
          Obx(() => Text('正在生成 (${currentProcessingIndex.value}/${selectedCommentIds.length})')),
        ],
      ),
    );
    
    try {
      await Get.dialog(dialog, barrierDismissible: false);
    } catch (e) {
      LoggerUtil.e('【XhsQuickReply】显示对话框时发生错误: $e');
    }
    
    try {
      // 为每条评论生成AI回复
      for (int i = 0; i < selectedCommentIds.length; i++) {
        currentProcessingIndex.value = i + 1;
        final commentId = selectedCommentIds[i];
        
        // 设置评论特定的生成状态
        final commentGeneratingKey = 'generating_$commentId';
        commentGeneratingMap[commentGeneratingKey] = true;
        
        // 查找评论内容
        final commentIndex = contentDetailController.comments.indexWhere((comment) => comment.id == commentId);
        if (commentIndex == -1) {
          commentGeneratingMap[commentGeneratingKey] = false;
          continue;
        }
        
        final comment = contentDetailController.comments[commentIndex];
        final commentText = comment.content;
        if (commentText.isEmpty) {
          commentGeneratingMap[commentGeneratingKey] = false;
          continue;
        }
        
        // 生成AI回复
        final aiReply = await generateAIReply(commentText, commentId: commentId);
        
        // 更新评论特定的生成状态
        commentGeneratingMap[commentGeneratingKey] = false;
        
        // 如果是第一条评论，将其设为默认回复内容
        if (i == 0 && commentAIReplies.containsKey(commentId)) {
          replyContent.value = commentAIReplies[commentId]!;
        }
        
        // 添加延迟，避免API请求过于频繁
        if (i < selectedCommentIds.length - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }
      
      // 尝试关闭进度对话框
      try {
        Get.back();
      } catch (e) {
        LoggerUtil.e('【XhsQuickReply】关闭进度对话框时发生错误: $e');
      }
      
      if (commentAIReplies.isNotEmpty) {
        // 使用Future.delayed来确保Snackbar显示在正确的上下文中
        await Future.delayed(const Duration(milliseconds: 100));
        try {
          final message = '成功为 ${commentAIReplies.length} 条评论生成AI回复';
          LoggerUtil.i('【XhsQuickReply】$message');
          Get.rawSnackbar(
            message: message,
            title: 'AI生成完成',
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );
        } catch (e) {
          LoggerUtil.e('【XhsQuickReply】显示Snackbar时发生错误: $e');
        }
      } else {
        throw Exception('AI回复生成失败');
      }
    } catch (e) {
      // 尝试关闭进度对话框
      try {
        Get.back();
      } catch (e2) {
        LoggerUtil.e('【XhsQuickReply】关闭进度对话框时发生错误: $e2');
      }
      
      // 使用Future.delayed来确保Snackbar显示在正确的上下文中
      await Future.delayed(const Duration(milliseconds: 100));
      try {
        final message = e.toString();
        LoggerUtil.e('【XhsQuickReply】生成失败: $message');
        Get.rawSnackbar(
          message: message,
          title: 'AI生成失败',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 2),
        );
      } catch (e2) {
        LoggerUtil.e('【XhsQuickReply】显示Snackbar时发生错误: $e2');
      }
    } finally {
      isGeneratingAIReply.value = false;
      // 通知调用者操作已完成
      onComplete?.call();
    }
  }
  
  // 批量回复评论
  void startBatchReply() {
    // 重置索引和状态
    currentReplyIndex.value = 0;
    isReplying.value = true;
    successCount.value = 0;
    failCount.value = 0;
    
    // 显示进度对话框
    try {
      Get.dialog(
        AlertDialog(
          title: const Text('批量回复中'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const LinearProgressIndicator(),
              const SizedBox(height: 16),
              Obx(() => Text('正在回复 (${currentReplyIndex.value}/${selectedCommentIds.length})')),
            ],
          ),
        ),
        barrierDismissible: false,
      );
    } catch (e) {
      print('显示对话框时发生错误: $e');
    }
    
    // 开始批量回复
    replyNextComment();
  }
  
  // 回复下一条评论
  void replyNextComment() async {
    if (currentReplyIndex.value >= selectedCommentIds.length) {
      // 所有评论已回复完毕
      try {
        Get.back(); // 关闭进度对话框
      } catch (e) {
        print('关闭进度对话框时发生错误: $e');
      }
      
      try {
        Get.snackbar(
          '批量回复完成',
          '成功: ${successCount.value}条, 失败: ${failCount.value}条',
          snackPosition: SnackPosition.BOTTOM,
        );
      } catch (e) {
        print('显示SnackBar时发生错误: $e');
      }
      
      isReplying.value = false;
      return;
    }
    
    // 获取当前需要回复的评论ID
    final commentId = selectedCommentIds[currentReplyIndex.value];
    
    // 获取该评论的AI回复内容（如果有）
    String replyText = commentAIReplies[commentId] ?? replyContent.value;
    
    try {
      // 尝试获取cookie
      String cookieStr = '';
      
      // 从Get.arguments中获取accountCookie
      if (Get.arguments is Map && Get.arguments.containsKey('accountCookie')) {
        cookieStr = Get.arguments['accountCookie'] as String;
        LoggerUtil.i('【XhsQuickReply】从参数中获取到cookie，长度: ${cookieStr.length}');
      } else {
        // 如果Get.arguments中没有accountCookie，则尝试从其他地方获取
        LoggerUtil.i('【XhsQuickReply】参数中没有accountCookie，尝试从其他地方获取');
        
        // 尝试获取当前选择的账号ID
        String? accountId;
        if (Get.arguments is Map && Get.arguments.containsKey('accountId')) {
          accountId = Get.arguments['accountId'];
          LoggerUtil.i('【XhsQuickReply】从参数中获取到账号ID: $accountId');
        }
        
        if (accountId == null || accountId.isEmpty) {
          throw Exception('无法获取账号ID');
        }
        
        // 使用PlatformService获取账号的cookie
        final response = await contentDetailController.getPlatformService().getXhsAccountCookie(accountId: accountId);
        if (response.isSuccess && response.data != null && response.data!.isNotEmpty) {
          cookieStr = response.data!;
          LoggerUtil.i('【XhsQuickReply】成功获取到账号cookie，长度: ${cookieStr.length}');
        } else {
          throw Exception('获取账号cookie失败: ${response.msg}');
        }
      }
      
      if (cookieStr.isEmpty) {
        throw Exception('无法获取账号Cookie');
      }
      
      // 获取笔记ID
      String noteId = '';
      if (contentDetailController.noteDetail.value != null && contentDetailController.noteDetail.value!.id.isNotEmpty) {
        noteId = contentDetailController.noteDetail.value!.id;
      } else if (contentDetailController.noteFromList.value != null) {
        noteId = contentDetailController.noteFromList.value!.id;
      }
      
      if (noteId.isEmpty) {
        throw Exception('无法获取笔记ID');
      }
      
      LoggerUtil.i('【XhsQuickReply】开始回复评论 $commentId，笔记ID: $noteId');
      
      // 发送回复
      final result = await XhsService.postComment(
        cookieStr: cookieStr,
        noteId: noteId,
        content: replyText,
        rootCommentId: commentId,
      );
      
      if (result['success'] == true) {
        successCount.value++;
        LoggerUtil.i('【XhsQuickReply】成功回复评论 $commentId');
      } else {
        failCount.value++;
        LoggerUtil.e('【XhsQuickReply】回复评论 $commentId 失败: ${result['message']}');
      }
    } catch (e) {
      failCount.value++;
      LoggerUtil.e('【XhsQuickReply】回复评论异常: $e');
    }
    
    // 回复下一条评论（添加延时，避免频率限制）
    currentReplyIndex.value++;
    Future.delayed(Duration(seconds: timeIntervalSeconds.value), () {
      replyNextComment();
    });
  }
}

class XhsQuickReplySheet extends StatelessWidget {
  final ContentDetailController controller;
  late final XhsQuickReplyController quickReplyController;

  XhsQuickReplySheet({required this.controller, Key? key}) : super(key: key) {
    // 初始化控制器
    quickReplyController = Get.put(XhsQuickReplyController(contentDetailController: controller));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          _buildTitleBar(),
          
          const SizedBox(height: 16),
          
          // 回复时间间隔
          Obx(() => _buildSettingItem(
            '回复时间间隔',
            trailing: Row(
              children: [
                Text(
                  quickReplyController.getTimeIntervalLabel(),
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showTimeIntervalPicker(),
          )),
          
          // 回复评论选择
          Obx(() => _buildSettingItem(
            '回复评论选择',
            trailing: Row(
              children: [
                Text(
                  quickReplyController.selectedCommentIds.isEmpty 
                      ? '选择评论' 
                      : '已选择 ${quickReplyController.selectedCommentIds.length} 条评论',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showCommentSelectionPicker(),
          )),
          
          // 回复内容
          Obx(() => _buildSettingItem(
            '回复内容',
            trailing: Row(
              children: [
                Text(
                  quickReplyController.replyContent.value.length > 10
                      ? '${quickReplyController.replyContent.value.substring(0, 10)}...'
                      : quickReplyController.replyContent.value,
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.chevron_right, color: Colors.grey),
              ],
            ),
            onTap: () => _showReplyContentEditor(),
          )),
          
          const SizedBox(height: 24),
          
          // 底部按钮
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Get.back(),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.black54,
                    backgroundColor: Colors.grey[200],
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('取消'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _confirmQuickReply(),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: Colors.blue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('确定'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建标题栏
  Widget _buildTitleBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消', style: TextStyle(color: Colors.black54)),
        ),
        const Text(
          '一键回复',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        TextButton(
          onPressed: () => _confirmQuickReply(),
          child: const Text('确定', style: TextStyle(color: Colors.blue)),
        ),
      ],
    );
  }

  // 构建设置项
  Widget _buildSettingItem(String title, {Widget? trailing, VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade200),
          ),
        ),
        child: Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 15,
              ),
            ),
            const Spacer(),
            if (trailing != null) trailing,
          ],
        ),
      ),
    );
  }

  // 显示时间间隔选择器
  void _showTimeIntervalPicker() {
    // 时间间隔选项及其对应的秒数
    final timeIntervalOptions = [
      {'label': '5秒', 'seconds': 5},
      {'label': '10秒', 'seconds': 10},
      {'label': '30秒', 'seconds': 30},
      {'label': '1分钟', 'seconds': 60},
      {'label': '5分钟', 'seconds': 300},
      {'label': '10分钟', 'seconds': 600},
      {'label': '30分钟', 'seconds': 1800},
      {'label': '1小时', 'seconds': 3600},
    ];

    // 当前选中的时间间隔标签
    String currentLabel = '5秒';
    for (var option in timeIntervalOptions) {
      if (option['seconds'] == quickReplyController.timeIntervalSeconds.value) {
        currentLabel = option['label'] as String;
        break;
      }
    }

    Get.bottomSheet(
      Container(
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade200),
                ),
              ),
              child: const Text(
                '选择回复时间间隔',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(
              height: 300,
              child: ListView.builder(
                itemCount: timeIntervalOptions.length,
                itemBuilder: (context, index) {
                  final option = timeIntervalOptions[index];
                  final label = option['label'] as String;
                  final seconds = option['seconds'] as int;
                  
                  return ListTile(
                    title: Text(label),
                    trailing: label == currentLabel ? const Icon(Icons.check, color: Colors.blue) : null,
                    onTap: () {
                      // 更新时间间隔
                      quickReplyController.timeIntervalSeconds.value = seconds;
                      Get.back();
                    },
                  );
                },
              ),
            ),
            SafeArea(
              child: TextButton(
                onPressed: () => Get.back(),
                child: Text('content.xhs.quick_reply.cancel'.tr),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示评论选择器
  void _showCommentSelectionPicker() {
    // 用于跟踪选中的评论
    final selectedComments = <String>[].obs;
    
    // 初始化已选择的评论
    if (quickReplyController.selectedCommentIds.isNotEmpty) {
      selectedComments.addAll(quickReplyController.selectedCommentIds);
    }
    
    // 获取或创建commentHasAIReplyMap
    if (!Get.isRegistered<RxMap<String, bool>>(tag: 'commentHasAIReplyMap')) {
      Get.put(RxMap<String, bool>(), tag: 'commentHasAIReplyMap');
    }
    final commentHasAIReplyMap = Get.find<RxMap<String, bool>>(tag: 'commentHasAIReplyMap');
    
    final bottomSheet = Container(
      color: Colors.white,
      height: Get.height * 0.7, // 弹窗高度为屏幕的70%
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () {
                    try {
                      Navigator.of(Get.context!).pop();
                    } catch (e) {
                      LoggerUtil.e('【XhsQuickReply】关闭评论选择器时发生错误: $e');
                    }
                  },
                  child: const Text('取消'),
                ),
                const Text(
                  '选择要回复的评论',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Obx(() => TextButton(
                  onPressed: selectedComments.isNotEmpty ? () {
                    // 确认选择的评论
                    quickReplyController.selectedCommentIds.clear();
                    quickReplyController.selectedCommentIds.addAll(selectedComments);
                    try {
                      Navigator.of(Get.context!).pop();
                    } catch (e) {
                      LoggerUtil.e('【XhsQuickReply】关闭评论选择器时发生错误: $e');
                    }
                  } : null,
                  child: Text(
                    '确定(${selectedComments.length})',
                    style: TextStyle(
                      color: selectedComments.isNotEmpty ? const Color(0xFF7468E4) : Colors.grey,
                    ),
                  ),
                )),
              ],
            ),
          ),
          
          // 选择全部/取消全部
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(() => TextButton(
                  onPressed: () {
                    if (selectedComments.isEmpty || selectedComments.length < controller.comments.length) {
                      // 全选
                      selectedComments.clear();
                      selectedComments.addAll(controller.comments.map((comment) => comment.id));
                    } else {
                      // 取消全选
                      selectedComments.clear();
                    }
                  },
                  child: Text(
                    selectedComments.isEmpty || selectedComments.length < controller.comments.length 
                        ? '全选' 
                        : '取消全选',
                  ),
                )),
                Obx(() => Text(
                  '已选择 ${selectedComments.length}/${controller.comments.length}',
                  style: TextStyle(color: Colors.grey[600]),
                )),
              ],
            ),
          ),
          
          // AI回复按钮
          Obx(() => selectedComments.isNotEmpty ? Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'AI批量回复',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'AI会根据评论内容生成个性化回复',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: quickReplyController.isGeneratingAIReply.value 
                    ? null 
                    : () {
                      // 保存选中的评论
                      quickReplyController.selectedCommentIds.clear();
                      quickReplyController.selectedCommentIds.addAll(selectedComments);
                      
                      // 生成AI回复
                      quickReplyController.batchGenerateAIReply();
                      
                      // 直接关闭选择器，不再等待异步操作完成
                      try {
                        Navigator.of(Get.context!).pop();
                      } catch(e) {
                        LoggerUtil.e('【XhsQuickReply】关闭评论选择器时发生错误: $e');
                      }
                    },
                  icon: quickReplyController.isGeneratingAIReply.value 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.smart_toy),
                  label: Text(quickReplyController.isGeneratingAIReply.value ? '正在生成...' : '开始生成'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF7468E4),
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                ),
              ],
            ),
          ) : const SizedBox.shrink()),
          
          // 评论列表
          Expanded(
            child: Obx(() {
              if (controller.comments.isEmpty) {
                return const Center(child: Text('暂无评论可选择'));
              }
              
              return ListView.builder(
                itemCount: controller.comments.length,
                itemBuilder: (context, index) {
                  final comment = controller.comments[index];
                  final commentId = comment.id;
                  final String text = comment.content;
                  final int createTime = comment.createTime;
                  final String nickname = comment.userInfo.nickname;
                  
                  // 获取该评论的AI回复内容（如果有）
                  final bool hasAIReply = commentHasAIReplyMap[commentId] ?? quickReplyController.commentAIReplies.containsKey(commentId);
                  final String aiReplyContent = quickReplyController.commentAIReplies[commentId] ?? '';
                  
                  return Obx(() {
                    final isSelected = selectedComments.contains(commentId);
                    
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CheckboxListTile(
                          value: isSelected,
                          onChanged: (value) {
                            if (value == true) {
                              selectedComments.add(commentId);
                            } else {
                              selectedComments.remove(commentId);
                            }
                          },
                          title: Text(
                            nickname,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                          subtitle: Text(
                            text,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(fontSize: 13),
                          ),
                          secondary: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                controller.formatCommentTime(createTime),
                                style: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(width: 8),
                              // 单个评论的AI生成按钮
                              InkWell(
                                onTap: () async {
                                  // 获取评论特定的生成状态
                                  final commentGeneratingKey = 'generating_$commentId';
                                  RxMap<String, bool> commentGeneratingMap;
                                  try {
                                    commentGeneratingMap = Get.find<RxMap<String, bool>>(tag: 'commentGeneratingMap');
                                  } catch (e) {
                                    commentGeneratingMap = RxMap<String, bool>();
                                    // 确保Map被注册到GetX中
                                    if (!Get.isRegistered<RxMap<String, bool>>(tag: 'commentGeneratingMap')) {
                                      Get.put(commentGeneratingMap, tag: 'commentGeneratingMap');
                                    }
                                  }
                                  
                                  // 如果已经在生成中，则不重复点击
                                  if (commentGeneratingMap[commentGeneratingKey] == true) {
                                    LoggerUtil.i('【XhsQuickReply】评论 $commentId 正在生成AI回复中，忽略重复点击');
                                    return;
                                  }
                                  
                                  LoggerUtil.i('【XhsQuickReply】点击了评论 $commentId 的AI生成按钮');
                                  
                                  // 手动设置生成状态，确保UI立即更新
                                  commentGeneratingMap[commentGeneratingKey] = true;
                                  
                                  try {
                                    // 生成单个评论的AI回复
                                    LoggerUtil.i('【XhsQuickReply】准备为评论 $commentId 生成AI回复');
                                    final result = await quickReplyController.generateSingleCommentAIReply(commentId, text);
                                    
                                    // 如果生成成功，添加到选中的评论列表中（如果还没有选中）
                                    if (result != null && !selectedComments.contains(commentId)) {
                                      selectedComments.add(commentId);
                                      LoggerUtil.i('【XhsQuickReply】已将评论 $commentId 添加到选中列表');
                                    }
                                  } catch (e) {
                                    LoggerUtil.e('【XhsQuickReply】生成AI回复时发生异常: $e');
                                    try {
                                      Get.rawSnackbar(
                                        message: '生成失败: ${e.toString()}',
                                        title: '生成失败',
                                        snackPosition: SnackPosition.BOTTOM,
                                        duration: const Duration(seconds: 2),
                                      );
                                    } catch (e2) {
                                      LoggerUtil.e('【XhsQuickReply】显示Snackbar时发生错误: $e2');
                                    }
                                    // 确保状态被重置
                                    commentGeneratingMap[commentGeneratingKey] = false;
                                  }
                                },
                                child: Obx(() {
                                  // 获取评论特定的生成状态
                                  final commentGeneratingKey = 'generating_$commentId';
                                  RxMap<String, bool> commentGeneratingMap;
                                  try {
                                    commentGeneratingMap = Get.find<RxMap<String, bool>>(tag: 'commentGeneratingMap');
                                  } catch (e) {
                                    commentGeneratingMap = RxMap<String, bool>();
                                  }
                                  final isGenerating = commentGeneratingMap[commentGeneratingKey] ?? false;
                                  
                                  return Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFECE6FF),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: isGenerating
                                        ? const SizedBox(
                                            width: 12,
                                            height: 12,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF7468E4)),
                                            ),
                                          )
                                        : const Text(
                                            'AI',
                                            style: TextStyle(
                                              color: Color(0xFF7468E4),
                                              fontSize: 12,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                  );
                                }),
                              ),
                            ],
                          ),
                          dense: true,
                          controlAffinity: ListTileControlAffinity.leading,
                        ),
                        // 如果有AI回复内容，则显示（无论是否选中）
                        if (hasAIReply && aiReplyContent.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(left: 72, right: 16, bottom: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'AI回复内容',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF7468E4),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF5F3FF),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: const Color(0xFFE6E1FF)),
                                  ),
                                  child: Text(
                                    aiReplyContent,
                                    style: const TextStyle(fontSize: 13),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        const Divider(height: 1),
                      ],
                    );
                  });
                },
              );
            }),
          ),
        ],
      ),
    );
    
    Get.bottomSheet(
      bottomSheet,
      isScrollControlled: true,
      enableDrag: true,
    );
  }
  
  // 显示AI回复详情
  void _showAIReplyDetail(String commentId) {
    // 获取AI回复内容
    final aiReplyContent = quickReplyController.commentAIReplies[commentId];
    if (aiReplyContent == null) return;
    
    // 查找评论，获取用户信息
    final comment = controller.comments.firstWhere(
      (comment) => comment.id == commentId,
      orElse: () => controller.comments.first,
    );
    
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ClipOval(
                    child: Image.network(
                      comment.userInfo.image,
                      width: 32,
                      height: 32,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const CircleAvatar(
                        backgroundColor: Colors.grey,
                        radius: 16,
                        child: Icon(Icons.person, size: 16, color: Colors.white),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          comment.userInfo.nickname,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          comment.content,
                          style: const TextStyle(fontSize: 12),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'AI生成的回复内容:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  aiReplyContent,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      // 设置为回复内容
                      quickReplyController.replyContent.value = aiReplyContent;
                      Get.back();
                    },
                    child: const Text('使用此回复'),
                  ),
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('关闭'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // 生成AI回复
  void _generateAIReplies(List<String> commentIds) async {
    if (commentIds.isEmpty) return;
    
    // 保存选中的评论
    quickReplyController.selectedCommentIds.clear();
    quickReplyController.selectedCommentIds.addAll(commentIds);
    
    // 生成AI回复
    await quickReplyController.batchGenerateAIReply();
  }

  // 显示回复内容编辑器
  void _showReplyContentEditor() {
    final replyTemplates = [
      '感谢分享！',
      '非常感谢您的分享！',
      '很有价值的内容，谢谢分享！',
      '学习了，感谢分享！',
      '太棒了，感谢分享！',
    ];

    final textController = TextEditingController(text: quickReplyController.replyContent.value);
    final isGeneratingAI = false.obs;
    final aiError = ''.obs;

    Get.bottomSheet(
      Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '编辑回复内容',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: textController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: '请输入回复内容',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            // AI生成回复按钮
            Obx(() => isGeneratingAI.value 
              ? const Center(child: CircularProgressIndicator())
              : Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        isGeneratingAI.value = true;
                        aiError.value = '';
                        
                        try {
                          // 如果有选中的评论，使用第一个评论内容生成AI回复
                          String promptText = '';
                          if (quickReplyController.selectedCommentIds.isNotEmpty) {
                            final commentId = quickReplyController.selectedCommentIds.first;
                            final comment = controller.comments.firstWhere((c) => c.id == commentId);
                            promptText = comment.content;
                          } else {
                            // 否则使用通用提示
                            promptText = '请生成一个友好的评论回复';
                          }
                          
                          final aiReply = await quickReplyController.generateAIReply(promptText);
                          if (aiReply != null) {
                            textController.text = aiReply;
                          } else {
                            aiError.value = '生成失败，请重试';
                          }
                        } catch (e) {
                          aiError.value = '生成失败: $e';
                        } finally {
                          isGeneratingAI.value = false;
                        }
                      },
                      icon: const Icon(Icons.smart_toy, size: 16),
                      label: const Text('AI生成回复'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[100],
                        foregroundColor: Colors.blue[800],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Obx(() => aiError.value.isNotEmpty 
              ? Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  aiError.value,
                  style: const TextStyle(color: Colors.red, fontSize: 12),
                ),
              ) 
              : const SizedBox.shrink()
            ),
            const SizedBox(height: 16),
            const Text(
              '快速模板',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: replyTemplates.map((template) {
                return GestureDetector(
                  onTap: () {
                    textController.text = template;
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: template == quickReplyController.replyContent.value ? Colors.blue[100] : Colors.grey[100],
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: template == quickReplyController.replyContent.value ? Colors.blue : Colors.transparent,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      template,
                      style: TextStyle(
                        color: template == quickReplyController.replyContent.value ? Colors.blue : Colors.black87,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Get.back(),
                  child: const Text('取消'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // 保存回复内容
                    if (textController.text.trim().isNotEmpty) {
                      quickReplyController.replyContent.value = textController.text.trim();
                    }
                    Get.back();
                  },
                  child: const Text('保存'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  // 确认一键回复
  void _confirmQuickReply() {
    // 这里实现确认一键回复的逻辑
    Get.back(); // 关闭弹窗
    
    if (quickReplyController.selectedCommentIds.isEmpty) {
      // 如果没有选择评论，默认回复主贴
      controller.commentController.text = quickReplyController.replyContent.value;
      controller.commentText.value = quickReplyController.replyContent.value;
      controller.commentFocusNode.requestFocus();
    } else {
      // 如果选择了评论，依次回复
      quickReplyController.startBatchReply();
    }
  }
} 