import 'dart:math' as math;
import 'dart:ui';

import 'package:aitoearn_app/pages/content_detail/content_detail_controller.dart';
import 'package:aitoearn_app/pages/content_detail/widgets/comment_filter_sheet.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_note_detail_model.dart';
import 'package:aitoearn_app/widgets/common_loading.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:media_kit_video/media_kit_video.dart' as media_kit_video;

class ContentDetailPage extends GetView<ContentDetailController> {
  const ContentDetailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 设置状态栏为透明
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    return GetX<ContentDetailController>(
      init: controller,
      builder: (_) {
        // 全屏模式下只显示视频播放器
        if (controller.isFullScreen.value) {
          return Scaffold(
            backgroundColor: Colors.black,
            body: _buildVideoPlayer(),
          );
        }

        // 非全屏模式下显示完整页面
        return Scaffold(
          backgroundColor: Colors.white,
          extendBodyBehindAppBar: true,
          // 内容延伸到AppBar下方
          appBar: _buildCustomAppBar(),
          body: _buildBody(),
          bottomNavigationBar: _buildFixedCommentBar(),
        );
      },
    );
  }

  PreferredSizeWidget _buildCustomAppBar() {
    if (controller.isLoading.value || controller.noteDetail.value == null) {
      return AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
      );
    }

    final noteCard = controller.noteDetail.value!.noteCard;

    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
          child: Container(
            color: Colors.black.withOpacity(0.2),
            child: SafeArea(
              child: Row(
                children: [
                  // 返回按钮
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Get.back(),
                  ),

                  // 用户头像
                  ClipOval(
                    child: NetworkImageWidget(
                      noteCard.user.avatar,
                      width: 30,
                      height: 30,
                      fit: BoxFit.cover,
                    ),
                  ),

                  const SizedBox(width: 8),

                  // 用户昵称
                  Expanded(
                    child: Text(
                      noteCard.user.nickname,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // 更多按钮
                  IconButton(
                    icon: const Icon(Icons.more_horiz, color: Colors.white),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CommonLoading());
      }

      if (controller.hasError.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(controller.errorMessage.value),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Get.back(),
                child: Text('content.back'.tr),
              ),
            ],
          ),
        );
      }

      final noteDetail = controller.noteDetail.value!;
      return NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification scrollInfo) {
          // 检测是否滑动到底部，如果是则自动加载更多
          if (scrollInfo.metrics.pixels >
                  scrollInfo.metrics.maxScrollExtent - 200 &&
              !controller.isLoadingComments.value &&
              controller.hasMoreComments.value) {
            controller.loadMoreComments();
          }
          return false;
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildContentSection(noteDetail.noteCard),
              _buildCommentsSection(),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildContentSection(NoteCardDetail noteCard) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片或视频
        Stack(
          children: [
            if (noteCard.type.contains('video'))
              _buildVideoPlayer()
            else
              _buildImageGallery(noteCard.imageList),

            // 顶部渐变遮罩，增强状态栏内容可见性
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: 120,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.black.withOpacity(0.5), Colors.transparent],
                  ),
                ),
              ),
            ),
          ],
        ),

        // 标题和内容
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (noteCard.title.isNotEmpty)
                Text(
                  noteCard.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              if (noteCard.title.isNotEmpty) const SizedBox(height: 8),
              Text(noteCard.desc, style: const TextStyle(fontSize: 16)),
            ],
          ),
        ),

        // 作者信息
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  controller.getPublishTime(),
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ),
            ],
          ),
        ),

        // 互动数据
        // Padding(
        //   padding: const EdgeInsets.all(16.0),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.spaceAround,
        //     children: [
        //       _buildInteractionItem(
        //         Icons.favorite_border,
        //         controller.formatCount(noteCard.interactInfo.likedCount),
        //       ),
        //       _buildInteractionItem(
        //         Icons.chat_bubble_outline,
        //         controller.formatCount(noteCard.interactInfo.commentCount),
        //       ),
        //       _buildInteractionItem(
        //         Icons.star_border,
        //         controller.formatCount(noteCard.interactInfo.collectedCount),
        //       ),
        //       _buildInteractionItem(
        //         Icons.share,
        //         controller.formatCount(noteCard.interactInfo.shareCount),
        //       ),
        //     ],
        //   ),
        // ),
        const Divider(color: Colors.black12),
      ],
    );
  }

  Widget _buildImageGallery(List<NoteImage> images) {
    return Container(
      width: MediaQuery.of(Get.context!).size.width,
      height: 400, // 增加高度以适应状态栏
      child: PageView.builder(
        itemCount: images.length,
        onPageChanged: controller.onPageChanged,
        itemBuilder: (context, index) {
          return NetworkImageWidget(images[index].url, fit: BoxFit.cover);
        },
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Obx(() {
      if (!controller.isVideoInitialized.value) {
        return Container(
          height: 400, // 增加高度以适应状态栏
          color: Colors.black,
          child: const Center(child: CircularProgressIndicator()),
        );
      }

      // 获取屏幕尺寸
      final mediaQuery = MediaQuery.of(Get.context!);
      final screenWidth = mediaQuery.size.width;
      final screenHeight = mediaQuery.size.height;

      // 全屏模式下使用整个屏幕，否则根据比例计算高度（最小400像素）
      final videoHeight =
          controller.isFullScreen.value
              ? screenHeight
              : math.max(screenWidth / 16 * 9, 400.0); // 默认使用16:9比例

      // 全屏模式下使用整个屏幕宽度，否则使用屏幕宽度
      final containerWidth =
          controller.isFullScreen.value ? screenWidth : screenWidth;

      return GestureDetector(
        onTap: controller.toggleControls,
        onDoubleTap: controller.toggleFullScreen, // 添加双击切换全屏
        child: Container(
          width: containerWidth,
          height: videoHeight,
          color: Colors.black,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 视频播放器
              Center(
                child:
                    controller.videoController != null
                        ? media_kit_video.Video(
                          controller: controller.videoController!,
                          controls: media_kit_video.NoVideoControls,
                          fill: Colors.black,
                        )
                        : Container(),
              ),

              // 播放/暂停按钮
              if (controller.showControls.value)
                GestureDetector(
                  onTap: controller.togglePlay,
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      controller.isPlaying.value
                          ? Icons.pause
                          : Icons.play_arrow,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),

              // 底部控制栏
              if (controller.showControls.value)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: EdgeInsets.zero,
                    color: Colors.black.withOpacity(0.3),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 时间显示
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _formatDuration(controller.videoPosition.value),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                _formatDuration(controller.videoDuration.value),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // 进度条
                        SliderTheme(
                          data: SliderThemeData(
                            trackHeight: 2,
                            thumbShape: const RoundSliderThumbShape(
                              enabledThumbRadius: 6,
                            ),
                            overlayShape: const RoundSliderOverlayShape(
                              overlayRadius: 10,
                            ),
                            activeTrackColor: Colors.white,
                            inactiveTrackColor: Colors.white.withOpacity(0.3),
                            thumbColor: Colors.white,
                            overlayColor: Colors.white.withOpacity(0.3),
                          ),
                          child: Slider(
                            value:
                                controller.videoPosition.value.inMilliseconds
                                    .toDouble(),
                            min: 0,
                            max:
                                controller.videoDuration.value.inMilliseconds >
                                        0
                                    ? controller
                                        .videoDuration
                                        .value
                                        .inMilliseconds
                                        .toDouble()
                                    : 1.0,
                            onChanged: (value) {
                              controller.seekTo(
                                Duration(milliseconds: value.toInt()),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // 全屏模式下的返回按钮
              if (controller.isFullScreen.value &&
                  controller.showControls.value)
                Positioned(
                  top: 0,
                  left: 0,
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: GestureDetector(
                        onTap: controller.exitFullScreen,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black45,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  // 格式化时长
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  Widget _buildInteractionItem(IconData icon, String count) {
    return Row(
      children: [Icon(icon, size: 20), const SizedBox(width: 4), Text(count)],
    );
  }

  // 评论部分
  Widget _buildCommentsSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 评论标题和筛选按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                controller.comments.isNotEmpty 
                    ? 'content.comments.count'.trParams({'count': controller.comments.length.toString()})
                    : 'content.comments'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  // 一键回复按钮
                  GestureDetector(
                    onTap: controller.quickReply,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: const Color(0xFFECE6FF),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.reply,
                            size: 14,
                            color: Color(0xFF7468E4),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'content.douyin.one_click_reply'.tr,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF7468E4),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 筛选按钮
                  GestureDetector(
                    onTap: () {
                      showModalBottomSheet(
                        context: Get.context!,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder:
                            (context) =>
                                CommentFilterSheet(controller: controller),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.filter_list,
                            size: 16,
                            color: Colors.grey[700],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'content.filter'.tr,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          // 显示当前筛选状态
          Obx(() {
            if (controller.isFiltered.value) {
              return Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 6,
                          horizontal: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.filter_alt_outlined,
                              size: 14,
                              color: Colors.blue[700],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'content.filtered.comments@count'.trParams({'count': controller.filteredComments.length.toString()}),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        // 使用不会导航返回的clearFilters方法
                        controller.clearFilters();
                      },
                      child: Text('content.clear.filter'.tr, style: TextStyle(fontSize: 12)),
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          }),

          const SizedBox(height: 16),

          // 评论列表
          Obx(() {
            // 显示加载中状态
            if (controller.isLoadingComments.value &&
                controller.comments.isEmpty) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 32.0),
                  child: CommonLoading(),
                ),
              );
            }

            // 显示暂无评论
            if (controller.comments.isEmpty &&
                !controller.isLoadingComments.value) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 32.0),
                child: Center(
                  child: Text('content.no.comments'.tr, style: TextStyle(color: Colors.grey)),
                ),
              );
            }

            // 显示筛选后暂无评论
            if (controller.isFiltered.value &&
                controller.filteredComments.isEmpty) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 32.0),
                child: Center(
                  child: Text(
                    'content.no.filtered.comments'.tr,
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              );
            }

            // 决定使用哪个评论列表
            final commentList =
                controller.isFiltered.value
                    ? controller.filteredComments
                    : controller.comments;

            // 显示评论列表
            return ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount:
                  commentList.length +
                  (controller.hasMoreComments.value &&
                              !controller.isFiltered.value ||
                          controller.isLoadingComments.value
                      ? 1
                      : 0),
              itemBuilder: (context, index) {
                if (index == commentList.length) {
                  // 底部加载指示器
                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                      child: SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                  );
                }

                final comment = commentList[index];
                return _buildCommentItem(comment);
              },
            );
          }),

          // 添加底部间距，避免内容被底部输入框遮挡
          const SizedBox(height: 80),

          // 显示没有更多评论的提示
          Obx(() {
            if (!controller.hasMoreComments.value &&
                controller.comments.isNotEmpty &&
                !controller.isFiltered.value) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Center(
                  child: Text(
                    'content.no.more.comments'.tr,
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildCommentItem(XhsComment comment) {
    return Obx(() {
      final isInSelectionMode = controller.isCommentSelectionMode.value;
      final isSelected = controller.selectedComments.contains(comment.id);
      
      return Column(
        children: [
          InkWell(
            onTap: isInSelectionMode ? () => controller.toggleCommentSelection(comment.id) : null,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 选择框（仅在选择模式下显示）
                  if (isInSelectionMode)
                    Container(
                      margin: const EdgeInsets.only(right: 8, top: 5),
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected ? const Color(0xFF7468E4) : Colors.grey[400]!,
                          width: 1.5,
                        ),
                        color: isSelected ? const Color(0xFF7468E4) : Colors.transparent,
                      ),
                      child: isSelected ? const Icon(Icons.check, color: Colors.white, size: 12) : null,
                    ),
                  
                  // 用户头像
                  ClipOval(
                    child: NetworkImageWidget(
                      comment.userInfo.image,
                      width: 30,
                      height: 30,
                      fit: BoxFit.cover,
                      errorWidget: CircleAvatar(
                        backgroundColor: Colors.grey[300],
                        child: Icon(Icons.person, color: Colors.grey[500]),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // 评论内容
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 用户名
                        Text(
                          comment.userInfo.nickname,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 13,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 4),

                        // 评论内容
                        Text(comment.content, style: const TextStyle(fontSize: 12)),
                        // 评论时间和回复按钮
                        Row(
                          children: [
                            Text(
                              controller.formatCommentTime(comment.createTime),
                              style: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(width: 12),
                            GestureDetector(
                              onTap: () => _showCommentInputSheet(comment: comment),
                              child: Text(
                                'content.reply'.tr,
                                style: TextStyle(
                                  color: Colors.grey[800],
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 点赞按钮和数量
                  Container(
                    margin: EdgeInsets.only(left: 12),
                    child: GestureDetector(
                      onTap: () => controller.likeComment(comment),
                      child: Column(
                        children: [
                          Icon(
                            Icons.thumb_up_alt_outlined,
                            color: Colors.grey[400],
                            size: 20,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            comment.likeCount.isEmpty ? '0' : comment.likeCount,
                            style: TextStyle(color: Colors.grey[500], fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 子评论（如果有的话）
          if (comment.subComments.isNotEmpty)
            _buildSubComments(comment.subComments),
        ],
      );
    });
  }

  Widget _buildSubComments(List<XhsSubComment> subComments) {
    return Padding(
      padding: const EdgeInsets.only(left: 68.0, right: 16.0, bottom: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            subComments.map((subComment) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 12.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 用户头像
                    ClipOval(
                      child: NetworkImageWidget(
                        subComment.userInfo.image,
                        width: 30,
                        height: 30,
                        fit: BoxFit.cover,
                        errorWidget: CircleAvatar(
                          backgroundColor: Colors.grey[300],
                          child: Icon(Icons.person, color: Colors.grey[500]),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),

                    // 评论内容
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 用户名
                          Text(
                            subComment.userInfo.nickname,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 4),

                          // 评论内容
                          Text(
                            subComment.content,
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(height: 6),

                          // 评论时间和回复按钮
                          Row(
                            children: [
                              Text(
                                controller.formatCommentTime(
                                  subComment.createTime,
                                ),
                                style: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(width: 12),
                              GestureDetector(
                                onTap:
                                    () => _showCommentInputSheet(
                                      subComment: subComment,
                                    ),
                                child: Text(
                                  'content.reply'.tr,
                                  style: TextStyle(
                                    color: Colors.grey[800],
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 6),
                    // 点赞按钮和数量
                    GestureDetector(
                      onTap: () => controller.likeSubComment(subComment),
                      child: Column(
                        children: [
                          Icon(
                            Icons.thumb_up_alt_outlined,
                            color: Colors.grey[400],
                            size: 20,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            subComment.likeCount.isEmpty
                                ? '0'
                                : subComment.likeCount,
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  void _showCommentInputSheet({
    XhsComment? comment,
    XhsSubComment? subComment,
  }) {
    controller.setReplyTarget(comment: comment, subComment: subComment);

    Get.bottomSheet(
      _buildCommentInputSheetContent(),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    ).whenComplete(() {
      controller.clearReplyTarget();
    });

    Future.delayed(const Duration(milliseconds: 100), () {
      controller.commentFocusNode.requestFocus();
    });
  }

  Widget _buildCommentInputSheetContent() {
    return GestureDetector(
      // 点击空白区域隐藏键盘和表情选择器
      onTap: () {
        controller.hideKeyboard();
        controller.showEmojiPicker.value = false;
      },
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(Get.context!).viewInsets.bottom,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          decoration: const BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.0),
              topRight: Radius.circular(16.0),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: GestureDetector(
                            // 阻止点击事件冒泡，防止点击输入框时关闭表情选择器
                            onTap:
                                () => FocusScope.of(
                                  Get.context!,
                                ).requestFocus(controller.commentFocusNode),
                            child: Obx(
                              () => Container(
                                height: 40,
                                child: TextField(
                                  controller: controller.commentController,
                                  focusNode: controller.commentFocusNode,
                                  style: const TextStyle(color: Colors.black87),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    hintText: controller.getCommentHintText(),
                                    hintStyle: TextStyle(
                                      color: Colors.grey[400],
                                    ),
                                  ),
                                  onChanged:
                                      (val) =>
                                          controller.commentText.value = val,
                                  maxLines: 4,
                                  minLines: 1,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: controller.sendComment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.redAccent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                      ),
                      child: Text('content.send'.tr, style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () {},
                    icon: Icon(Icons.alternate_email, color: Colors.grey[400]),
                  ),
                  IconButton(
                    onPressed: controller.toggleEmojiPicker,
                    icon: Icon(
                      Icons.sentiment_satisfied_alt_outlined,
                      color: Colors.grey[400],
                    ),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: Icon(Icons.image_outlined, color: Colors.grey[400]),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: Icon(
                      Icons.add_circle_outline,
                      color: Colors.grey[400],
                    ),
                  ),
                ],
              ),
              Obx(
                () =>
                    controller.showEmojiPicker.value
                        ? _buildEmojiPicker()
                        : const SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmojiPicker() {
    return Container(
      height: 250,
      color: Colors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: Row(
              children: [
                Text(
                  "content.emoji.title".tr,
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Spacer(),
                TextButton(
                  onPressed: () {
                    controller.showEmojiPicker.value = false;
                  },
                  child: Text("content.emoji.close".tr, style: TextStyle(color: Colors.grey[600])),
                ),
              ],
            ),
          ),
          Divider(color: Colors.grey[300], height: 1),
          Expanded(
            child: DefaultTabController(
              length: 4,
              child: Column(
                children: [
                  TabBar(
                    tabs: [
                      Tab(text: "content.emoji.tab.common".tr),
                      Tab(text: "content.emoji.tab.all".tr),
                      Tab(text: "content.emoji.tab.popular".tr),
                      Tab(text: "content.emoji.tab.animals".tr),
                    ],
                    labelColor: Colors.black87,
                    unselectedLabelColor: Colors.grey[500],
                    indicatorColor: Colors.redAccent,
                    isScrollable: true,
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        // 常用表情
                        _buildCommonEmojis(),
                        // 全部表情
                        _buildAllEmojis(),
                        // 热门表情包
                        _buildPopularEmojis(),
                        // 动物表情包
                        _buildAnimalEmojis(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 常用表情
  Widget _buildCommonEmojis() {
    final commonEmojis = [
      "😊",
      "😂",
      "🤣",
      "❤️",
      "👍",
      "🎉",
      "😍",
      "🥰",
      "😘",
      "😭",
      "🤔",
      "🙄",
      "😅",
      "😁",
      "🥳",
      "🤗",
      "🙏",
      "👏",
      "🔥",
      "💯",
      "✨",
      "🌟",
      "💕",
      "💓",
    ];

    return _buildEmojiGrid(commonEmojis);
  }

  // 全部表情
  Widget _buildAllEmojis() {
    final List<Map<String, dynamic>> emojiCategories = [
      {
        "name": "content.emoji.category.face".tr,
        "emojis": <String>[
          "😀",
          "😃",
          "😄",
          "😁",
          "😆",
          "😅",
          "🤣",
          "😂",
          "🙂",
          "🙃",
          "😉",
          "😊",
          "😇",
          "🥰",
          "😍",
          "🤩",
          "😘",
          "😗",
          "😚",
          "😙",
          "😋",
          "😛",
          "😜",
          "🤪",
          "😝",
          "🤑",
          "🤗",
          "🤭",
          "🤫",
          "🤔",
          "🤐",
          "🤨",
          "😐",
          "😑",
          "😶",
          "😏",
          "😒",
          "🙄",
          "😬",
          "🤥",
          "😌",
          "😔",
          "😪",
          "🤤",
          "😴",
          "😷",
          "🤒",
          "🤕",
        ],
      },
      {
        "name": "content.emoji.category.gesture".tr,
        "emojis": <String>[
          "👍",
          "👎",
          "👌",
          "✌️",
          "🤞",
          "🤟",
          "🤘",
          "🤙",
          "👈",
          "👉",
          "👆",
          "👇",
          "☝️",
          "👋",
          "🤚",
          "🖐️",
          "✋",
          "🖖",
          "👏",
          "🙌",
          "👐",
          "🤲",
          "🤝",
          "🙏",
        ],
      },
      {
        "name": "content.emoji.category.symbol".tr,
        "emojis": <String>[
          "❤️",
          "🧡",
          "💛",
          "💚",
          "💙",
          "💜",
          "🖤",
          "💔",
          "❣️",
          "💕",
          "💞",
          "💓",
          "💗",
          "💖",
          "💘",
          "💝",
          "💟",
          "☮️",
          "✝️",
          "☪️",
          "🔯",
          "🕎",
          "☯️",
          "☦️",
          "🛐",
          "⛎",
          "♈",
          "♉",
          "♊",
          "♋",
          "♌",
          "♍",
          "♎",
          "♏",
          "♐",
          "♑",
          "♒",
          "♓",
        ],
      },
    ];

    return ListView.builder(
      itemCount: emojiCategories.length,
      itemBuilder: (context, categoryIndex) {
        final Map<String, dynamic> category = emojiCategories[categoryIndex];
        final String categoryName = category["name"] as String;
        final List<String> emojis = category["emojis"] as List<String>;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                categoryName,
                style: TextStyle(
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            GridView.builder(
              physics: NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 8,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: emojis.length,
              itemBuilder: (context, index) {
                final String emoji = emojis[index];
                return GestureDetector(
                  onTap: () {
                    controller.insertEmoji(emoji);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    alignment: Alignment.center,
                    child: Text(emoji, style: TextStyle(fontSize: 24)),
                  ),
                );
              },
            ),
            SizedBox(height: 16),
          ],
        );
      },
    );
  }

  // 热门表情包
  Widget _buildPopularEmojis() {
    final popularEmojis = [
      "🤣",
      "💯",
      "🔥",
      "😅",
      "🙏",
      "👍",
      "💕",
      "🎉",
      "✨",
      "😊",
      "🥳",
      "🤩",
      "😎",
      "🤔",
      "😂",
      "🥰",
      "💪",
      "👏",
      "🌈",
      "⭐",
      "🌟",
      "💓",
      "💝",
      "🎊",
      "🙌",
      "🤗",
      "💃",
      "🕺",
      "🍾",
      "🥂",
      "🏆",
      "🎯",
      "🚀",
      "💰",
      "💸",
      "🤑",
    ];

    return _buildEmojiGrid(popularEmojis);
  }

  // 动物表情包
  Widget _buildAnimalEmojis() {
    final animalEmojis = [
      "🐶",
      "🐱",
      "🐭",
      "🐹",
      "🐰",
      "🦊",
      "🐻",
      "🐼",
      "🐨",
      "🐯",
      "🦁",
      "🐮",
      "🐷",
      "🐸",
      "🐵",
      "🙈",
      "🙉",
      "🙊",
      "🐔",
      "🐧",
      "🐦",
      "🐤",
      "🦆",
      "🦅",
      "🦉",
      "🦇",
      "🐺",
      "🐗",
      "🐴",
      "🦄",
      "🐝",
      "🐛",
      "🦋",
      "🐌",
      "🐞",
      "🐜",
      "🦟",
      "🦗",
      "🕷️",
      "🦂",
      "🐢",
      "🐍",
      "🦎",
      "🦖",
      "🦕",
      "🐙",
      "🦑",
      "🦐",
      "🦞",
      "🦀",
      "🐡",
      "🐠",
      "🐟",
      "🐬",
      "🐳",
      "🐋",
      "🦈",
      "🐊",
      "🐅",
      "🐆",
      "🦓",
      "🦍",
      "🦧",
      "🐘",
      "🦛",
      "🦏",
      "🐪",
      "🐫",
      "🦒",
      "🦘",
      "🐃",
      "🐂",
    ];

    return _buildEmojiGrid(animalEmojis);
  }

  // 通用表情网格构建方法
  Widget _buildEmojiGrid(List<String> emojis) {
    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 8,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: emojis.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            controller.insertEmoji(emojis[index]);
          },
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            alignment: Alignment.center,
            child: Text(emojis[index], style: TextStyle(fontSize: 24)),
          ),
        );
      },
    );
  }

  // 底部固定的评论输入框
  Widget _buildFixedCommentBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 3.0),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            // 评论输入框
            Expanded(
              child: GestureDetector(
                onTap: () => _showCommentInputSheet(),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 5.0,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(24.0),
                  ),
                  child: Text(
                    'content.comment.placeholder'.tr,
                    style: TextStyle(color: Colors.grey[500], fontSize: 14),
                  ),
                ),
              ),
            ),

            // 互动按钮
            GetX<ContentDetailController>(
              init: controller,
              builder: (_) {
                final noteDetail = controller.noteDetail.value;
                if (noteDetail != null) {
                  final interactInfo = noteDetail.noteCard.interactInfo;
                  return Row(
                    children: [
                      // 点赞按钮
                      Padding(
                        padding: const EdgeInsets.only(left: 16.0, right: 12),
                        child: GestureDetector(
                          onTap: controller.likeNote,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.thumb_up_alt_outlined,
                                color:
                                    controller.isLiked.value
                                        ? Colors.red
                                        : Colors.grey[500],
                                size: 18,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                interactInfo.likedCount,
                                style: TextStyle(
                                  color:
                                      controller.isLiked.value
                                          ? Colors.red
                                          : Colors.grey[500],
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      _buildInteractionButton(
                        Icons.chat_bubble_outline,
                        interactInfo.commentCount,
                      ),
                      _buildInteractionButton(
                        Icons.share,
                        interactInfo.shareCount,
                      ),
                    ],
                  );
                }
                return Row(
                  children: [
                    _buildInteractionButton(Icons.thumb_up_alt_outlined, '0'),
                    _buildInteractionButton(Icons.chat_bubble_outline, '0'),
                    _buildInteractionButton(Icons.share, '0'),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // 互动按钮
  Widget _buildInteractionButton(IconData icon, String count) {
    return Padding(
      padding: const EdgeInsets.only(left: 16.0, right: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.grey[500], size: 18),
          const SizedBox(height: 2),
          Text(count, style: TextStyle(color: Colors.grey[500], fontSize: 12)),
        ],
      ),
    );
  }

  // 显示评论筛选弹窗
  void _showCommentFilterSheet() {
    Get.bottomSheet(
      CommentFilterSheet(controller: controller),
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      isScrollControlled: true,
      // 允许内容自由滚动
      enableDrag: true,
      // 允许拖拽关闭
      isDismissible: true, // 允许点击外部关闭
    );
  }
}
