import 'dart:async';

import 'package:aitoearn_app/api/models/base_response.dart';
import 'package:aitoearn_app/api/platform_service.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/models/douyin_models/douyin_aweme_detail_model.dart';
import 'package:aitoearn_app/pages/content_detail/widgets/douyin_comment_filter_sheet.dart';
import 'package:aitoearn_app/pages/content_detail/widgets/douyin_quick_reply_sheet.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:media_kit/media_kit.dart' as media_kit;
import 'package:media_kit_video/media_kit_video.dart' as media_kit_video;

class DouyinContentDetailController extends GetxController {
  final _platformService = PlatformService();
  final isLoading = true.obs;
  final hasError = false.obs;
  final errorMessage = ''.obs;

  final awemeId = ''.obs;
  final awemeDetail = Rx<AwemeDetail?>(null);

  // 点赞相关
  final isLiked = false.obs;
  final isLiking = false.obs;

  // 评论相关
  final comments = <dynamic>[].obs; // 评论列表
  final commentCursor = ''.obs; // 评论列表游标
  final hasMoreComments = true.obs; // 是否有更多评论，初始化为true以便首次加载
  final isLoadingComments = false.obs; // 是否正在加载评论
  final commentText = ''.obs;
  final isSendingComment = false.obs; // 添加发送评论状态
  final minCommentLength = 1.obs; // 最小评论长度
  final maxCommentLength = 100.obs; // 最大评论长度
  
  // 评论筛选相关
  final commentSortType = 'latest'.obs; // 评论排序类型：latest(最新)、earliest(最早)、most_liked(最多点赞)
  final showUnrepliedOnly = false.obs; // 是否只显示未回复的评论
  final filteredComments = <dynamic>[].obs; // 筛选后的评论列表
  final isFiltered = false.obs; // 是否已筛选
  final commentStartTime = Rx<DateTime?>(null); // 评论开始时间
  final commentEndTime = Rx<DateTime?>(null); // 评论结束时间
  final minLikes = 0.obs; // 最小点赞数
  final maxLikes = 0.obs; // 最大点赞数
  final commentKeywords = <String>[].obs; // 关键词列表

  // 视频相关
  media_kit.Player? player;
  media_kit_video.VideoController? videoController;
  final isVideoInitialized = false.obs;
  final isPlaying = false.obs;
  final videoPosition = Duration.zero.obs;
  final videoDuration = Duration.zero.obs;
  final showControls = false.obs;
  final isFullScreen = false.obs;
  Timer? _controlsTimer;
  StreamSubscription? _positionSubscription;

  // 评论输入框控制器
  late TextEditingController commentController;
  final FocusNode commentFocusNode = FocusNode();
  final showEmojiPicker = false.obs;
  final replyCommentId = ''.obs;

  // 回复间隔选项
  final replyIntervals = ['不间隔', '5秒', '10秒', '30秒', '60秒'].obs;
  final selectedIntervalIndex = 0.obs;
  
  // 已选择的评论
  final selectedComments = <String>[].obs;
  
  // 是否显示评论选择模式
  final isCommentSelectionMode = false.obs;
  
  // 格式化时间戳
  String _formatTimestamp(int timestamp) {
    final DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final DateTime now = DateTime.now();
    final Duration difference = now.difference(dateTime);
    
    if (difference.inDays > 365) {
      return '${dateTime.year}年${dateTime.month}月${dateTime.day}日';
    } else if (difference.inDays > 30) {
      return '${dateTime.month}月${dateTime.day}日';
    } else if (difference.inDays > 1) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
  
  // 公开的格式化时间戳方法
  String formatTimestamp(int timestamp) {
    return _formatTimestamp(timestamp);
  }
  
  // 构建时间间隔按钮
  Widget _buildIntervalButton(String label, int seconds, DouyinQuickReplyController controller) {
    return Obx(() {
      final isSelected = controller.timeIntervalSeconds.value == seconds;
      return Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: GestureDetector(
          onTap: () => controller.timeIntervalSeconds.value = seconds,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFFECE6FF) : Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? const Color(0xFF7468E4) : Colors.transparent,
                width: 1,
              ),
            ),
            child: Text(
              label,
              style: TextStyle(
                color: isSelected ? const Color(0xFF7468E4) : Colors.black87,
              ),
            ),
          ),
        ),
      );
    });
  }
  
  // 构建模式按钮
  Widget _buildModeButton(String text, {required IconData icon, required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: const Color(0xFF7468E4), size: 24),
            const SizedBox(height: 8),
            Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }
  




  @override
  void onInit() {
    super.onInit();
    commentController = TextEditingController();
    commentFocusNode.addListener(() {
      if (commentFocusNode.hasFocus && showEmojiPicker.value) {
        showEmojiPicker.value = false;
      }
    });

    // 处理传递过来的参数
    if (Get.arguments is Map) {
      final args = Get.arguments as Map;
      if (args.containsKey('awemeId')) {
        awemeId.value = args['awemeId'];
        fetchAwemeDetail();
      } else {
        isLoading.value = false;
        hasError.value = true;
        errorMessage.value = '无效的抖音作品ID';
      }
    } else if (Get.arguments is String) {
      // 兼容直接传递awemeId的方式
      awemeId.value = Get.arguments;
      fetchAwemeDetail();
    } else {
      isLoading.value = false;
      hasError.value = true;
      errorMessage.value = '无效的抖音作品ID';
    }
    
    // 检查当前账号，如果为空但有账号列表，则选择第一个抖音账号
    _checkAndSetCurrentAccount();
  }

  // 检查并设置当前账号
  void _checkAndSetCurrentAccount() {
    try {
      final accountService = AccountPersistentService.to;
      final currentAccount = accountService.currentAccount.value;
      
      // 如果当前没有选择账号，但账号列表不为空
      if (currentAccount == null && accountService.accounts.isNotEmpty) {
        LoggerUtil.i('【DouyinContentDetail】当前未选择账号，但账号列表不为空，尝试选择一个抖音账号');
        
        // 查找抖音账号
        final douyinAccounts = accountService.accounts.where(
          (account) => account.type == PlatTypeEnum.douyin
        ).toList();
        
        if (douyinAccounts.isNotEmpty) {
          // 选择第一个抖音账号
          accountService.setCurrentAccount(douyinAccounts.first);
          LoggerUtil.i('【DouyinContentDetail】已自动选择抖音账号: ${douyinAccounts.first.nickname}');
          
          // 显示提示
          Get.snackbar(
            '提示',
            '已自动选择账号: ${douyinAccounts.first.nickname}',
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );
        } else {
          LoggerUtil.w('【DouyinContentDetail】未找到可用的抖音账号');
        }
      } else if (currentAccount != null && currentAccount.type != PlatTypeEnum.douyin) {
        LoggerUtil.w('【DouyinContentDetail】当前选择的不是抖音账号，尝试切换到抖音账号');
        
        // 查找抖音账号
        final douyinAccounts = accountService.accounts.where(
          (account) => account.type == PlatTypeEnum.douyin
        ).toList();
        
        if (douyinAccounts.isNotEmpty) {
          // 选择第一个抖音账号
          accountService.setCurrentAccount(douyinAccounts.first);
          LoggerUtil.i('【DouyinContentDetail】已自动切换到抖音账号: ${douyinAccounts.first.nickname}');
          
          // 显示提示
          Get.snackbar(
            '提示',
            '已自动切换到抖音账号: ${douyinAccounts.first.nickname}',
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );
        }
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】检查并设置当前账号异常: $e');
    }
  }

  // 显示当前账号信息
  void _showCurrentAccountInfo() {
    try {
      final accountService = AccountPersistentService.to;
      final currentAccount = accountService.currentAccount.value;
      
      if (currentAccount != null) {
        if (currentAccount.type == PlatTypeEnum.douyin) {
          LoggerUtil.i('【DouyinContentDetail】当前使用抖音账号: ${currentAccount.nickname}');
          
          // 使用Future.delayed来确保在构建完成后显示Snackbar
          Future.delayed(const Duration(milliseconds: 100), () {
            try {
              Get.rawSnackbar(
                message: '当前使用抖音账号: ${currentAccount.nickname}',
                title: '账号信息',
                snackPosition: SnackPosition.TOP,
                duration: const Duration(seconds: 2),
                backgroundColor: Colors.green.withOpacity(0.7),
                animationDuration: const Duration(milliseconds: 500),
                forwardAnimationCurve: Curves.easeOutCirc,
                reverseAnimationCurve: Curves.easeInCirc,
                overlayBlur: 0.0,
                overlayColor: Colors.transparent,
              );
            } catch (e) {
              LoggerUtil.e('【DouyinContentDetail】显示账号信息Snackbar时发生错误: $e');
            }
          });
        } else {
          LoggerUtil.w('【DouyinContentDetail】当前账号不是抖音账号，尝试切换到抖音账号');
          _checkAndSetCurrentAccount();
        }
      } else {
        LoggerUtil.w('【DouyinContentDetail】当前未选择任何账号，尝试选择抖音账号');
        _checkAndSetCurrentAccount();
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】显示当前账号信息异常: $e');
    }
  }

  @override
  void onClose() {
    // 确保退出全屏
    if (isFullScreen.value) {
      exitFullScreen();
    }

    // 停止视频位置监听
    _positionSubscription?.cancel();

    // 取消控制栏隐藏计时器
    _cancelHideControlsTimer();

    // 恢复系统UI样式
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    commentController.dispose();
    commentFocusNode.dispose();
    player?.dispose();
    super.onClose();
  }

  // 显示评论筛选面板
  void showCommentFilterSheet() {
    Get.bottomSheet(
      DouyinCommentFilterSheet(controller: this),
      isScrollControlled: true,
      enableDrag: true,
    );
  }

  // 更改评论排序类型
  void changeCommentSortType(String type) {
    commentSortType.value = type;
  }

  // 切换只看未回复
  void toggleUnrepliedOnly() {
    showUnrepliedOnly.value = !showUnrepliedOnly.value;
  }

  // 重置评论筛选
  void resetCommentFilters() {
    _clearFilters();
    Get.back(); // 关闭筛选面板
    // 重新加载评论
    comments.clear();
    commentCursor.value = '';
    loadComments();
  }

  // 清空所有筛选条件（不关闭弹窗，不导航返回）
  void clearFilters() {
    _clearFilters();
  }
  
  // 内部方法：清空所有筛选条件
  void _clearFilters() {
    commentSortType.value = 'latest';
    showUnrepliedOnly.value = false;
    commentStartTime.value = null;
    commentEndTime.value = null;
    minLikes.value = 0;
    maxLikes.value = 0;
    commentKeywords.clear();
    isFiltered.value = false;
    filteredComments.clear();
  }

  // 应用评论筛选
  void applyCommentFilters() {
    isFiltered.value = true;
    // 关闭筛选面板
    Get.back();
    // 应用筛选
    _filterComments();
  }

  // 筛选评论
  void _filterComments() {
    if (!isFiltered.value) {
      filteredComments.clear();
      return;
    }

    // 打印筛选条件
    LoggerUtil.i('【DouyinContentDetail】应用评论筛选: 排序类型=${commentSortType.value}, 只看未回复=${showUnrepliedOnly.value}');
    LoggerUtil.i('【DouyinContentDetail】筛选前评论数量: ${comments.length}');

    // 创建评论副本进行筛选
    final List<dynamic> filtered = List.from(comments);

    // 1. 按时间范围筛选
    if (commentStartTime.value != null || commentEndTime.value != null) {
      filtered.removeWhere((comment) {
        final int createTime = comment['create_time'] is int ? comment['create_time'] : 0;
        final commentTime = DateTime.fromMillisecondsSinceEpoch(createTime * 1000);
        
        bool matchesStartTime = true;
        if (commentStartTime.value != null) {
          matchesStartTime = commentTime.isAfter(commentStartTime.value!) || 
                            commentTime.isAtSameMomentAs(commentStartTime.value!);
        }
        
        bool matchesEndTime = true;
        if (commentEndTime.value != null) {
          matchesEndTime = commentTime.isBefore(commentEndTime.value!) || 
                          commentTime.isAtSameMomentAs(commentEndTime.value!);
        }
        
        return !(matchesStartTime && matchesEndTime);
      });
    }
    
    // 2. 按点赞数筛选
    if (minLikes.value > 0 || maxLikes.value > 0) {
      filtered.removeWhere((comment) {
        final int likes = int.tryParse(comment['digg_count']?.toString() ?? '0') ?? 0;
        
        bool matchesMinLikes = true;
        if (minLikes.value > 0) {
          matchesMinLikes = likes >= minLikes.value;
        }
        
        bool matchesMaxLikes = true;
        if (maxLikes.value > 0) {
          matchesMaxLikes = likes <= maxLikes.value;
        }
        
        return !(matchesMinLikes && matchesMaxLikes);
      });
    }
    
    // 3. 按关键词筛选
    if (commentKeywords.isNotEmpty) {
      filtered.removeWhere((comment) {
        final String text = comment['text'] ?? '';
        // 如果任何一个关键词匹配，则保留该评论
        return !commentKeywords.any((keyword) => 
          text.toLowerCase().contains(keyword.toLowerCase()));
      });
    }

    // 4. 根据排序类型排序
    try {
      switch (commentSortType.value) {
        case 'latest':
          filtered.sort((a, b) {
            final int timeA = a['create_time'] is int ? a['create_time'] : 0;
            final int timeB = b['create_time'] is int ? b['create_time'] : 0;
            return timeB.compareTo(timeA); // 降序，最新的在前
          });
          break;
        case 'earliest':
          filtered.sort((a, b) {
            final int timeA = a['create_time'] is int ? a['create_time'] : 0;
            final int timeB = b['create_time'] is int ? b['create_time'] : 0;
            return timeA.compareTo(timeB); // 升序，最早的在前
          });
          break;
        case 'most_liked':
          filtered.sort((a, b) {
            final int likesA = int.tryParse(a['digg_count']?.toString() ?? '0') ?? 0;
            final int likesB = int.tryParse(b['digg_count']?.toString() ?? '0') ?? 0;
            return likesB.compareTo(likesA); // 降序，点赞最多的在前
          });
          break;
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】评论排序异常: $e');
    }

    // 5. 只看未回复
    if (showUnrepliedOnly.value) {
      try {
        filtered.removeWhere((comment) {
          final int replyCount = int.tryParse(comment['reply_comment_total']?.toString() ?? '0') ?? 0;
          return replyCount > 0;
        });
      } catch (e) {
        LoggerUtil.e('【DouyinContentDetail】筛选未回复评论异常: $e');
      }
    }

    // 更新筛选后的评论列表
    filteredComments.value = filtered;
    LoggerUtil.i('【DouyinContentDetail】筛选后评论数量: ${filteredComments.length}');
  }

  // 一键回复功能
  void quickReply() async {
    // 显示一键回复弹窗
    Get.bottomSheet(
      DouyinQuickReplySheet(controller: this),
      isScrollControlled: true,
      enableDrag: true,
    );
  }
  
  // 显示回复模板列表
  void showReplyTemplates() async {
    final selectedReply = await Get.bottomSheet(
      Container(
        padding: const EdgeInsets.only(top: 16, bottom: 24),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖动条
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // 标题
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '选择回复模板',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Get.back(),
                    child: Icon(Icons.close, color: Colors.grey[700], size: 20),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 预设回复列表
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    _buildQuickReplyItemCard('感谢支持！'),
                    _buildQuickReplyItemCard('谢谢观看，欢迎点赞关注'),
                    _buildQuickReplyItemCard('感谢评论，后续会有更多精彩内容'),
                    _buildQuickReplyItemCard('您的建议我们会认真考虑'),
                    _buildQuickReplyItemCard('感谢反馈，我们会继续努力'),
                    _buildQuickReplyItemCard('感谢关注，更多精彩内容敬请期待'),
                    _buildQuickReplyItemCard('已收到您的反馈，我们会尽快处理'),
                    _buildQuickReplyItemCard('谢谢支持，我们会做得更好'),
                    _buildQuickReplyItemCard('欢迎继续关注我们的更新'),
                    _buildQuickReplyItemCard('谢谢，我们会持续创作优质内容'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
      enableDrag: true,
    );

    if (selectedReply != null) {
      if (isCommentSelectionMode.value && selectedComments.isNotEmpty) {
        // 如果是在评论选择模式下，则批量回复选中的评论
        _batchReplyToComments(selectedReply);
      } else {
        // 否则只设置输入框内容
        commentController.text = selectedReply;
        commentText.value = selectedReply;
        commentFocusNode.requestFocus();
      }
    }
  }
  
  // 批量回复评论
  Future<void> _batchReplyToComments(String replyText) async {
    // 如果没有选中的评论，直接返回
    if (selectedComments.isEmpty) {
      Get.snackbar(
        '提示',
        '请先选择要回复的评论',
        backgroundColor: Colors.black87,
        colorText: Colors.white,
      );
      return;
    }
    
    // 获取回复间隔时间（秒）
    int intervalSeconds = 0;
    switch (selectedIntervalIndex.value) {
      case 1: // 5秒
        intervalSeconds = 5;
        break;
      case 2: // 10秒
        intervalSeconds = 10;
        break;
      case 3: // 30秒
        intervalSeconds = 30;
        break;
      case 4: // 60秒
        intervalSeconds = 60;
        break;
      default: // 不间隔
        intervalSeconds = 0;
    }
    
    // 显示进度对话框
    Get.dialog(
      AlertDialog(
        title: const Text('批量回复中'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Obx(() => Text('正在回复 ${selectedComments.length} 条评论，请稍候...')),
          ],
        ),
      ),
      barrierDismissible: false,
    );
    
    // 批量回复评论
    int successCount = 0;
    int failCount = 0;
    
    for (int i = 0; i < selectedComments.length; i++) {
      final commentId = selectedComments[i];
      
      try {
        // 查找评论对象
        final commentIndex = comments.indexWhere((comment) => comment['cid'] == commentId);
        if (commentIndex == -1) continue;
        
        final comment = comments[commentIndex];
        
        // 发送回复
        final success = await replyToComment(commentId, replyText);
        
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
        
        // 如果设置了间隔且不是最后一条评论，则等待
        if (intervalSeconds > 0 && i < selectedComments.length - 1) {
          await Future.delayed(Duration(seconds: intervalSeconds));
        }
      } catch (e) {
        LoggerUtil.e('【DouyinContentDetail】批量回复评论异常: $e');
        failCount++;
      }
    }
    
    // 关闭进度对话框
    Get.back();
    
    // 退出评论选择模式
    isCommentSelectionMode.value = false;
    selectedComments.clear();
    
    // 显示结果
    Get.snackbar(
      '批量回复完成',
      '成功: $successCount, 失败: $failCount',
      backgroundColor: Colors.black87,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
    );
    
    // 刷新评论列表
    if (successCount > 0) {
      comments.clear();
      commentCursor.value = '';
      loadComments();
    }
  }
  
  // 回复单条评论
  Future<bool> replyToComment(String commentId, String text) async {
    try {
      LoggerUtil.i('【DouyinContentDetail】开始回复评论, commentId: $commentId, text: $text');
      
      // 检查并设置当前账号
      _checkAndSetCurrentAccount();
      
      // 获取当前账号的Cookie
      final currentAccount = AccountPersistentService.to.currentAccount.value;
      if (currentAccount == null) {
        throw Exception('请先登录抖音账号');
      }
      
      // 检查refreshToken是否存在
      String cookie;
      if (currentAccount.refreshToken == null || currentAccount.refreshToken!.isEmpty) {
        // 尝试使用cookie代替
        if (currentAccount.cookie == null || currentAccount.cookie!.isEmpty) {
          throw Exception('抖音账号登录信息不完整，请重新登录');
        }
        cookie = currentAccount.cookie!;
        LoggerUtil.i('【DouyinContentDetail】使用cookie回复评论');
      } else {
        cookie = currentAccount.refreshToken!;
        LoggerUtil.i('【DouyinContentDetail】使用refreshToken回复评论');
      }
      
      // 调用API发送评论
      final response = await _platformService.publishDouyinVideoComment(
        awemeId: awemeId.value,
        text: text,
        cookie: cookie,
        replyId: commentId,
      );
      
      if (response.success) {
        LoggerUtil.i('【DouyinContentDetail】回复评论成功');
        return true;
      } else {
        LoggerUtil.e('【DouyinContentDetail】回复评论失败: ${response.msg}');
        return false;
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】回复评论异常: $e');
      return false;
    }
  }
  
  // 切换评论选择状态
  void toggleCommentSelection(String commentId) {
    if (selectedComments.contains(commentId)) {
      selectedComments.remove(commentId);
    } else {
      selectedComments.add(commentId);
    }
  }
  
  // 取消评论选择模式
  void cancelCommentSelectionMode() {
    isCommentSelectionMode.value = false;
    selectedComments.clear();
  }
  
  // 构建快速回复项
  Widget _buildQuickReplyItemCard(String text) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 0,
      color: const Color(0xFFF8F8F8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: () => Get.back(result: text),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  text,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> fetchAwemeDetail() async {
    isLoading.value = true;
    hasError.value = false;

    try {
      LoggerUtil.i(
        '【DouyinContentDetail】开始获取抖音作品详情, awemeId: ${awemeId.value}',
      );

      // 检查并设置当前账号
      _checkAndSetCurrentAccount();
      
      // 显示当前账号信息
      _showCurrentAccountInfo();

      final response = await _platformService.getDouyinAwemeDetail(
        awemeId: awemeId.value,
      );

      if (response.success && response.data != null) {
        LoggerUtil.i('【DouyinContentDetail】获取抖音作品详情成功，开始解析数据');

        try {
          // 打印收到的数据结构，便于调试
          LoggerUtil.i('【DouyinContentDetail】响应数据: ${response.data}');

          // 确保数据是Map类型
          final Map<String, dynamic> dataMap;
          if (response.data is Map<String, dynamic>) {
            dataMap = response.data as Map<String, dynamic>;
          } else {
            LoggerUtil.e(
              '【DouyinContentDetail】响应数据格式不正确，期望得到Map，但收到了${response.data.runtimeType}',
            );
            throw Exception('响应数据格式不正确');
          }

          // 检查响应数据是否包含 aweme_detail 字段
          if (dataMap.containsKey('aweme_detail') &&
              dataMap['aweme_detail'] != null &&
              dataMap['aweme_detail'] is Map<String, dynamic>) {
            final awemeDetailMap =
                dataMap['aweme_detail'] as Map<String, dynamic>;
            awemeDetail.value = _parseAwemeDetail(awemeDetailMap);

            if (awemeDetail.value != null) {
              LoggerUtil.i('【DouyinContentDetail】数据解析成功');
              // 初始化播放器
              _initPlayer();
              
              // 重置评论状态并加载评论
              comments.clear();
              commentCursor.value = '';
              hasMoreComments.value = true;
              isFiltered.value = false;
              filteredComments.clear();
              
              // 加载评论
              Future.delayed(Duration(milliseconds: 500), () {
                loadComments();
              });
            } else {
              LoggerUtil.e('【DouyinContentDetail】解析 aweme_detail 数据失败，结果为空');
              throw Exception('解析 aweme_detail 数据失败');
            }
          } else {
            LoggerUtil.e('【DouyinContentDetail】响应数据中缺少 aweme_detail 字段或格式不正确');
            throw Exception('响应数据中缺少 aweme_detail');
          }
        } catch (e, stackTrace) {
          LoggerUtil.e('【DouyinContentDetail】数据解析或播放器初始化失败: $e', e, stackTrace);
          throw Exception('解析数据异常: $e');
        }
      } else {
        LoggerUtil.e('【DouyinContentDetail】获取抖音作品详情失败: ${response.msg}');
        throw Exception(response.msg ?? '获取详情失败');
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】获取抖音作品详情异常: $e');
      hasError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载评论
  Future<void> loadComments() async {
    // 如果正在加载评论，或者已经没有更多评论且不是首次加载，则直接返回
    if (isLoadingComments.value ||
        (!hasMoreComments.value && commentCursor.value.isNotEmpty)) {
      return;
    }

    isLoadingComments.value = true;

    try {
      LoggerUtil.i(
        '【DouyinContentDetail】开始获取评论, awemeId: ${awemeId.value}, cursor: ${commentCursor.value}',
      );

      // 检查并设置当前账号
      _checkAndSetCurrentAccount();

      // 获取当前账号信息
      final currentAccount = AccountPersistentService.to.currentAccount.value;
      String? cookie;
      
      if (currentAccount != null) {
        // 优先使用refreshToken，如果没有则使用cookie
        if (currentAccount.refreshToken != null && currentAccount.refreshToken!.isNotEmpty) {
          cookie = currentAccount.refreshToken;
          LoggerUtil.i('【DouyinContentDetail】使用refreshToken获取评论');
        } else if (currentAccount.cookie != null && currentAccount.cookie!.isNotEmpty) {
          cookie = currentAccount.cookie;
          LoggerUtil.i('【DouyinContentDetail】使用cookie获取评论');
        }
      }

      final response = await _platformService.getDouyinVideoComments(
        awemeId: awemeId.value,
        cursor: commentCursor.value,
        count: 20, // 增加每页数量
        cookie: cookie, // 添加cookie参数，如果为null也没关系，API会处理
      );

      if (response.success && response.data != null) {
        LoggerUtil.i('【DouyinContentDetail】获取评论成功');

        // 解析评论数据
        final Map<String, dynamic> dataMap = response.data as Map<String, dynamic>;
        
        // 打印数据结构，便于调试
        LoggerUtil.i('【DouyinContentDetail】评论数据结构: ${dataMap.keys.toList()}');
        
        // 获取评论列表
        final List<dynamic> commentList = dataMap['comments'] is List ? 
            dataMap['comments'] as List<dynamic> : [];
        
        // 获取下一页游标
        final String cursor = dataMap['cursor']?.toString() ?? '';
        final bool hasMore = dataMap['has_more'] == 1;
        
        LoggerUtil.i('【DouyinContentDetail】获取到 ${commentList.length} 条评论，游标: $cursor, 是否有更多: $hasMore');
        
        // 更新状态
        if (commentList.isNotEmpty) {
          comments.addAll(commentList);
        }
        commentCursor.value = cursor;
        hasMoreComments.value = hasMore;
        
        // 如果已筛选，应用筛选
        if (isFiltered.value) {
          _filterComments();
        }
        
        LoggerUtil.i('【DouyinContentDetail】评论加载完成，共 ${comments.length} 条');
      } else {
        LoggerUtil.e('【DouyinContentDetail】获取评论失败: ${response.msg}');
      }
    } catch (e, stack) {
      LoggerUtil.e('【DouyinContentDetail】获取评论异常: $e', e, stack);
    } finally {
      isLoadingComments.value = false;
    }
  }

  /// 点赞切换
  void toggleLike() {
    isLiked.value = !isLiked.value;
    // TODO: 实现实际的点赞接口调用
  }

  /// 加载更多评论
  Future<void> loadMoreComments() async {
    if (hasMoreComments.value && !isLoadingComments.value) {
      await loadComments();
    }
  }

  // 手动解析AwemeDetail，避免JSON序列化错误
  AwemeDetail? _parseAwemeDetail(Map<String, dynamic> awemeDetailData) {
    if (awemeDetailData == null) {
      return null;
    }

    try {
      // 打印详情数据的键，便于调试
      LoggerUtil.i(
        '【DouyinContentDetail】解析作品详情, 键值包括: ${awemeDetailData.keys.toList()}',
      );

      // 提取statistics数据
      final statisticsData =
          awemeDetailData['statistics'] as Map<String, dynamic>?;
      if (statisticsData == null) {
        LoggerUtil.e('【DouyinContentDetail】统计数据为空');
        return null;
      }

      // 提取author数据
      final authorData = awemeDetailData['author'] as Map<String, dynamic>?;
      if (authorData == null) {
        LoggerUtil.e('【DouyinContentDetail】作者数据为空');
        return null;
      }

      // 提取music数据
      final musicData = awemeDetailData['music'] as Map<String, dynamic>?;
      if (musicData == null) {
        LoggerUtil.e('【DouyinContentDetail】音乐数据为空');
        // 创建一个默认的Music对象，避免因为音乐数据缺失而导致整个解析失败
        final defaultMusic = Music(
          id: '',
          title: '',
          playUrl: '',
          author: '',
          album: '',
          duration: 0,
        );

        // 提取video数据
        final videoData = awemeDetailData['video'] as Map<String, dynamic>?;
        if (videoData == null) {
          LoggerUtil.e('【DouyinContentDetail】视频数据为空');
          return null;
        }

        // 构造Statistics对象 - 使用字符串转换，避免类型转换错误
        final statistics = Statistics(
          commentCount: _getStringValue(statisticsData, 'comment_count', '0'),
          diggCount: _getStringValue(statisticsData, 'digg_count', '0'),
          collectCount: _getStringValue(statisticsData, 'collect_count', '0'),
          shareCount: _getStringValue(statisticsData, 'share_count', '0'),
          playCount: _getStringValue(statisticsData, 'play_count', '0'),
        );

        // 构造Author对象
        final author = Author(
          nickname: _getStringValue(authorData, 'nickname', ''),
          secUid: _getStringValue(authorData, 'sec_uid', ''),
          avatarThumb: _extractAvatarUrl(authorData, 'avatar_thumb'),
          signature: _getStringValue(authorData, 'signature', ''),
          uniqueId: _getStringValue(authorData, 'unique_id', ''),
          uid: _getStringValue(authorData, 'uid', ''),
        );

        // 构造Video对象及其子对象
        final video = _parseVideoData(videoData);
        if (video == null) {
          LoggerUtil.e('【DouyinContentDetail】解析视频数据失败');
          return null;
        }

        // 构造AwemeDetail对象
        return AwemeDetail(
          statistics: statistics,
          awemeId: _getStringValue(awemeDetailData, 'aweme_id', ''),
          desc: _getStringValue(awemeDetailData, 'desc', ''),
          author: author,
          music: defaultMusic,
          video: video,
          createTime: _getIntValue(awemeDetailData, 'create_time', 0),
        );
      }

      // 如果有音乐数据，则提取音乐信息
      final music = Music(
        id: _getStringValue(musicData, 'id', ''),
        title: _getStringValue(musicData, 'title', ''),
        playUrl: _extractMusicUrl(musicData),
        author: _getStringValue(musicData, 'author', ''),
        album: _getStringValue(musicData, 'album', ''),
        duration: _getIntValue(musicData, 'duration', 0),
      );

      // 提取video数据
      final videoData = awemeDetailData['video'] as Map<String, dynamic>?;
      if (videoData == null) {
        LoggerUtil.e('【DouyinContentDetail】视频数据为空');
        return null;
      }

      // 构造Statistics对象 - 使用字符串转换，避免类型转换错误
      final statistics = Statistics(
        commentCount: _getStringValue(statisticsData, 'comment_count', '0'),
        diggCount: _getStringValue(statisticsData, 'digg_count', '0'),
        collectCount: _getStringValue(statisticsData, 'collect_count', '0'),
        shareCount: _getStringValue(statisticsData, 'share_count', '0'),
        playCount: _getStringValue(statisticsData, 'play_count', '0'),
      );

      // 构造Author对象
      final author = Author(
        nickname: _getStringValue(authorData, 'nickname', ''),
        secUid: _getStringValue(authorData, 'sec_uid', ''),
        avatarThumb: _extractAvatarUrl(authorData, 'avatar_thumb'),
        signature: _getStringValue(authorData, 'signature', ''),
        uniqueId: _getStringValue(authorData, 'unique_id', ''),
        uid: _getStringValue(authorData, 'uid', ''),
      );

      // 构造Video对象及其子对象
      final video = _parseVideoData(videoData);
      if (video == null) {
        LoggerUtil.e('【DouyinContentDetail】解析视频数据失败');
        return null;
      }

      // 构造AwemeDetail对象
      return AwemeDetail(
        statistics: statistics,
        awemeId: _getStringValue(awemeDetailData, 'aweme_id', ''),
        desc: _getStringValue(awemeDetailData, 'desc', ''),
        author: author,
        music: music,
        video: video,
        createTime: _getIntValue(awemeDetailData, 'create_time', 0),
      );
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】手动解析作品详情异常: $e');
      return null;
    }
  }

  // 解析Video数据
  Video? _parseVideoData(Map<String, dynamic> videoData) {
    try {
      // 提取封面数据
      final coverData = videoData['cover'] as Map<String, dynamic>?;
      final dynamicCoverData =
          videoData['dynamic_cover'] as Map<String, dynamic>?;
      final playAddrData = videoData['play_addr'] as Map<String, dynamic>?;

      if (coverData == null ||
          dynamicCoverData == null ||
          playAddrData == null) {
        return null;
      }

      // 构造VideoCover对象
      final cover = VideoCover(
        uri: _getStringValue(coverData, 'uri', ''),
        urlList: _extractUrlList(coverData, 'url_list'),
        width: _getIntValue(coverData, 'width', 0),
        height: _getIntValue(coverData, 'height', 0),
      );

      final dynamicCover = VideoCover(
        uri: _getStringValue(dynamicCoverData, 'uri', ''),
        urlList: _extractUrlList(dynamicCoverData, 'url_list'),
        width: _getIntValue(dynamicCoverData, 'width', 0),
        height: _getIntValue(dynamicCoverData, 'height', 0),
      );

      // 构造VideoPlayAddr对象
      final playAddr = VideoPlayAddr(
        uri: _getStringValue(playAddrData, 'uri', ''),
        urlList: _extractUrlList(playAddrData, 'url_list'),
        width: _getIntValue(playAddrData, 'width', 0),
        height: _getIntValue(playAddrData, 'height', 0),
        urlKey: _getStringValue(playAddrData, 'url_key', ''),
        dataSize: _getIntValue(playAddrData, 'data_size', 0),
        fileHash: _getStringValue(playAddrData, 'file_hash', ''),
      );

      // 构造Video对象
      return Video(
        cover: cover,
        dynamicCover: dynamicCover,
        playAddr: playAddr,
        duration: _getIntValue(videoData, 'duration', 0),
        width: _getIntValue(videoData, 'width', 0),
        height: _getIntValue(videoData, 'height', 0),
        ratio: _getStringValue(videoData, 'ratio', ''),
      );
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】解析视频数据异常: $e');
      return null;
    }
  }

  // 提取头像URL
  String _extractAvatarUrl(Map<String, dynamic> authorData, String key) {
    try {
      if (authorData.containsKey(key) &&
          authorData[key] is Map<String, dynamic> &&
          authorData[key].containsKey('url_list') &&
          authorData[key]['url_list'] is List &&
          (authorData[key]['url_list'] as List).isNotEmpty) {
        return (authorData[key]['url_list'] as List).first.toString();
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】提取头像URL异常: $e');
    }
    return '';
  }

  // 提取音乐URL
  String _extractMusicUrl(Map<String, dynamic> musicData) {
    try {
      if (musicData.containsKey('play_url') &&
          musicData['play_url'] is Map<String, dynamic> &&
          musicData['play_url'].containsKey('uri')) {
        return musicData['play_url']['uri'].toString();
      } else if (musicData.containsKey('play_url') &&
          musicData['play_url'] is String) {
        return musicData['play_url'].toString();
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】提取音乐URL异常: $e');
    }
    return '';
  }

  // 提取URL列表
  List<String> _extractUrlList(Map<String, dynamic> data, String key) {
    try {
      if (data.containsKey(key) && data[key] is List) {
        return (data[key] as List).map((url) => url.toString()).toList();
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】提取URL列表异常: $e');
    }
    return [];
  }

  // 辅助函数：安全获取字符串值
  String _getStringValue(
    Map<String, dynamic> data,
    String key,
    String defaultValue,
  ) {
    try {
      final value = data[key];
      if (value == null) return defaultValue;
      return value.toString();
    } catch (e) {
      return defaultValue;
    }
  }

  // 辅助函数：安全获取整数值
  int _getIntValue(Map<String, dynamic> data, String key, int defaultValue) {
    try {
      final value = data[key];
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) {
        return int.tryParse(value) ?? defaultValue;
      }
      return defaultValue;
    } catch (e) {
      return defaultValue;
    }
  }

  void _initializeContent() {
    if (awemeDetail.value != null) {
      // 设置初始点赞状态
      isLiked.value = false; // 需要根据API返回值设置

      // 初始化视频
      final videoUrl = awemeDetail.value!.video.playAddr.urlList.first;
      if (videoUrl.isNotEmpty) {
        _initializeVideo(videoUrl);
      }
    }
  }

  // 初始化视频
  Future<void> _initializeVideo(String videoUrl) async {
    try {
      // 创建媒体播放器
      player = media_kit.Player();
      videoController = media_kit_video.VideoController(player!);

      // 打开视频URL
      await player!.open(media_kit.Media(videoUrl));

      // 设置视频循环播放
      await player!.setPlaylistMode(media_kit.PlaylistMode.loop);

      // 添加视频位置监听
      _positionSubscription = player!.stream.position.listen((position) {
        videoPosition.value = position;
      });

      // 获取视频时长
      player!.stream.duration.listen((duration) {
        videoDuration.value = duration;
      });

      // 监听播放状态
      player!.stream.playing.listen((playing) {
        isPlaying.value = playing;
      });

      // 监听播放器状态
      player!.stream.completed.listen((completed) {
        if (completed) {
          // 视频播放完成后的处理
          player!.seek(Duration.zero);
        }
      });

      isVideoInitialized.value = true;

      // 自动播放视频
      await player!.play();

      // 显示控制栏，然后自动隐藏
      showControls.value = true;
      _startHideControlsTimer();
    } catch (e) {
      LoggerUtil.e('视频初始化失败: $e');
      hasError.value = true;
      errorMessage.value = '视频加载失败: $e';
    }
  }

  /// 切换播放/暂停状态
  void togglePlayPause() {
    if (player == null) return;

    if (isPlaying.value) {
      player!.pause();
    } else {
      player!.play();
    }

    // 切换控制栏显示状态
    showControls.value = true;
    _startHideControlsTimer();
  }

  /// 跳转到指定时间
  void seekTo(Duration position) {
    if (player == null) return;
    player!.seek(position);
  }

  // 切换控制栏显示/隐藏
  void toggleControls() {
    showControls.value = !showControls.value;
    if (showControls.value && player != null && isPlaying.value) {
      _startHideControlsTimer();
    } else {
      _cancelHideControlsTimer();
    }
  }

  /// 开始计时器，用于自动隐藏控制栏
  void _startHideControlsTimer() {
    _cancelHideControlsTimer();
    _controlsTimer = Timer(const Duration(seconds: 3), () {
      showControls.value = false;
    });
  }

  /// 取消隐藏控制栏的计时器
  void _cancelHideControlsTimer() {
    _controlsTimer?.cancel();
    _controlsTimer = null;
  }

  // 切换全屏模式
  void toggleFullScreen() {
    if (isFullScreen.value) {
      exitFullScreen();
    } else {
      enterFullScreen();
    }
  }

  // 进入全屏模式
  void enterFullScreen() {
    isFullScreen.value = true;
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  // 退出全屏模式
  void exitFullScreen() {
    isFullScreen.value = false;
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  // 获取评论列表 - 删除此方法，使用上面的loadComments替代
  Future<void> fetchComments({bool isRefresh = false}) async {
    // 此方法已被新的loadComments方法替代
  }

  /// 发送评论
  Future<void> sendComment() async {
    if (commentText.value.isEmpty || isSendingComment.value) {
      return;
    }

    // 检查评论长度
    if (commentText.value.length < minCommentLength.value ||
        commentText.value.length > maxCommentLength.value) {
      Get.snackbar(
        '提示',
        '评论长度应在 ${minCommentLength.value} 到 ${maxCommentLength.value} 个字符之间',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    isSendingComment.value = true;

    try {
      LoggerUtil.i(
        '【DouyinContentDetail】开始发送评论, awemeId: ${awemeId.value}, text: ${commentText.value}, replyCommentId: ${replyCommentId.value}',
      );

      // 检查并设置当前账号
      _checkAndSetCurrentAccount();

      // 获取当前账号的Cookie
      final currentAccount = AccountPersistentService.to.currentAccount.value;
      if (currentAccount == null) {
        throw Exception('请先登录抖音账号');
      }
      
      // 是否是回复评论
      final bool isReply = replyCommentId.value.isNotEmpty;
      
      // 检查refreshToken是否存在
      if (currentAccount.refreshToken == null || currentAccount.refreshToken!.isEmpty) {
        // 尝试使用cookie代替
        if (currentAccount.cookie == null || currentAccount.cookie!.isEmpty) {
          throw Exception('抖音账号登录信息不完整，请重新登录');
        }
        
        LoggerUtil.i('【DouyinContentDetail】使用cookie代替refreshToken发送评论');
        
        final response = await _platformService.publishDouyinVideoComment(
          awemeId: awemeId.value,
          text: commentText.value,
          cookie: currentAccount.cookie!,
          replyId: isReply ? replyCommentId.value : "0",  // 如果是回复，传入评论ID
        );
        
        if (response.success) {
          LoggerUtil.i('【DouyinContentDetail】评论发送成功');
          
          // 清空评论框和回复ID
          commentController.clear();
          commentText.value = '';
          replyCommentId.value = '';
          
          // 重新加载评论
          comments.clear();
          commentCursor.value = '';
          loadComments();
          
          Get.snackbar(
            '成功',
            isReply ? '回复已发送' : '评论已发送',
            snackPosition: SnackPosition.BOTTOM,
          );
        } else {
          LoggerUtil.e('【DouyinContentDetail】评论发送失败: ${response.msg}');
          throw Exception(response.msg ?? '评论发送失败');
        }
      } else {
        // 使用refreshToken发送评论
        final response = await _platformService.publishDouyinVideoComment(
          awemeId: awemeId.value,
          text: commentText.value,
          cookie: currentAccount.refreshToken!,
          replyId: isReply ? replyCommentId.value : "0",  // 如果是回复，传入评论ID
        );

        if (response.success) {
          LoggerUtil.i('【DouyinContentDetail】评论发送成功');
          
          // 清空评论框和回复ID
          commentController.clear();
          commentText.value = '';
          replyCommentId.value = '';
          
          // 重新加载评论
          comments.clear();
          commentCursor.value = '';
          loadComments();
          
          Get.snackbar(
            '成功',
            isReply ? '回复已发送' : '评论已发送',
            snackPosition: SnackPosition.BOTTOM,
          );
        } else {
          LoggerUtil.e('【DouyinContentDetail】评论发送失败: ${response.msg}');
          throw Exception(response.msg ?? '评论发送失败');
        }
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】评论发送异常: $e');
      Get.snackbar(
        '错误',
        '评论发送失败: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isSendingComment.value = false;
    }
  }

  // 处理评论响应
  void _handleCommentResponse(BaseResponse<Map<String, dynamic>?> response) {
    // 清空输入框
    commentController.clear();
    commentText.value = '';
    commentFocusNode.unfocus();

    if (response.success && response.data != null) {
      LoggerUtil.i('【DouyinContentDetail】发送评论成功');

      // 处理返回的评论数据
      final commentData = response.data!['comment'];
      if (commentData != null) {
        // 将新评论添加到评论列表的开头
        comments.insert(0, commentData);
        comments.refresh();

        Get.snackbar(
          '发送成功',
          '评论已发布',
          snackPosition: SnackPosition.BOTTOM,
          duration: Duration(seconds: 2),
          backgroundColor: Colors.green.withOpacity(0.7),
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          '提示',
          '评论已提交，但未返回评论数据',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange.withOpacity(0.7),
          colorText: Colors.white,
        );
      }
    } else {
      LoggerUtil.e('【DouyinContentDetail】发送评论失败: ${response.msg}');
      Get.snackbar(
        '发送失败',
        response.msg ?? '未知错误',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    }
  }

  String getPublishTime() {
    if (awemeDetail.value != null) {
      final createTime = DateTime.fromMillisecondsSinceEpoch(
        awemeDetail.value!.createTime * 1000,
      );
      return formatPublishTime(createTime);
    }
    return '未知时间';
  }

  // 格式化发布时间
  String formatPublishTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    // 今年内的日期显示为"MM月DD日"
    if (dateTime.year == now.year) {
      return '${dateTime.month}月${dateTime.day}日';
    }
    // 不是今年的显示为"YYYY年MM月DD日"
    else {
      return '${dateTime.year}年${dateTime.month}月${dateTime.day}日';
    }
  }

  // 格式化计数
  String formatCount(String countStr) {
    try {
      final count = int.parse(countStr);
      if (count >= 10000) {
        return '${(count / 10000).toStringAsFixed(1)}万';
      }
      return countStr;
    } catch (e) {
      return countStr;
    }
  }

  /// 加载指定评论的二级评论
  ///
  /// [cid] 一级评论ID
  /// [cursor] 分页游标，默认为空
  /// [count] 每页数量，默认为10
  Future<void> loadReplyComments(
    String cid, {
    String cursor = '',
    int count = 10,
  }) async {
    if (cid.isEmpty) {
      LoggerUtil.e('【DouyinContentDetail】评论ID为空，无法加载二级评论');
      return;
    }

    // 查找对应的一级评论
    final commentIndex = comments.indexWhere(
      (comment) => comment['cid'] == cid,
    );
    if (commentIndex == -1) {
      LoggerUtil.e('【DouyinContentDetail】未找到对应的一级评论: $cid');
      return;
    }

    try {
      LoggerUtil.i(
        '【DouyinContentDetail】开始获取二级评论, awemeId: ${awemeId.value}, cid: $cid, cursor: $cursor',
      );

      // 获取当前评论对象的副本
      final comment = Map<String, dynamic>.from(comments[commentIndex]);

      // 标记为加载中
      final loadingComment = {...comment};
      loadingComment['isLoadingReplies'] = true;
      comments[commentIndex] = loadingComment;
      comments.refresh();

      // 检查并设置当前账号
      _checkAndSetCurrentAccount();

      // 获取当前账号信息
      final currentAccount = AccountPersistentService.to.currentAccount.value;
      String? cookie;
      
      if (currentAccount != null) {
        // 优先使用refreshToken，如果没有则使用cookie
        if (currentAccount.refreshToken != null && currentAccount.refreshToken!.isNotEmpty) {
          cookie = currentAccount.refreshToken;
          LoggerUtil.i('【DouyinContentDetail】使用refreshToken获取二级评论');
        } else if (currentAccount.cookie != null && currentAccount.cookie!.isNotEmpty) {
          cookie = currentAccount.cookie;
          LoggerUtil.i('【DouyinContentDetail】使用cookie获取二级评论');
        }
      }

      final response = await _platformService.getDouyinVideoReplyComments(
        awemeId: awemeId.value,
        commentId: cid,
        cursor: cursor,
        count: count,
        cookie: cookie, // 添加cookie参数
      );

      if (response.success && response.data != null) {
        LoggerUtil.i('【DouyinContentDetail】获取二级评论成功');

        final Map<String, dynamic> dataMap =
            response.data as Map<String, dynamic>;

        // 再次获取评论（因为可能在请求过程中发生了变化）
        final updatedCommentIndex = comments.indexWhere(
          (comment) => comment['cid'] == cid,
        );
        if (updatedCommentIndex == -1) {
          LoggerUtil.e('【DouyinContentDetail】二级评论加载完成后未找到对应的一级评论: $cid');
          return;
        }

        final updatedComment = Map<String, dynamic>.from(
          comments[updatedCommentIndex],
        );

        // 解析二级评论数据
        if (dataMap.containsKey('comments') && dataMap['comments'] is List) {
          final repliesList = dataMap['comments'] as List;

          // 如果是第一页，直接替换reply_comment字段
          if (cursor.isEmpty || cursor == '0') {
            updatedComment['reply_comment'] = repliesList;
          } else {
            // 否则追加到现有列表
            final existingReplies =
                updatedComment['reply_comment'] as List? ?? [];
            updatedComment['reply_comment'] = [
              ...existingReplies,
              ...repliesList,
            ];
          }

          // 更新游标和是否有更多评论
          updatedComment['reply_cursor'] = dataMap['cursor']?.toString() ?? '';
          updatedComment['has_more_replies'] = dataMap['has_more'] == 1;

          // 更新评论总数
          if (dataMap.containsKey('total')) {
            updatedComment['reply_comment_total'] = dataMap['total'];
          }

          LoggerUtil.i(
            '【DouyinContentDetail】二级评论加载成功，数量: ${repliesList.length}',
          );
        } else {
          LoggerUtil.e('【DouyinContentDetail】二级评论数据格式不正确');
          // 如果没有数据，设置为空列表
          updatedComment['reply_comment'] =
              updatedComment['reply_comment'] ?? [];
          updatedComment['has_more_replies'] = false;
        }

        // 取消加载状态
        updatedComment['isLoadingReplies'] = false;

        // 更新评论对象
        comments[updatedCommentIndex] = updatedComment;
        comments.refresh();
      } else {
        LoggerUtil.e('【DouyinContentDetail】获取二级评论失败: ${response.msg}');

        // 再次获取评论以取消加载状态
        final updatedCommentIndex = comments.indexWhere(
          (comment) => comment['cid'] == cid,
        );
        if (updatedCommentIndex != -1) {
          final updatedComment = Map<String, dynamic>.from(
            comments[updatedCommentIndex],
          );
          updatedComment['isLoadingReplies'] = false;
          comments[updatedCommentIndex] = updatedComment;
          comments.refresh();
        }
      }
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】获取二级评论异常: $e');

      // 发生异常时，取消加载状态
      final updatedCommentIndex = comments.indexWhere(
        (comment) => comment['cid'] == cid,
      );
      if (updatedCommentIndex != -1) {
        final updatedComment = Map<String, dynamic>.from(
          comments[updatedCommentIndex],
        );
        updatedComment['isLoadingReplies'] = false;
        comments[updatedCommentIndex] = updatedComment;
        comments.refresh();
      }
    }
  }

  void _initPlayer() {
    try {
      LoggerUtil.i('【DouyinContentDetail】开始初始化视频播放器');

      // 销毁之前的播放器（如果存在）
      player?.dispose();

      // 创建新的播放器实例，添加更多配置选项
      final newPlayer = media_kit.Player(
        configuration: const media_kit.PlayerConfiguration(
          logLevel: media_kit.MPVLogLevel.info,
          // media_kit 1.1.10版本中PlayerConfiguration不支持vo和additionalMpvFlags属性
        ),
      );
      player = newPlayer;

      LoggerUtil.i('【DouyinContentDetail】创建视频控制器');
      videoController = media_kit_video.VideoController(
        newPlayer,
        configuration: const media_kit_video.VideoControllerConfiguration(
          enableHardwareAcceleration: true,
          androidAttachSurfaceAfterVideoParameters: false, // 尝试修改附加Surface的时机
        ),
      );

      // 播放视频
      final videoUrl = _getVideoUrl();
      if (videoUrl != null && videoUrl.isNotEmpty) {
        LoggerUtil.i('【DouyinContentDetail】准备打开视频: $videoUrl');

        // 设置播放器事件监听
        newPlayer.stream.error.listen((error) {
          LoggerUtil.e('【DouyinContentDetail】播放器错误: $error');
        });

        newPlayer.stream.log.listen((log) {
          LoggerUtil.i('【DouyinContentDetail】播放器日志: $log');
        });

        newPlayer.stream.videoParams.listen((params) {
          LoggerUtil.i('【DouyinContentDetail】视频参数: $params');
          if (params.w != null && params.h != null) {
            LoggerUtil.i(
              '【DouyinContentDetail】视频尺寸: ${params.w} x ${params.h}',
            );
          }
        });

        // 添加播放状态监听
        newPlayer.stream.playing.listen((playing) {
          LoggerUtil.i('【DouyinContentDetail】播放状态: $playing');
          isPlaying.value = playing;
        });

        newPlayer.stream.duration.listen((duration) {
          LoggerUtil.i('【DouyinContentDetail】视频时长: $duration');
          videoDuration.value = duration;
        });

        newPlayer.stream.position.listen((position) {
          videoPosition.value = position;
        });

        newPlayer.stream.width.listen((width) {
          LoggerUtil.i('【DouyinContentDetail】视频宽度: $width');
        });

        newPlayer.stream.height.listen((height) {
          LoggerUtil.i('【DouyinContentDetail】视频高度: $height');
        });

        // 打开视频
        newPlayer.open(media_kit.Media(videoUrl), play: true);
        LoggerUtil.i('【DouyinContentDetail】视频已开始加载');
      } else {
        LoggerUtil.w('【DouyinContentDetail】视频播放地址为空，无法初始化播放器');
      }
    } catch (e, stack) {
      LoggerUtil.e('【DouyinContentDetail】初始化播放器失败: $e', e, stack);
    }
  }

  String? _getVideoUrl() {
    try {
      final detail = awemeDetail.value;
      if (detail == null) {
        LoggerUtil.w('【DouyinContentDetail】awemeDetail为空，无法获取视频地址');
        return null;
      }

      // 从play_addr字段获取视频URL
      if (detail.video != null &&
          detail.video!.playAddr != null &&
          detail.video!.playAddr!.urlList != null &&
          detail.video!.playAddr!.urlList!.isNotEmpty) {
        // 获取url_list数组中的所有URL，打印出来
        final urlList = detail.video!.playAddr!.urlList!;
        LoggerUtil.i('【DouyinContentDetail】视频URL列表: $urlList');

        // 使用第一个URL，因为它看起来是CDN链接，可能更可靠
        final videoUrl = urlList.first;
        LoggerUtil.i('【DouyinContentDetail】选择的视频URL: $videoUrl');

        return videoUrl;
      }

      LoggerUtil.w('【DouyinContentDetail】未找到有效的视频播放地址');
      return null;
    } catch (e) {
      LoggerUtil.e('【DouyinContentDetail】获取视频地址时出错: $e');
      return null;
    }
  }

  /// 跳转到全屏播放
  void goToFullScreen(BuildContext context) {
    final controller = videoController;
    if (controller != null) {
      // 不使用 value.handle，因为 VideoController 没有 value 属性
      // 改为检查 controller 是否已初始化
      // 这里实现全屏播放的逻辑
      // 例如使用 Navigator.push 跳转到全屏页面
      LoggerUtil.i('【DouyinContentDetail】全屏播放功能待实现');
    } else {
      LoggerUtil.w('【DouyinContentDetail】视频控制器未初始化，无法全屏播放');
    }
  }

  // 设置评论时间范围
  void setCommentTimeRange(DateTime? start, DateTime? end) {
    commentStartTime.value = start;
    commentEndTime.value = end;
  }

  // 设置点赞数范围
  void setLikesRange(int min, int max) {
    minLikes.value = min;
    maxLikes.value = max;
  }

  // 添加关键词
  void addKeyword(String keyword) {
    if (keyword.isNotEmpty && !commentKeywords.contains(keyword)) {
      commentKeywords.add(keyword);
    }
  }

  // 移除关键词
  void removeKeyword(String keyword) {
    commentKeywords.remove(keyword);
  }

  // 切换表情选择器显示状态
  void toggleEmojiPicker() {
    showEmojiPicker.value = !showEmojiPicker.value;
    if (showEmojiPicker.value) {
      commentFocusNode.unfocus();
    } else {
      commentFocusNode.requestFocus();
    }
  }

  // 插入表情
  void insertEmoji(String emoji) {
    final text = commentController.text;
    final selection = commentController.selection;
    final newText = text.replaceRange(selection.start, selection.end, emoji);
    commentController.text = newText;
    commentController.selection = TextSelection.collapsed(
      offset: selection.baseOffset + emoji.length,
    );
    commentText.value = newText;
  }

  // 隐藏键盘
  void hideKeyboard() {
    commentFocusNode.unfocus();
  }

  // 获取评论提示文本
  String getCommentHintText() {
    if (replyCommentId.value.isNotEmpty) {
      // 查找评论以获取用户昵称
      final commentIndex = comments.indexWhere((comment) => comment['cid'] == replyCommentId.value);
      if (commentIndex != -1) {
        final nickname = comments[commentIndex]['user']?['nickname'] ?? '用户';
        return '回复 $nickname';
      }
    }
    return '说点什么...';
  }

  // 清除回复目标
  void clearReplyTarget() {
    replyCommentId.value = '';
    commentController.clear();
    commentText.value = '';
    showEmojiPicker.value = false;
  }

  // 当前正在回复的评论ID
  // final replyCommentId = RxString('');
  
  /// 准备回复评论
  void prepareReplyComment(String commentId) {
    replyCommentId.value = commentId;
    
    // 找到评论对象，获取用户昵称
    final comment = comments.firstWhere(
      (c) => c['cid']?.toString() == commentId,
      orElse: () => <String, dynamic>{},
    );
    
    final user = comment['user'] as Map<String, dynamic>?;
    final nickname = user?['nickname'] ?? '用户';
    
    // 聚焦评论输入框
    commentFocusNode.requestFocus();
    
    // 设置提示
    Get.snackbar(
      '回复评论',
      '正在回复 $nickname 的评论',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 2),
      backgroundColor: Colors.blue.withOpacity(0.7),
      colorText: Colors.white,
    );
  }
}


