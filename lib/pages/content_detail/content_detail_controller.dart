import 'dart:async';

import 'package:aitoearn_app/api/platform_service.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/pages/content_detail/widgets/xhs_quick_reply_sheet.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_note_detail_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/xhs_service.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:media_kit/media_kit.dart' as media_kit;
import 'package:media_kit_video/media_kit_video.dart' as media_kit_video;

class ContentDetailController extends GetxController {
  final _platformService = PlatformService();
  final isLoading = true.obs;
  final hasError = false.obs;
  final errorMessage = ''.obs;

  // 提供获取平台服务的公共方法
  PlatformService getPlatformService() {
    return _platformService;
  }

  final noteFromList = Rx<XhsNoteItem?>(null);
  final noteDetail = Rx<NoteDetailItem?>(null);

  // 点赞相关
  final isLiked = false.obs;
  final isLiking = false.obs;

  // 评论相关
  final comments = <XhsComment>[].obs; // 评论列表
  final commentCursor = ''.obs; // 评论列表游标
  final hasMoreComments = false.obs; // 是否有更多评论
  final isLoadingComments = false.obs; // 是否正在加载评论
  final commentXsecToken = ''.obs;
  final commentText = ''.obs;
  final replyToComment = Rx<XhsComment?>(null);
  final replyToSubComment = Rx<XhsSubComment?>(null);
  final showEmojiPicker = false.obs;
  
  // 评论选择模式
  final isCommentSelectionMode = false.obs;
  final selectedComments = <String>[].obs; // 存储选中的评论ID

  // 评论筛选相关
  final commentSortType =
      "latest"
          .obs; // 排序方式：latest(最新)、earliest(最早)、most_liked(最多点赞)、least_liked(最少点赞)
  final showUnrepliedOnly = false.obs; // 只看未回复
  final commentStartTime = Rx<DateTime?>(null); // 评论开始时间
  final commentEndTime = Rx<DateTime?>(null); // 评论结束时间
  final minLikes = 0.obs; // 最小点赞数
  final maxLikes = 0.obs; // 最大点赞数
  final commentKeywords = <String>[].obs; // 关键词列表
  final filteredComments = <XhsComment>[].obs; // 筛选后的评论列表
  final isFiltered = false.obs; // 是否已筛选

  // 视频相关
  media_kit.Player? player;
  media_kit_video.VideoController? videoController;
  final isVideoInitialized = false.obs;
  final isPlaying = false.obs;
  final videoPosition = Duration.zero.obs;
  final videoDuration = Duration.zero.obs;
  final showControls = false.obs;
  final isFullScreen = false.obs;
  Timer? _controlsTimer;
  StreamSubscription? _positionSubscription;

  final currentPage = 0.obs;

  // 评论输入框控制器
  late TextEditingController commentController;
  final FocusNode commentFocusNode = FocusNode();

  @override
  void onInit() {
    super.onInit();
    commentController = TextEditingController();
    commentFocusNode.addListener(() {
      if (commentFocusNode.hasFocus && showEmojiPicker.value) {
        showEmojiPicker.value = false;
      }
    });

    // 处理传递过来的参数
    if (Get.arguments is Map) {
      final args = Get.arguments as Map;
      if (args.containsKey('noteItem')) {
        noteFromList.value = args['noteItem'];
        fetchNoteDetail();
      } else {
        isLoading.value = false;
        hasError.value = true;
        errorMessage.value = 'content.error.invalid_note'.tr;
      }
    } else if (Get.arguments is XhsNoteItem) {
      // 兼容旧的参数传递方式
      noteFromList.value = Get.arguments;
      fetchNoteDetail();
    } else {
      isLoading.value = false;
      hasError.value = true;
      errorMessage.value = 'content.error.invalid_note'.tr;
    }
  }

  @override
  void onClose() {
    // 确保退出全屏
    if (isFullScreen.value) {
      exitFullScreen();
    }

    // 停止视频位置监听
    _positionSubscription?.cancel();

    // 取消控制栏隐藏计时器
    _cancelHideControlsTimer();

    // 恢复系统UI样式
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    commentController.dispose();
    commentFocusNode.dispose();
    player?.dispose();
    super.onClose();
  }

  Future<void> fetchNoteDetail() async {
    isLoading.value = true;
    hasError.value = false;

    try {
      print('【ContentDetail】开始获取笔记详情');

      // 尝试获取当前选择的账号，如果没有则从账号列表中找一个小红书账号
      String? accountId;

      // 检查是否有从互动页面传递过来的账号ID
      final Map<String, dynamic>? arguments =
          Get.arguments is Map ? Get.arguments : null;
      if (arguments != null && arguments.containsKey('accountId')) {
        accountId = arguments['accountId'];
        print('【ContentDetail】从参数中获取到账号ID: $accountId');
      }

      // 如果没有从参数获取到账号ID，则从账号列表中找一个小红书账号
      if (accountId == null || accountId.isEmpty) {
        final accounts = AccountPersistentService.to.accounts;
        print('【ContentDetail】从账号列表中查找小红书账号，账号总数: ${accounts.length}');

        // 打印所有账号信息，便于调试
        for (var acc in accounts) {
          print(
            '【ContentDetail】账号: ${acc.nickname}, ID: ${acc.id}, UID: ${acc.uid}, 类型: ${acc.type}',
          );
        }

        final xhsAccount = accounts.firstWhereOrNull(
          (acc) => acc.type == PlatTypeEnum.xhs,
        );

        if (xhsAccount != null) {
          accountId = xhsAccount.id ?? xhsAccount.uid;
          print(
            '【ContentDetail】找到小红书账号: ${xhsAccount.nickname}, ID: $accountId',
          );
        } else {
          throw Exception('content.error.no_xhs_account'.tr);
        }
      }

      print('【ContentDetail】使用账号ID: $accountId 获取笔记详情');
      final response = await _platformService.getXhsNoteDetail(
        accountId: accountId,
        noteId: noteFromList.value!.id,
        xsecToken: noteFromList.value!.xsecToken,
      );

      if (response.isSuccess && response.data != null) {
        print('【ContentDetail】获取笔记详情成功');
        noteDetail.value = response.data;
        _initializeContent();
        // 获取详情成功后加载评论
        fetchComments();
      } else {
        print('【ContentDetail】获取笔记详情失败: ${response.msg}');
        throw Exception(response.msg ?? 'content.error.get_detail_failed'.tr);
      }
    } catch (e) {
      print('【ContentDetail】获取笔记详情异常: $e');
      hasError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  void _initializeContent() {
    final noteCard = noteDetail.value?.noteCard;
    if (noteCard != null) {
      // 设置初始点赞状态
      isLiked.value = noteCard.liked || noteCard.interactInfo.liked;

      if (noteCard.type.contains('video')) {
        final videoUrl = noteCard.video?.media.stream.url;
        if (videoUrl != null && videoUrl.isNotEmpty) {
          _initializeVideo(videoUrl);
        }
      }
    }
  }

  // 初始化视频
  Future<void> _initializeVideo(String videoUrl) async {
    try {
      // 创建媒体播放器
      player = media_kit.Player();
      videoController = media_kit_video.VideoController(player!);

      // 打开视频URL
      await player!.open(media_kit.Media(videoUrl));

      // 设置视频循环播放
      await player!.setPlaylistMode(media_kit.PlaylistMode.loop);

      // 添加视频位置监听
      _positionSubscription = player!.stream.position.listen((position) {
        videoPosition.value = position;
      });

      // 获取视频时长
      player!.stream.duration.listen((duration) {
        videoDuration.value = duration;
      });

      // 监听播放状态
      player!.stream.playing.listen((playing) {
        isPlaying.value = playing;
      });

      // 监听播放器状态
      player!.stream.completed.listen((completed) {
        if (completed) {
          // 视频播放完成后的处理
          player!.seek(Duration.zero);
        }
      });

      isVideoInitialized.value = true;

      // 自动播放视频
      await player!.play();

      // 显示控制栏，然后自动隐藏
      showControls.value = true;
      _startHideControlsTimer();
    } catch (e) {
      print('视频初始化失败: $e');
      hasError.value = true;
      errorMessage.value = '视频加载失败: $e';
    }
  }

  // 切换播放/暂停
  void togglePlay() {
    if (player == null) return;

    if (isPlaying.value) {
      player!.pause();
      // 暂停时保持控制栏显示
      showControls.value = true;
      _cancelHideControlsTimer();
    } else {
      player!.play();
      // 播放时短暂显示控制栏，然后自动隐藏
      showControls.value = true;
      _startHideControlsTimer();
    }
  }

  // 设置视频位置
  void seekTo(Duration position) {
    if (player == null) return;
    player!.seek(position);
  }

  // 切换控制栏显示/隐藏
  void toggleControls() {
    showControls.value = !showControls.value;
    if (showControls.value && player != null && isPlaying.value) {
      _startHideControlsTimer();
    } else {
      _cancelHideControlsTimer();
    }
  }

  // 开始隐藏控制栏的计时器
  void _startHideControlsTimer() {
    _cancelHideControlsTimer();
    _controlsTimer = Timer(const Duration(seconds: 3), () {
      showControls.value = false;
    });
  }

  // 取消隐藏控制栏的计时器
  void _cancelHideControlsTimer() {
    _controlsTimer?.cancel();
    _controlsTimer = null;
  }

  // 获取评论列表
  Future<void> fetchComments({bool isRefresh = false}) async {
    if (isLoadingComments.value) return;

    if (isRefresh) {
      commentCursor.value = '';
      comments.clear();
    }

    if (!hasMoreComments.value && !isRefresh && comments.isNotEmpty) return;

    isLoadingComments.value = true;

    try {
      final account = AccountPersistentService.to.accounts.firstWhere(
        (acc) => acc.type.toString().contains('xhs'),
        orElse: () => throw Exception('content.error.no_xhs_account'.tr),
      );

      final xhsService = XhsService(account: account);
      final result = await xhsService.getNoteCommentsInstance(
        noteId: noteFromList.value!.id,
        xsecToken: noteFromList.value!.xsecToken,
        cursor: commentCursor.value,
        cookieStrl: account.refreshToken ?? '',
      );

      if (result['success'] == true && result['data'] != null) {
        final data = result['data'];

        // 更新游标
        commentCursor.value = data['cursor'] ?? '';

        // 更新是否有更多评论
        hasMoreComments.value = data['has_more'] ?? false;

        // 添加评论到列表
        if (data['comments'] != null) {
          final newComments = (data['comments'] as List).cast<XhsComment>();
          comments.addAll(newComments);
        }
      } else {
        LoggerUtil.e('获取评论失败: ${result['message']}');
      }
    } catch (e) {
      LoggerUtil.e('获取评论异常: $e');
    } finally {
      isLoadingComments.value = false;
    }
  }

  // 加载更多评论
  void loadMoreComments() {
    if (!isLoadingComments.value && hasMoreComments.value) {
      fetchComments();
    }
  }

  // 切换评论排序方式
  void changeCommentSortType(String type) {
    if (commentSortType.value != type) {
      commentSortType.value = type;
      // 重新获取评论
      comments.clear();
      commentCursor.value = '';
      fetchComments(isRefresh: true);
    }
  }

  // 切换只看未回复
  void toggleUnrepliedOnly() {
    showUnrepliedOnly.value = !showUnrepliedOnly.value;
    // 重新获取评论
    comments.clear();
    commentCursor.value = '';
    fetchComments(isRefresh: true);
  }

  // 设置评论时间范围
  void setCommentTimeRange(DateTime? start, DateTime? end) {
    commentStartTime.value = start;
    commentEndTime.value = end;
  }

  // 设置点赞数范围
  void setLikesRange(int min, int max) {
    minLikes.value = min;
    maxLikes.value = max;
  }

  // 添加关键词
  void addKeyword(String keyword) {
    if (keyword.isNotEmpty && !commentKeywords.contains(keyword)) {
      commentKeywords.add(keyword);
    }
  }

  // 移除关键词
  void removeKeyword(String keyword) {
    commentKeywords.remove(keyword);
  }

  // 清空所有筛选条件（在筛选弹窗中使用，会关闭弹窗）
  void resetCommentFilters() {
    _clearFilters();
    Get.back(); // 关闭筛选弹窗
  }
  
  // 清空所有筛选条件（不关闭弹窗，不导航返回）
  void clearFilters() {
    _clearFilters();
  }
  
  // 内部方法：清空所有筛选条件
  void _clearFilters() {
    commentSortType.value = "latest";
    showUnrepliedOnly.value = false;
    commentStartTime.value = null;
    commentEndTime.value = null;
    minLikes.value = 0;
    maxLikes.value = 0;
    commentKeywords.clear();
    isFiltered.value = false;
    filteredComments.clear();
  }

  // 应用筛选条件
  void applyCommentFilters() {
    // 应用本地筛选
    _filterComments();
    Get.back(); // 关闭筛选弹窗
  }

  // 本地筛选评论
  void _filterComments() {
    // 标记为已筛选状态
    isFiltered.value = true;
    
    // 清空之前筛选的结果
    filteredComments.clear();
    
    // 复制原始评论列表进行筛选
    List<XhsComment> filtered = List.from(comments);
    
    // 打印筛选条件
    LoggerUtil.i('【ContentDetail】应用评论筛选: 排序类型=${commentSortType.value}, 只看未回复=${showUnrepliedOnly.value}');
    LoggerUtil.i('【ContentDetail】筛选前评论数量: ${filtered.length}');
    
    // 1. 按时间范围筛选
    if (commentStartTime.value != null || commentEndTime.value != null) {
      filtered = filtered.where((comment) {
        final commentTime = DateTime.fromMillisecondsSinceEpoch(comment.createTime * 1000);
        
        bool matchesStartTime = true;
        if (commentStartTime.value != null) {
          matchesStartTime = commentTime.isAfter(commentStartTime.value!) || 
                            commentTime.isAtSameMomentAs(commentStartTime.value!);
        }
        
        bool matchesEndTime = true;
        if (commentEndTime.value != null) {
          matchesEndTime = commentTime.isBefore(commentEndTime.value!) || 
                          commentTime.isAtSameMomentAs(commentEndTime.value!);
        }
        
        return matchesStartTime && matchesEndTime;
      }).toList();
    }
    
    // 2. 按点赞数筛选
    if (minLikes.value > 0 || maxLikes.value > 0) {
      filtered = filtered.where((comment) {
        final likes = int.tryParse(comment.likeCount) ?? 0;
        
        bool matchesMinLikes = true;
        if (minLikes.value > 0) {
          matchesMinLikes = likes >= minLikes.value;
        }
        
        bool matchesMaxLikes = true;
        if (maxLikes.value > 0) {
          matchesMaxLikes = likes <= maxLikes.value;
        }
        
        return matchesMinLikes && matchesMaxLikes;
      }).toList();
    }
    
    // 3. 按关键词筛选
    if (commentKeywords.isNotEmpty) {
      filtered = filtered.where((comment) {
        // 如果任何一个关键词匹配，则保留该评论
        return commentKeywords.any((keyword) => 
          comment.content.toLowerCase().contains(keyword.toLowerCase()));
      }).toList();
    }
    
    // 4. 只看未回复
    if (showUnrepliedOnly.value) {
      filtered = filtered.where((comment) {
        // 子评论数为0表示未回复
        final subCommentCount = int.tryParse(comment.subCommentCount) ?? 0;
        return subCommentCount == 0;
      }).toList();
    }
    
    // 5. 排序
    switch (commentSortType.value) {
      case 'latest': // 最新评论
        filtered.sort((a, b) => b.createTime.compareTo(a.createTime));
        break;
      case 'earliest': // 最早评论
        filtered.sort((a, b) => a.createTime.compareTo(b.createTime));
        break;
      case 'most_liked': // 最多点赞
        filtered.sort((a, b) {
          final aLikes = int.tryParse(a.likeCount) ?? 0;
          final bLikes = int.tryParse(b.likeCount) ?? 0;
          return bLikes.compareTo(aLikes);
        });
        break;
      case 'least_liked': // 最少点赞
        filtered.sort((a, b) {
          final aLikes = int.tryParse(a.likeCount) ?? 0;
          final bLikes = int.tryParse(b.likeCount) ?? 0;
          return aLikes.compareTo(bLikes);
        });
        break;
    }
    
    // 更新筛选后的评论列表
    filteredComments.addAll(filtered);
    LoggerUtil.i('【ContentDetail】筛选后评论数量: ${filteredComments.length}');
  }

  void onPageChanged(int index) {
    currentPage.value = index;
  }

  String getPublishTime() {
    // 尝试从笔记详情中获取发布时间
    final noteCard = noteDetail.value?.noteCard;
    if (noteCard != null) {
      // 优先使用最后更新时间
      if (noteCard.lastUpdateTime != null) {
        final updateTime = DateTime.fromMillisecondsSinceEpoch(
          noteCard.lastUpdateTime! * 1000,
        );
        return formatPublishTime(updateTime);
      }

      // 其次使用API返回的时间字段
      if (noteCard.timeStr != null && noteCard.timeStr!.isNotEmpty) {
        return noteCard.timeStr!;
      }

      // 如果API没有返回时间，则使用模拟时间
      final random = DateTime.now().millisecondsSinceEpoch % 10;
      final days = random + 1; // 1-10天的随机值

      final publishTime = DateTime.now().subtract(Duration(days: days));
      return formatPublishTime(publishTime);
    }
    return 'content.unknown_time'.tr;
  }

  // 格式化发布时间
  String formatPublishTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    // 今年内的日期显示为"MM月DD日"
    if (dateTime.year == now.year) {
      return 'content.date.month_day'.trParams({
        'month': dateTime.month.toString(),
        'day': dateTime.day.toString()
      });
    }
    // 不是今年的显示为"YYYY年MM月DD日"
    else {
      return 'content.date.year_month_day'.trParams({
        'year': dateTime.year.toString(),
        'month': dateTime.month.toString(),
        'day': dateTime.day.toString()
      });
    }
  }

  // 格式化计数
  String formatCount(String countStr) {
    try {
      final count = int.parse(countStr);
      if (count >= 10000) {
        return 'content.count.wan'.trParams({
          'value': (count / 10000).toStringAsFixed(1)
        });
      }
      return countStr;
    } catch (e) {
      return countStr;
    }
  }

  // 格式化时间
  String formatCommentTime(int timestamp) {
    final now = DateTime.now();
    final commentTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final difference = now.difference(commentTime);

    if (difference.inDays > 365) {
      return 'content.time.years_ago'.trParams({
        'count': (difference.inDays / 365).floor().toString()
      });
    } else if (difference.inDays > 30) {
      return 'content.time.months_ago'.trParams({
        'count': (difference.inDays / 30).floor().toString()
      });
    } else if (difference.inDays > 0) {
      return 'content.time.days_ago'.trParams({
        'count': difference.inDays.toString()
      });
    } else if (difference.inHours > 0) {
      return 'content.time.hours_ago'.trParams({
        'count': difference.inHours.toString()
      });
    } else if (difference.inMinutes > 0) {
      return 'content.time.minutes_ago'.trParams({
        'count': difference.inMinutes.toString()
      });
    } else {
      return 'content.time.just_now'.tr;
    }
  }

  // 进入全屏模式
  void enterFullScreen() {
    if (isFullScreen.value) return;

    // 设置横屏方向
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // 隐藏状态栏和导航栏
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

    isFullScreen.value = true;
  }

  // 退出全屏模式
  void exitFullScreen() {
    if (!isFullScreen.value) return;

    // 恢复竖屏方向
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    // 恢复状态栏和导航栏
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: SystemUiOverlay.values,
    );

    isFullScreen.value = false;
  }

  // 切换全屏模式
  void toggleFullScreen() {
    if (isFullScreen.value) {
      exitFullScreen();
    } else {
      enterFullScreen();
    }
  }

  void setReplyTarget({XhsComment? comment, XhsSubComment? subComment}) {
    replyToComment.value = comment;
    replyToSubComment.value = subComment;
  }

  void clearReplyTarget() {
    replyToComment.value = null;
    replyToSubComment.value = null;
    commentController.clear();
    commentText.value = '';
    showEmojiPicker.value = false;
  }

  String getCommentHintText() {
    if (replyToSubComment.value != null) {
      return 'content.reply_to'.trParams({
        'user': replyToSubComment.value!.userInfo.nickname
      });
    } else if (replyToComment.value != null) {
      return 'content.reply_to'.trParams({
        'user': replyToComment.value!.userInfo.nickname
      });
    } else {
      return 'content.leave_comment'.tr;
    }
  }

  Future<void> sendComment() async {
    if (commentText.value.isEmpty) return;

    try {
      final account = AccountPersistentService.to.accounts.firstWhere(
        (acc) => acc.type.toString().contains('xhs'),
        orElse: () => throw Exception('未找到小红书账号'),
      );

      // 获取XhsService实例
      final xhsService = XhsService(account: account);

      // 准备请求参数
      String? rootCommentId;
      String? targetCommentId;

      // 如果是回复评论
      if (replyToComment.value != null) {
        rootCommentId = replyToComment.value!.id;

        // 如果是回复子评论
        if (replyToSubComment.value != null) {
          targetCommentId = replyToSubComment.value!.id;
        }
      }

      // 发送评论
      final response = await xhsService.postCommentInstance(
        noteId: noteFromList.value!.id,
        content: commentText.value,
        rootCommentId: rootCommentId,
        targetCommentId: targetCommentId,
      );

      // 检查响应状态
      if (response['success'] == true) {
        // 评论发送成功
        commentText.value = ''; // 清空评论输入框
        hideKeyboard(); // 隐藏键盘

        // 关闭评论输入框和表情选择器
        showEmojiPicker.value = false;
        clearReplyTarget();

        // 关闭底部弹出框
        Get.back();

        // 刷新评论列表
        await fetchComments(isRefresh: true);

        // 显示成功提示
        showToast('content.comment.success'.tr);
      } else {
        // 评论发送失败
        showToast('content.comment.failed'.trParams({'message': response['message'] ?? ''}));
      }
    } catch (e) {
      LoggerUtil.e('发送评论异常: $e');
      showToast('content.comment.failed'.trParams({'message': e.toString()}));
    }
  }

  void toggleEmojiPicker() {
    if (commentFocusNode.hasFocus) {
      commentFocusNode.unfocus();
      Future.delayed(const Duration(milliseconds: 200), () {
        showEmojiPicker.value = !showEmojiPicker.value;
      });
    } else {
      showEmojiPicker.value = !showEmojiPicker.value;
    }
  }

  // 点赞评论
  Future<void> likeComment(XhsComment comment) async {
    try {
      // 检查是否有从互动页面传递过来的账号ID
      String? accountId;
      final Map<String, dynamic>? arguments = Get.arguments is Map ? Get.arguments : null;
      
      if (arguments != null && arguments.containsKey('accountId')) {
        accountId = arguments['accountId'];
        print('【点赞评论】从参数中获取到账号ID: $accountId');
      }
      
      // 如果没有从参数获取到账号ID，则从账号列表中找一个小红书账号
      if (accountId == null || accountId.isEmpty) {
        final accounts = AccountPersistentService.to.accounts;
        final xhsAccount = accounts.firstWhereOrNull(
          (acc) => acc.type == PlatTypeEnum.xhs,
        );

        if (xhsAccount != null) {
          accountId = xhsAccount.id ?? xhsAccount.uid;
          print('【点赞评论】找到小红书账号: ${xhsAccount.nickname}, ID: $accountId');
        } else {
          throw Exception('未找到小红书账号');
        }
      }
      
      // 根据当前评论的点赞状态决定是点赞还是取消点赞
      final response = comment.liked
          ? await _platformService.dislikeXhsComment(
              accountId: accountId,
              noteId: noteFromList.value!.id,
              commentId: comment.id,
            )
          : await _platformService.likeXhsComment(
              accountId: accountId,
              noteId: noteFromList.value!.id,
              commentId: comment.id,
            );
      
      if (response.isSuccess) {
        // 更新评论的点赞状态
        final index = comments.indexWhere((c) => c.id == comment.id);
        if (index != -1) {
          final updatedComment = XhsComment(
            id: comment.id,
            content: comment.content,
            atUsers: comment.atUsers,
            createTime: comment.createTime,
            subCommentHasMore: comment.subCommentHasMore,
            status: comment.status,
            liked: !comment.liked, // 反转点赞状态
            showTags: comment.showTags,
            subComments: comment.subComments,
            noteId: comment.noteId,
            ipLocation: comment.ipLocation,
            // 更新点赞数量
            likeCount: comment.liked
                ? (int.parse(comment.likeCount) - 1).toString() // 取消点赞减1
                : (int.parse(comment.likeCount) + 1).toString(), // 点赞加1
            userInfo: comment.userInfo,
            subCommentCount: comment.subCommentCount,
            subCommentCursor: comment.subCommentCursor,
          );
          
          comments[index] = updatedComment;
        }
        
        // 显示操作成功提示
        showToast(comment.liked ? 'content.unlike.success'.tr : 'content.like.success'.tr);
      } else {
        // 显示错误提示
        showToast('content.operation.failed'.trParams({'message': response.msg ?? ''}));
      }
    } catch (e) {
      LoggerUtil.e('点赞评论异常: $e');
      showToast('content.operation.failed'.trParams({'message': e.toString()}));
    }
  }

  // 点赞子评论
  Future<void> likeSubComment(XhsSubComment subComment) async {
    try {
      // 检查是否有从互动页面传递过来的账号ID
      String? accountId;
      final Map<String, dynamic>? arguments = Get.arguments is Map ? Get.arguments : null;
      
      if (arguments != null && arguments.containsKey('accountId')) {
        accountId = arguments['accountId'];
        print('【点赞子评论】从参数中获取到账号ID: $accountId');
      }
      
      // 如果没有从参数获取到账号ID，则从账号列表中找一个小红书账号
      if (accountId == null || accountId.isEmpty) {
        final accounts = AccountPersistentService.to.accounts;
        final xhsAccount = accounts.firstWhereOrNull(
          (acc) => acc.type == PlatTypeEnum.xhs,
        );

        if (xhsAccount != null) {
          accountId = xhsAccount.id ?? xhsAccount.uid;
          print('【点赞子评论】找到小红书账号: ${xhsAccount.nickname}, ID: $accountId');
        } else {
          throw Exception('未找到小红书账号');
        }
      }
      
      // 根据当前子评论的点赞状态决定是点赞还是取消点赞
      final response = subComment.liked
          ? await _platformService.dislikeXhsComment(
              accountId: accountId,
              noteId: noteFromList.value!.id,
              commentId: subComment.id,
            )
          : await _platformService.likeXhsComment(
              accountId: accountId,
              noteId: noteFromList.value!.id,
              commentId: subComment.id,
            );
      
      if (response.isSuccess) {
        // 更新子评论的点赞状态
        // 首先找到父评论
        for (int i = 0; i < comments.length; i++) {
          final comment = comments[i];
          final subIndex = comment.subComments.indexWhere((s) => s.id == subComment.id);
          
          if (subIndex != -1) {
            // 找到了子评论，更新它
            final updatedSubComment = XhsSubComment(
              id: subComment.id,
              content: subComment.content,
              liked: !subComment.liked, // 反转点赞状态
              // 更新点赞数量
              likeCount: subComment.liked
                  ? (int.parse(subComment.likeCount) - 1).toString() // 取消点赞减1
                  : (int.parse(subComment.likeCount) + 1).toString(), // 点赞加1
              createTime: subComment.createTime,
              ipLocation: subComment.ipLocation,
              noteId: subComment.noteId,
              status: subComment.status,
              atUsers: subComment.atUsers,
              userInfo: subComment.userInfo,
              showTags: subComment.showTags,
              targetComment: subComment.targetComment,
            );
            
            // 创建更新后的评论列表
            final updatedSubComments = List<XhsSubComment>.from(comment.subComments);
            updatedSubComments[subIndex] = updatedSubComment;
            
            // 更新父评论
            final updatedComment = XhsComment(
              id: comment.id,
              content: comment.content,
              atUsers: comment.atUsers,
              createTime: comment.createTime,
              subCommentHasMore: comment.subCommentHasMore,
              status: comment.status,
              liked: comment.liked,
              showTags: comment.showTags,
              subComments: updatedSubComments, // 使用更新后的子评论列表
              noteId: comment.noteId,
              ipLocation: comment.ipLocation,
              likeCount: comment.likeCount,
              userInfo: comment.userInfo,
              subCommentCount: comment.subCommentCount,
              subCommentCursor: comment.subCommentCursor,
            );
            
            comments[i] = updatedComment;
            break;
          }
        }
        
        // 显示操作成功提示
        showToast(subComment.liked ? 'content.unlike.success'.tr : 'content.like.success'.tr);
      } else {
        // 显示错误提示
        showToast('content.operation.failed'.trParams({'message': response.msg ?? ''}));
      }
    } catch (e) {
      LoggerUtil.e('点赞子评论异常: $e');
      showToast('content.operation.failed'.trParams({'message': e.toString()}));
    }
  }

  // 一键回复功能
  void quickReply() {
    // 显示一键回复弹窗
    Get.bottomSheet(
      XhsQuickReplySheet(controller: this),
      isScrollControlled: true,
      enableDrag: true,
    );
  }
  
  // 批量回复评论
  Future<void> batchReplyToComments(String replyText) async {
    if (selectedComments.isEmpty) return;
    
    LoggerUtil.i('【XhsQuickReply】开始批量回复 ${selectedComments.length} 条评论');
    
    // 显示进度对话框
    Get.dialog(
      AlertDialog(
        title: Text('content.xhs.batch_reply_progress'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            LinearProgressIndicator(),
            SizedBox(height: 16),
            Text('content.xhs.batch_reply_in_progress'.tr),
          ],
        ),
      ),
      barrierDismissible: false,
    );
    
    int successCount = 0;
    int failCount = 0;
    
    try {
      // 尝试获取cookie
      String cookieStr = '';
      if (Get.arguments is Map && Get.arguments['accountCookie'] != null) {
        cookieStr = Get.arguments['accountCookie'] as String;
      }
      
      // 尝试获取笔记ID
      String noteId = '';
      if (noteDetail.value != null && noteDetail.value!.id.isNotEmpty) {
        noteId = noteDetail.value!.id;
      } else if (noteFromList.value != null) {
        noteId = noteFromList.value!.id;
      }
      
      if (cookieStr.isEmpty || noteId.isEmpty) {
        throw '无法获取账号Cookie或笔记ID';
      }
      
      for (int i = 0; i < selectedComments.length; i++) {
        final commentId = selectedComments[i];
        
        // 查找评论
        final commentIndex = comments.indexWhere((c) => c.id == commentId);
        if (commentIndex == -1) continue;
        
        final comment = comments[commentIndex];
        
        try {
          // 发送评论回复
          final result = await XhsService.postComment(
            cookieStr: cookieStr,
            noteId: noteId,
            content: replyText,
            rootCommentId: commentId,
          );
          
          if (result.isNotEmpty) {
            successCount++;
            LoggerUtil.i('【XhsQuickReply】成功回复评论 ${i+1}/${selectedComments.length}');
          } else {
            failCount++;
            LoggerUtil.e('【XhsQuickReply】回复评论失败 ${i+1}/${selectedComments.length}');
          }
        } catch (e) {
          failCount++;
          LoggerUtil.e('【XhsQuickReply】回复评论异常: $e');
        }
        
        // 添加延迟，避免频繁请求
        if (i < selectedComments.length - 1) {
          await Future.delayed(Duration(seconds: 3));
        }
      }
      
      // 关闭进度对话框
      Get.back();
      
      // 显示结果
      Get.snackbar(
        'content.xhs.batch_reply_result'.tr, 
        'content.xhs.batch_reply_summary'.trParams({
          'success': successCount.toString(),
          'fail': failCount.toString(),
        }),
        snackPosition: SnackPosition.BOTTOM,
        duration: Duration(seconds: 3),
      );
      
      // 刷新评论列表
      fetchComments();
      
    } catch (e) {
      // 确保关闭进度对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      LoggerUtil.e('【XhsQuickReply】批量回复评论异常: $e');
      
      Get.snackbar(
        'content.xhs.batch_reply_error'.tr,
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      // 退出评论选择模式
      isCommentSelectionMode.value = false;
      selectedComments.clear();
    }
  }
  
  // 切换评论选择状态
  void toggleCommentSelection(String commentId) {
    if (selectedComments.contains(commentId)) {
      selectedComments.remove(commentId);
    } else {
      selectedComments.add(commentId);
    }
  }

  // 点赞笔记
  Future<void> likeNote() async {
    if (isLiking.value) return;
    if (noteDetail.value == null || noteFromList.value == null) return;

    isLiking.value = true;

    try {
      final account = AccountPersistentService.to.accounts.firstWhere(
        (acc) => acc.type.toString().contains('xhs'),
        orElse: () => throw Exception('未找到小红书账号'),
      );

      // 根据当前点赞状态选择调用点赞或取消点赞接口
      final response =
          isLiked.value
              ? await _platformService.dislikeXhsNote(
                accountId: account.id!,
                noteId: noteFromList.value!.id,
              )
              : await _platformService.likeXhsNote(
                accountId: account.id!,
                noteId: noteFromList.value!.id,
              );

      if (response.isSuccess) {
        // 更新点赞状态
        isLiked.value = !isLiked.value;

        // 更新点赞数量
        final noteCard = noteDetail.value!.noteCard;
        final interactInfo = noteCard.interactInfo;

        // 从接口返回中获取新的点赞数量
        int newLikeCount;
        if (response.data != null && response.data!.containsKey('like_count')) {
          // 取消点赞接口返回的是新的点赞数
          newLikeCount =
              int.tryParse(response.data!['like_count'].toString()) ?? 0;
        } else {
          // 点赞接口没有返回新的点赞数，需要手动计算
          final currentLikes = int.tryParse(interactInfo.likedCount) ?? 0;
          newLikeCount = isLiked.value ? currentLikes + 1 : currentLikes - 1;
          if (newLikeCount < 0) newLikeCount = 0;
        }

        // 创建新的InteractInfo对象，因为原对象是不可变的
        final newInteractInfo = InteractInfo(
          likedCount: newLikeCount.toString(),
          collectedCount: interactInfo.collectedCount,
          commentCount: interactInfo.commentCount,
          shareCount: interactInfo.shareCount,
        );

        // 提示用户
        Get.snackbar(
          'content.hint'.tr,
          isLiked.value ? 'content.like.success'.tr : 'content.unlike.success'.tr,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        Get.snackbar(
          'content.hint'.tr,
          'content.like.failed'.trParams({'message': response.msg ?? ''}),
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar('content.hint'.tr, 'content.like.exception'.trParams({'message': e.toString()}), snackPosition: SnackPosition.BOTTOM);
    } finally {
      isLiking.value = false;
    }
  }

  /// 隐藏键盘
  void hideKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  /// 显示Toast提示
  void showToast(String message) {
    Get.snackbar(
      'content.hint'.tr,
      message,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  /// 插入表情
  void insertEmoji(String emoji) {
    final text = commentController.text;
    final selection = commentController.selection;

    // 在光标位置插入表情
    final newText = text.replaceRange(selection.start, selection.end, emoji);

    // 更新文本和光标位置
    commentController.text = newText;
    commentText.value = newText;

    // 移动光标到插入表情后的位置
    final newPosition = selection.start + emoji.length;
    commentController.selection = TextSelection.collapsed(offset: newPosition);
  }
}
