import 'dart:math' as math;
import 'dart:ui';

import 'package:aitoearn_app/models/douyin_models/douyin_aweme_detail_model.dart';
import 'package:aitoearn_app/pages/content_detail/douyin_content_detail_controller.dart';
import 'package:aitoearn_app/widgets/common_loading.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:media_kit_video/media_kit_video.dart' as media_kit_video;

class DouyinContentDetailPage extends GetView<DouyinContentDetailController> {
  const DouyinContentDetailPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 设置状态栏为透明
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    return GetX<DouyinContentDetailController>(
      init: controller,
      builder: (_) {
        // 全屏模式下只显示视频播放器
        if (controller.isFullScreen.value) {
          return Scaffold(
            backgroundColor: Colors.black,
            body: _buildVideoPlayer(),
          );
        }

        // 非全屏模式下显示完整页面
        return Scaffold(
          backgroundColor: Colors.white,
          extendBodyBehindAppBar: true,
          // 内容延伸到AppBar下方
          appBar: _buildCustomAppBar(),
          body: _buildBody(),
          bottomNavigationBar: _buildFixedCommentBar(),
        );
      },
    );
  }

  PreferredSizeWidget _buildCustomAppBar() {
    if (controller.isLoading.value || controller.awemeDetail.value == null) {
      return AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Get.back(),
        ),
      );
    }

    final detail = controller.awemeDetail.value!;

    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 1, sigmaY: 1),
          child: Container(
            color: Colors.black.withOpacity(0.2),
            child: SafeArea(
              child: Row(
                children: [
                  // 返回按钮
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Get.back(),
                  ),

                  // 用户头像
                  ClipOval(
                    child: NetworkImageWidget(
                      detail.author.avatarThumb,
                      width: 30,
                      height: 30,
                      fit: BoxFit.cover,
                      errorWidget: Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.person,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 8),

                  // 用户昵称
                  Expanded(
                    child: Text(
                      detail.author.nickname,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // 更多按钮
                  IconButton(
                    icon: const Icon(Icons.more_horiz, color: Colors.white),
                    onPressed: () {},
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CommonLoading());
      }

      if (controller.hasError.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(controller.errorMessage.value),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Get.back(),
                child: Text('content.back'.tr),
              ),
            ],
          ),
        );
      }

      final awemeDetail = controller.awemeDetail.value!;
      return NotificationListener<ScrollNotification>(
        onNotification: (ScrollNotification scrollInfo) {
          // 检测是否滑动到底部，如果是则自动加载更多评论
          if (scrollInfo.metrics.pixels >
                  scrollInfo.metrics.maxScrollExtent - 200 &&
              !controller.isLoadingComments.value &&
              controller.hasMoreComments.value) {
            controller.loadMoreComments();
          }
          return false;
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildContentSection(awemeDetail),
              _buildCommentsSection(),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildContentSection(AwemeDetail awemeDetail) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 视频
        Stack(
          children: [
            _buildVideoPlayer(),

            // 顶部渐变遮罩，增强状态栏内容可见性
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: 120,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.black.withOpacity(0.5), Colors.transparent],
                  ),
                ),
              ),
            ),
          ],
        ),

        // 标题和内容
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Text(
                awemeDetail.desc,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              // 发布时间
              Text(
                _formatTimestamp(awemeDetail.createTime),
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),

              // 作者信息
              Row(
                children: [
                  // 头像
                  ClipOval(
                    child: NetworkImageWidget(
                      awemeDetail.author.avatarThumb,
                      width: 40,
                      height: 40,
                      fit: BoxFit.cover,
                      errorWidget: Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.person,
                          size: 24,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 用户名和签名
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          awemeDetail.author.nickname,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (awemeDetail.author.signature.isNotEmpty)
                          Text(
                            awemeDetail.author.signature,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                  // 关注按钮
                  // ElevatedButton(
                  //   onPressed: () {},
                  //   style: ElevatedButton.styleFrom(
                  //     backgroundColor: Colors.red,
                  //     foregroundColor: Colors.white,
                  //     shape: RoundedRectangleBorder(
                  //       borderRadius: BorderRadius.circular(20),
                  //     ),
                  //     padding: const EdgeInsets.symmetric(
                  //       horizontal: 16,
                  //       vertical: 8,
                  //     ),
                  //   ),
                  //   child: const Text('关注'),
                  // ),
                ],
              ),

              const SizedBox(height: 16),
              const Divider(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCommentsSection() {
    return Obx(() {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 评论标题
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatCommentCount('content.comments'.tr, controller.awemeDetail.value?.statistics.commentCount ?? "0"),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // 操作按钮组
                Row(
                  children: [
                    // 一键回复按钮
                    GestureDetector(
                      onTap: controller.quickReply,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: const Color(0xFFECE6FF),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.reply,
                              size: 14,
                              color: Color(0xFF7468E4),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'content.douyin.one_click_reply'.tr,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF7468E4),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // 筛选按钮
                    GestureDetector(
                      onTap: () {
                        controller.showCommentFilterSheet();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF8F8F8),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.filter_list,
                              size: 16,
                              color: Color(0xFF666666),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'content.filter'.tr,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),
            
            // 显示当前筛选状态
            Obx(() {
              if (controller.isFiltered.value) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 10),
                          decoration: BoxDecoration(
                            color: const Color(0xFFECE6FF),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.filter_alt_outlined, size: 14, color: Color(0xFF7468E4)),
                              const SizedBox(width: 4),
                              Text(
                                'content.filtered.comments@count'.trParams({'count': controller.filteredComments.length.toString()}),
                                style: const TextStyle(fontSize: 12, color: Color(0xFF7468E4)),
                              ),
                            ],
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          // 使用不会导航返回的clearFilters方法
                          controller.clearFilters();
                        },
                        child: Text('content.clear.filter'.tr, style: const TextStyle(fontSize: 12)),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            // 评论列表
            Obx(() {
              // 显示加载中状态
              if (controller.isLoadingComments.value && controller.comments.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: CommonLoading(),
                  ),
                );
              }

              // 显示暂无评论
              if (controller.comments.isEmpty && !controller.isLoadingComments.value) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 32.0),
                  child: Center(
                    child: Text('content.no.comments'.tr, style: const TextStyle(color: Colors.grey)),
                  ),
                );
              }

              // 显示评论列表
              final commentList = controller.isFiltered.value
                  ? controller.filteredComments
                  : controller.comments;

              return ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 0.0),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: commentList.length + (controller.isLoadingComments.value ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == commentList.length) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: CommonLoading(),
                      ),
                    );
                  }

                  final comment = commentList[index];
                  return _buildCommentItem(comment);
                },
              );
            }),

            // 加载更多
            if (controller.hasMoreComments.value &&
                !controller.isLoadingComments.value &&
                !controller.isFiltered.value)
              Center(
                child: TextButton(
                  onPressed: controller.loadMoreComments,
                  child: Text('content.douyin.load_more'.tr),
                ),
              ),

            // 底部空间
            const SizedBox(height: 80),
          ],
        ),
      );
    });
  }

  Widget _buildCommentItem(dynamic comment) {
    // 从评论数据中提取信息
    final String text = comment['text'] ?? '评论内容';
    final String cid = comment['cid'] ?? '';
    final int createTime = comment['create_time'] ?? 0;
    final int diggCount =
        int.tryParse(comment['digg_count']?.toString() ?? '0') ?? 0;

    // 获取用户信息
    final Map<String, dynamic>? user = comment['user'];
    final String nickname = user?['nickname'] ?? '用户';
    final Map<String, dynamic>? avatarThumb = user?['avatar_thumb'];
    final List<String> avatarUrls =
        avatarThumb?['url_list'] is List
            ? (avatarThumb!['url_list'] as List)
                .map((url) => url.toString())
                .toList()
            : [];
    final String avatarUrl = avatarUrls.isNotEmpty ? avatarUrls.first : '';

    // 计算评论时间
    final String timeAgo = _formatTimestamp(createTime);

    // 检查是否有二级评论
    final List<dynamic>? replyComments =
        comment['reply_comment'] is List
            ? comment['reply_comment'] as List<dynamic>
            : null;
    final int replyCommentTotal =
        int.tryParse(comment['reply_comment_total']?.toString() ?? '0') ?? 0;

    // 检查是否在选择模式下以及是否被选中
    final bool isInSelectionMode = controller.isCommentSelectionMode.value;
    final bool isSelected = controller.selectedComments.contains(cid);

    return Obx(() {
      final isInSelectionMode = controller.isCommentSelectionMode.value;
      final isSelected = controller.selectedComments.contains(cid);

      return InkWell(
        onTap: isInSelectionMode ? () => controller.toggleCommentSelection(cid) : null,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 一级评论
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 选择框（仅在选择模式下显示）
                  if (isInSelectionMode)
                    Container(
                      margin: const EdgeInsets.only(right: 8, top: 6),
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected ? const Color(0xFF7468E4) : Colors.grey,
                          width: 1.5,
                        ),
                        color: isSelected ? const Color(0xFF7468E4) : Colors.white,
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              size: 14,
                              color: Colors.white,
                            )
                          : null,
                    ),

                  // 头像
                  ClipOval(
                    child:
                        avatarUrl.isNotEmpty
                            ? NetworkImageWidget(
                               avatarUrl,
                              width: 30,
                              height: 30,
                              fit: BoxFit.cover,
                          errorWidget: CircleAvatar(
                            backgroundColor: Colors.grey[300],
                            child: const Icon(Icons.person, color: Colors.white),
                          ),
                            )
                            : Container(
                              width: 30,
                              height: 30,
                              color: Colors.grey[300],
                              child: const Center(
                                child: Icon(Icons.person, color: Colors.white),
                              ),
                            ),
                  ),
                  const SizedBox(width: 12),
                  // 评论内容
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 用户名
                        Text(
                          nickname,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        // 评论内容
                        Text(text, style: const TextStyle(fontSize: 14)),
                        const SizedBox(height: 4),
                        // 评论时间和点赞
                        Row(
                          children: [
                            Text(
                              timeAgo,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            const Spacer(),
                            // 回复按钮
                            GestureDetector(
                              onTap: () {
                                controller.prepareReplyComment(cid);
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.reply,
                                      size: 14,
                                      color: Colors.grey[600],
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'content.reply'.tr,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            // 点赞按钮
                            Row(
                              children: [
                                Icon(
                                  Icons.favorite_border,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _formatCount(diggCount.toString()),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // 二级评论
              if (replyComments != null && replyComments.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(left: isInSelectionMode ? 40.0 : 42.0, top: 8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children:
                        replyComments
                            .map(
                              (replyComment) =>
                                  _buildReplyCommentItem(replyComment),
                            )
                            .toList(),
                  ),
                ),

              // 显示加载中状态
              if (comment['isLoadingReplies'] == true)
                Padding(
                  padding: EdgeInsets.only(left: isInSelectionMode ? 40.0 : 42.0, top: 8.0),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.grey[400]!,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'content.douyin.loading_replies'.tr,
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),

              // 显示查看更多回复按钮
              if (replyCommentTotal > (replyComments?.length ?? 0) &&
                  replyCommentTotal > 0 &&
                  comment['isLoadingReplies'] != true)
                Padding(
                  padding: EdgeInsets.only(left: isInSelectionMode ? 40.0 : 42.0, top: 4.0),
                  child: GestureDetector(
                    onTap: () {
                      // 获取当前评论的回复游标
                      final replyCursor = comment['reply_cursor'] ?? '';
                      controller.loadReplyComments(cid, cursor: replyCursor);
                    },
                    child: Text(
                      'content.douyin.view_all_replies'.trParams({'count': replyCommentTotal.toString()}),
                      style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  // 构建二级评论项
  Widget _buildReplyCommentItem(dynamic replyComment) {
    // 从评论数据中提取信息
    final String text = replyComment['text'] ?? '评论内容';
    final int createTime = replyComment['create_time'] ?? 0;
    final int diggCount =
        int.tryParse(replyComment['digg_count']?.toString() ?? '0') ?? 0;

    // 获取用户信息
    final Map<String, dynamic>? user = replyComment['user'];
    final String nickname = user?['nickname'] ?? '用户';
    final Map<String, dynamic>? avatarThumb = user?['avatar_thumb'];
    final List<String> avatarUrls =
        avatarThumb?['url_list'] is List
            ? (avatarThumb!['url_list'] as List)
                .map((url) => url.toString())
                .toList()
            : [];
    final String avatarUrl = avatarUrls.isNotEmpty ? avatarUrls.first : '';

    // 计算评论时间
    final String timeAgo = _formatTimestamp(createTime);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头像
          ClipOval(
            child:
                avatarUrl.isNotEmpty
                    ? NetworkImageWidget(
                       avatarUrl,
                      width: 24,
                      height: 24,
                      fit: BoxFit.cover,
                  errorWidget: Container(
                    color: Colors.grey[300],
                    child: const Icon(
                      Icons.person,
                      size: 12,
                      color: Colors.white,
                    ),
                  ),
                    )
                    : Container(
                      width: 24,
                      height: 24,
                      color: Colors.grey[300],
                      child: const Center(
                        child: Icon(
                          Icons.person,
                          size: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
          ),
          const SizedBox(width: 8),
          // 评论内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户名
                Text(
                  nickname,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                // 评论内容
                Text(text, style: const TextStyle(fontSize: 12)),
                const SizedBox(height: 2),
                // 评论时间和点赞
                Row(
                  children: [
                    Text(
                      timeAgo,
                      style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                    ),
                    const Spacer(),
                    // 点赞按钮
                    Row(
                      children: [
                        Icon(
                          Icons.favorite_border,
                          size: 12,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 2),
                        Text(
                          _formatCount(diggCount.toString()),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建视频播放器
  Widget _buildVideoPlayer() {
    return Obx(() {
      if (controller.videoController == null) {
        return Container(
          height: 400, // 增加高度以适应状态栏
          color: Colors.black,
          child: const Center(child: CircularProgressIndicator()),
        );
      }

      // 获取屏幕尺寸
      final mediaQuery = MediaQuery.of(Get.context!);
      final screenWidth = mediaQuery.size.width;
      final screenHeight = mediaQuery.size.height;

      // 全屏模式下使用整个屏幕，否则根据比例计算高度（最小400像素）
      final videoHeight =
          controller.isFullScreen.value
              ? screenHeight
              : math.max(screenWidth / 16 * 9, 400.0); // 默认使用16:9比例

      // 全屏模式下使用整个屏幕宽度，否则使用屏幕宽度
      final containerWidth =
          controller.isFullScreen.value ? screenWidth : screenWidth;

      return GestureDetector(
        onTap: () => controller.showControls.value = !controller.showControls.value,
        onDoubleTap: () => controller.togglePlayPause(),
        child: Container(
          width: containerWidth,
          height: videoHeight,
          color: Colors.black,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 视频播放器
              Center(
                child: media_kit_video.Video(
                  controller: controller.videoController!,
                  controls: media_kit_video.NoVideoControls,
                  fill: Colors.black,
                ),
              ),

              // 播放/暂停按钮
              if (controller.showControls.value)
                GestureDetector(
                  onTap: controller.togglePlayPause,
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      controller.isPlaying.value
                          ? Icons.pause
                          : Icons.play_arrow,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),

              // 底部控制栏
              if (controller.showControls.value)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: EdgeInsets.zero,
                    color: Colors.black.withOpacity(0.3),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 时间显示
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                _formatDuration(controller.videoPosition.value),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                _formatDuration(controller.videoDuration.value),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // 进度条
                        SliderTheme(
                          data: SliderThemeData(
                            trackHeight: 2,
                            thumbShape: const RoundSliderThumbShape(
                              enabledThumbRadius: 6,
                            ),
                            overlayShape: const RoundSliderOverlayShape(
                              overlayRadius: 10,
                            ),
                            activeTrackColor: Colors.white,
                            inactiveTrackColor: Colors.white.withOpacity(0.3),
                            thumbColor: Colors.white,
                            overlayColor: Colors.white.withOpacity(0.3),
                          ),
                          child: Slider(
                            value:
                                controller.videoPosition.value.inMilliseconds
                                    .toDouble(),
                            min: 0,
                            max:
                                controller.videoDuration.value.inMilliseconds >
                                        0
                                    ? controller
                                        .videoDuration
                                        .value
                                        .inMilliseconds
                                        .toDouble()
                                    : 1.0,
                            onChanged: (value) {
                              controller.seekTo(
                                Duration(milliseconds: value.toInt()),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // 全屏模式下的返回按钮
              if (controller.isFullScreen.value &&
                  controller.showControls.value)
                Positioned(
                  top: 0,
                  left: 0,
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: GestureDetector(
                        onTap: controller.exitFullScreen,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black45,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  void _showCommentInputSheet({String? commentId}) {
    if (commentId != null) {
      controller.replyCommentId.value = commentId;
    }

    Get.bottomSheet(
      _buildCommentInputSheetContent(),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    ).whenComplete(() {
      controller.clearReplyTarget();
    });

    Future.delayed(const Duration(milliseconds: 100), () {
      controller.commentFocusNode.requestFocus();
    });
  }

  Widget _buildCommentInputSheetContent() {
    return GestureDetector(
      // 点击空白区域隐藏键盘和表情选择器
      onTap: () {
        controller.hideKeyboard();
        controller.showEmojiPicker.value = false;
      },
      child: Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(Get.context!).viewInsets.bottom,
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          decoration: const BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.0),
              topRight: Radius.circular(16.0),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: GestureDetector(
                            // 阻止点击事件冒泡，防止点击输入框时关闭表情选择器
                            onTap: () => FocusScope.of(Get.context!).requestFocus(controller.commentFocusNode),
                            child: Obx(
                              () => Container(
                                height: 40,
                                child: TextField(
                                  controller: controller.commentController,
                                  focusNode: controller.commentFocusNode,
                                  style: const TextStyle(color: Colors.black87),
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    hintText: controller.getCommentHintText(),
                                    hintStyle: TextStyle(color: Colors.grey[400]),
                                  ),
                                  onChanged: (val) => controller.commentText.value = val,
                                  maxLines: 4,
                                  minLines: 1,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        controller.sendComment();
                        Get.back();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.redAccent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                      ),
                      child: Text('content.send'.tr, style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () {},
                    icon: Icon(Icons.alternate_email, color: Colors.grey[400]),
                  ),
                  IconButton(
                    onPressed: controller.toggleEmojiPicker,
                    icon: Icon(Icons.sentiment_satisfied_alt_outlined, color: Colors.grey[400]),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: Icon(Icons.image_outlined, color: Colors.grey[400]),
                  ),
                  IconButton(
                    onPressed: () {},
                    icon: Icon(Icons.add_circle_outline, color: Colors.grey[400]),
                  ),
                ],
              ),
              Obx(() => controller.showEmojiPicker.value ? _buildEmojiPicker() : const SizedBox.shrink()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmojiPicker() {
    return Container(
      height: 250,
      color: Colors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              children: [
                Text(
                  "content.emoji.title".tr,
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    controller.showEmojiPicker.value = false;
                  },
                  child: Text("content.emoji.close".tr, style: TextStyle(color: Colors.grey[600])),
                ),
              ],
            ),
          ),
          Divider(color: Colors.grey[300], height: 1),
          Expanded(
            child: DefaultTabController(
              length: 4,
              child: Column(
                children: [
                  TabBar(
                    tabs: [
                      Tab(text: "content.emoji.tab.common".tr),
                      Tab(text: "content.emoji.tab.all".tr),
                      Tab(text: "content.emoji.tab.popular".tr),
                      Tab(text: "content.emoji.tab.animals".tr),
                    ],
                    labelColor: Colors.black87,
                    unselectedLabelColor: Colors.grey[500],
                    indicatorColor: Colors.redAccent,
                    isScrollable: true,
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildCommonEmojis(),
                        _buildAllEmojis(),
                        _buildPopularEmojis(),
                        _buildAnimalEmojis(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 常用表情
  Widget _buildCommonEmojis() {
    final commonEmojis = [
      "😊", "😂", "🤣", "❤️", "👍", "🎉", "😍", "🥰", "😘", "😭",
      "🤔", "🙄", "😅", "😁", "🥳", "🤗", "🙏", "👏", "🔥", "💯",
      "✨", "🌟", "💕", "💓",
    ];
    return _buildEmojiGrid(commonEmojis);
  }

  // 全部表情
  Widget _buildAllEmojis() {
    final List<Map<String, dynamic>> emojiCategories = [
      {
        "name": "content.emoji.category.face".tr,
        "emojis": <String>[
          "😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂", "🙂", "🙃",
          "😉", "😊", "😇", "🥰", "😍", "🤩", "😘", "😗", "😚", "😙",
          "😋", "😛", "😜", "🤪", "😝", "🤑", "🤗", "🤭", "🤫", "🤔",
          "🤐", "🤨", "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "🤥",
          "😌", "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕",
        ],
      },
      {
        "name": "content.emoji.category.gesture".tr,
        "emojis": <String>[
          "👍", "👎", "👌", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉",
          "👆", "👇", "☝️", "👋", "🤚", "🖐️", "✋", "🖖", "👏", "🙌",
          "👐", "🤲", "🤝", "🙏",
        ],
      },
      {
        "name": "content.emoji.category.symbol".tr,
        "emojis": <String>[
          "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "💔", "❣️", "💕",
          "💞", "💓", "💗", "💖", "💘", "💝", "💟", "☮️", "✝️", "☪️",
          "🔯", "🕎", "☯️", "☦️", "🛐", "⛎", "♈", "♉", "♊", "♋",
          "♌", "♍", "♎", "♏", "♐", "♑", "♒", "♓",
        ],
      },
    ];

    return ListView.builder(
      itemCount: emojiCategories.length,
      itemBuilder: (context, categoryIndex) {
        final Map<String, dynamic> category = emojiCategories[categoryIndex];
        final String categoryName = category["name"] as String;
        final List<String> emojis = category["emojis"] as List<String>;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                categoryName,
                style: const TextStyle(
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            GridView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 8,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: emojis.length,
              itemBuilder: (context, index) {
                final String emoji = emojis[index];
                return GestureDetector(
                  onTap: () {
                    controller.insertEmoji(emoji);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    alignment: Alignment.center,
                    child: Text(emoji, style: const TextStyle(fontSize: 24)),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  // 热门表情包
  Widget _buildPopularEmojis() {
    final popularEmojis = [
      "🤣", "💯", "🔥", "😅", "🙏", "👍", "💕", "🎉", "✨", "😊",
      "🥳", "🤩", "😎", "🤔", "😂", "🥰", "💪", "👏", "🌈", "⭐",
      "🌟", "💓", "💝", "🎊", "🙌", "🤗", "💃", "🕺", "🍾", "🥂",
      "🏆", "🎯", "🚀", "💰", "💸", "🤑",
    ];
    return _buildEmojiGrid(popularEmojis);
  }

  // 动物表情包
  Widget _buildAnimalEmojis() {
    final animalEmojis = [
      "🐶", "🐱", "🐭", "🐹", "🐰", "🦊", "🐻", "🐼", "🐨", "🐯",
      "🦁", "🐮", "🐷", "🐸", "🐵", "🙈", "🙉", "🙊", "🐔", "🐧",
      "🐦", "🐤", "🦆", "🦅", "🦉", "🦇", "🐺", "🐗", "🐴", "🦄",
      "🐝", "🐛", "🦋", "🐌", "🐞", "🐜",
    ];
    return _buildEmojiGrid(animalEmojis);
  }

  // 通用表情网格构建方法
  Widget _buildEmojiGrid(List<String> emojis) {
    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 8,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: emojis.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            controller.insertEmoji(emojis[index]);
          },
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
            ),
            alignment: Alignment.center,
            child: Text(emojis[index], style: const TextStyle(fontSize: 24)),
          ),
        );
      },
    );
  }

  // 修改底部固定的评论输入框
  Widget _buildFixedCommentBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 3.0),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            // 评论输入框
            Expanded(
              child: GestureDetector(
                onTap: () => _showCommentInputSheet(),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(24.0),
                  ),
                  child: Text(
                    'content.comment.placeholder'.tr,
                    style: TextStyle(color: Colors.grey[500], fontSize: 14),
                  ),
                ),
              ),
            ),

            // 互动按钮
            Row(
              children: [
                // 点赞按钮
                _buildInteractionButton(
                  icon: Icons.favorite_border,
                  count: controller.awemeDetail.value?.statistics.diggCount ?? '0',
                  isActive: controller.isLiked.value,
                  onTap: () {
                    // TODO: 实现点赞功能
                    controller.isLiked.value = !controller.isLiked.value;
                  },
                ),

                // 评论数
                _buildInteractionButton(
                  icon: Icons.chat_bubble_outline,
                  count: controller.awemeDetail.value?.statistics.commentCount ?? '0',
                  onTap: () {},
                ),

                // 收藏按钮
                _buildInteractionButton(
                  icon: Icons.star_border,
                  count: controller.awemeDetail.value?.statistics.collectCount ?? '0',
                  onTap: () {
                    // TODO: 收藏功能
                  },
                ),

                // 分享按钮
                _buildInteractionButton(
                  icon: Icons.share,
                  count: controller.awemeDetail.value?.statistics.shareCount ?? '0',
                  onTap: () {
                    // TODO: 分享功能
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required String count,
    bool isActive = false,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: isActive ? Colors.red : Colors.black54, size: 20),
            const SizedBox(height: 2),
            Text(
              _formatCount(count),
              style: TextStyle(
                fontSize: 10,
                color: isActive ? Colors.red : Colors.black54,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 格式化时长
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // 格式化数字（转换为K、W等）
  String _formatCount(String count) {
    final int num = int.tryParse(count) ?? 0;
    if (num < 1000) return num.toString();
    if (num < 10000) return '${(num / 1000).toStringAsFixed(1)}K';
    return '${(num / 10000).toStringAsFixed(1)}W';
  }

  // 格式化评论数
  String _formatCommentCount(String prefix, String count) {
    final int num = int.tryParse(count) ?? 0;
    if (num == 0) return prefix;
    return '$prefix($count)';
  }

  // 格式化时间戳
  String _formatTimestamp(int timestamp) {
    final DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(
      timestamp * 1000,
    );
    final DateTime now = DateTime.now();
    final Duration difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return 'time.year_ago'.tr.trParams({'year': (difference.inDays / 365).floor().toString()});
    } else if (difference.inDays > 30) {
      return 'time.month_ago'.tr.trParams({'month': (difference.inDays / 30).floor().toString()});
    } else if (difference.inDays > 0) {
      return 'time.day_ago'.tr.trParams({'day': difference.inDays.toString()});
    } else if (difference.inHours > 0) {
      return 'time.hour_ago'.tr.trParams({'hour': difference.inHours.toString()});
    } else if (difference.inMinutes > 0) {
      return 'time.minute_ago'.tr.trParams({'minute': difference.inMinutes.toString()});
    } else {
      return 'time.just_now'.tr;
    }
  }
}
