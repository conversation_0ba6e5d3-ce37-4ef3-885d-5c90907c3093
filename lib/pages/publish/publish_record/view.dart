import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/publish/publish_record/logic.dart';
import 'package:aitoearn_app/pages/publish/publish_record/state.dart';
import 'package:aitoearn_app/pages/publish/video_publish_binding.dart';
import 'package:aitoearn_app/pages/publish/video_publish_page.dart';
import 'package:aitoearn_app/pages/publish/widgets/publish_progress_view.dart';
import 'package:aitoearn_app/pages/publish/widgets/tabs_calendar.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/calendar/view.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

class PublishRecordPage extends StatefulWidget {
  const PublishRecordPage({super.key});

  @override
  State<PublishRecordPage> createState() => _PublishRecordPageState();
}

class _PublishRecordPageState extends State<PublishRecordPage> {
  final PublishRecordLogic logic = Get.put(PublishRecordLogic());
  final PublishRecordState state = Get.find<PublishRecordLogic>().state;

  Widget? _buildToday(BuildContext context, DateTime day, DateTime focusedDay) {
    return day.day
        .toString()
        .toNormalText(
          color: AppColors.primaryColor,
          fontWeight: FontWeight.bold,
        )
        .center()
        .width(30)
        .height(30)
        .center();
  }

  Widget? _buildSelected(
    BuildContext context,
    DateTime day,
    DateTime focusedDay,
  ) {
    return day.day
        .toString()
        .toNormalText(color: Colors.white)
        .center()
        .decorated(
          gradient: AppColors.blueAndPurple,
          borderRadius: BorderRadius.circular(5),
        )
        .width(30)
        .height(30)
        .center();
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      useDefaultBackground: false,
      backgroundColor: AppColors.white,
      body: Scaffold(
        body: PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            logic.popHandler(didPop, result);
          },
          child: CalendarComponent(
            key: UniqueKey(),
            onDaySelected:
                (DateTime selectedDay, DateTime focusedDay) =>
                    logic.onDaySelected(selectedDay, focusedDay),
            calendarBuilders: CalendarBuilders(
              todayBuilder: _buildToday,
              selectedBuilder: _buildSelected,
            ),
            bottomWidget: PublishProgressView(),
            topRightWidget: TabsCalendar(selectedIndex: 0),
          ),
        ),
        floatingActionButton: SizedBox(
          height: 45,
          width: 45,
          child: FloatingActionButton(
            shape: const CircleBorder(),
            onPressed: () {
              VideoPublishBinding().dependencies();
              Get.to(() => const VideoPublishPage());
            },
            backgroundColor: Colors.blue,
            heroTag: 'publish_fab',
            tooltip: '添加发布',
            child: const Icon(Icons.add, size: 28, color: Colors.white),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    Get.delete<PublishRecordLogic>();
    super.dispose();
  }
}
