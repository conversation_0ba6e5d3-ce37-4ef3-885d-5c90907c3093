import 'dart:io';

import 'package:aitoearn_app/pages/publish/publish_record/state.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class PublishRecordLogic extends GetxController {
  final PublishRecordState state = PublishRecordState();

  void popHandler(bool didPop, Object? result) {
    if (state.lastTime == null ||
        DateTime.now().difference(state.lastTime!) >
            PublishRecordState.duration) {
      state.lastTime = DateTime.now();
      showToast('exitConfirm'.tr);
    } else {
      if (Platform.isAndroid) {
        SystemNavigator.pop();
      } else if (Platform.isIOS) {
        exit(0);
      }
    }
  }

  void onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    // showToast('点击了日期$selectedDay');
  }
}
