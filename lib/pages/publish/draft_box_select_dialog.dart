import 'package:aitoearn_app/models/draft_models/draft_model.dart';
import 'package:aitoearn_app/store/draft/draft_store_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 草稿箱选择弹窗
class DraftBoxSelectDialog extends StatefulWidget {
  const DraftBoxSelectDialog({Key? key}) : super(key: key);

  /// 显示草稿箱选择弹窗
  static Future<Map<String, dynamic>?> show(BuildContext context) async {
    return showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const DraftBoxSelectDialog(),
    );
  }

  @override
  State<DraftBoxSelectDialog> createState() => _DraftBoxSelectDialogState();
}

class _DraftBoxSelectDialogState extends State<DraftBoxSelectDialog> {
  // 草稿箱服务
  late final DraftStoreService _draftStoreService;
  
  // 草稿箱列表
  late final RxList<DraftBox> draftBoxes;
  
  // 选中的草稿箱ID
  final RxString selectedBoxId = ''.obs;
  
  // 新草稿箱名称控制器
  final TextEditingController _newBoxNameController = TextEditingController();
  
  // 是否显示新建草稿箱表单
  final RxBool showNewBoxForm = false.obs;

  @override
  void initState() {
    super.initState();
    
    // 获取草稿箱服务
    _draftStoreService = Get.find<DraftStoreService>();
    
    // 获取草稿箱列表
    draftBoxes = _draftStoreService.draftBoxes;
    
    // 默认选中默认草稿箱
    final defaultBox = _draftStoreService.getDefaultDraftBox();
    if (defaultBox != null) {
      selectedBoxId.value = defaultBox.id;
    }
  }

  @override
  void dispose() {
    _newBoxNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖动条
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              '选择草稿箱',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 草稿箱列表
          Obx(() {
            if (draftBoxes.isEmpty) {
              return const Padding(
                padding: EdgeInsets.all(20),
                child: Center(
                  child: Text('暂无草稿箱，请先创建'),
                ),
              );
            }
            
            return Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.4,
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: draftBoxes.length,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                itemBuilder: (context, index) {
                  final box = draftBoxes[index];
                  return _buildDraftBoxItem(box);
                },
              ),
            );
          }),
          
          const SizedBox(height: 16),
          
          // 新建草稿箱表单
          // Obx(() {
          //   if (!showNewBoxForm.value) {
          //     return Padding(
          //       padding: const EdgeInsets.symmetric(horizontal: 20),
          //       child: OutlinedButton.icon(
          //         onPressed: () {
          //           showNewBoxForm.value = true;
          //         },
          //         icon: const Icon(Icons.add, size: 18),
          //         label: const Text('新建草稿箱'),
          //         style: OutlinedButton.styleFrom(
          //           padding: const EdgeInsets.symmetric(vertical: 12),
          //           side: BorderSide(color: Colors.grey[300]!),
          //           shape: RoundedRectangleBorder(
          //             borderRadius: BorderRadius.circular(8),
          //           ),
          //         ),
          //       ),
          //     );
          //   }
          //
          //   return Padding(
          //     padding: const EdgeInsets.symmetric(horizontal: 20),
          //     child: Column(
          //       children: [
          //         TextField(
          //           controller: _newBoxNameController,
          //           decoration: const InputDecoration(
          //             hintText: '输入草稿箱名称',
          //             border: OutlineInputBorder(),
          //             contentPadding: EdgeInsets.symmetric(
          //               horizontal: 12,
          //               vertical: 12,
          //             ),
          //           ),
          //           autofocus: true,
          //         ),
          //         const SizedBox(height: 8),
          //         Row(
          //           children: [
          //             Expanded(
          //               child: OutlinedButton(
          //                 onPressed: () {
          //                   showNewBoxForm.value = false;
          //                   _newBoxNameController.clear();
          //                 },
          //                 style: OutlinedButton.styleFrom(
          //                   padding: const EdgeInsets.symmetric(vertical: 12),
          //                   side: BorderSide(color: Colors.grey[300]!),
          //                   shape: RoundedRectangleBorder(
          //                     borderRadius: BorderRadius.circular(8),
          //                   ),
          //                 ),
          //                 child: const Text('取消'),
          //               ),
          //             ),
          //             const SizedBox(width: 12),
          //             Expanded(
          //               child: ElevatedButton(
          //                 onPressed: () {
          //                   _createNewDraftBox();
          //                 },
          //                 style: ElevatedButton.styleFrom(
          //                   padding: const EdgeInsets.symmetric(vertical: 12),
          //                   backgroundColor: Colors.blue,
          //                   shape: RoundedRectangleBorder(
          //                     borderRadius: BorderRadius.circular(8),
          //                   ),
          //                 ),
          //                 child: const Text('创建'),
          //               ),
          //             ),
          //           ],
          //         ),
          //       ],
          //     ),
          //   );
          // }),
          
          const SizedBox(height: 16),
          
          // 底部按钮
          Padding(
            padding: EdgeInsets.only(
              left: 20,
              right: 20,
              bottom: 20 + MediaQuery.of(context).padding.bottom,
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      side: BorderSide(color: Colors.grey[300]!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _confirmSelection();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      backgroundColor: Colors.blue,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: const Text('确认'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建草稿箱项
  Widget _buildDraftBoxItem(DraftBox box) {
    return Obx(() {
      final isSelected = selectedBoxId.value == box.id;
      
      return InkWell(
        onTap: () {
          selectedBoxId.value = box.id;
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? Colors.blue.withOpacity(0.1) : Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // 草稿箱图标
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getBoxColor(box.color),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Icon(
                    _getBoxIcon(box.icon),
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // 草稿箱信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          box.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (box.isDefault)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue[100],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '默认',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.blue[800],
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${box.drafts.length}个草稿 · ${_formatDate(box.updateTime)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              // 选中状态
              if (isSelected)
                const Icon(
                  Icons.check_circle,
                  color: Colors.blue,
                  size: 24,
                ),
            ],
          ),
        ),
      );
    });
  }

  // 创建新草稿箱
  Future<void> _createNewDraftBox() async {
    final name = _newBoxNameController.text.trim();
    
    if (name.isEmpty) {
      Get.snackbar('提示', '请输入草稿箱名称');
      return;
    }
    
    // 创建新草稿箱
    final newBox = await _draftStoreService.createDraftBox(
      name: name,
      description: '自定义草稿箱',
    );
    
    // 选中新创建的草稿箱
    selectedBoxId.value = newBox.id;
    
    // 隐藏表单
    showNewBoxForm.value = false;
    _newBoxNameController.clear();
  }

  // 确认选择
  void _confirmSelection() {
    if (selectedBoxId.value.isEmpty) {
      Get.snackbar('提示', '请选择一个草稿箱');
      return;
    }
    
    // 返回选中的草稿箱ID
    Navigator.pop(context, {
      'boxId': selectedBoxId.value,
    });
  }

  // 获取草稿箱颜色
  Color _getBoxColor(String? colorStr) {
    if (colorStr == null || colorStr.isEmpty) {
      return Colors.blue;
    }
    
    try {
      return Color(int.parse(colorStr.replaceAll('#', '0xFF')));
    } catch (e) {
      return Colors.blue;
    }
  }

  // 获取草稿箱图标
  IconData _getBoxIcon(String? iconStr) {
    if (iconStr == null || iconStr.isEmpty) {
      return Icons.folder;
    }
    
    switch (iconStr) {
      case 'folder':
        return Icons.folder;
      case 'description':
        return Icons.description;
      case 'videocam':
        return Icons.videocam;
      case 'image':
        return Icons.image;
      case 'favorite':
        return Icons.favorite;
      case 'star':
        return Icons.star;
      default:
        return Icons.folder;
    }
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return '刚刚';
        }
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }
}