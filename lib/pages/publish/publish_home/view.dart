import 'package:aitoearn_app/pages/account_manager/account_list_page.dart';
import 'package:aitoearn_app/pages/publish/hotspot/view.dart';
import 'package:aitoearn_app/pages/publish/publish_home/logic.dart';
import 'package:aitoearn_app/pages/publish/publish_record/view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lazy_indexed_stack/flutter_lazy_indexed_stack.dart';
import 'package:get/get.dart';

class PublishHomePage extends StatefulWidget {
  const PublishHomePage({super.key});

  @override
  State<PublishHomePage> createState() => _PublishHomePageState();
}

class _PublishHomePageState extends State<PublishHomePage> {
  final PublishHomeLogic logic = Get.put(PublishHomeLogic());

  final List<Widget> _pages = [
    const PublishRecordPage(),
    const HotspotPage(),
    const AccountListPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => LazyIndexedStack(index: logic.currentIndex.value, children: _pages),
    );
  }

  @override
  void dispose() {
    Get.delete<PublishHomeLogic>();
    super.dispose();
  }
}
