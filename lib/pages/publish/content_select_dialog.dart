import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/pages/publish/video_publish_binding.dart';
import 'package:aitoearn_app/pages/publish/video_publish_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 内容选择弹窗
class ContentSelectDialog extends StatelessWidget {
  const ContentSelectDialog({super.key});

  /// 显示内容选择弹窗
  static void show(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => const ContentSelectDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          _buildTitleBar(),
          
          // 选项列表
          _buildOptionsList(context),
          
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
  
  // 标题栏
  Widget _buildTitleBar() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.black12, width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              '取消',
              style: TextStyle(
                fontSize: 16,
                color: Colors.black54,
              ),
            ),
          ),
          const Text(
            '选择内容',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          // 右侧占位，保持标题居中
          const SizedBox(width: 70),
        ],
      ),
    );
  }
  
  // 选项列表
  Widget _buildOptionsList(BuildContext context) {
    // 确保已注入PublishLogic
    final PublishLogic logic = Get.find<PublishLogic>();
    // 判断当前是否已在发布页面
    final bool isInPublishPage = context.findAncestorWidgetOfExactType<VideoPublishPage>() != null;
    
    return Column(
      children: [
        _buildOptionItem(
          title: '从相册选择图片',
          onTap: () async {
            Get.back(); // 先关闭弹窗
            
            // 使用PublishLogic的pickMultiImages方法选择多张图片
            await logic.pickMultiImages();
            
            // 只有当不在发布页面时，才打开新的发布页面
            if (logic.state.selectedImages.isNotEmpty && !isInPublishPage) {
              _openPublishPage('album');
            }
          },
        ),
        _buildOptionItem(
          title: '从相册选择视频',
          onTap: () async {
            Get.back();
            
            // 使用PublishLogic的pickVideoFromGallery方法选择单个视频
            await logic.pickVideoFromGallery();
            
            // 只有当不在发布页面时，才打开新的发布页面
            if (logic.state.videoPath.value.isNotEmpty && !isInPublishPage) {
              _openPublishPage('video');
            }
          },
        ),
        _buildOptionItem(
          title: '草稿箱',
          onTap: () {
            Get.back();
            // 如果不在发布页面，才打开新的发布页面
            if (!isInPublishPage) {
              Get.toNamed('/draftBoxes');
            } else {
              // 在发布页面内，直接导航到草稿箱
              Get.toNamed('/draftBoxes');
            }
          },
        ),
        // 底部指示器
        Container(
          margin: const EdgeInsets.symmetric(vertical: 16),
          width: 40,
          height: 5,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.2),
            borderRadius: BorderRadius.circular(2.5),
          ),
        ),
      ],
    );
  }
  
  // 选项项
  Widget _buildOptionItem({
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Center(
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
        ),
      ),
    );
  }
  
  // 打开发布页面
  void _openPublishPage(String sourceType) {
    // 先进行依赖注入
    VideoPublishBinding().dependencies();
    
    // 打开视频发布页面
    Get.to(() => const VideoPublishPage(), arguments: {'sourceType': sourceType});
  }
} 