import 'dart:io';

import 'package:aitoearn_app/pages/account_manager/account_list_page.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_boxes_page.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/pages/publish/state.dart';
import 'package:aitoearn_app/pages/publish/video_publish_binding.dart';
import 'package:aitoearn_app/pages/publish/video_publish_page.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_demo_entry.dart';
import 'package:aitoearn_app/plat_core/plats/plat_kwai/kwai_test_page.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_test_page.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PublishPage extends StatefulWidget {
  const PublishPage({super.key});

  @override
  State<PublishPage> createState() => _PublishPageState();
}

class _PublishPageState extends State<PublishPage> {
  final PublishLogic logic = Get.put(PublishLogic());
  final PublishState state = Get.find<PublishLogic>().state;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('平台测试'),
          backgroundColor: Colors.purple,
          foregroundColor: Colors.white,
          actions: [
            // 添加测试登录按钮
            IconButton(
              icon: const Icon(Icons.login),
              tooltip: '测试登录',
              onPressed: () {
                Get.toNamed(AppRouter.testLoginPath);
              },
            ),
            // 添加抖音POI测试按钮
            IconButton(
              icon: const Icon(Icons.location_on),
              tooltip: '抖音POI测试',
              onPressed: () => Get.toNamed(AppRouter.douyinPoiTestPage),
            ),
            // 添加草稿箱按钮
            IconButton(
              icon: const Icon(Icons.folder_special),
              tooltip: '草稿箱',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DraftBoxesPage(),
                  ),
                );
              },
            ),
            // 添加账号管理按钮
            IconButton(
              icon: const Icon(Icons.account_circle),
              tooltip: '账号管理',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AccountListPage(),
                  ),
                );
              },
            ),
          ],
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 图片选择和预览区域
                _buildImagePickerSection(),

                const SizedBox(height: 24),

                // 草稿箱入口卡片
                _buildPlatformCard(
                  context,
                  title: '草稿箱管理',
                  description: '查看和管理已保存的草稿，包括图片、视频和文本内容',
                  icon: Icons.folder_special,
                  color: Colors.amber,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DraftBoxesPage(),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 16),

                _buildPlatformCard(
                  context,
                  title: '抖音测试',
                  description: '测试抖音平台的各项功能，包括热点、活动、话题、发布等',
                  icon: Icons.music_note,
                  color: Colors.blue,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DouyinDemoEntry(),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 16),

                _buildPlatformCard(
                  context,
                  title: '快手测试',
                  description: '测试快手平台的视频发布功能',
                  icon: Icons.play_circle_fill,
                  color: Colors.red,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const KwaiTestPage(),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 16),

                _buildPlatformCard(
                  context,
                  title: '小红书测试',
                  description: '测试小红书平台的图文和视频发布功能',
                  icon: Icons.book,
                  color: Colors.pink,
                  onTap: () {},
                ),

                const SizedBox(height: 16),

                _buildPlatformCard(
                  context,
                  title: '微信视频号测试',
                  description: '测试微信视频号的视频发布功能',
                  icon: Icons.video_call,
                  color: Colors.green,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WxSphTestPage(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        // 添加浮动操作按钮，快速进入草稿箱
        floatingActionButton: SizedBox(
          height: 45,
          width: 45,
          child: FloatingActionButton(
            onPressed: () {
              // 打开视频发布页面前先进行绑定
              VideoPublishBinding().dependencies();
              Get.to(() => const VideoPublishPage());
            },
            backgroundColor: Colors.blue,
            heroTag: 'publish_fab',
            tooltip: '添加发布',
            child: const Icon(Icons.add, size: 28),
          ),
        ),
      ),
    );
  }

  // 构建图片选择和预览区域
  Widget _buildImagePickerSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '图片选择',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                // 添加保存到草稿箱的按钮
                TextButton.icon(
                  onPressed: () async {
                    if (state.selectedImages.isEmpty) {
                      ScaffoldMessenger.of(
                        context,
                      ).showSnackBar(const SnackBar(content: Text('请先选择图片')));
                      return;
                    }

                    // 保存到草稿箱
                    final result = await logic.saveImagesToDraft();

                    if (result) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('已保存到草稿箱'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('保存失败'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  icon: const Icon(Icons.save),
                  label: const Text('存草稿'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(
              () =>
                  state.selectedImages.isEmpty
                      ? _buildImagePickerButton()
                      : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildImageGrid(),
                          const SizedBox(height: 16),
                          _buildImagePickerButton(),
                        ],
                      ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建图片选择按钮
  Widget _buildImagePickerButton() {
    return InkWell(
      onTap: () async {
        await logic.pickMultiImages();
      },
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate,
              size: 40,
              color: Colors.grey.shade600,
            ),
            const SizedBox(height: 8),
            Text('从相册选择', style: TextStyle(color: Colors.grey.shade600)),
          ],
        ),
      ),
    );
  }

  // 构建图片网格
  Widget _buildImageGrid() {
    return Obx(
      () => GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: state.selectedImages.length,
        itemBuilder: (context, index) {
          return _buildImageItem(state.selectedImages[index], index);
        },
      ),
    );
  }

  // 构建单个图片项
  Widget _buildImageItem(File image, int index) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.file(
            image,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => logic.removeImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.black54,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, size: 16, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPlatformCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 32),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.grey.shade400),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    Get.delete<PublishLogic>();
    super.dispose();
  }
}
