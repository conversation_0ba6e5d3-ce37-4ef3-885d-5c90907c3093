import 'package:aitoearn_app/api/hot/hot_api.dart';
import 'package:aitoearn_app/api/hot/models/hot_platform_model.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_event/state.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:get/get.dart';

class HotEventLogic extends GetxController {
  final HotEventState state = HotEventState();

  /// 获取热点事件数据
  getHotEventData() async {
    var res = await getAllHotTopics();
    if (res == null) {
      return showError('networkError'.tr);
    }
    List<HotPlatformModel> platData = [];
    for (var topic in res) {
      state.platEventMap.value = {
        ...state.platEventMap.value,
        topic.platform.id: topic.topics,
      };
      platData.add(topic.platform);
    }
    state.platData.value = platData;
    state.currentPlat.value = platData[0].id;
  }

  /// 切换平台
  changePlat(String platId) {
    state.currentPlat.value = platId;
  }
}
