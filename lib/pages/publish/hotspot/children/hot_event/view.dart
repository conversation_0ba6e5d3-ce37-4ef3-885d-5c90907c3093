import 'package:aitoearn_app/api/hot/models/hot_event_model.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_event/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_event/state.dart';
import 'package:aitoearn_app/pages/publish/hotspot/utils/hotspot_utils.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_plat_select.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HotEventComponent extends StatefulWidget {
  const HotEventComponent({super.key});

  @override
  State<HotEventComponent> createState() => _HotEventComponentState();
}

class _HotEventComponentState extends State<HotEventComponent> {
  final HotEventLogic logic = Get.put(HotEventLogic());
  final HotEventState state = Get.find<HotEventLogic>().state;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    logic.getHotEventData();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(
          () => HotPlatSelect(
            onChangeHotConPlat: (platId) {
              logic.changePlat(platId);
              // 切换平台后滚动到顶部
              if (_scrollController.hasClients) {
                _scrollController.animateTo(
                  0.0,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              }
            },
            hotConCurrentPlat: state.currentPlat.value,
            hotConPlatData: state.platData.value,
          ),
        ),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Obx(() {
              var data = state.platEventMap.value[state.currentPlat.value];
              return Skeletonizer(
                enabled: data == null,
                child: ListView.builder(
                  controller: _scrollController,
                  padding: EdgeInsets.zero,
                  itemCount: data?.length ?? 4,
                  itemBuilder: (context, index) {
                    Topics? topic = data?[index];

                    return Container(
                      margin: const EdgeInsets.only(bottom: 10),
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey.shade300,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: InkWell(
                        onTap: () {
                          skipHotDetails(topic?.url);
                        },
                        child: _buildTopicItem(topic, index),
                      ),
                    );
                  },
                ),
              );
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildTopicItem(Topics? topic, int index) {
    return Row(
      children: [
        // 序号
        _buildRankCircle(index),
        Gaps.hGap10,
        // 标题和热度
        Expanded(flex: 2, child: _buildTitleAndHot(topic)),
        Gaps.hGap10,
        // 折线图
        _buildMiniLineChart(topic),
      ],
    );
  }

  Widget _buildRankCircle(int index) {
    return Container(
      width: 30,
      height: 30,
      decoration: BoxDecoration(
        color: AppColors.primaryColor,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Center(
        child: (index + 1).toString().toNormalText(
          color: Colors.white,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTitleAndHot(Topics? topic) {
    String title = topic?.title ?? '热点标题';
    String hotValue = "${topic?.hotValue ?? '0'}w";

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.toNormalText(
          fontSize: Dimens.font_sp14,
          fontWeight: FontWeight.w600,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        Gaps.vGap4,
        hotValue.toNormalText(
          color: Colors.red,
          fontSize: Dimens.font_sp12,
          fontWeight: FontWeight.w500,
        ),
      ],
    );
  }

  // 使用 fl_chart 和真实数据实现迷你折线图
  Widget _buildMiniLineChart(Topics? topic) {
    if (topic == null) {
      // 骨架屏状态下的占位符
      return Container(
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(4),
        ),
      );
    }

    List<FlSpot> spots = _getHotValueSpots(topic.hotValueHistory);

    // 如果没有历史数据，不显示折线图
    if (spots.isEmpty) {
      return const SizedBox.shrink();
    }

    double minY = spots.map((e) => e.y).reduce((a, b) => a < b ? a : b);
    double maxY = spots.map((e) => e.y).reduce((a, b) => a > b ? a : b);

    // 如果最大值和最小值相等，给一点范围
    if (minY == maxY) {
      minY = maxY * 0.9;
      maxY = maxY * 1.1;
    }

    return Expanded(
      flex: 1,
      child: SizedBox(
        height: 50,
        child: LineChart(
          LineChartData(
            gridData: const FlGridData(show: false),
            titlesData: const FlTitlesData(show: false),
            borderData: FlBorderData(show: false),
            minX: 0,
            maxX: (spots.length - 1).toDouble(),
            minY: minY,
            maxY: maxY,
            lineBarsData: [
              LineChartBarData(
                spots: spots,
                isCurved: true,
                color: Colors.red,
                barWidth: 2,
                isStrokeCapRound: true,
                dotData: const FlDotData(show: false),
                belowBarData: BarAreaData(
                  show: true,
                  color: Colors.red.withValues(alpha: 0.1),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 根据真实的 hotValueHistory 数据生成图表点
  List<FlSpot> _getHotValueSpots(List<HotValueHistory>? hotValueHistory) {
    if (hotValueHistory == null || hotValueHistory.isEmpty) {
      return [];
    }

    List<FlSpot> spots = [];
    for (int i = 0; i < hotValueHistory.length; i++) {
      double hotValue = hotValueHistory[i].hotValue ?? 0;
      spots.add(FlSpot(i.toDouble(), hotValue));
    }

    return spots;
  }

  @override
  void dispose() {
    Get.delete<HotEventLogic>();
    _scrollController.dispose();
    super.dispose();
  }
}
