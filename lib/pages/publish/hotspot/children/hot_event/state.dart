import 'package:aitoearn_app/api/hot/models/hot_event_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_platform_model.dart';
import 'package:get/get.dart';

class HotEventState {
  /// 热点事件的平台数据
  Rx<List<HotPlatformModel>?> platData = Rx<List<HotPlatformModel>?>(null);

  /// key=平台ID val=该平台的热点事件数据
  Rx<Map<String, List<Topics>?>> platEventMap = Rx(<String, List<Topics>?>{});

  /// 当前选择的平台ID
  var currentPlat = ''.obs;
}
