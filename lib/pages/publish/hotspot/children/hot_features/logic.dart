import 'package:aitoearn_app/api/hot/hot_api.dart';
import 'package:aitoearn_app/api/hot/models/get_all_topics_params_model.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_features/state.dart';
import 'package:aitoearn_app/pages/publish/hotspot/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/state.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:get/get.dart';

class HotFeaturesLogic extends GetxController {
  final HotFeaturesState state = HotFeaturesState();

  /// 这里使用热门内容的榜单数据
  final HotspotState hotConState = Get.find<HotspotLogic>().state;
  final HotspotLogic hotConLogic = Get.put(HotspotLogic());

  HotFeaturesLogic() {
    if (hotConState.hotConPlatData.value != null) {
      state.currentPlat.value = hotConState.hotConPlatData.value![0].id;
    } else {
      ever(hotConState.hotConPlatData, (platData) {
        if (state.currentPlat.value == '') {
          state.currentPlat.value = platData![0].id;
        }
      });
    }
  }

  /// 获取专题数据列表
  getTopicData({bool isRefresh = false}) async {
    if (state.isLoading.value) {
      return;
    }

    if (isRefresh) {
      state.currentPage.value = 1;
      state.hasMore.value = true;
      state.topicDataList.value = null;
    }

    if (!state.hasMore.value && !isRefresh) {
      return;
    }

    state.isLoading.value = true;

    var res = await getAllTopics(
      GetAllTopicsParamsModel(
        page: state.currentPage.value,
        pageSize: state.pageSize,
        msgType: state.currentTopicType.value,
        timeType: state.currentTimeRange.value,
        platformId: state.currentPlat.value,
      ),
    );

    state.isLoading.value = false;

    if (res == null) {
      if (isRefresh) {
        showError('networkError'.tr);
        state.topicDataList.value = [];
      }
      return;
    }

    if (isRefresh || state.topicDataList.value == null) {
      state.topicDataList.value = res.items;
    } else {
      state.topicDataList.value = [...state.topicDataList.value!, ...res.items];
    }

    state.hasMore.value = res.items.length == state.pageSize;
    if (state.hasMore.value) {
      state.currentPage.value++;
    }
  }

  /// 加载更多
  loadMore() async {
    await getTopicData(isRefresh: false);
  }

  /// 刷新
  Future<void> refreshList() async {
    await getTopicData(isRefresh: true);
  }

  /// 获取时间范围
  getTimeRange() async {
    var res = await getTopicTimeTypes('AIGC');
    if (res != null) {
      state.timeRangeList.value = res;
      state.currentTimeRange.value = res[0];
    }
  }

  /// 获取专题类型
  getTopicType() async {
    var res = await getMsgType();
    if (res != null) {
      state.topicTypeList.value = res;
      state.currentTopicType.value = res[0];
    }
  }

  /// 切换平台
  changePlat(String platId) async {
    state.currentPlat.value = platId;
    state.topicDataList.value = null;
    await hotConLogic.getPlatRanking(platId);
    refreshList();
  }

  /// 切换时间范围
  changeTimeRange(String timeRange) {
    state.currentTimeRange.value = timeRange;
    state.topicDataList.value = null;
    refreshList();
  }

  /// 切换专题类型
  changeTopicType(String topicType) {
    state.currentTopicType.value = topicType;
    state.topicDataList.value = null;
    refreshList();
  }
}
