import 'package:aitoearn_app/api/hot/models/hot_topics_module.dart';
import 'package:get/get.dart';

class HotFeaturesState {
  /// 分页
  final currentPage = 1.obs;

  /// 页数
  final pageSize = 20;

  /// 分页加载
  final isLoading = false.obs;

  /// 是否有更多数据
  final hasMore = true.obs;

  /// 当前选择的平台
  final currentPlat = ''.obs;

  /// 当前选择的时间范围
  final currentTimeRange = ''.obs;

  /// 当前选择的专题类型
  final currentTopicType = ''.obs;

  /// 时间范围
  Rx<List<String>?> timeRangeList = Rx(null);

  /// 专题类型
  Rx<List<String>?> topicTypeList = Rx(null);

  /// 专题数据
  Rx<List<Items>?> topicDataList = Rx(null);
}
