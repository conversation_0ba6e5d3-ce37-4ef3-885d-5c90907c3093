import 'package:aitoearn_app/api/hot/models/hot_topics_module.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_features/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_features/state.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_features/widgets/hot_features_options.dart';
import 'package:aitoearn_app/pages/publish/hotspot/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/state.dart';
import 'package:aitoearn_app/pages/publish/hotspot/utils/hotspot_utils.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_con_list.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_plat_select.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/load_more_indicator.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HotFeaturesComponent extends StatefulWidget {
  const HotFeaturesComponent({super.key});

  @override
  State<HotFeaturesComponent> createState() => _HotFeaturesComponentState();
}

class _HotFeaturesComponentState extends State<HotFeaturesComponent> {
  final HotFeaturesLogic logic = Get.put(HotFeaturesLogic());
  final HotFeaturesState state = Get.find<HotFeaturesLogic>().state;
  final HotspotState hotConState = Get.find<HotspotLogic>().state;

  @override
  void initState() {
    super.initState();
    initData();
  }

  initData() async {
    await logic.getTimeRange();
    await logic.getTopicType();
    logic.getTopicData(isRefresh: true);
  }

  // 构建内容信息
  Widget _buildContentInfo(Items? item) {
    return Row(
      children: [
        Flexible(
          child: (item?.title ?? '标题...........').toNormalText(
            fontWeight: FontWeight.bold,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // 构建作者信息（头像、姓名、时间）
  Widget _buildAuthorInfo(Items? item) {
    return Row(
      children: [
        // 头像
        Container(
          width: 28,
          height: 28,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(14)),
          child: NetworkImageWidget(
            '${item?.avatar}',
            width: 28,
            height: 28,
            borderRadius: BorderRadius.circular(14),
            placeholder: Container(
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(14),
              ),
            ),
          ),
        ),
        Gaps.hGap8,
        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              (item?.author ?? '用户名').toNormalText(
                fontSize: Dimens.font_sp10,
                fontWeight: FontWeight.w500,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Gaps.vGap4,
              (item?.updateTime ?? '1天前').toNormalText(
                fontSize: Dimens.font_sp8,
                color: Colors.grey[600]!,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 作品信息
  Widget _buildWorksInfo(Items? item) {
    return Flexible(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween, // 改为 spaceBetween
        children: [
          // 收藏
          worksInfoIcon(
            Icons.favorite_border,
            item == null,
            item?.collectCount,
          ),
          // 评论
          worksInfoIcon(
            Icons.comment_outlined,
            item == null,
            item?.commentCount,
          ),
          // 点赞
          worksInfoIcon(Icons.favorite_outline, item == null, item?.likeCount),
          // 分享
          worksInfoIcon(Icons.share_outlined, item == null, item?.shareCount),
        ],
      ),
    );
  }

  _buildHotItem(Items? item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // 左侧图片
          Container(
            width: 120,
            height: 90,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            child:
                item?.cover != null
                    ? NetworkImageWidget(
                      '${item?.cover}',
                      width: 120,
                      height: 90,
                      borderRadius: BorderRadius.circular(8),
                      placeholder: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    )
                    : Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.image,
                        color: Colors.grey[500],
                        size: 24,
                      ),
                    ),
          ),
          const SizedBox(width: 12),
          // 右侧文本
          Expanded(
            child: SizedBox(
              height: 90,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            _buildContentInfo(item),
                            Gaps.vGap8,
                            _buildAuthorInfo(item),
                          ],
                        ),
                      ),
                    ],
                  ),
                  _buildWorksInfo(item),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(
          () => HotPlatSelect(
            onChangeHotConPlat: logic.changePlat,
            hotConCurrentPlat: state.currentPlat.value,
            hotConPlatData: hotConState.hotConPlatData.value,
          ),
        ),

        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Column(
              children: [
                const HotFeaturesOptions(),
                Gaps.vGap8,

                Expanded(
                  child: Obx(() {
                    if (state.topicDataList.value != null &&
                        state.topicDataList.value!.isEmpty) {
                      return RefreshIndicator(
                        onRefresh: logic.refreshList,
                        child: ListView(
                          children: [
                            const SizedBox(height: 200),
                            Center(
                              child: 'noData'.tr.toNormalText(
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return Skeletonizer(
                      enabled: state.topicDataList.value == null,
                      child: RefreshIndicator(
                        onRefresh: logic.refreshList,
                        child: NotificationListener<ScrollNotification>(
                          onNotification: (ScrollNotification scrollInfo) {
                            // 滚动到底部自动加载
                            if (scrollInfo.metrics.pixels ==
                                scrollInfo.metrics.maxScrollExtent) {
                              if (state.hasMore.value &&
                                  !state.isLoading.value) {
                                logic.loadMore();
                              }
                            }
                            return false;
                          },
                          child: ListView.builder(
                            padding: EdgeInsets.zero,
                            itemCount:
                                (state.topicDataList.value?.length ?? 5) + 1,
                            itemBuilder: (context, index) {
                              if (index ==
                                  (state.topicDataList.value?.length ?? 5)) {
                                return _buildLoadMoreIndicator();
                              }

                              final item =
                                  state.topicDataList.value?.isNotEmpty == true
                                      ? state.topicDataList.value![index]
                                      : null;

                              return InkWell(
                                onTap: () {
                                  skipHotDetails(item?.url);
                                },
                                child: _buildHotItem(item),
                              );
                            },
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 添加加载更多指示器
  Widget _buildLoadMoreIndicator() {
    return Obx(
      () => LoadMoreIndicator(
        isLoading: state.isLoading.value,
        hasMore: state.hasMore.value,
        visible: state.topicDataList.value != null,
      ),
    );
  }

  @override
  void dispose() {
    Get.delete<HotFeaturesLogic>();
    super.dispose();
  }
}
