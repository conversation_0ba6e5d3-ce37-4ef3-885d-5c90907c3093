import 'package:aitoearn_app/pages/publish/hotspot/children/hot_features/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_features/state.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/dropdown/common_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 热门专题 列表的筛选
class HotFeaturesOptions extends StatefulWidget {
  const HotFeaturesOptions({super.key});

  @override
  _HotFeaturesOptionsState createState() => _HotFeaturesOptionsState();
}

class _HotFeaturesOptionsState extends State<HotFeaturesOptions> {
  final HotFeaturesLogic logic = Get.put(HotFeaturesLogic());
  final HotFeaturesState state = Get.find<HotFeaturesLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,

      children: [
        // 时间范围
        Obx(
          () => CommonDropdown(
            items: state.timeRangeList.value,
            value: state.currentTimeRange.value,
            onChanged: (String? value) {
              logic.changeTimeRange(value!);
            },
            isLoading: state.timeRangeList.value == null,
          ),
        ),

        Gaps.hGap15,

        // 专题类型
        Obx(
          () => CommonDropdown(
            items: state.topicTypeList.value,
            value: state.currentTopicType.value,
            onChanged: (String? value) {
              logic.changeTopicType(value!);
            },
            isLoading: state.topicTypeList.value == null,
          ),
        ),
      ],
    );
  }
}
