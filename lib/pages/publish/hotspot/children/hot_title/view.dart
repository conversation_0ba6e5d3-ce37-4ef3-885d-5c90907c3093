import 'package:aitoearn_app/api/hot/models/hot_title_model.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_title/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_title/state.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_classify_select.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_plat_select.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_title_item.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HotTitleComponent extends StatefulWidget {
  const HotTitleComponent({super.key});

  @override
  State<HotTitleComponent> createState() => _HotTitleComponentState();
}

class _HotTitleComponentState extends State<HotTitleComponent> {
  final HotTitleLogic logic = Get.put(HotTitleLogic());
  final HotTitleState state = Get.find<HotTitleLogic>().state;

  @override
  void initState() {
    super.initState();
    logic.getAllPlatData();
  }

  /// 爆款标题块
  Widget hotTitleBlock(HotTitleTopBModel? data) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部标题
          Padding(
            padding: const EdgeInsets.all(16),
            child: (data?.category ?? 'title........').toNormalText(
              fontSize: Dimens.font_sp16,
              fontWeight: FontWeight.w600,
              color: AppColors.textColor,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // 列表
          Column(
            children:
                data?.titles != null
                    ? data!.titles.asMap().entries.map((entry) {
                      int index = entry.key;
                      Items item = entry.value;
                      return HotTitleItem(item: item, index: index);
                    }).toList()
                    : [1, 2, 3, 4].map((index) {
                      return HotTitleItem(index: index);
                    }).toList(),
          ),
          // 查看更多按钮
          CustomButton(
            onPressed: () {
              Get.toNamed(
                AppRouter.hotTitleMorePath,
                arguments: {
                  'category': data?.category,
                  'platformId': state.currentPlat.value,
                },
              );
            },
            text: 'hot_seeMore'.tr,
            fontSize: Dimens.font_sp14,
            textColor: AppColors.primaryColor,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(
          () => HotPlatSelect(
            onChangeHotConPlat: logic.changePlat,
            hotConCurrentPlat: state.currentPlat.value,
            hotConPlatData: state.allPlatData.value,
          ),
        ),

        Expanded(
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Column(
              children: [
                Obx(() {
                  var data =
                      state.platCategoryMap.value[state.currentPlat.value];

                  return Skeletonizer(
                    enabled: data == null,
                    child: HotClassifySelect(
                      classifyList: data ?? [],
                      currentClassify: '',
                      onClassifyChange: (val) {
                        Get.toNamed(
                          AppRouter.hotTitleMorePath,
                          arguments: {
                            'category': val,
                            'platformId': state.currentPlat.value,
                          },
                        );
                      },
                    ),
                  );
                }),

                Gaps.vGap8,

                Expanded(
                  child: Obx(() {
                    var list =
                        state.topPlatDataMap.value[state.currentPlat.value];

                    return Skeletonizer(
                      enabled: list == null,
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        itemCount: list?.length ?? 4,
                        itemBuilder: (context, index) {
                          var data = list?[index];

                          return hotTitleBlock(data);
                        },
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    Get.delete<HotTitleLogic>();
    super.dispose();
  }
}
