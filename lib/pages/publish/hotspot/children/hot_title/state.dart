import 'package:aitoearn_app/api/hot/models/hot_platform_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_title_model.dart';
import 'package:get/get.dart';

class HotTitleState {
  /// 爆款标题的所有标签前五条数据Map key=平台Id value=数据
  final topPlatDataMap = Rx(<String, List<HotTitleTopBModel>>{});

  /// 爆款标题所有平台
  final allPlatData = Rx<List<HotPlatformModel>?>(null);

  /// 爆款标题分类 key=平台ID value=分类数据
  final platCategoryMap = Rx(<String, List<String>>{});

  /// 爆款标题当前选择的平台
  final currentPlat = ''.obs;
}
