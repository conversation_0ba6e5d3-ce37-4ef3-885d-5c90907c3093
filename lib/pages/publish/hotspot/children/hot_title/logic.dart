import 'package:aitoearn_app/api/hot/hot_api.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_title/state.dart';
import 'package:get/get.dart';

class HotTitleLogic extends GetxController {
  final HotTitleState state = HotTitleState();

  /// 获取爆款标题的所有平台数据
  getAllPlatData() async {
    var res = await findPlatformsWithData();
    if (res != null) {
      state.allPlatData.value = res;
      // 默认选择第一个平台
      if (res.isNotEmpty) {
        changePlat(res[0].id);
      }
    }
  }

  /// 切换平台
  changePlat(String platId) async {
    if (state.currentPlat.value == platId) {
      return;
    }
    state.currentPlat.value = platId;

    /// 获取该平台的分类数据
    if (!state.platCategoryMap.value.containsKey(platId)) {
      var categoriesRes = await findCategoriesByPlatform(platId);
      if (categoriesRes != null) {
        state.platCategoryMap.value = {
          ...state.platCategoryMap.value,
          platId: categoriesRes,
        };
      }
    }

    /// 获取该平台的前五条数据
    var topDataRes = await findTopByPlatformAndCategories(platId);
    if (!state.topPlatDataMap.value.containsKey(platId)) {
      if (topDataRes != null) {
        state.topPlatDataMap.value = {
          ...state.topPlatDataMap.value,
          platId: topDataRes,
        };
      }
    }
  }
}
