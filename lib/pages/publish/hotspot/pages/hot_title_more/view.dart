import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_title/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_title/state.dart';
import 'package:aitoearn_app/pages/publish/hotspot/pages/hot_title_more/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/pages/hot_title_more/state.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_classify_select.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_title_item.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HotTitleMorePage extends StatefulWidget {
  const HotTitleMorePage({super.key});

  @override
  State<HotTitleMorePage> createState() => _HotTitleMorePageState();
}

class _HotTitleMorePageState extends State<HotTitleMorePage> {
  final HotTitleMoreLogic logic = Get.put(HotTitleMoreLogic());
  final HotTitleMoreState state = Get.find<HotTitleMoreLogic>().state;
  final HotTitleState hotTitleState = Get.find<HotTitleLogic>().state;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => BasePage(
        title: '${'hot_title'.tr} - ${state.category.value}',
        body: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.only(left: 15),
                    child: Obx(() {
                      var data =
                          hotTitleState.platCategoryMap.value[hotTitleState
                              .currentPlat
                              .value];

                      return HotClassifySelect(
                        classifyList: data ?? [],
                        currentClassify: state.category.value,
                        onClassifyChange: (val) {
                          if (val == '') {
                            Get.back();
                            return;
                          }
                          state.category.value = val;
                          logic.getData();
                        },
                      );
                    }),
                  ),
                ],
              ),

              Gaps.vGap10,

              Expanded(
                child: Obx(
                  () => Skeletonizer(
                    enabled: state.data.value == null,
                    child: ListView.builder(
                      itemCount: state.data.value?.length ?? 10,
                      itemBuilder: (context, index) {
                        final item = state.data.value?[index];
                        return HotTitleItem(index: index, item: item);
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    Get.delete<HotTitleMoreLogic>();
    super.dispose();
  }
}
