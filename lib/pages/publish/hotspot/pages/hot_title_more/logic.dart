import 'package:aitoearn_app/api/hot/hot_api.dart';
import 'package:aitoearn_app/pages/publish/hotspot/pages/hot_title_more/state.dart';
import 'package:get/get.dart';

class HotTitleMoreLogic extends GetxController {
  final HotTitleMoreState state = HotTitleMoreState();

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments is Map) {
      state.category.value = Get.arguments['category'] ?? '';
      state.platformId.value = Get.arguments['platformId'] ?? '';

      getData();
    }
  }

  /// 获取数据
  getData() async {
    state.data.value = null;
    var res = await findByPlatformAndCategory(
      state.platformId.value,
      state.category.value,
      1,
      200,
    );
    if (res != null) {
      state.data.value = res.items;
    }
  }
}
