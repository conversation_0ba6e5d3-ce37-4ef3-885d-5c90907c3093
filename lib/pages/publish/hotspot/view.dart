import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_event/view.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_features/view.dart';
import 'package:aitoearn_app/pages/publish/hotspot/children/hot_title/view.dart';
import 'package:aitoearn_app/pages/publish/hotspot/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/state.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/build_topic_select.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_con_list.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_plat_select.dart';
import 'package:aitoearn_app/pages/publish/widgets/tabs_calendar.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/calendar/view.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lazy_indexed_stack/flutter_lazy_indexed_stack.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

Widget? _buildDefault(BuildContext context, DateTime day, DateTime focusedDay) {
  return day.day
      .toString()
      .toNormalText(
        color: Colors.white, // 设置普通单元格文字为白色
        fontSize: 14,
        fontWeight: FontWeight.bold,
      )
      .center()
      .width(30)
      .height(30)
      .center();
}

Widget? _buildToday(BuildContext context, DateTime day, DateTime focusedDay) {
  return day.day
      .toString()
      .toNormalText(
        color: AppColors.primaryHighLightColor,
        fontWeight: FontWeight.bold,
      )
      .center()
      .width(30)
      .height(30)
      .center();
}

Widget? _buildSelected(
  BuildContext context,
  DateTime day,
  DateTime focusedDay,
) {
  return day.day
      .toString()
      .toNormalText(color: AppColors.primaryColor)
      .center()
      .decorated(color: Colors.white, borderRadius: BorderRadius.circular(5))
      .width(30)
      .height(30)
      .center();
}

class HotspotPage extends StatefulWidget {
  const HotspotPage({super.key});

  @override
  State<HotspotPage> createState() => _HotspotPageState();
}

class _HotspotPageState extends State<HotspotPage> {
  final HotspotLogic logic = Get.put(HotspotLogic());
  final HotspotState state = Get.find<HotspotLogic>().state;

  @override
  void initState() {
    super.initState();
    logic.getHotConPlatData();
  }

  /// 热门内容
  Widget hotConBuild() {
    return Column(
      children: [
        Obx(
          () => HotPlatSelect(
            onChangeHotConPlat: logic.changeHotConPlat,
            hotConCurrentPlat: state.hotConCurrentPlat.value,
            hotConPlatData: state.hotConPlatData.value,
          ),
        ),
        const HotConList(),
      ],
    );
  }

  /// 爆款标题
  Widget hotTitleBuild() {
    return const Column(children: []);
  }

  /// 热点
  Widget hotSpotBuild() {
    return LazyIndexedStack(
      index: state.topicSelected.value,
      children: [
        hotConBuild(),
        const HotEventComponent(),
        const HotFeaturesComponent(),
        const HotTitleComponent(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      useSafeArea: false,
      useDefaultBackground: false,
      backgroundColor: AppColors.backgroundColor,
      statusBarIconBrightness: Brightness.light,

      body: Obx(() {
        var availableDays =
            state.hotConPlatDatesMap.value.containsKey(
                  state.hotConCurrentPlat.value,
                )
                ? state.hotConPlatDatesMap.value[state.hotConCurrentPlat.value]
                    ?.map((e) => DateTime.parse(e.queryDate))
                    .toList()
                : null;

        return CalendarComponent(
          key: state.calendarKey,
          hideCalendar: state.topicSelected.value == 0 ? false : true,
          defaultCalendarFormat: CalendarFormat.week,
          calendarBackgroundImage:
              'assets/images/publish/hotspot/hotspot_back.png',
          onDaySelected:
              (DateTime selectedDay, DateTime focusedDay) =>
                  logic.onDaySelected(selectedDay, focusedDay),
          calendarBuilders: const CalendarBuilders(
            todayBuilder: _buildToday,
            selectedBuilder: _buildSelected,
            defaultBuilder: _buildDefault,
          ),
          daysOfWeekColor: Colors.white,
          topRightWidget: TabsCalendar(
            selectedIndex: 1,
            selectedColor: Colors.white,
            unselectedColor: Colors.white.withValues(alpha: 0.5),
            lineColor: Colors.white,
          ),
          bottomWidget: hotSpotBuild(),
          calendarBottomLineColor: Colors.white,
          calendarBottomExtensionWidget: const BuildTopicSelect(),
          calendarBottomPadding: 65,
          yearMonthColor: Colors.white,
          availableDays: availableDays,
        );
      }),
    );
  }

  @override
  void dispose() {
    Get.delete<HotspotLogic>();
    super.dispose();
  }
}
