import 'package:aitoearn_app/api/hot/models/hot_platform_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_platform_ranking_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_ranking_contents_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_ranking_dates_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class HotspotState {
  /// 热点专题数据
  final topicData = [
    {
      'name': 'hot_content'.tr,
      'backUrl': 'assets/images/publish/hotspot/hot_content.png',
    },
    {
      'name': 'hot_event'.tr,
      'backUrl': 'assets/images/publish/hotspot/hot_event.png',
    },
    {
      'name': 'hot_features'.tr,
      'backUrl': 'assets/images/publish/hotspot/hot_features.png',
    },
    {
      'name': 'hot_title'.tr,
      'backUrl': 'assets/images/publish/hotspot/hot_title.png',
    },
  ];

  final calendarKey = UniqueKey();

  /// 热点选择
  final topicSelected = 0.obs;

  // -------------------------------------- 热门内容 --------------------------------------

  /// 分页
  final hotConCurrentPage = 1.obs;

  /// 页数
  final hotConPageSize = 20;

  /// 分页加载
  final hotConIsLoading = false.obs;

  /// 是否有更多数据
  final hotConHasMore = true.obs;

  /// 热门内容列表数据
  Rx<List<Items>?> hotConListData = Rx<List<Items>?>(null);

  /// 热门内容当前选择的平台
  final hotConCurrentPlat = ''.obs;

  /// 热门内容当前选择的标签
  final hotConCurrentLabel = ''.obs;

  /// 热门内容当前选择的日期
  final hotConCurrentDate = ''.obs;

  /// 热门内容榜单数据 key=平台ID val=该平台的榜单数据
  Rx<Map<String, List<HotPlatformRankingModel>?>> hotConPlatRankingMap = Rx(
    <String, List<HotPlatformRankingModel>?>{},
  );

  /// 热门内容的平台数据
  Rx<List<HotPlatformModel>?> hotConPlatData = Rx<List<HotPlatformModel>?>(
    null,
  );

  /// 热门内容，所有平台的标签 key=平台 val=该平台的标签
  Rx<Map<String, List<String>?>> hotConPlatLabelMap = Rx(
    <String, List<String>?>{},
  );

  /// 热门内容的日期，所有平台的标签 key=平台 val=该平台的可选择日期
  Rx<Map<String, List<HotRankingDatesModel>?>> hotConPlatDatesMap = Rx(
    <String, List<HotRankingDatesModel>?>{},
  );
}
