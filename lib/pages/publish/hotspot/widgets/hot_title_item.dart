import 'package:aitoearn_app/api/hot/models/hot_title_model.dart';
import 'package:aitoearn_app/pages/publish/hotspot/utils/hotspot_utils.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/utils/index.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';

class HotTitleItem extends StatelessWidget {
  final Items? item;
  final int index;

  const HotTitleItem({required this.index, this.item, super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        skipHotDetails(item?.url);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: AppColors.dividerColor, width: 0.5),
          ),
        ),
        child: Row(
          children: [
            // 左边序号和标题
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: (index + 1).toString().toNormalText(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    child: (item?.title ?? 'title.....................')
                        .toNormalText(
                          fontSize: 14,
                          color: AppColors.textColor,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                  ),
                ],
              ),
            ),
            Gaps.hGap10,
            // 右边互动量
            ('互动量${formatNumber(item?.engagement ?? 0)}')
                .toString()
                .toNormalText(
                  fontSize: Dimens.font_sp12,
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
          ],
        ),
      ),
    );
  }
}
