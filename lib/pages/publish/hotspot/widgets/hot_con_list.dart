import 'package:aitoearn_app/api/hot/models/hot_ranking_contents_model.dart';
import 'package:aitoearn_app/pages/publish/hotspot/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/utils/hotspot_utils.dart';
import 'package:aitoearn_app/pages/publish/hotspot/widgets/hot_classify_select.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/utils/dialog/dialog_helper.dart';
import 'package:aitoearn_app/utils/index.dart';
import 'package:aitoearn_app/widgets/load_more_indicator.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

Widget worksInfoIcon(IconData icon, bool loading, int? num) {
  if ((num == null || num <= 0) && loading == false) {
    return const SizedBox();
  }

  return Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      Icon(icon, size: Dimens.font_sp12, color: Colors.grey[600]),
      Gaps.hGap4,
      Text(
        formatNumber(num ?? 0),
        style: TextStyle(fontSize: Dimens.font_sp10, color: Colors.grey[600]),
      ),
    ],
  );
}

/// 热点 - 热门内容列表
class HotConList extends StatefulWidget {
  const HotConList({super.key});

  @override
  _HotConListState createState() => _HotConListState();
}

class _HotConListState extends State<HotConList> {
  final logic = Get.find<HotspotLogic>();
  late final state = logic.state;

  // 构建内容信息（标题和标签）
  Widget _buildContentInfo(Items? item) {
    return Row(
      children: [
        TDTag(
          item?.category ?? '类型',
          isOutline: true,
          theme: TDTagTheme.primary,
          size: TDTagSize.small,
        ),
        Gaps.hGap8,
        Flexible(
          child: (item?.title ?? '标题...........').toNormalText(
            fontWeight: FontWeight.bold,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // 构建作者信息（头像、姓名、时间）
  Widget _buildAuthorInfo(Items? item) {
    return Row(
      children: [
        // 头像
        Container(
          width: 28,
          height: 28,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(14)),
          child: NetworkImageWidget(
            '${item?.author?.avatar}',
            width: 28,
            height: 28,
            borderRadius: BorderRadius.circular(14),
            placeholder: Container(
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(14),
              ),
            ),
          ),
        ),
        Gaps.hGap8,
        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              (item?.author?.name ?? '用户名').toNormalText(
                fontSize: Dimens.font_sp10,
                fontWeight: FontWeight.w500,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Gaps.vGap4,
              (item?.updateTime ?? '1天前').toNormalText(
                fontSize: Dimens.font_sp8,
                color: Colors.grey[600]!,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建互动总数
  Widget _buildInteractionCount(Items? item) {
    var interactionCount =
        item?.anaAdd?.addInteractiveCount ?? item?.collectCount ?? 0;
    if (interactionCount <= 0) {
      return const SizedBox();
    }

    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: TDTheme.of(context).brandColor1,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          (formatNumber(interactionCount)).toNormalText(
            fontWeight: FontWeight.bold,
            fontSize: Dimens.font_sp12,
          ),
          'hot_interactionSum'.tr.toNormalText(
            color: TDTheme.of(context).grayColor7,
            fontSize: Dimens.font_sp8,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // 作品信息
  Widget _buildWorksInfo(Items? item) {
    return Flexible(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 评论
          worksInfoIcon(
            Icons.comment_outlined,
            item == null,
            item?.stats?.commentCount ?? item?.anaAdd?.addCommentCount,
          ),
          // 点赞
          worksInfoIcon(
            Icons.favorite_outline,
            item == null,
            item?.stats?.likeCount ?? item?.anaAdd?.addLikeCount,
          ),
          // 分享
          worksInfoIcon(
            Icons.share_outlined,
            item == null,
            item?.shareCount ?? item?.anaAdd?.addShareCount ?? 0,
          ),
          // 查看
          worksInfoIcon(
            Icons.visibility_outlined,
            item == null,
            item?.stats?.viewCount ?? item?.anaAdd?.addViewCount ?? 0,
          ),
        ],
      ),
    );
  }

  // 列表item
  _buildHotItem(Items? item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          // 左侧图片
          Container(
            width: 120,
            height: 90,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            child:
                item?.cover != null
                    ? NetworkImageWidget(
                      '${item?.cover}',
                      width: 120,
                      height: 90,
                      borderRadius: BorderRadius.circular(8),
                      placeholder: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    )
                    : Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.image,
                        color: Colors.grey[500],
                        size: 24,
                      ),
                    ),
          ),
          const SizedBox(width: 12),
          // 右侧文本
          Expanded(
            child: SizedBox(
              height: 90,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            _buildContentInfo(item),
                            Gaps.vGap8,
                            _buildAuthorInfo(item),
                          ],
                        ),
                      ),
                      Gaps.hGap8,
                      _buildInteractionCount(item),
                    ],
                  ),
                  _buildWorksInfo(item),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String formatDate(String dateStr) {
    final date = DateFormat('yyyy-MM-dd HH:mm:ss').parse(dateStr);
    return DateFormat('yyyy/MM/dd HH:mm', 'zh_CN').format(date);
  }

  // 数据说明
  Widget _buildDataDescription() {
    return Obx(() {
      var platRanking =
          state.hotConPlatRankingMap.value[state.hotConCurrentPlat.value]?[0];

      return Skeletonizer(
        enabled: platRanking == null,
        child: InkWell(
          onTap: () {
            if (platRanking == null) return;
            final formatted = formatDate(platRanking.updateTime);

            DialogHelper.showAlertDialog(
              title: 'hot_dataDescription'.tr,
              content: """
${'hot_updateTime'.tr}：${platRanking.updateFrequency}
${'hot_statisticDataDeadline'.tr}：$formatted
${'hot_timeView'.tr}：${'hot_byDay'.tr}
${'hot_sortRule'.tr}：${'hot_sortRuleDesc'.tr}
        """,
              onConfirm: () {
                Get.back(result: true);
              },
            );
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(
                Icons.info_outline,
                size: Dimens.font_sp14,
                color: AppColors.primaryColor,
              ),
              Gaps.hGap4,
              'hot_dataDescription'.tr.toNormalText(
                color: AppColors.primaryColor,
                fontSize: Dimens.font_sp12,
              ),
            ],
          ),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Gaps.hGap5,
                    Obx(
                      () => Skeletonizer(
                        enabled:
                            !state.hotConPlatLabelMap.value.containsKey(
                              state.hotConCurrentPlat.value,
                            ),
                        child: HotClassifySelect(
                          classifyList:
                              state.hotConPlatLabelMap.value[state
                                  .hotConCurrentPlat
                                  .value] ??
                              [],
                          currentClassify: state.hotConCurrentLabel.value,
                          onClassifyChange:
                              (val) => {
                                state.hotConCurrentLabel.value = val,
                                logic.refreshList(),
                              },
                        ),
                      ),
                    ),
                  ],
                ),
                _buildDataDescription(),
              ],
            ),
            Gaps.vGap15,
            Expanded(
              child: Obx(() {
                if (state.hotConListData.value != null &&
                    state.hotConListData.value!.isEmpty) {
                  return RefreshIndicator(
                    onRefresh: logic.refreshList,
                    child: ListView(
                      children: [
                        const SizedBox(height: 200),
                        Center(
                          child: 'noData'.tr.toNormalText(color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return Skeletonizer(
                  enabled: state.hotConListData.value == null,
                  child: RefreshIndicator(
                    onRefresh: logic.refreshList,
                    child: NotificationListener<ScrollNotification>(
                      onNotification: (ScrollNotification scrollInfo) {
                        // 滚动到底部自动加载
                        if (scrollInfo.metrics.pixels ==
                            scrollInfo.metrics.maxScrollExtent) {
                          if (state.hotConHasMore.value &&
                              !state.hotConIsLoading.value) {
                            logic.loadMore();
                          }
                        }
                        return false;
                      },
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        itemCount:
                            (state.hotConListData.value?.length ?? 5) + 1,
                        itemBuilder: (context, index) {
                          if (index ==
                              (state.hotConListData.value?.length ?? 5)) {
                            return _buildLoadMoreIndicator();
                          }

                          final item =
                              state.hotConListData.value?.isNotEmpty == true
                                  ? state.hotConListData.value![index]
                                  : null;

                          return InkWell(
                            onTap: () {
                              skipHotDetails(item?.url);
                            },
                            child: _buildHotItem(item),
                          );
                        },
                      ),
                    ),
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  // 添加加载更多指示器
  Widget _buildLoadMoreIndicator() {
    return Obx(
      () => LoadMoreIndicator(
        isLoading: state.hotConIsLoading.value,
        hasMore: state.hotConHasMore.value,
        visible: state.hotConListData.value != null,
      ),
    );
  }
}
