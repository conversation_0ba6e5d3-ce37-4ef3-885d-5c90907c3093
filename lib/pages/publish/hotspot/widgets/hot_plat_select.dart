import 'package:aitoearn_app/api/hot/models/hot_platform_model.dart';
import 'package:aitoearn_app/config/app_config.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

/// 热点 -- 平台选择
class HotPlatSelect extends StatefulWidget {
  const HotPlatSelect({
    required this.onChangeHotConPlat,
    super.key,
    this.hotConPlatData,
    this.hotConCurrentPlat = '',
  });

  final List<HotPlatformModel>? hotConPlatData;
  final String hotConCurrentPlat;
  final Function(String platId) onChangeHotConPlat;

  @override
  _HotPlatSelectState createState() => _HotPlatSelectState();
}

class _HotPlatSelectState extends State<HotPlatSelect> {
  // final HotspotLogic logic = Get.put(HotspotLogic());
  // final HotspotState state = Get.find<HotspotLogic>().state;

  Widget _buildPlatformList() {
    return SizedBox(
      height: 27,
      child: Skeletonizer(
        enabled: widget.hotConPlatData == null,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: widget.hotConPlatData?.length ?? 4,
          itemBuilder: (context, index) {
            final platform =
                widget.hotConPlatData?.isNotEmpty == true
                    ? widget.hotConPlatData![index]
                    : null;

            final totalCount = widget.hotConPlatData?.length ?? 4;

            return Container(
              height: 27,
              margin: EdgeInsets.only(right: index + 1 == totalCount ? 0 : 12),
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              decoration: BoxDecoration(
                gradient:
                    widget.hotConCurrentPlat == platform?.id
                        ? AppColors.blueAndPurple
                        : null,
                borderRadius: BorderRadius.circular(4),
              ),
              child: GestureDetector(
                onTap: () {
                  if (platform?.id != null) {
                    widget.onChangeHotConPlat(platform!.id);
                  }
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 19,
                      height: 19,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child:
                          platform?.icon != null
                              ? NetworkImageWidget(
                                '${AppConfig.yikaOssHost}/${platform?.icon}',
                                width: 70,
                                height: 70,
                                borderRadius: BorderRadius.circular(8),
                                placeholder: const CircularProgressIndicator(),
                              )
                              : Icon(
                                Icons.image,
                                size: 12,
                                color: Colors.grey[500],
                              ),
                    ),
                    Gaps.hGap8,
                    Text(
                      platform?.name ?? 'Platform $index',
                      style: TextStyle(
                        fontSize: 12,
                        color:
                            widget.hotConCurrentPlat == platform?.id
                                ? Colors.white
                                : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(
        left: Dimens.gap_dp15,
        right: Dimens.gap_dp15,
        top: 18,
        bottom: 12,
      ),
      child: _buildPlatformList(),
    );
  }
}
