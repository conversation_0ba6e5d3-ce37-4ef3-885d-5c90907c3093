import 'package:aitoearn_app/pages/publish/hotspot/logic.dart';
import 'package:aitoearn_app/pages/publish/hotspot/state.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 热点 -- 专题选择
class BuildTopicSelect extends StatefulWidget {
  const BuildTopicSelect({super.key});

  @override
  _BuildTopicSelectState createState() => _BuildTopicSelectState();
}

class _BuildTopicSelectState extends State<BuildTopicSelect> {
  final HotspotLogic logic = Get.put(HotspotLogic());
  final HotspotState state = Get.find<HotspotLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: Dimens.gap_dp15),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: List.generate(state.topicData.length, (index) {
            final isSelected = state.topicSelected.value == index;
            final topicItem = state.topicData[index];
            return Expanded(
              child: GestureDetector(
                onTap: () => logic.topicChange(index),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut,
                  height: isSelected ? 60 : 50,
                  margin: EdgeInsets.only(
                    right: index < state.topicData.length - 1 ? 12 : 0,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Stack(
                      children: [
                        ExtendedImage.asset(
                          topicItem['backUrl']!,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          filterQuality: FilterQuality.low,
                        ),
                        Center(
                          child: topicItem['name']?.toNormalText(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
        ),
      ),
    );
  }
}
