import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/utils/universal_bottom_sheet.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 分类选择
class HotClassifySelect extends StatefulWidget {
  const HotClassifySelect({
    required this.currentClassify,
    required this.classifyList,
    required this.onClassifyChange,
    super.key,
  });

  /// 当前选择的分类
  final String currentClassify;

  /// 分类数据
  final List<String> classifyList;

  /// 当前分类选择change 事件
  final ValueChanged<String>? onClassifyChange;

  @override
  _HotClassifySelectState createState() => _HotClassifySelectState();
}

class _HotClassifySelectState extends State<HotClassifySelect> {
  List<String> get _fullClassifyList => [
    'hot_all_classify'.tr,
    ...widget.classifyList,
  ];

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        UniversalBottomSheet.show(
          title: 'hot_select_classify'.tr,
          context: context,
          maxHeight: MediaQuery.of(context).size.height * 0.5,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: SingleChildScrollView(
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        _fullClassifyList.map((item) {
                          final isSelected =
                              widget.currentClassify == ''
                                  ? item == _fullClassifyList[0]
                                  : item == widget.currentClassify;

                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 150),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () {
                                  Navigator.pop(context);
                                  final selectedValue =
                                      item == _fullClassifyList[0] ? '' : item;
                                  widget.onClassifyChange?.call(selectedValue);
                                },
                                borderRadius: BorderRadius.circular(16),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient:
                                        isSelected
                                            ? const LinearGradient(
                                              colors: [
                                                Colors.blue,
                                                Colors.purple,
                                              ],
                                              begin: Alignment.centerLeft,
                                              end: Alignment.centerRight,
                                            )
                                            : null,
                                    color: isSelected ? null : Colors.grey[100],
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color:
                                          isSelected
                                              ? Colors.transparent
                                              : Colors.grey[300]!,
                                      width: 1,
                                    ),
                                  ),
                                  child: item.toNormalText(
                                    color:
                                        isSelected
                                            ? Colors.white
                                            : Colors.black87,
                                    fontWeight:
                                        isSelected
                                            ? FontWeight.w500
                                            : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          (widget.currentClassify == ''
                  ? _fullClassifyList[0]
                  : widget.currentClassify)
              .toNormalText(),
          Gaps.hGap5,
          const Icon(Icons.arrow_drop_down),
        ],
      ),
    );
  }
}
