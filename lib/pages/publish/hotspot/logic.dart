import 'package:aitoearn_app/api/hot/hot_api.dart';
import 'package:aitoearn_app/api/hot/models/hot_platform_ranking_model.dart';
import 'package:aitoearn_app/pages/publish/hotspot/state.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:aitoearn_app/widgets/calendar/logic.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class HotspotLogic extends GetxController {
  final HotspotState state = HotspotState();
  CalendarLogic? calendarLogic;

  /// 专题切换
  topicChange(int index) {
    state.topicSelected.value = index;
  }

  /// 日历点击
  void onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    state.hotConCurrentDate.value = DateFormat(
      'yyyy-MM-dd',
    ).format(selectedDay);
    state.hotConListData.value = null;
    getHotConList(); // 重新获取列表数据
  }

  /// -------------------------------------- 热门内容 --------------------------------------

  /// 获取热门内容平台数据
  getHotConPlatData() async {
    var res = await getPlatformList();
    if (res != null) {
      state.hotConPlatData.value = res;
      state.hotConCurrentPlat.value = res[0].id;
      getHotConPlatLabelData(res[0].id);
    }
  }

  /// 切换热门内容平台选择
  changeHotConPlat(String platId) {
    if (state.hotConCurrentPlat.value == platId) {
      return;
    }
    state.hotConCurrentPlat.value = platId;
    state.hotConListData.value = null;
    state.hotConCurrentLabel.value = '';
    getHotConPlatLabelData(platId);
  }

  /// 获取平台榜单数据，会缓存数据
  Future<List<HotPlatformRankingModel>?> getPlatRanking(String platId) async {
    if (state.hotConPlatRankingMap.value.containsKey(platId)) {
      return state.hotConPlatRankingMap.value[platId];
    }
    var platformRanking = await getPlatformRanking(platId);
    // 重新赋值 ranking map
    state.hotConPlatRankingMap.value = {
      ...state.hotConPlatRankingMap.value,
      platId: platformRanking,
    };
    return platformRanking;
  }

  /// 热门内容 - 获取平台的标签数据
  getHotConPlatLabelData(String platId) async {
    if (!state.hotConPlatLabelMap.value.containsKey(platId)) {
      var platformRanking = await getPlatRanking(platId);
      if (platformRanking == null || platformRanking.isEmpty) {
        return;
      }

      // 获取标签
      var rankingLabelRes = await getRankingLabel(platformRanking[0].id);
      if (rankingLabelRes != null) {
        // 重新赋值 label map
        state.hotConPlatLabelMap.value = {
          ...state.hotConPlatLabelMap.value,
          platId: rankingLabelRes,
        };
      }

      // 获取时间
      var rankingDatesRes = await getRankingDates(platformRanking[0].id);
      if (rankingDatesRes != null) {
        // 重新赋值 dates map
        state.hotConPlatDatesMap.value = {
          ...state.hotConPlatDatesMap.value,
          platId: rankingDatesRes,
        };
      }
    }

    final tag = state.calendarKey.toString();
    calendarLogic ??= Get.find<CalendarLogic>(tag: tag);
    state.hotConCurrentDate.value =
        state.hotConPlatDatesMap.value[platId]?.first.queryDate ?? '';
    calendarLogic!.setFocusedDay(DateTime.parse(state.hotConCurrentDate.value));

    refreshList();
  }

  /// 热门内容 - 列表获取
  getHotConList({bool isRefresh = false}) async {
    if (state.hotConIsLoading.value) {
      return;
    }

    if (isRefresh) {
      state.hotConCurrentPage.value = 1;
      state.hotConHasMore.value = true;
      state.hotConListData.value = null;
    }

    if (!state.hotConHasMore.value && !isRefresh) {
      return;
    }

    state.hotConIsLoading.value = true;

    var res = await getRankingContents(
      state.hotConPlatRankingMap.value[state.hotConCurrentPlat.value]![0].id,
      state.hotConCurrentPage.value,
      state.hotConPageSize,
      category:
          state.hotConCurrentLabel.value != ''
              ? state.hotConCurrentLabel.value
              : null,
      date: state.hotConCurrentDate.value,
    );

    state.hotConIsLoading.value = false;

    if (res == null) {
      if (isRefresh) {
        showError('networkError'.tr);
        state.hotConListData.value = [];
      }
      return;
    }

    if (isRefresh || state.hotConListData.value == null) {
      state.hotConListData.value = res.items;
    } else {
      state.hotConListData.value = [
        ...state.hotConListData.value!,
        ...res.items,
      ];
    }

    state.hotConHasMore.value = res.items.length == state.hotConPageSize;
    if (state.hotConHasMore.value) {
      state.hotConCurrentPage.value++;
    }
  }

  /// 加载更多
  loadMore() async {
    await getHotConList(isRefresh: false);
  }

  /// 刷新
  Future<void> refreshList() async {
    await getHotConList(isRefresh: true);
  }
}
