import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/draft_models/draft_model.dart';
import 'package:aitoearn_app/pages/publish/draft_box_select_dialog.dart';
import 'package:aitoearn_app/pages/publish/state.dart';
import 'package:aitoearn_app/plat_core/models/platform_publish_model_factory.dart';
import 'package:aitoearn_app/plat_core/models/publish_factory.dart';
import 'package:aitoearn_app/services/content_security_service.dart';
import 'package:aitoearn_app/store/draft/draft_store_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

class PublishLogic extends GetxController {
  final PublishState state = PublishState();

  late final PublishFactory publishFactory;

  @override
  void onInit() {
    super.onInit();

    try {
      publishFactory = Get.find<PublishFactory>();
    } catch (e) {
      publishFactory = Get.put<PublishFactory>(
        PublishFactory(),
        permanent: true,
      );
    }

    // 确保发布状态正确初始化
    resetPublishState();
  }

  // 选择发布账号
  void selectAccount(String accountId, String accountName) {
    state.selectedAccountId.value = accountId;
  }

  // 设置选中的账号列表
  void setSelectedAccounts(List<Map<String, dynamic>> accounts) {
    LoggerUtil.i('【发布逻辑】设置选中账号列表，数量: ${accounts.length}');
    for (int i = 0; i < accounts.length; i++) {
      final account = accounts[i];
      LoggerUtil.i(
        '【发布逻辑】账号 $i: id=${account['id']}, name=${account['name']}, platform=${account['platform']}, type=${account['type']}',
      );
    }

    state.selectedAccounts.clear();
    state.selectedAccounts.addAll(accounts);

    LoggerUtil.i('【发布逻辑】当前选中账号数量: ${state.selectedAccounts.length}');

    // 同时更新单账号选择字段，保持兼容性
    if (accounts.isNotEmpty) {
      state.selectedAccountId.value = accounts[0]['id'];
    } else {
      state.selectedAccountId.value = '';
    }
  }

  // 清空选中的账号列表
  void clearSelectedAccounts() {
    state.selectedAccounts.clear();
    state.selectedAccountId.value = '';
  }

  // 设置视频标题
  void setVideoTitle(String title) {
    state.videoTitle.value = title;
  }

  // 设置视频简介
  void setVideoDescription(String description) {
    state.videoDescription.value = description;
  }

  // 添加标签
  void addTag(String tag) {
    if (!state.videoTags.contains(tag) && tag.isNotEmpty) {
      state.videoTags.add(tag);
    }
  }

  // 删除标签
  void removeTag(String tag) {
    state.videoTags.remove(tag);
  }

  // 设置位置
  void setLocation(String location) {
    state.location.value = location;
  }

  // 设置完整的位置信息
  void setLocationInfo(Map<String, dynamic> locationInfo) {
    state.location.value = locationInfo['name'] ?? '';
    state.locationInfo.value = locationInfo;
  }

  // 切换定时发布
  void toggleScheduled() {
    state.isScheduled.value = !state.isScheduled.value;
    if (!state.isScheduled.value) {
      state.scheduledTime.value = null;
    }
  }

  // 设置定时发布时间
  void setScheduledTime(DateTime time) {
    state.scheduledTime.value = time;
    state.isScheduled.value = true;
  }

  // 设置视频路径
  void setVideoPath(String path) {
    state.videoPath.value = path;
    // 当设置新的视频路径时，清空之前上传的URL
    state.uploadedVideoUrl.value = '';
  }

  // 设置上传后的视频URL
  void setUploadedVideoUrl(String url) {
    state.uploadedVideoUrl.value = url;
  }

  // 获取账号配置
  Map<String, dynamic>? getAccountConfig(String accountId) {
    return state.accountPlatformConfigs[accountId];
  }

  // 设置账号配置
  void setAccountConfig(String accountId, Map<String, dynamic> config) {
    state.accountPlatformConfigs[accountId] = config;
  }

  // 清除账号配置
  void clearAccountConfig(String accountId) {
    state.accountPlatformConfigs.remove(accountId);
  }

  // 清除所有账号配置
  void clearAllAccountConfigs() {
    state.accountPlatformConfigs.clear();
  }

  // 从相册选择多张图片
  Future<void> pickMultiImages() async {
    final ImagePicker picker = ImagePicker();

    // 选择多张图片
    final List<XFile> images = await picker.pickMultiImage();

    if (images.isNotEmpty) {
      // 清空已选择的视频，因为视频和图片是互斥的
      state.videoPath.value = '';
      state.videoThumbnail.value = null;

      // 清空已选择的图片，然后添加新选择的图片
      state.selectedImages.clear();

      // 添加新选择的图片
      for (var image in images) {
        state.selectedImages.add(File(image.path));
      }
    }
  }

  // 从相册选择单个视频
  Future<void> pickVideoFromGallery() async {
    final ImagePicker picker = ImagePicker();
    final XFile? video = await picker.pickVideo(source: ImageSource.gallery);

    if (video != null) {
      // 清空已选择的图片，因为视频和图片是互斥的
      state.selectedImages.clear();

      // 设置视频路径
      state.videoPath.value = video.path;

      // 重置自定义封面标志
      state.isCustomThumbnail.value = false;

      // 生成视频缩略图
      await _generateVideoThumbnail(video.path);
    }
  }

  // 从相册选择视频封面图片
  Future<void> pickVideoThumbnailFromGallery() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);

    if (image != null) {
      // 设置自定义视频封面
      state.videoThumbnail.value = File(image.path);
      state.isCustomThumbnail.value = true; // 标记为自定义封面
    }
  }

  // 生成视频缩略图
  Future<void> _generateVideoThumbnail(String videoPath) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: videoPath,
        thumbnailPath: tempDir.path,
        imageFormat: ImageFormat.JPEG,
        maxWidth: 512,
        quality: 75,
      );

      if (thumbnailPath != null) {
        state.videoThumbnail.value = File(thumbnailPath);
        state.isCustomThumbnail.value = false; // 自动生成的缩略图，不是自定义封面
      }
    } catch (e) {
      LoggerUtil.i(
        'video_publish.logic.generate_thumbnail_failed'.trParams({
          'error': e.toString(),
        }),
      );
      state.videoThumbnail.value = null;
      state.isCustomThumbnail.value = false;
    }
  }

  // 删除选中的图片
  void removeImage(int index) {
    if (index >= 0 && index < state.selectedImages.length) {
      state.selectedImages.removeAt(index);
    }
  }

  // 清空所有选中的图片
  void clearImages() {
    state.selectedImages.clear();
  }

  // 清除已选择的视频
  void clearVideo() {
    state.videoPath.value = '';
    state.videoThumbnail.value = null;
    state.isCustomThumbnail.value = false; // 重置自定义封面标志
  }

  // 内容安全审查
  Future<bool> checkContentSafety() async {
    state.isChecking.value = true;

    try {
      // 获取要检查的内容
      final contents = await _getPublishContents();

      if (contents.isEmpty) {
        Get.snackbar('提示', '没有找到需要检查的内容');
        return false;
      }

      LoggerUtil.i('【内容安全检查】开始检查 ${contents.length} 个项目');

      // 准备检查数据
      final contentTexts = <String>[];
      final itemIds = <String>[];

      for (int i = 0; i < contents.length; i++) {
        final contentMap = contents[i];
        final title = contentMap['title'] ?? '';
        final description = contentMap['description'] ?? '';
        final itemId = contentMap['itemId'] ?? i.toString();

        // 组合标题和描述
        final content = '$title\n$description'.trim();
        contentTexts.add(content);
        itemIds.add(itemId);
      }

      // 执行安全检查
      final results = await ContentSecurityService.checkMultipleTextSecurity(
        contentTexts,
        itemIds: itemIds,
      );

      // 显示检查结果
      _showContentSecurityResults(results);

      // 返回是否所有内容都安全
      final allSafe = results.every((r) => !r.isSensitive);
      return allSafe;
    } catch (e) {
      LoggerUtil.e('【内容安全检查】检查过程出错: $e');
      Get.snackbar('错误', '内容安全检查失败: $e');
      return false;
    } finally {
      state.isChecking.value = false;
    }
  }

  // 获取平台特定的发布参数
  Map<String, dynamic> getPlatformSpecificParams(String platform) {
    Map<String, dynamic> params = {};

    switch (platform) {
      case 'douyin':
        params = {
          'allow_comment': true,
          'allow_share': true,
          'visibility': 'public',
          'music_id': '',
        };
        break;

      case 'xhs':
        params = {
          'topic_ids': [],
          'allow_comment': true,
          'allow_share': true,
          'sync_to_moments': false,
        };
        break;

      case 'ks':
      case 'kwai':
        params = {
          'allow_comment': true,
          'allow_download': true,
          'music_id': '',
          'hashtag_ids': [],
        };
        break;

      case 'wx-sph':
      case 'wxSph':
        params = {
          'allow_comment': true,
          'allow_share': true,
          'friend_only': false,
          'short_title': '',
          'ext_link': '',
          'is_original': false,
          'activity': null,
          'mentioned_users': <Map<String, dynamic>>[],
          'is_private': false,
        };
        break;

      default:
        // 默认参数
        params = {'allow_comment': true, 'allow_share': true};
    }

    return params;
  }

  // 获取账号的平台配置
  Map<String, dynamic> getAccountPlatformConfig(Map<String, dynamic> account) {
    final accountKey = '${account['id']}_${account['platform']}';
    return state.accountPlatformConfigs[accountKey] ?? {};
  }

  // 设置账号的平台配置
  void setAccountPlatformConfig(
    Map<String, dynamic> account,
    Map<String, dynamic> config,
  ) {
    final accountKey = '${account['id']}_${account['platform']}';
    state.accountPlatformConfigs[accountKey] = config;
  }

  // 清除账号的平台配置
  void clearAccountPlatformConfig(Map<String, dynamic> account) {
    final accountKey = '${account['id']}_${account['platform']}';
    state.accountPlatformConfigs.remove(accountKey);
  }

  // 重置发布状态
  void resetPublishState() {
    state.isPublishing.value = false;
    state.publishProgress.value = 0.0;
    state.publishStatus.value = '';
    state.publishTasks.clear();
    publishFactory.clearTasks();
    LoggerUtil.i('发布状态已重置');
  }

  // 使用平台模型发布视频
  Future<bool> publishVideoWithPlatformModels({
    bool backgroundMode = false,
  }) async {
    // 检查是否有发布任务正在进行（只检查本地状态）
    if (state.isPublishing.value) {
      LoggerUtil.w('检测到本地发布状态异常，尝试重置状态');
      resetPublishState();

      // 给用户一个提示，然后允许重新发布
      Get.snackbar(
        '状态重置',
        '检测到异常状态，已自动重置，请重新尝试发布',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange[100],
        colorText: Colors.orange[800],
        duration: const Duration(seconds: 2),
      );
      return false;
    }

    if (state.videoPath.value.isEmpty && state.selectedImages.isEmpty) {
      Get.snackbar(
        'video_publish.logic.error'.tr,
        'video_publish.logic.please_select_media'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
      );
      return false;
    }

    LoggerUtil.i('【发布检查】当前选中账号数量: ${state.selectedAccounts.length}');
    if (state.selectedAccounts.isNotEmpty) {
      for (int i = 0; i < state.selectedAccounts.length; i++) {
        final account = state.selectedAccounts[i];
        LoggerUtil.i(
          '【发布检查】选中账号 $i: id=${account['id']}, name=${account['name']}, platform=${account['platform']}, type=${account['type']}',
        );
      }
    }

    if (state.selectedAccounts.isEmpty) {
      LoggerUtil.w('【发布检查】没有选中任何账号，无法发布');
      Get.snackbar(
        'video_publish.logic.error'.tr,
        'video_publish.logic.please_select_account'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
      );
      return false;
    }

    state.isPublishing.value = true;
    state.publishProgress.value = 0.0;
    state.publishStatus.value = '准备发布任务...';
    state.publishTasks.clear();

    LoggerUtil.i('开始发布，初始化状态: progress=0.0, status=准备发布任务...');

    if (backgroundMode) {
      state.isBackgroundPublishing.value = true;
      state.isBackgroundPublishingCompleted.value = false;
      state.backgroundPublishResult.value = {};

      Get.snackbar(
        'video_publish.logic.background_publish'.tr,
        'video_publish.logic.background_notice'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.blue[100],
        colorText: Colors.blue[800],
        duration: const Duration(seconds: 2),
      );
    } else {
      LoggerUtil.i('显示发布进度对话框');
      _showPublishProgressDialog();
    }

    try {
      // 准备媒体文件
      final File? video =
          state.videoPath.value.isNotEmpty ? File(state.videoPath.value) : null;
      final List<File>? images =
          state.selectedImages.isNotEmpty ? state.selectedImages : null;
      final File? cover = state.videoThumbnail.value;

      LoggerUtil.i('准备发布媒体文件:');
      LoggerUtil.i('  video: ${video?.path ?? "null"}');
      LoggerUtil.i('  images: ${images?.length ?? 0} 个文件');
      LoggerUtil.i('  cover: ${cover?.path ?? "null"}');
      LoggerUtil.i('  videoPath: ${state.videoPath.value}');
      LoggerUtil.i('  selectedImages: ${state.selectedImages.length} 个文件');

      // 验证媒体文件
      if (video == null && (images == null || images.isEmpty)) {
        throw Exception(
          '发布内容必须包含视频或图片，当前video=${video?.path}, images=${images?.length}',
        );
      }

      // 创建平台发布模型列表，传递通用的标题和描述作为默认值
      final publishModels =
          PlatformPublishModelFactory.createBatchFromAccountConfigs(
            selectedAccounts: state.selectedAccounts,
            accountConfigs: state.accountPlatformConfigs,
            video: video,
            images: images,
            cover: cover,
            defaultTitle:
                state.videoTitle.value.isNotEmpty
                    ? state.videoTitle.value
                    : null,
            defaultDescription:
                state.videoDescription.value.isNotEmpty
                    ? state.videoDescription.value
                    : null,
            defaultTopics: state.videoTags.isNotEmpty ? state.videoTags : null,
          );

      // 验证发布模型
      final validationError = PlatformPublishModelFactory.validatePublishModels(
        publishModels,
      );
      if (validationError != null) {
        throw Exception(validationError);
      }

      // 获取发布统计
      final publishStats = PlatformPublishModelFactory.getPublishStats(
        publishModels,
      );
      LoggerUtil.i('发布统计: $publishStats');

      // 使用新的发布方法
      final tasks = await publishFactory.publishWithPlatformModels(
        publishModels,
        progressCallback: (progress, message) {
          state.publishProgress.value = progress;
          state.publishStatus.value = message;
          // 强制刷新UI
          state.publishProgress.refresh();
          state.publishStatus.refresh();
        },
      );

      // 更新发布任务状态
      state.publishTasks.clear();
      for (final task in tasks) {
        state.publishTasks.add({
          'id': task.id,
          'platform': task.platform,
          'account': task.account,
          'status': task.status.value.displayText, // 使用友好的显示文本
          'statusIcon': task.status.value.icon, // 添加状态图标
          'completed': task.status.value == PublishTaskStatus.completed,
          'progress': task.progress.value,
          'error':
              task.errorMessage.value.isEmpty ? null : task.errorMessage.value,
        });
      }

      // 获取发布结果统计
      final stats = publishFactory.getPublishResultStats();

      // 更新最终状态
      state.publishStatus.value = '发布完成！';
      state.publishProgress.value = 1.0;

      if (!backgroundMode) {
        await Future.delayed(const Duration(seconds: 1));
        if (Get.isDialogOpen ?? false) {
          Get.back();
        }
      }

      // 保存发布结果
      final Map<String, dynamic> publishResult = {
        'totalTasks': stats['total'],
        'successCount': stats['success'],
        'failCount': stats['failed'],
        'tasks': state.publishTasks,
        'completedAt': DateTime.now().toIso8601String(),
        'publishStats': publishStats,
      };

      if (backgroundMode) {
        state.isBackgroundPublishingCompleted.value = true;
        state.backgroundPublishResult.value = publishResult;

        Get.snackbar(
          'video_publish.logic.background_complete'.tr,
          'video_publish.logic.success_failed'.trParams({
            'success': stats['success'].toString(),
            'failed': stats['failed'].toString(),
          }),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor:
              stats['failed'] > 0 ? Colors.orange[100] : Colors.green[100],
          colorText:
              stats['failed'] > 0 ? Colors.orange[800] : Colors.green[800],
          duration: const Duration(seconds: 3),
          mainButton: TextButton(
            onPressed: () {
              showBackgroundPublishResultDialog(publishResult);
            },
            child: Text('video_publish.logic.view_details'.tr),
          ),
        );
      } else {
        if (stats['failed'] > 0) {
          Get.snackbar(
            'video_publish.logic.publish_complete'.tr,
            'video_publish.logic.success_failed'.trParams({
              'success': stats['success'].toString(),
              'failed': stats['failed'].toString(),
            }),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.orange[100],
            colorText: Colors.orange[800],
            duration: const Duration(seconds: 3),
          );
        } else {
          Get.snackbar(
            'video_publish.logic.publish_success'.tr,
            'video_publish.logic.all_success'.trParams({
              'count': stats['success'].toString(),
            }),
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green[100],
            colorText: Colors.green[800],
            duration: const Duration(seconds: 2),
          );
        }
      }

      state.isPublishing.value = false;

      Future.delayed(const Duration(seconds: 1), () {
        if (!backgroundMode) {
          _resetState();
        } else {
          state.publishProgress.value = 0.0;
          state.isBackgroundPublishing.value = false;
        }
      });

      return true;
    } catch (e) {
      LoggerUtil.e('发布失败: ${e.toString()}');

      state.isPublishing.value = false;
      state.publishProgress.value = 0.0;

      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'video_publish.logic.publish_failed'.tr,
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
        duration: const Duration(seconds: 3),
      );

      return false;
    }
  }

  // 显示发布进度弹窗
  void _showPublishProgressDialog() {
    Get.dialog(
      WillPopScope(
        onWillPop: () async => false, // 防止用户通过返回键关闭弹窗
        child: Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'video_publish.logic.publishing'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                Obx(() {
                  return Column(
                    children: [
                      LinearProgressIndicator(
                        value: state.publishProgress.value,
                        backgroundColor: Colors.grey[200],
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        '${(state.publishProgress.value * 100).toStringAsFixed(0)}%',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  );
                }),
                const SizedBox(height: 20),
                Obx(
                  () => Text(
                    state.publishStatus.value.isEmpty
                        ? 'video_publish.logic.publish_progress'.trParams({
                          'count': state.selectedAccounts.length.toString(),
                        })
                        : state.publishStatus.value,
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ),
                const SizedBox(height: 10),
                // 平台发布状态列表
                Obx(() {
                  if (state.publishTasks.isEmpty) {
                    return Container();
                  }

                  return Container(
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: state.publishTasks.length,
                      padding: const EdgeInsets.all(8),
                      itemBuilder: (context, index) {
                        final task = state.publishTasks[index];
                        final platform = task['platform'] ?? '';
                        final status = task['status'] ?? '准备中';
                        final isCompleted = task['completed'] ?? false;

                        // 根据平台选择图标
                        Widget platformIcon;
                        switch (platform) {
                          case 'douyin':
                            platformIcon = Image.asset(
                              'assets/images/account/plat_icon/douyin.png',
                              width: 20,
                              height: 20,
                            );
                            break;
                          case 'xhs':
                            platformIcon = Image.asset(
                              'assets/images/account/plat_icon/xhs.png',
                              width: 20,
                              height: 20,
                            );
                            break;
                          case 'ks':
                          case 'kwai':
                            platformIcon = Image.asset(
                              'assets/images/account/plat_icon/ks.png',
                              width: 20,
                              height: 20,
                            );
                            break;
                          case 'wx-sph':
                            platformIcon = Image.asset(
                              'assets/images/account/plat_icon/wx-sph.png',
                              width: 20,
                              height: 20,
                            );
                            break;
                          default:
                            platformIcon = const Icon(
                              Icons.account_circle,
                              size: 20,
                            );
                        }

                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              platformIcon,
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  task['account']['nickname'] ?? platform,
                                  style: const TextStyle(fontSize: 14),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 8),
                              isCompleted
                                  ? const Icon(
                                    Icons.check_circle,
                                    color: Colors.green,
                                    size: 18,
                                  )
                                  : const SizedBox(
                                    width: 18,
                                    height: 18,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.blue,
                                      ),
                                    ),
                                  ),
                            ],
                          ),
                        );
                      },
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  // 重置状态
  void _resetState() {
    state.videoTitle.value = '';
    state.videoDescription.value = '';
    state.videoTags.clear();
    state.location.value = '';
    state.isScheduled.value = false;
    state.scheduledTime.value = null;
    state.videoPath.value = '';
    state.selectedAccounts.clear();
    state.selectedAccountId.value = '';
    state.selectedImages.clear();
    state.videoThumbnail.value = null;
  }

  // 显示AI帮写弹窗
  void showAIWritingDialog(BuildContext context) {
    // 实现AI帮写功能
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('video_publish.logic.ai_write'.tr),
            content: Text('video_publish.logic.ai_generating'.tr),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('video_publish.cancel'.tr),
              ),
              TextButton(
                onPressed: () {
                  // 模拟AI生成内容
                  setVideoTitle('AI生成的标题');
                  setVideoDescription('AI生成的视频简介，包含相关的关键词和描述...');
                  Navigator.pop(context);
                },
                child: Text('video_publish.logic.use'.tr),
              ),
            ],
          ),
    );
  }

  // 显示后台发布结果弹窗
  void showBackgroundPublishResultDialog(Map<String, dynamic> result) {
    final int totalTasks = result['totalTasks'] ?? 0;
    final int successCount = result['successCount'] ?? 0;
    final int failCount = result['failCount'] ?? 0;
    final List<Map<String, dynamic>> tasks = List<Map<String, dynamic>>.from(
      result['tasks'] ?? [],
    );

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'video_publish.logic.publish_result'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'video_publish.logic.total_tasks'.trParams({
                  'count': totalTasks.toString(),
                }),
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    'video_publish.logic.success'.trParams({
                      'count': successCount.toString(),
                    }),
                    style: TextStyle(color: Colors.green[700]),
                  ),
                  const SizedBox(width: 16),
                  if (failCount > 0) ...[
                    const Icon(Icons.error, color: Colors.red, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      'video_publish.logic.failed'.trParams({
                        'count': failCount.toString(),
                      }),
                      style: TextStyle(color: Colors.red[700]),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 16),
              if (tasks.isNotEmpty)
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: tasks.length,
                    padding: const EdgeInsets.all(8),
                    itemBuilder: (context, index) {
                      final task = tasks[index];
                      final platform = task['platform'] ?? '';
                      final accountName =
                          task['account']['nickname'] ?? platform;
                      final isCompleted = task['completed'] ?? false;
                      final status = task['status'] ?? '';

                      return ListTile(
                        dense: true,
                        title: Text(accountName),
                        subtitle: Text(status),
                        trailing:
                            isCompleted
                                ? const Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                )
                                : const Icon(Icons.error, color: Colors.red),
                      );
                    },
                  ),
                ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Get.back();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: Text('video_publish.logic.close'.tr),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 保存为草稿
  Future<bool> saveToDraft(String boxId) async {
    try {
      // 获取草稿箱服务
      final draftStoreService = Get.find<DraftStoreService>();

      // 准备草稿数据
      final String title =
          state.videoTitle.value.isEmpty
              ? 'video_publish.logic.untitled_draft'.tr
              : state.videoTitle.value;
      final String description = state.videoDescription.value;
      final List<String> tags = state.videoTags.toList();
      final Map<String, dynamic>? locationInfo =
          state.locationInfo.value.isEmpty ? null : state.locationInfo.value;

      // 处理视频路径和图片路径
      String? videoPath;
      List<String>? imagePaths;
      String? thumbnailPath;

      if (state.videoPath.value.isNotEmpty) {
        videoPath = state.videoPath.value;
        if (state.videoThumbnail.value != null) {
          thumbnailPath = state.videoThumbnail.value!.path;
        }
      } else if (state.selectedImages.isNotEmpty) {
        imagePaths = state.selectedImages.map((file) => file.path).toList();
      }

      // 创建草稿对象
      final draft = DraftItem(
        id:
            state.draftId.value.isEmpty
                ? DateTime.now().millisecondsSinceEpoch.toString()
                : state.draftId.value,
        title: title,
        description: description,
        tags: tags,
        locationInfo: locationInfo,
        isScheduled: state.isScheduled.value,
        scheduledTime: state.scheduledTime.value,
        videoPath: videoPath,
        imagePaths: imagePaths,
        thumbnailPath: thumbnailPath,
        selectedAccounts: state.selectedAccounts.toList(),
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
      );

      // 检查是否是更新现有草稿
      if (state.draftId.value.isNotEmpty && state.draftBoxId.value.isNotEmpty) {
        // 更新现有草稿
        LoggerUtil.i(
          'video_publish.logic.update_draft'.trParams({
            'id': state.draftId.value,
          }),
        );
        final success = await draftStoreService.updateDraft(
          boxId: state.draftBoxId.value,
          draftId: state.draftId.value,
          draft: draft,
        );

        return success;
      } else {
        // 创建新草稿
        LoggerUtil.i('video_publish.logic.create_new_draft'.tr);
        final success = await draftStoreService.createDraft(
          boxId: boxId,
          draft: draft,
        );

        if (success) {
          state.draftId.value = draft.id;
          state.draftBoxId.value = boxId;
        }

        return success;
      }
    } catch (e) {
      LoggerUtil.e(
        'video_publish.logic.save_draft_failed'.trParams({
          'error': e.toString(),
        }),
      );
      return false;
    }
  }

  /// 保存图片到草稿箱
  Future<bool> saveImagesToDraft() async {
    try {
      // 检查是否有图片可保存
      if (state.selectedImages.isEmpty) {
        return false;
      }

      // 显示草稿箱选择弹窗
      final result = await DraftBoxSelectDialog.show(Get.context!);

      if (result != null && result['boxId'] != null) {
        // 获取草稿箱服务
        final draftStoreService = Get.find<DraftStoreService>();

        // 准备草稿数据
        final DateTime now = DateTime.now();
        final String dateStr = now.toString().substring(0, 16);
        final String title = 'video_publish.logic.image_draft_title'.trParams({
          'date': dateStr,
        });
        final String description = 'video_publish.logic.image_draft_desc'.tr;
        final List<String> tags = ['测试', '图片'];

        // 处理图片路径
        List<String> imagePaths =
            state.selectedImages.map((file) => file.path).toList();

        // 创建草稿对象
        final draft = DraftItem(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: title,
          description: description,
          tags: tags,
          imagePaths: imagePaths,
          createTime: DateTime.now(),
          updateTime: DateTime.now(),
        );

        // 创建草稿
        final success = await draftStoreService.createDraft(
          boxId: result['boxId'],
          draft: draft,
        );

        return success;
      }

      return false;
    } catch (e) {
      LoggerUtil.e(
        'video_publish.logic.image_draft_failed'.trParams({
          'error': e.toString(),
        }),
      );
      return false;
    }
  }

  // 清除所有内容
  void clearAllContent() {
    LoggerUtil.i('video_publish.logic.clear_all'.tr);

    // 清空状态
    state.videoTitle.value = '';
    state.videoDescription.value = '';
    state.videoTags.clear();
    state.locationInfo.value = {};
    state.location.value = '';
    state.isScheduled.value = false;
    state.scheduledTime.value = null;
    state.selectedAccounts.clear();
    state.videoPath.value = '';
    state.videoThumbnail.value = null;
    state.selectedImages.clear();
    state.draftId.value = '';
    state.draftBoxId.value = '';
  }

  /// 获取要检查的发布内容
  Future<List<Map<String, String>>> _getPublishContents() async {
    final contents = <Map<String, String>>[];

    // 如果有选中的账号，为每个账号创建一个检查项
    if (state.selectedAccounts.isNotEmpty) {
      for (int i = 0; i < state.selectedAccounts.length; i++) {
        final account = state.selectedAccounts[i];
        final platform = account['type'] ?? 'unknown';
        final accountName = account['name'] ?? '未知账号';

        contents.add({
          'itemId': '${platform}_$i',
          'title': state.videoTitle.value,
          'description': state.videoDescription.value,
          'platform': platform,
          'accountName': accountName,
        });
      }
    } else {
      // 如果没有选中账号，至少检查一次内容
      contents.add({
        'itemId': 'default',
        'title': state.videoTitle.value,
        'description': state.videoDescription.value,
        'platform': 'default',
        'accountName': '默认',
      });
    }

    return contents;
  }

  /// 显示内容安全检查结果
  void _showContentSecurityResults(List<PublishItemSecurityResult> results) {
    final sensitiveResults = results.where((r) => r.isSensitive).toList();
    final hasError = results.any((r) => r.error != null);

    if (sensitiveResults.isEmpty && !hasError) {
      // 所有内容都正常
      Get.snackbar(
        '检测完成',
        '所有内容均通过安全检测，可以正常发布',
        backgroundColor: Colors.green.withOpacity(0.1),
        colorText: Colors.green,
        icon: const Icon(Icons.check_circle, color: Colors.green),
        duration: const Duration(seconds: 3),
      );
    } else {
      // 有敏感内容或错误，显示详细对话框
      Get.dialog(
        AlertDialog(
          title: Row(
            children: [
              Icon(
                sensitiveResults.isNotEmpty ? Icons.warning : Icons.error,
                color: sensitiveResults.isNotEmpty ? Colors.orange : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                sensitiveResults.isNotEmpty ? '检测到敏感内容' : '检测出现错误',
                style: TextStyle(
                  color:
                      sensitiveResults.isNotEmpty ? Colors.orange : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (sensitiveResults.isNotEmpty) ...[
                  const Text(
                    '以下内容可能包含敏感信息，建议修改后再发布：',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 12),
                  ...sensitiveResults.map(
                    (result) => _buildResultItem(result, true),
                  ),
                ],

                if (hasError) ...[
                  if (sensitiveResults.isNotEmpty) const SizedBox(height: 16),
                  const Text(
                    '部分内容检测失败：',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...results
                      .where((r) => r.error != null)
                      .map((result) => _buildResultItem(result, false)),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(onPressed: () => Get.back(), child: const Text('关闭')),
            ElevatedButton(
              onPressed: () {
                Get.back();
                // 可以在这里添加继续发布的逻辑
              },
              child: const Text('继续发布'),
            ),
          ],
        ),
      );
    }
  }

  /// 构建检查结果项
  Widget _buildResultItem(PublishItemSecurityResult result, bool isSensitive) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            isSensitive
                ? Colors.orange.withOpacity(0.1)
                : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isSensitive
                  ? Colors.orange.withOpacity(0.3)
                  : Colors.red.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSensitive ? Icons.warning : Icons.error,
                size: 16,
                color: isSensitive ? Colors.orange : Colors.red,
              ),
              const SizedBox(width: 4),
              Text(
                '项目 ${result.itemId}',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: isSensitive ? Colors.orange : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            result.content.length > 100
                ? '${result.content.substring(0, 100)}...'
                : result.content,
            style: const TextStyle(fontSize: 12),
          ),
          if (result.error != null) ...[
            const SizedBox(height: 4),
            Text(
              '错误: ${result.error}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.red,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
