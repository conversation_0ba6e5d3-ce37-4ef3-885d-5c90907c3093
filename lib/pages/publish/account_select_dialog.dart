import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 账号选择控制器
class AccountSelectController extends GetxController {
  // 账号列表控制器
  final AccountListController accountController;
  // 账号持久化服务
  final AccountPersistentService accountService;
  // 选中的账号ID列表
  final RxList<String> selectedAccountIds = <String>[].obs;
  // 搜索关键词
  final RxString searchKeyword = ''.obs;
  // 选中的空间索引
  final RxInt selectedGroupIndex = 0.obs;
  // 是否正在加载
  final RxBool isLoading = true.obs;

  AccountSelectController({
    required this.accountController,
    required this.accountService,
    List<Map<String, dynamic>>? initialSelectedAccounts,
  }) {
    // 初始化已选中的账号
    if (initialSelectedAccounts != null && initialSelectedAccounts.isNotEmpty) {
      for (var account in initialSelectedAccounts) {
        if (account['id'] != null) {
          selectedAccountIds.add(account['id'].toString());
        }
      }
    }

    // 始终将加载状态设置为true
    isLoading.value = true;

    // 预加载数据，使用单独的函数初始化，避免构造函数中进行异步操作
    _initData();
  }

  @override
  void onClose() {
    // 清理资源
    isLoading.value = true;
    super.onClose();
  }

  // 初始化数据
  Future<void> _initData() async {
    try {
      // 先确保isLoading为true
      // isLoading.value = true;
      // 预先清空空间数据，避免旧数据闪现
      // accountController.groups.clear();
      // 先从API获取账号数据
      // await accountService.fetchAccountsFromApi();
      // 等待账号数据获取完成后，再获取空间数据并分配账号
      // await accountController.loadGroupsFromApi();
    } catch (e) {
      LoggerUtil.e(e);
      // 出错时使用本地数据
      showError('networkError'.tr);
    } finally {
      // 数据加载完成，更新UI状态
      isLoading.value = false;
    }
  }

  // 刷新数据
  Future<void> refreshData() async {
    isLoading.value = true;

    try {
      // 刷新账号列表
      await accountController.refreshAccountsStatus();
    } finally {
      isLoading.value = false;
    }
  }

  // 设置搜索关键词
  void setSearchKeyword(String keyword) {
    searchKeyword.value = keyword;
  }

  // 设置选中的空间索引
  void setSelectedGroupIndex(int index) {
    selectedGroupIndex.value = index;
  }

  // 切换账号选中状态
  void toggleAccountSelection(String accountId) {
    if (selectedAccountIds.contains(accountId)) {
      selectedAccountIds.remove(accountId);
    } else {
      selectedAccountIds.add(accountId);
    }
  }

  // 清空选中的账号
  void clearSelectedAccounts() {
    selectedAccountIds.clear();
  }

  // TODO 获取选中的账号信息
  // getSelectedAccounts() {
  //   final List<AccountUserInfoModle> result = [];
  //
  //   for (var uid in selectedAccountIds) {
  //     // 从账号服务中获取完整的账号信息
  //     final accountInfo = accountService.getAccountByUid(uid);
  //     if (accountInfo != null) {
  //       result.add(accountInfo);
  //     }
  //   }
  //
  //   return result;
  // }

  List<Map<String, dynamic>> getSelectedAccounts() {
    final List<Map<String, dynamic>> result = [];

    for (var uid in selectedAccountIds) {
      // 从账号服务中获取完整的账号信息
      final accountInfo = accountService.getAccountByUid(uid);
      if (accountInfo != null) {
        // 将AccountUserInfoModle转换为Map
        String platformType = 'douyin';
        switch (accountInfo.type) {
          case PlatTypeEnum.douyin:
            platformType = 'douyin';
            break;
          case PlatTypeEnum.xhs:
            platformType = 'xhs';
            break;
          case PlatTypeEnum.kwai:
            platformType = 'ks';
            break;
          case PlatTypeEnum.wxWph:
            platformType = 'wx-sph';
            break;
          default:
            platformType = 'unknown';
            break;
        }

        result.add({
          'id': accountInfo.uid,
          'name': accountInfo.account,
          'nickname': accountInfo.nickname,
          'avatar': accountInfo.avatar,
          'platform': platformType,
          'type': platformType, // 同时设置type字段，保持兼容性
          'online': accountInfo.online,
          'cookie': accountInfo.cookie,
          'accessToken': accountInfo.accessToken,
          'refreshToken': accountInfo.refreshToken,
          'account_model': accountInfo, // 保存完整的账号模型
        });
      }
    }

    return result;
  }
}

/// 账号选择弹窗
class AccountSelectDialog extends StatelessWidget {
  final List<Map<String, dynamic>>? initialSelectedAccounts;

  const AccountSelectDialog({super.key, this.initialSelectedAccounts});

  /// 显示账号选择弹窗
  static Future<Map<String, dynamic>?> show(
    BuildContext context, {
    List<Map<String, dynamic>>? initialSelectedAccounts,
  }) async {
    // 确保先注册AccountListController
    if (!Get.isRegistered<AccountListController>()) {
      Get.put(AccountListController());
    }

    // 确保每次都创建新的控制器
    if (Get.isRegistered<AccountSelectController>()) {
      Get.delete<AccountSelectController>();
    }

    return await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (_) => AccountSelectDialog(
            initialSelectedAccounts: initialSelectedAccounts,
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 获取账号列表控制器和账号服务
    final accountController = Get.find<AccountListController>();
    final accountService = Get.find<AccountPersistentService>();

    // 创建账号选择控制器，确保是新实例
    final controller = Get.put(
      AccountSelectController(
        accountController: accountController,
        accountService: accountService,
        initialSelectedAccounts: initialSelectedAccounts,
      ),
      tag: DateTime.now().toString(),
    );

    // 文本编辑控制器
    final searchController = TextEditingController();
    searchController.addListener(() {
      controller.setSearchKeyword(searchController.text);
    });

    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // 标题栏
          _buildTitleBar(context, controller),

          // 搜索框
          _buildSearchBar(searchController),

          // 已选账号提示
          _buildSelectedAccountsHint(controller),

          // 空间和账号列表区域
          Expanded(
            child: Row(
              children: [
                // 左侧空间列表
                _buildGroupList(controller),

                // 右侧账号列表
                _buildAccountList(controller),
              ],
            ),
          ),

          // 底部指示器
          Container(
            margin: const EdgeInsets.symmetric(vertical: 8),
            width: 40,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.2),
              borderRadius: BorderRadius.circular(2.5),
            ),
          ),

          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  // 标题栏
  Widget _buildTitleBar(
    BuildContext context,
    AccountSelectController controller,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.black12, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'video_publish.account_select.cancel'.tr,
              style: const TextStyle(fontSize: 16, color: Colors.black54),
            ),
          ),
          Text(
            'video_publish.account_select.title'.tr,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          Obx(
            () => TextButton(
              onPressed: () {
                // 返回选中的账号信息
                final selectedAccounts = controller.getSelectedAccounts();
                Navigator.pop(context, {'accounts': selectedAccounts});
              },
              child: Text(
                'video_publish.account_select.confirm'.tr,
                style: TextStyle(
                  fontSize: 16,
                  color:
                      controller.selectedAccountIds.isEmpty
                          ? Colors.grey
                          : Theme.of(context).primaryColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 已选账号提示
  Widget _buildSelectedAccountsHint(AccountSelectController controller) {
    return Obx(() {
      if (controller.selectedAccountIds.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        color: Colors.blue.withOpacity(0.1),
        child: Row(
          children: [
            Text(
              'video_publish.account_select.selected_count'.trParams({
                'count': controller.selectedAccountIds.length.toString(),
              }),
              style: const TextStyle(
                color: Colors.blue,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: () => controller.clearSelectedAccounts(),
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Text('video_publish.account_select.clear'.tr),
            ),
          ],
        ),
      );
    });
  }

  // 搜索框
  Widget _buildSearchBar(TextEditingController searchController) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: searchController,
        decoration: InputDecoration(
          hintText: 'video_publish.account_select.search'.tr,
          hintStyle: TextStyle(color: Colors.grey[400]),
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          filled: true,
          fillColor: Colors.grey[100],
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  // 空间列表
  Widget _buildGroupList(AccountSelectController controller) {
    return Obx(() {
      final groups = controller.accountController.groups;

      // 如果正在加载，显示加载指示器
      if (controller.isLoading.value) {
        return Container(
          width: 100,
          color: Colors.grey[100],
          child: const Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        );
      }

      // 如果没有空间，显示空视图
      if (groups.isEmpty) {
        return Container(
          width: 100,
          color: Colors.grey[100],
          child: Center(
            child: Text(
              'video_publish.account_select.no_groups'.tr,
              style: const TextStyle(color: Colors.grey, fontSize: 14),
            ),
          ),
        );
      }

      return Container(
        width: 100,
        color: Colors.grey[100],
        child: ListView.builder(
          itemCount: groups.length,
          itemBuilder: (context, index) {
            final group = groups[index];

            return Obx(() {
              final isSelected = controller.selectedGroupIndex.value == index;

              return InkWell(
                onTap: () => controller.setSelectedGroupIndex(index),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 16,
                    horizontal: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.transparent,
                    border: Border(
                      left: BorderSide(
                        color: isSelected ? Colors.blue : Colors.transparent,
                        width: 3,
                      ),
                    ),
                  ),
                  child: Text(
                    group.name,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isSelected ? Colors.blue : Colors.black87,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      fontSize: 14,
                    ),
                  ),
                ),
              );
            });
          },
        ),
      );
    });
  }

  // 账号列表
  Widget _buildAccountList(AccountSelectController controller) {
    return Obx(() {
      // 如果正在加载，显示加载指示器
      if (controller.isLoading.value) {
        return const Expanded(
          child: Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        );
      }

      final groups = controller.accountController.groups;

      // 如果没有空间，显示空视图
      if (groups.isEmpty) {
        return Expanded(
          child: Center(
            child: Text(
              'video_publish.account_select.no_accounts'.tr,
              style: TextStyle(color: Colors.grey[600], fontSize: 16),
            ),
          ),
        );
      }

      // 获取当前选中的空间
      final selectedGroup = groups[controller.selectedGroupIndex.value];
      final searchKeyword = controller.searchKeyword.value;

      // 根据搜索关键词过滤账号
      final filteredAccounts =
          selectedGroup.accounts.where((account) {
            return searchKeyword.isEmpty ||
                account.name.contains(searchKeyword) ||
                (account.nickname?.contains(searchKeyword) ?? false);
          }).toList();

      return Expanded(
        child:
            filteredAccounts.isEmpty
                ? Center(
                  child: Text(
                    'video_publish.account_select.no_accounts'.tr,
                    style: TextStyle(color: Colors.grey[600], fontSize: 16),
                  ),
                )
                : ListView.builder(
                  itemCount: filteredAccounts.length,
                  itemBuilder: (context, index) {
                    final account = filteredAccounts[index];
                    final accountId = account.uid;

                    return Obx(() {
                      final isSelected = controller.selectedAccountIds.contains(
                        accountId,
                      );

                      // 获取平台图标
                      Widget platformIcon;
                      switch (account.platType) {
                        case 'douyin':
                          platformIcon = Image.asset(
                            'assets/images/account/plat_icon/douyin.png',
                            width: 16,
                            height: 16,
                          );
                          break;
                        case 'xhs':
                          platformIcon = Image.asset(
                            'assets/images/account/plat_icon/xhs.png',
                            width: 16,
                            height: 16,
                          );
                          break;
                        case 'ks':
                          platformIcon = Image.asset(
                            'assets/images/account/plat_icon/ks.png',
                            width: 16,
                            height: 16,
                          );
                          break;
                        case 'wx-sph':
                          platformIcon = Image.asset(
                            'assets/images/account/plat_icon/wx-sph.png',
                            width: 16,
                            height: 16,
                          );
                          break;
                        default:
                          platformIcon = const Icon(
                            Icons.account_circle,
                            size: 16,
                          );
                      }

                      return ListTile(
                        leading: Stack(
                          children: [
                            CircleAvatar(
                              backgroundImage:
                                  account.avatar != null &&
                                          account.avatar!.isNotEmpty
                                      ? NetworkImage(account.avatar!)
                                      : null,
                              child:
                                  account.avatar == null ||
                                          account.avatar!.isEmpty
                                      ? Icon(
                                        Icons.person,
                                        color:
                                            account.online
                                                ? Colors.grey
                                                : Colors.grey[400],
                                      )
                                      : null,
                            ),
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: Container(
                                padding: const EdgeInsets.all(1),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 1.5,
                                  ),
                                ),
                                child: Opacity(
                                  opacity: account.online ? 1.0 : 0.6,
                                  child: platformIcon,
                                ),
                              ),
                            ),
                          ],
                        ),
                        title: Row(
                          children: [
                            Text(
                              account.nickname ?? '',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color:
                                    account.online ? Colors.black : Colors.grey,
                                fontSize: Dimens.font_sp12,
                              ),
                            ),
                            // if (account.nickname != null) ...[
                            //   const SizedBox(width: 4),
                            //   Text(
                            //     account.nickname!,
                            //     style: TextStyle(
                            //       color:
                            //           account.online
                            //               ? Colors.grey[600]
                            //               : Colors.grey[400],
                            //       fontSize: 12,
                            //     ),
                            //   ),
                            // ],
                          ],
                        ),
                        subtitle: Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color:
                                    account.online ? Colors.green : Colors.grey,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'video_publish.account_select.status'.trParams({
                                'status':
                                    account.online
                                        ? 'video_publish.account_select.online'
                                            .tr
                                        : 'video_publish.account_select.offline'
                                            .tr,
                              }),
                              style: TextStyle(
                                fontSize: 12,
                                color:
                                    account.online
                                        ? Colors.grey[800]
                                        : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                        trailing: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected ? Colors.blue : Colors.grey[200],
                            border: Border.all(
                              color:
                                  isSelected ? Colors.blue : Colors.grey[400]!,
                              width: 1,
                            ),
                          ),
                          child:
                              isSelected
                                  ? const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 16,
                                  )
                                  : null,
                        ),
                        onTap:
                            () => controller.toggleAccountSelection(accountId),
                      );
                    });
                  },
                ),
      );
    });
  }
}
