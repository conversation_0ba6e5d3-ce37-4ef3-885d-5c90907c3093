import 'package:aitoearn_app/enums/ai_create_type.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/widgets/ai_create_button.dart';
import 'package:aitoearn_app/widgets/topic_selector.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 小红书发布配置页面
class XiaohongshuConfigPage extends StatefulWidget {
  final Map<String, dynamic> account;
  final PublishLogic logic;
  final Map<String, dynamic> initialConfig;

  const XiaohongshuConfigPage({
    required this.account, required this.logic, required this.initialConfig, super.key,
  });

  @override
  State<XiaohongshuConfigPage> createState() => _XiaohongshuConfigPageState();
}

class _XiaohongshuConfigPageState extends State<XiaohongshuConfigPage> {
  late Map<String, dynamic> _config;
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _config = Map<String, dynamic>.from(widget.initialConfig);

    // 初始化控制器
    _titleController.text = _config['custom_title'] ?? '';
    _descController.text = _config['description'] ?? '';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          title: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: const Color(0xFFFF2442),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    '小',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.account['nickname'] ?? '小红书账号'}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: _saveConfig,
              child: const Text(
                '保存',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题配置
              _buildTitleSection(),
              const SizedBox(height: 24),

              // 描述配置
              _buildDescriptionSection(),
              const SizedBox(height: 24),

              // 话题配置
              _buildTopicsSection(),
              const SizedBox(height: 24),

              // 可见性配置
              _buildVisibilitySection(),
              const SizedBox(height: 24),

              // 评论设置
              _buildCommentSection(),
              const SizedBox(height: 24),

              // 位置设置
              _buildLocationSection(),
              const SizedBox(height: 100), // 底部留白
            ],
          ),
        ),
      ),
    );
  }

  // 标题配置
  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标题',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _titleController,
            maxLength: 20,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '填写笔记标题',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) {
              _config['custom_title'] = value;
            },
          ),
        ),
        const SizedBox(height: 8),
        AiCreateButton(
          type: AiCreateType.title,
          tips: '基于视频内容智能生成标题',
          videoFilePath:
              widget.logic.state.videoPath.value.isNotEmpty
                  ? widget.logic.state.videoPath.value
                  : null,
          uploadedVideoUrl:
              widget.logic.state.uploadedVideoUrl.value.isNotEmpty
                  ? widget.logic.state.uploadedVideoUrl.value
                  : null,
          maxLength: 20,
          onAiCreateFinish: (text) {
            _titleController.text = text;
            _config['custom_title'] = text;
          },
        ),
      ],
    );
  }

  // 描述配置
  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _descController,
            maxLength: 1000,
            maxLines: 4,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '添加笔记正文...',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) {
              _config['description'] = value;
            },
          ),
        ),
        const SizedBox(height: 8),
        AiCreateButton(
          type: AiCreateType.description,
          tips: '基于视频内容智能生成描述',
          videoFilePath:
              widget.logic.state.videoPath.value.isNotEmpty
                  ? widget.logic.state.videoPath.value
                  : null,
          uploadedVideoUrl:
              widget.logic.state.uploadedVideoUrl.value.isNotEmpty
                  ? widget.logic.state.uploadedVideoUrl.value
                  : null,
          maxLength: 1000,
          onAiCreateFinish: (text) {
            _descController.text = text;
            _config['description'] = text;
          },
        ),
      ],
    );
  }

  // 话题配置
  Widget _buildTopicsSection() {
    final accessToken =
        widget.account['accessToken'] ?? widget.account['cookie'] ?? '';
    final selectedTopics = (_config['topics'] as List<String>?) ?? [];

    return TopicSelector(
      platform: 'xhs',
      accessToken: accessToken,
      maxCount: 10,
      selectedTopics: selectedTopics,
      tips: '您可以添加10个话题',
      onChanged: (topics) {
        setState(() {
          _config['topics'] = topics;
        });
      },
    );
  }

  // 可见性配置
  Widget _buildVisibilitySection() {
    final isPrivate = _config['is_private'] as bool? ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '谁可以看',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildVisibilityOption(
                value: false,
                groupValue: isPrivate,
                title: '公开',
                subtitle: '所有人可见',
                onChanged: (value) {
                  setState(() {
                    _config['is_private'] = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildVisibilityOption(
                value: true,
                groupValue: isPrivate,
                title: '仅自己可见',
                subtitle: '私密笔记',
                onChanged: (value) {
                  setState(() {
                    _config['is_private'] = value;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 可见性选项组件
  Widget _buildVisibilityOption({
    required bool value,
    required bool groupValue,
    required String title,
    required String subtitle,
    required ValueChanged<bool> onChanged,
  }) {
    final isSelected = value == groupValue;

    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? const Color(0xFFFF2442).withOpacity(0.1)
                  : Colors.white,
          border: Border.all(
            color:
                isSelected ? const Color(0xFFFF2442) : const Color(0xFFD9D9D9),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color:
                          isSelected
                              ? const Color(0xFFFF2442)
                              : const Color(0xFFD9D9D9),
                      width: 2,
                    ),
                    color:
                        isSelected
                            ? const Color(0xFFFF2442)
                            : Colors.transparent,
                  ),
                  child:
                      isSelected
                          ? const Center(
                            child: Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 10,
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color:
                        isSelected
                            ? const Color(0xFFFF2442)
                            : const Color(0xFF262626),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color:
                    isSelected
                        ? const Color(0xFFFF2442).withOpacity(0.7)
                        : const Color(0xFF8C8C8C),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 评论设置
  Widget _buildCommentSection() {
    final disableComment = _config['disable_comment'] as bool? ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '评论设置',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '关闭评论',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF262626),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '开启后，其他用户无法评论此笔记',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Switch(
                value: disableComment,
                onChanged: (value) {
                  setState(() {
                    _config['disable_comment'] = value;
                  });
                },
                activeColor: const Color(0xFFFF2442),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 位置设置
  Widget _buildLocationSection() {
    final selectedLocation =
        _config['selected_location'] as Map<String, dynamic>?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '添加地点',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () {
            // TODO: 实现位置选择功能
            Get.snackbar('提示', '位置选择功能开发中');
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFFAFAFA),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: const Color(0xFFD9D9D9)),
            ),
            child: Row(
              children: [
                const Icon(Icons.location_on, color: Color(0xFF8C8C8C)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    selectedLocation != null
                        ? selectedLocation['name'] ?? '未知位置'
                        : '点击添加地点',
                    style: TextStyle(
                      color:
                          selectedLocation != null
                              ? const Color(0xFF262626)
                              : const Color(0xFFBFBFBF),
                    ),
                  ),
                ),
                if (selectedLocation != null) ...[
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _config['selected_location'] = null;
                      });
                    },
                    child: const Icon(
                      Icons.clear,
                      color: Color(0xFF8C8C8C),
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                const Icon(Icons.chevron_right, color: Color(0xFF8C8C8C)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 保存配置
  void _saveConfig() {
    // TODO: 保存配置到PublishLogic
    Get.back(result: _config);
  }
}
