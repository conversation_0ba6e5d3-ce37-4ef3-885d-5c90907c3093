import 'package:aitoearn_app/enums/ai_create_type.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/widgets/ai_create_button.dart';
import 'package:aitoearn_app/widgets/topic_selector.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 抖音发布配置页面
class DouyinConfigPage extends StatefulWidget {
  final Map<String, dynamic> account;
  final PublishLogic logic;
  final Map<String, dynamic> initialConfig;

  const DouyinConfigPage({
    required this.account, required this.logic, required this.initialConfig, super.key,
  });

  @override
  State<DouyinConfigPage> createState() => _DouyinConfigPageState();
}

class _DouyinConfigPageState extends State<DouyinConfigPage> {
  late Map<String, dynamic> _config;
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _config = Map<String, dynamic>.from(widget.initialConfig);

    // 初始化控制器
    _titleController.text = _config['custom_title'] ?? '';
    _descController.text = _config['description'] ?? '';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          title: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Center(
                  child: Text(
                    '抖',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.account['nickname'] ?? '抖音账号'}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: _saveConfig,
              child: const Text(
                '保存',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题配置
              _buildTitleSection(),
              const SizedBox(height: 24),

              // 描述配置
              _buildDescriptionSection(),
              const SizedBox(height: 24),

              // 话题配置
              _buildTopicsSection(),
              const SizedBox(height: 24),

              // 自主声明
              _buildSelfDeclareSection(),
              const SizedBox(height: 24),

              // 谁可以看
              _buildVisibilitySection(),
              const SizedBox(height: 24),

              // @好友
              _buildMentionSection(),
              const SizedBox(height: 24),

              // 活动参与
              _buildActivitySection(),
              const SizedBox(height: 24),

              // 热点关联
              _buildHotspotSection(),
              const SizedBox(height: 24),

              // 合集选择
              _buildMixSection(),
              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  // 标题配置
  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标题',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _titleController,
            maxLength: 30,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '好的标题可以获得更多浏览',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) {
              _config['custom_title'] = value;
            },
          ),
        ),
        const SizedBox(height: 8),
        AiCreateButton(
          type: AiCreateType.title,
          tips: '基于视频内容智能生成标题',
          videoFilePath:
              widget.logic.state.videoPath.value.isNotEmpty
                  ? widget.logic.state.videoPath.value
                  : null,
          uploadedVideoUrl:
              widget.logic.state.uploadedVideoUrl.value.isNotEmpty
                  ? widget.logic.state.uploadedVideoUrl.value
                  : null,
          maxLength: 30,
          onAiCreateFinish: (text) {
            _titleController.text = text;
            _config['custom_title'] = text;
          },
        ),
      ],
    );
  }

  // 描述配置
  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _descController,
            maxLength: 1000,
            maxLines: 4,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '添加作品简介',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) {
              _config['description'] = value;
            },
          ),
        ),
        const SizedBox(height: 8),
        AiCreateButton(
          type: AiCreateType.description,
          tips: '基于视频内容智能生成描述',
          videoFilePath:
              widget.logic.state.videoPath.value.isNotEmpty
                  ? widget.logic.state.videoPath.value
                  : null,
          uploadedVideoUrl:
              widget.logic.state.uploadedVideoUrl.value.isNotEmpty
                  ? widget.logic.state.uploadedVideoUrl.value
                  : null,
          maxLength: 1000,
          onAiCreateFinish: (text) {
            _descController.text = text;
            _config['description'] = text;
          },
        ),
      ],
    );
  }

  // 话题配置
  Widget _buildTopicsSection() {
    final accessToken =
        widget.account['accessToken'] ?? widget.account['cookie'] ?? '';
    final selectedTopics = (_config['topics'] as List<String>?) ?? [];

    return TopicSelector(
      platform: 'douyin',
      accessToken: accessToken,
      maxCount: 5,
      selectedTopics: selectedTopics,
      tips: '您可以添加5个话题',
      onChanged: (topics) {
        setState(() {
          _config['topics'] = topics;
        });
      },
    );
  }

  // 自主声明配置
  Widget _buildSelfDeclareSection() {
    final selfDeclare = _config['self_declare'] as String?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '自主声明',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selfDeclare,
              hint: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Text('选择声明', style: TextStyle(color: Color(0xFFBFBFBF))),
              ),
              isExpanded: true,
              icon: const Padding(
                padding: EdgeInsets.only(right: 12),
                child: Icon(
                  Icons.keyboard_arrow_down,
                  color: Color(0xFF8C8C8C),
                ),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'self_shoot',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('内容自行拍摄'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'from_net_v3',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('内容取材网络'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'aigc',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('内容由AI生成'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'maybe_unsuitable',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('可能引人不适'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'only_fun_new',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('虚构演绎，仅供娱乐'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'dangerous_behavior',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('危险行为，请勿模仿'),
                  ),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _config['self_declare'] = value;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  // 可见性配置
  Widget _buildVisibilitySection() {
    final visibilityType = _config['visibility_type'] as int? ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '谁可以看',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildVisibilityOption(
                value: 0,
                groupValue: visibilityType,
                title: '公开',
                subtitle: '(所有人可见)',
                onChanged: (value) {
                  setState(() {
                    _config['visibility_type'] = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildVisibilityOption(
                value: 2,
                groupValue: visibilityType,
                title: '好友可见',
                subtitle: '',
                onChanged: (value) {
                  setState(() {
                    _config['visibility_type'] = value;
                  });
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildVisibilityOption(
                value: 1,
                groupValue: visibilityType,
                title: '私密',
                subtitle: '(仅自己可见)',
                onChanged: (value) {
                  setState(() {
                    _config['visibility_type'] = value;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 可见性选项组件
  Widget _buildVisibilityOption({
    required int value,
    required int groupValue,
    required String title,
    required String subtitle,
    required ValueChanged<int?> onChanged,
  }) {
    final isSelected = value == groupValue;

    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF6366F1) : Colors.white,
          border: Border.all(
            color:
                isSelected ? const Color(0xFF6366F1) : const Color(0xFFD9D9D9),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color:
                          isSelected ? Colors.white : const Color(0xFFD9D9D9),
                      width: 2,
                    ),
                    color: isSelected ? Colors.white : Colors.transparent,
                  ),
                  child:
                      isSelected
                          ? Center(
                            child: Container(
                              width: 6,
                              height: 6,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Color(0xFF6366F1),
                              ),
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 6),
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color:
                          isSelected ? Colors.white : const Color(0xFF262626),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            if (subtitle.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 11,
                  color:
                      isSelected
                          ? Colors.white.withOpacity(0.8)
                          : const Color(0xFF8C8C8C),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  // @好友配置
  Widget _buildMentionSection() {
    final mentionedUsers =
        _config['mentioned_users'] as List<Map<String, String>>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              '@好友',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF262626),
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: const Color(0xFFF0F0F0),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Text(
                '您可以添加100个好友',
                style: TextStyle(fontSize: 12, color: Color(0xFF8C8C8C)),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          constraints: const BoxConstraints(minHeight: 80),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (mentionedUsers.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      mentionedUsers.map((user) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFF1890FF),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                '@${user['nickname'] ?? ''}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(width: 4),
                              GestureDetector(
                                onTap: () {
                                  setState(() {
                                    mentionedUsers.remove(user);
                                    _config['mentioned_users'] = mentionedUsers;
                                  });
                                },
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 14,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                ),
                const SizedBox(height: 12),
              ],
              InkWell(
                onTap: () {
                  Get.snackbar('提示', '好友选择功能开发中');
                },
                borderRadius: BorderRadius.circular(4),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: const Color(0xFF1890FF)),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.add, color: Color(0xFF1890FF), size: 16),
                      SizedBox(width: 4),
                      Text(
                        '添加好友',
                        style: TextStyle(
                          color: Color(0xFF1890FF),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivitySection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '活动参与功能开发中',
        style: TextStyle(color: Color(0xFF8C8C8C)),
      ),
    );
  }

  Widget _buildHotspotSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '热点关联功能开发中',
        style: TextStyle(color: Color(0xFF8C8C8C)),
      ),
    );
  }

  Widget _buildMixSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '合集选择功能开发中',
        style: TextStyle(color: Color(0xFF8C8C8C)),
      ),
    );
  }

  // 保存配置
  void _saveConfig() {
    // TODO: 保存配置到PublishLogic
    Get.back(result: _config);
  }
}
