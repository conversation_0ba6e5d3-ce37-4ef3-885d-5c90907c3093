import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/enums/ai_create_type.dart';
import 'package:aitoearn_app/models/draft_models/draft_model.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_boxes_page.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/pages/publish/widgets/account_selector.dart';
import 'package:aitoearn_app/pages/publish/widgets/bottom_action_bar.dart';
import 'package:aitoearn_app/pages/publish/widgets/media_preview_section.dart';
import 'package:aitoearn_app/pages/publish/widgets/publish_progress_dialog.dart';
import 'package:aitoearn_app/pages/publish/widgets/publish_result_dialog.dart';
import 'package:aitoearn_app/pages/publish/widgets/schedule_section.dart';
import 'package:aitoearn_app/pages/publish/widgets/tag_section.dart';
import 'package:aitoearn_app/widgets/ai_create_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class VideoPublishPage extends StatefulWidget {
  const VideoPublishPage({super.key});

  @override
  State<VideoPublishPage> createState() => _VideoPublishPageState();
}

class _VideoPublishPageState extends State<VideoPublishPage> {
  // 通过Get.find获取控制器
  late final PublishLogic logic;

  // 内容来源类型
  String sourceType = 'album';

  // 选中的账号列表
  List<Map<String, dynamic>> selectedAccounts = [];

  // 标题和简介的文本控制器
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  // 焦点节点
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _descriptionFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    logic = Get.find<PublishLogic>();

    // 在构建完成后清除所有内容并加载
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 先清除所有内容
      _clearAllContent();

      // 获取路由参数
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>? ??
          Get.arguments as Map<String, dynamic>?;
      if (args != null && args.containsKey('sourceType')) {
        // 更新 sourceType
        final newSourceType = args['sourceType'] ?? 'album';
        if (sourceType != newSourceType) {
          setState(() {
            sourceType = newSourceType;
          });
        }

        // 根据来源类型加载内容
        switch (sourceType) {
          case 'album':
            // 从相册加载图片
            LoggerUtil.i('从相册加载图片内容');
            break;

          case 'video':
            // 从相册加载视频
            LoggerUtil.i('从相册加载视频内容');
            break;

          case 'draft':
            // 从草稿箱加载
            LoggerUtil.i('从草稿箱加载内容');
            _loadDraftContent(args);
            break;

          case 'gallery':
            // 从图片库加载
            LoggerUtil.i('从图片库加载内容');
            break;
        }
      }
    });

    // 设置文本控制器的监听
    _titleController.addListener(() {
      logic.setVideoTitle(_titleController.text);
    });

    _descriptionController.addListener(() {
      logic.setVideoDescription(_descriptionController.text);
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _titleFocusNode.dispose();
    _descriptionFocusNode.dispose();
    super.dispose();
  }

  // 清除所有内容
  void _clearAllContent() {
    LoggerUtil.i('清除所有内容');

    // 清空文本控制器
    _titleController.text = '';
    _descriptionController.text = '';

    // 使用 logic 提供的方法清空状态
    logic.clearAllContent();

    // 清空本地变量
    selectedAccounts = [];
  }

  // 加载草稿内容
  void _loadDraftContent(Map<String, dynamic>? args) {
    if (args == null) {
      LoggerUtil.e('加载草稿内容失败：参数为空');
      return;
    }

    // 如果没有草稿对象，说明是新建草稿
    if (!args.containsKey('draft')) {
      LoggerUtil.i('新建草稿，不需要加载草稿内容');

      // 如果有草稿箱ID，记录下来以便保存时使用
      if (args.containsKey('boxId')) {
        logic.state.draftBoxId.value = args['boxId'] as String;
        LoggerUtil.i('设置草稿箱ID: ${logic.state.draftBoxId.value}');
      }

      return;
    }

    try {
      // 获取草稿对象
      final DraftItem draft = args['draft'] as DraftItem;
      LoggerUtil.i('加载草稿：${draft.title}');

      // 设置标题和描述
      _titleController.text = draft.title;
      _descriptionController.text = draft.description;

      // 设置标签
      if (draft.tags.isNotEmpty) {
        for (final tag in draft.tags) {
          logic.addTag(tag);
        }
      }

      // 设置位置信息
      if (draft.locationInfo != null && draft.locationInfo!.isNotEmpty) {
        logic.setLocationInfo(draft.locationInfo!);
      }

      // 设置定时发布
      if (draft.isScheduled && draft.scheduledTime != null) {
        logic.setScheduledTime(draft.scheduledTime!);
      }

      // 设置选中的账号
      if (draft.selectedAccounts != null &&
          draft.selectedAccounts!.isNotEmpty) {
        // 使用 logic 的方法设置账号，而不是直接修改 state
        List<Map<String, dynamic>> accounts = List<Map<String, dynamic>>.from(
          draft.selectedAccounts!,
        );
        logic.setSelectedAccounts(accounts);

        // 同步更新本地变量，用于初始化账号选择对话框
        selectedAccounts = accounts;
      }

      // 设置视频或图片 - 互斥处理
      if (draft.videoPath != null && draft.videoPath!.isNotEmpty) {
        // 加载视频
        final videoFile = File(draft.videoPath!);
        if (videoFile.existsSync()) {
          logic.setVideoPath(draft.videoPath!);

          // 如果有缩略图，设置缩略图
          if (draft.thumbnailPath != null && draft.thumbnailPath!.isNotEmpty) {
            final thumbnailFile = File(draft.thumbnailPath!);
            if (thumbnailFile.existsSync()) {
              logic.state.videoThumbnail.value = thumbnailFile;
            }
          } else {
            // 没有缩略图，不尝试生成
            LoggerUtil.i('视频没有缩略图');
          }
        } else {
          LoggerUtil.e('视频文件不存在：${draft.videoPath}');
          // 延迟显示提示，确保在构建完成后执行
          Future.microtask(() {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('video_publish.video_file_not_exist'.tr),
                ),
              );
            }
          });
        }
      } else if (draft.imagePaths != null && draft.imagePaths!.isNotEmpty) {
        // 加载图片
        List<File> validImages = [];
        for (final path in draft.imagePaths!) {
          final imageFile = File(path);
          if (imageFile.existsSync()) {
            validImages.add(imageFile);
          }
        }

        if (validImages.isNotEmpty) {
          logic.state.selectedImages.clear();
          logic.state.selectedImages.addAll(validImages);
        } else {
          LoggerUtil.e('所有图片文件均不存在');
          // 延迟显示提示，确保在构建完成后执行
          Future.microtask(() {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('video_publish.image_file_not_exist'.tr),
                ),
              );
            }
          });
        }
      }

      // 设置草稿ID和草稿箱ID，方便后续更新草稿
      if (args.containsKey('draftId')) {
        logic.state.draftId.value = args['draftId'] as String;
        LoggerUtil.i('设置草稿ID: ${logic.state.draftId.value}');
      }

      if (args.containsKey('boxId')) {
        logic.state.draftBoxId.value = args['boxId'] as String;
        LoggerUtil.i('设置草稿箱ID: ${logic.state.draftBoxId.value}');
      }
    } catch (e) {
      LoggerUtil.e('加载草稿内容异常：$e');
      // 延迟显示提示，确保在构建完成后执行
      Future.microtask(() {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'video_publish.load_draft_failed'.trParams({
                  'error': e.toString(),
                }),
              ),
            ),
          );
        }
      });
    }
  }

  // 清除所有焦点
  void _clearFocus() {
    _titleFocusNode.unfocus();
    _descriptionFocusNode.unfocus();
    FocusScope.of(context).unfocus();
  }

  // 选择内容（图片或视频）
  void _selectContent() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text('video_publish.select_from_album'.tr),
                onTap: () async {
                  Navigator.pop(context); // 关闭底部菜单

                  // 清除旧内容
                  logic.state.videoPath.value = '';
                  logic.state.videoThumbnail.value = null;
                  logic.state.selectedImages.clear();

                  // 选择新图片
                  await logic.pickMultiImages();
                },
              ),
              ListTile(
                leading: const Icon(Icons.videocam),
                title: Text('video_publish.select_video_from_album'.tr),
                onTap: () async {
                  Navigator.pop(context); // 关闭底部菜单

                  // 清除旧内容
                  logic.state.videoPath.value = '';
                  logic.state.videoThumbnail.value = null;
                  logic.state.selectedImages.clear();

                  // 选择新视频
                  await logic.pickVideoFromGallery();
                },
              ),
              ListTile(
                leading: const Icon(Icons.folder_special),
                title: Text('video_publish.select_from_draft'.tr),
                onTap: () async {
                  Navigator.pop(context); // 关闭底部菜单

                  // 使用 Navigator.push 而不是 Get.toNamed，以便能够正确返回
                  final args =
                      ModalRoute.of(context)?.settings.arguments
                          as Map<String, dynamic>? ??
                      Get.arguments as Map<String, dynamic>?;
                  final fromDraftDetail = args?['fromDraftDetail'] ?? false;

                  if (fromDraftDetail) {
                    // 如果是从草稿详情页来的，直接返回
                    Navigator.pop(context);
                  } else {
                    // 否则导航到草稿箱列表页
                    await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DraftBoxesPage(),
                      ),
                    );
                  }
                },
              ),
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  // 显示发布进度弹窗
  void _showPublishProgressDialog() {
    Get.dialog(PublishProgressDialog(logic: logic), barrierDismissible: false);
  }

  // 显示发布结果详情
  void _showPublishResultDetails(List<Map<String, dynamic>> tasks) {
    // 确保组件仍然挂载
    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return PublishResultDialog(
          tasks: tasks,
          onClose: () {
            // 关闭结果详情页
            Navigator.pop(context);

            // 关闭发布页面，确保mounted检查
            if (mounted && Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
        );
      },
    );
  }

  // 显示发布选项
  void _showPublishOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'video_publish.publish_options'.tr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              // 直接发布选项
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.publish, color: Colors.blue[700]),
                ),
                title: Text('video_publish.publish_now'.tr),
                subtitle: Text('video_publish.publish_now_desc'.tr),
                onTap: () async {
                  Navigator.pop(context); // 关闭选项菜单

                  final result = await logic.publishVideoWithPlatformModels(
                    backgroundMode: false,
                  );

                  // 发布成功后处理
                  if (result) {
                    // 如果有发布任务，显示详细结果
                    if (logic.state.publishTasks.isNotEmpty) {
                      // 复制一份任务列表，因为原列表会被清空
                      final tasks = List<Map<String, dynamic>>.from(
                        logic.state.publishTasks,
                      );

                      // 直接显示结果详情，不使用延迟
                      if (mounted) {
                        _showPublishResultDetails(tasks);
                      }
                    } else {
                      // 如果没有任务或已经清空，直接关闭页面
                      if (mounted && Navigator.canPop(context)) {
                        Navigator.pop(context);
                      }
                    }
                  }
                },
              ),
              const SizedBox(height: 10),
              // 后台发布选项
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.send_to_mobile, color: Colors.green[700]),
                ),
                title: Text('video_publish.publish_background'.tr),
                subtitle: Text('video_publish.publish_background_desc'.tr),
                onTap: () async {
                  Navigator.pop(context); // 关闭选项菜单

                  // 执行后台发布
                  final result = await logic.publishVideoWithPlatformModels(
                    backgroundMode: true,
                  );

                  // 发布开始后关闭发布页面
                  if (result && mounted && Navigator.canPop(context)) {
                    Navigator.pop(context);
                  }
                },
              ),
              const SizedBox(height: 20),
              // 取消按钮
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    side: BorderSide(color: Colors.grey[300]!),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: Text('video_publish.cancel'.tr),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 输入字段
  Widget _buildInputField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String hint,
    AiCreateType? aiType,
    int? maxLength,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            controller: controller,
            focusNode: focusNode,
            autofocus: false,
            maxLength: maxLength,
            maxLines: aiType == AiCreateType.description ? 3 : 1,
            // 确保不自动获取焦点
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(color: Colors.grey[400]),
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
              counterText: '', // 隐藏字符计数器
            ),
            style: const TextStyle(fontSize: 16, color: Colors.black87),
          ),
          // AI生成按钮
          if (aiType != null) ...[
            const SizedBox(height: 8),
            Obx(() {
              // 获取当前视频路径和已上传的URL
              final videoPath = logic.state.videoPath.value;
              final uploadedUrl = logic.state.uploadedVideoUrl.value;

              return AiCreateButton(
                type: aiType,
                tips:
                    aiType == AiCreateType.title
                        ? '基于视频内容智能生成标题'
                        : '基于视频内容智能生成描述',
                videoFilePath: videoPath.isNotEmpty ? videoPath : null,
                uploadedVideoUrl: uploadedUrl.isNotEmpty ? uploadedUrl : null,
                maxLength:
                    maxLength ?? (aiType == AiCreateType.title ? 30 : 500),
                onAiCreateFinish: (text) {
                  controller.text = text;
                  // 手动触发监听器
                  if (aiType == AiCreateType.title) {
                    logic.setVideoTitle(text);
                  } else {
                    logic.setVideoDescription(text);
                  }
                },
              );
            }),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _clearFocus, // 点击空白区域时清除焦点
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          title: Text(
            'video_publish.title'.tr,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.black,
              size: 20,
            ),
            onPressed: () {
              // 获取路由参数，判断是否来自草稿详情页
              final args =
                  ModalRoute.of(context)?.settings.arguments
                      as Map<String, dynamic>? ??
                  Get.arguments as Map<String, dynamic>?;
              final fromDraftDetail = args?['fromDraftDetail'] ?? false;
              final draftId = args?['draftId'];
              final boxId = args?['boxId'];

              // 如果是从草稿详情页来的，返回时可能需要刷新草稿列表
              if (fromDraftDetail && draftId != null) {
                LoggerUtil.i('从草稿详情页返回，可能需要刷新草稿列表');
                // 直接返回，刷新逻辑已在草稿详情页的 Navigator.push().then() 中处理
                Navigator.of(context).pop();
              } else {
                // 普通返回
                Navigator.of(context).pop();
              }
            },
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 媒体预览区域
                    Container(
                      color: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MediaPreviewSection(
                            logic: logic,
                            onTap: _selectContent,
                          ),
                        ],
                      ),
                    ),

                    // 视频标题输入
                    _buildInputField(
                      controller: _titleController,
                      focusNode: _titleFocusNode,
                      hint: 'video_publish.add_video_title'.tr,
                      aiType: AiCreateType.title,
                      maxLength: 30,
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 10,
                      ),
                      height: 1,
                      color: Colors.grey[300],
                    ),
                    // 视频简介输入
                    _buildInputField(
                      controller: _descriptionController,
                      focusNode: _descriptionFocusNode,
                      hint: 'video_publish.add_video_description'.tr,
                      aiType: AiCreateType.description,
                      maxLength: 500,
                    ),

                    // 话题和@好友
                    TagSection(logic: logic, clearFocus: _clearFocus),
                    // 账号选择区域 - 使用新的AccountSelector组件
                    AccountSelector(
                      logic: logic,
                      onAccountsUpdated: (accounts) {
                        selectedAccounts = accounts;
                      },
                      clearFocus: _clearFocus,
                    ),
                    // 位置信息
                    // LocationSection(logic: logic),

                    // 定时发布
                    ScheduleSection(logic: logic),

                    // 高级设置
                    // ListTile(
                    //   leading: const Icon(
                    //     Icons.settings,
                    //     color: Colors.black54,
                    //   ),
                    //   title: Text(
                    //     'video_publish.advanced_settings'.tr,
                    //     style: const TextStyle(color: Colors.black87),
                    //   ),
                    //   trailing: const Icon(
                    //     Icons.chevron_right,
                    //     color: Colors.black45,
                    //   ),
                    //   contentPadding: const EdgeInsets.symmetric(
                    //     horizontal: 16,
                    //   ),
                    //   onTap: _clearFocus,
                    // ),
                  ],
                ),
              ),
            ),

            // 底部操作栏
            BottomActionBar(
              logic: logic,
              clearFocus: _clearFocus,
              showPublishOptions: _showPublishOptions,
              showPublishResultDetails: _showPublishResultDetails,
            ),
          ],
        ),
      ),
    );
  }
}
