import 'dart:io';

import 'package:get/get.dart';

class PublishState {
  // 已选择的账号ID
  final RxString selectedAccountId = ''.obs;
  
  // 已选择的账号列表
  final RxList<Map<String, dynamic>> selectedAccounts = <Map<String, dynamic>>[].obs;
  
  // 视频标题
  final RxString videoTitle = ''.obs;
  
  // 视频简介
  final RxString videoDescription = ''.obs;
  
  // 视频标签
  final RxList<String> videoTags = <String>[].obs;
  
  // 选择的位置
  final RxString location = ''.obs;
  
  // 完整的位置信息
  final Rx<Map<String, dynamic>> locationInfo = Rx<Map<String, dynamic>>({});
  
  // 是否定时发布
  final RxBool isScheduled = false.obs;
  
  // 定时发布时间
  final Rx<DateTime?> scheduledTime = Rx<DateTime?>(null);
  
  // 视频路径
  final RxString videoPath = ''.obs;

  // 上传后的视频URL
  final RxString uploadedVideoUrl = ''.obs;

  // 视频缩略图
  final Rx<File?> videoThumbnail = Rx<File?>(null);
  
  // 是否使用自定义视频封面
  final RxBool isCustomThumbnail = false.obs;
  
  // 是否正在检查内容
  final RxBool isChecking = false.obs;
  
  // 是否正在发布
  final RxBool isPublishing = false.obs;
  
  // 选择的图片列表
  final RxList<File> selectedImages = <File>[].obs;
  
  // 发布进度 (0.0 - 1.0)
  final RxDouble publishProgress = 0.0.obs;
  
  // 发布状态信息
  final RxString publishStatus = ''.obs;
  
  // 发布任务列表
  final RxList<Map<String, dynamic>> publishTasks = <Map<String, dynamic>>[].obs;
  
  // 是否在后台发布
  final RxBool isBackgroundPublishing = false.obs;
  
  // 后台发布是否完成
  final RxBool isBackgroundPublishingCompleted = false.obs;
  
  // 后台发布结果
  final Rx<Map<String, dynamic>> backgroundPublishResult = Rx<Map<String, dynamic>>({});
  
  // 草稿ID，用于更新草稿
  final RxString draftId = ''.obs;
  
  // 草稿箱ID，用于保存草稿
  final RxString draftBoxId = ''.obs;

  // 每个账号的平台特定配置
  final RxMap<String, Map<String, dynamic>> accountPlatformConfigs = <String, Map<String, dynamic>>{}.obs;

  PublishState() {
    ///Initialize variables
  }
}
