import 'package:aitoearn_app/pages/publish/widgets/publish_progress_mock_data.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    this.dashWidth = 1.0,
    this.dashSpace = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    double startY = 0;
    final paint = Paint()
      ..color = color
      ..strokeWidth = dashWidth
      ..style = PaintingStyle.stroke;
      
    while (startY < size.height) {
      // 画线段
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashWidth),
        paint,
      );
      startY += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// 发布平台模型类
class PublishPlatform {
  final String id;
  final String name;
  final String icon;
  final int count;

  PublishPlatform({
    required this.id,
    required this.name,
    required this.icon,
    required this.count,
  });
}

// 账号模型类
class PublishAccount {
  final String id;
  final String name;
  final String avatar;
  final double progress;

  PublishAccount({
    required this.id,
    required this.name,
    required this.avatar,
    required this.progress,
  });
}

// 发布历史模型类
class PublishHistoryItem {
  final String id;
  final String time; // 例如: "10:34"
  final String accountName;
  final String accountAvatar;
  final String title;
  final List<String> topics;
  final String imageUrl;
  final String status; // 例如: "已发布"
  final Map<String, int> stats; // 例如: {"views": 10000, "comments": 2400, "likes": 1300, "shares": 6800}

  PublishHistoryItem({
    required this.id,
    required this.time,
    required this.accountName,
    required this.accountAvatar,
    required this.title,
    required this.topics,
    required this.imageUrl,
    required this.status,
    required this.stats,
  });
}

// 控制器
class PublishProgressController extends GetxController {
  final RxList<PublishPlatform> platforms = <PublishPlatform>[].obs;
  final RxList<PublishAccount> accounts = <PublishAccount>[].obs;
  final RxList<PublishHistoryItem> historyItems = <PublishHistoryItem>[].obs;
  final RxString selectedPlatform = "".obs;
  final RxBool isExpanded = false.obs;

  @override
  void onInit() {
    super.onInit();
    // 模拟数据
    _loadMockData();
  }

  void _loadMockData() {
    // 从模拟数据加载
    platforms.value = PublishProgressMockData.getPlatforms();

    // 设置默认选中的平台
    if (platforms.isNotEmpty) {
      selectedPlatform.value = platforms[0].id;
    }

    // 模拟账号数据
    accounts.value = PublishProgressMockData.getAccounts();

    // 模拟历史记录数据
    historyItems.value = PublishProgressMockData.getHistoryItems();
  }

  void toggleExpand() {
    isExpanded.value = !isExpanded.value;
  }

  void selectPlatform(String platformId) {
    selectedPlatform.value = platformId;
  }
}

class PublishProgressView extends StatelessWidget {
  PublishProgressView({Key? key}) : super(key: key);

  final PublishProgressController controller = Get.put(PublishProgressController());

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 平台选择栏
          _buildPlatformSelector(),
          
          // 发布进度
          _buildPublishProgress(),
          
          // 发布历史
          Expanded(
            child: SingleChildScrollView(
              child: _buildPublishHistory(),
            ),
          ),
        ],
      ),
    );
  }

  // 平台选择器
  Widget _buildPlatformSelector() {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 5),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFEEEEEE), width: 1),
        ),
      ),
      child: Obx(
        () => Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: _buildPlatformItemsWithSeparator(),
        ),
      ),
    );
  }

  // 构建带分隔符的平台项
  List<Widget> _buildPlatformItemsWithSeparator() {
    List<Widget> items = [];
    
    for (int i = 0; i < controller.platforms.length; i++) {
      items.add(_buildPlatformItem(controller.platforms[i]));
      
      // 在除最后一项外的每个项后添加分隔线
      if (i < controller.platforms.length - 1) {
        items.add(Container(
          height: 20,
          width: 1,
          color: Colors.grey.withOpacity(0.2),
        ));
      }
    }
    
    return items;
  }

  // 单个平台项
  Widget _buildPlatformItem(PublishPlatform platform) {
    final bool isSelected = controller.selectedPlatform.value == platform.id;
    
    return GestureDetector(
      onTap: () => controller.selectPlatform(platform.id),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 平台图标
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Image.asset(
                    platform.icon,
                    width: 20,
                    height: 20,
                    fit: BoxFit.cover,
                  ),
                ),
                Gaps.hGap4,
                // 平台数量
                Text(
                  platform.count.toString(),
                  style: TextStyle(
                    color: isSelected
                        ? AppColors.primaryColor
                        : Colors.black54,
                    fontSize: 14,
                    fontWeight: isSelected
                        ? FontWeight.w500
                        : FontWeight.normal,
                  ),
                ),
              ],
            ),
            // Gaps.vGap6,
            // 下方指示条
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              height: 2,
              width: isSelected ? 24 : 0,
              decoration: BoxDecoration(
                gradient: isSelected
                    ? AppColors.blueAndPurple
                    : null,
                borderRadius: BorderRadius.circular(1),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 发布进度
  Widget _buildPublishProgress() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '发布进度',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Gaps.vGap15,
          Obx(
            () => Column(
              children: controller.accounts.map((account) {
                return _buildAccountProgress(account);
              }).toList(),
            ),
          ),
          Gaps.vGap10,
          Center(
            child: GestureDetector(
              onTap: controller.toggleExpand,
              child: Obx(
                () => Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      controller.isExpanded.value ? '收起' : '展开',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                    Icon(
                      controller.isExpanded.value
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: Colors.grey,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 账号发布进度
  Widget _buildAccountProgress(PublishAccount account) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(15),
            child: Image.asset(
              account.avatar,
              width: 30,
              height: 30,
              fit: BoxFit.cover,
            ),
          ),
          Gaps.hGap10,
          Text(
            account.name,
            style: TextStyle(fontSize: 14),
          ),
          Gaps.hGap10,
          Expanded(
            child: Stack(
              children: [
                Container(
                  height: 6,
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                Container(
                  height: 6,
                  width: (MediaQuery.of(Get.context!).size.width - 140) * account.progress / 100,
                  decoration: BoxDecoration(
                    gradient: AppColors.blueAndPurple,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ],
            ),
          ),
          Gaps.hGap10,
          Text(
            '${account.progress.toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  // 发布历史
  Widget _buildPublishHistory() {
    return Obx(
      () => Stack(
        children: [
          // 垂直时间轴线 - 虚线效果
          Positioned(
            left: 24.5, // 精确定位，使其在圆点中间
            top: 15, // 从第一个时间点下方开始
            bottom: 20,
            child: CustomPaint(
              size: const Size(1, double.infinity),
              painter: DashedLinePainter(
                color: Color(0xFF9965FF).withOpacity(0.5), // 使用紫色，与圆点颜色接近
                dashWidth: 1.5,
                dashSpace: 2,
              ),
            ),
          ),
          // 历史项列表
          Column(
            children: controller.historyItems.map((item) {
              return _buildHistoryItem(item);
            }).toList(),
          ),
        ],
      ),
    );
  }

  // 单个历史记录
  Widget _buildHistoryItem(PublishHistoryItem item) {
    return Container(
      margin: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间点
          Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 紫色圆点
                Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    color: Color(0xFF9965FF), // 精确匹配设计图中的紫色
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                Gaps.hGap10,
                // 时间文本
                Text(
                  item.time,
                  style: TextStyle(
                    color: Color(0xFF9965FF), // 精确匹配设计图中的紫色
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          // 内容卡片
          Container(
            margin: const EdgeInsets.only(left: 5),
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 左侧内容
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 账号信息
                      Row(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(15),
                            child: Image.asset(
                              item.accountAvatar,
                              width: 30,
                              height: 30,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Gaps.hGap8,
                          Expanded(
                            child: Text(
                              item.accountName,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      Gaps.vGap10,
                      // 标题
                      Text(
                        item.title,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Gaps.vGap8,
                      // 话题
                      Container(
                        height: 40,
                        child: Wrap(
                          spacing: 5,
                          runSpacing: 5,
                          children: item.topics.map((topic) {
                            return Text(
                              '#$topic',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            );
                          }).toList(),
                        ),
                      ),
                      Gaps.vGap10,
                      // 数据统计
                      Row(
                        children: [
                          _buildStatItem(Icons.visibility_outlined, '${item.stats['views']}w'),
                          Gaps.hGap15,
                          _buildStatItem(Icons.chat_bubble_outline, '${item.stats['comments']}k'),
                          Gaps.hGap15,
                          _buildStatItem(Icons.thumb_up_outlined, '${item.stats['likes']}k'),
                          Gaps.hGap15,
                          _buildStatItem(Icons.share_outlined, '${item.stats['shares']}k'),
                        ],
                      ),
                    ],
                  ),
                ),
                Gaps.hGap15,
                // 右侧图片
                Container(
                  width: 80,
                  height: 80,
                  child: Stack(
                    children: [
                      // 图片
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: AssetImage(item.imageUrl),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      // 已发布标签 - 放在图片上方
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 3,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE0F7E7),
                            borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(6),
                              topRight: Radius.circular(7),
                            ),
                          ),
                          child: Text(
                            item.status,
                            style: const TextStyle(
                              color: Color(0xFF5BCFA0),
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              height: 1.0,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 统计项
  Widget _buildStatItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey,
        ),
        Gaps.hGap4,
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
} 