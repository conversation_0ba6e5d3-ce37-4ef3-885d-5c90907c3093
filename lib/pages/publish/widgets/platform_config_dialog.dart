import 'package:aitoearn_app/config/plat_config/plat_config.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_models.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/pages/publish/widgets/platform_configs/douyin_config_widget.dart';
import 'package:aitoearn_app/pages/publish/widgets/platform_configs/wx_sph_config_widget.dart';
import 'package:aitoearn_app/pages/publish/widgets/platform_configs/xiaohongshu_config_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 平台配置对话框
class PlatformConfigDialog extends StatefulWidget {
  final Map<String, dynamic> account;
  final PublishLogic logic;

  const PlatformConfigDialog({
    required this.account,
    required this.logic,
    super.key,
  });

  static Future<Map<String, dynamic>?> show(
    BuildContext context, {
    required Map<String, dynamic> account,
    required PublishLogic logic,
  }) async {
    return showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => PlatformConfigDialog(account: account, logic: logic),
    );
  }

  @override
  State<PlatformConfigDialog> createState() => _PlatformConfigDialogState();
}

class _PlatformConfigDialogState extends State<PlatformConfigDialog> {
  late Map<String, dynamic> _config;
  late AccountPlatInfo? _platformInfo;

  final TextEditingController _titleController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeConfig();
  }

  void _initializeConfig() {
    final platform = widget.account['platform'] ?? '';

    _platformInfo = _getPlatformInfo(platform);

    // 获取账号的唯一标识
    final accountKey = '${widget.account['id']}_$platform';

    // 获取已保存的配置或创建默认配置
    _config =
        widget.logic.state.accountPlatformConfigs[accountKey] ??
        _createDefaultConfig();

    _titleController.text = _config['custom_title'] ?? '';
  }

  AccountPlatInfo? _getPlatformInfo(String platform) {
    return getPlatformInfoByString(platform);
  }

  Map<String, dynamic> _createDefaultConfig() {
    final platform = widget.account['platform'] ?? '';
    final baseConfig = {
      'custom_title': '',
      'topics': <String>[],
      'is_scheduled': false,
      'scheduled_time': null,
      'allow_comment': true,
      'allow_share': true,
      'visibility': 'public',
      'location': null,
      ...widget.logic.getPlatformSpecificParams(platform),
    };

    // 添加抖音特定的默认配置
    if (platform == 'douyin') {
      baseConfig.addAll({
        'self_declare': null,
        'visibility_type': 0, // 0: 公开, 1: 仅自己, 2: 仅好友
        'mentioned_users': <Map<String, String>>[],
        'selected_hotspot': null,
        'selected_activity': null,
        'selected_mix': null,
        'selected_music': null,
        'enable_original_sound': true,
      });
    }

    // 添加视频号特定的默认配置
    if (platform == 'wx-sph' || platform == 'wxSph') {
      baseConfig.addAll({
        'short_title': '',
        'ext_link': '',
        'is_original': false,
        'activity': null,
        'mentioned_users': <Map<String, dynamic>>[],
        'is_private': false,
      });
    }

    return baseConfig;
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白区域时让所有输入框失去焦点
        FocusScope.of(context).unfocus();
      },
      child: Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: _buildConfigForm(),
              ),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final platform = widget.account['platform'] ?? '';
    final platformName = _platformInfo?.name ?? platform;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          // 平台图标
          _buildPlatformIcon(),
          const SizedBox(width: 12),

          // 标题信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${widget.account['nickname'] ?? widget.account['name'] ?? 'video_publish.unknown_account'.tr} 发布配置',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  platformName,
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          // 关闭按钮
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
          ),
        ],
      ),
    );
  }

  Widget _buildPlatformIcon() {
    if (_platformInfo?.icon != null) {
      return Image.asset(_platformInfo!.icon, width: 24, height: 24);
    } else {
      return const Icon(Icons.account_circle, size: 24);
    }
  }

  Widget _buildConfigForm() {
    final platform = widget.account['platform'] ?? '';

    // 根据平台显示对应的配置组件，所有配置都在平台文件中
    switch (platform) {
      case 'douyin':
        return DouyinConfigWidget(
          account: widget.account,
          logic: widget.logic,
          config: _config,
          onConfigChanged: (config) {
            setState(() {
              _config = config;
            });
          },
        );
      case 'xhs':
        return XiaohongshuConfigWidget(
          account: widget.account,
          logic: widget.logic,
          config: _config,
          onConfigChanged: (config) {
            setState(() {
              _config = config;
            });
          },
        );
      case 'wx-sph':
      case 'wxSph':
        return WxSphConfigWidget(
          account: widget.account,
          logic: widget.logic,
          config: _config,
          onConfigChanged: (config) {
            setState(() {
              _config = config;
            });
          },
        );
      default:
        return Container();
    }
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveConfig,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4776E6),
                foregroundColor: Colors.white,
              ),
              child: const Text('保存配置'),
            ),
          ),
        ],
      ),
    );
  }

  void _saveConfig() {
    final accountKey = '${widget.account['id']}_${widget.account['platform']}';
    widget.logic.state.accountPlatformConfigs[accountKey] =
        Map<String, dynamic>.from(_config);

    Navigator.of(context).pop(_config);
  }
}
