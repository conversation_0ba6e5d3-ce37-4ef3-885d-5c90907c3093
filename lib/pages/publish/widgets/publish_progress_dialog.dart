import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PublishProgressDialog extends StatelessWidget {
  final PublishLogic logic;

  const PublishProgressDialog({required this.logic, super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // 防止用户通过返回键关闭弹窗
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '正在发布',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              Obx(() {
                // 计算进度百分比
                final progressPercent =
                    (logic.state.publishProgress.value * 100).toInt();

                return Column(
                  children: [
                    LinearProgressIndicator(
                      value: logic.state.publishProgress.value,
                      backgroundColor: Colors.grey[200],
                      valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                      minHeight: 8,
                    ),
                    const SizedBox(height: 10),
                    Text(
                      '$progressPercent%',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                );
              }),
              const SizedBox(height: 20),
              Obx(() {
                return Text(
                  logic.state.publishStatus.value.isEmpty
                      ? '正在将内容发布到${logic.state.selectedAccounts.length}个平台，请稍候...'
                      : logic.state.publishStatus.value,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                );
              }),
              const SizedBox(height: 10),
              // 平台发布状态列表
              Obx(() {
                if (logic.state.publishTasks.isEmpty) {
                  return Container(
                    padding: const EdgeInsets.all(16),
                    child: const Center(child: CircularProgressIndicator()),
                  );
                }

                return Container(
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: logic.state.publishTasks.length,
                    padding: const EdgeInsets.all(8),
                    itemBuilder: (context, index) {
                      final task = logic.state.publishTasks[index];
                      final platform = task['platform'] ?? '';
                      final status = task['status'] ?? '准备中';
                      final isCompleted = task['completed'] ?? false;

                      // 根据平台选择图标
                      Widget platformIcon;
                      switch (platform) {
                        case 'douyin':
                          platformIcon = Image.asset(
                            'assets/images/account/plat_icon/douyin.png',
                            width: 20,
                            height: 20,
                          );
                          break;
                        case 'xhs':
                          platformIcon = Image.asset(
                            'assets/images/account/plat_icon/xhs.png',
                            width: 20,
                            height: 20,
                          );
                          break;
                        case 'ks':
                        case 'kwai':
                          platformIcon = Image.asset(
                            'assets/images/account/plat_icon/ks.png',
                            width: 20,
                            height: 20,
                          );
                          break;
                        case 'wx-sph':
                          platformIcon = Image.asset(
                            'assets/images/account/plat_icon/wx-sph.png',
                            width: 20,
                            height: 20,
                          );
                          break;
                        default:
                          platformIcon = const Icon(
                            Icons.account_circle,
                            size: 20,
                          );
                      }

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            platformIcon,
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    task['account']['nickname'] ?? platform,
                                    style: const TextStyle(fontSize: 14),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    status,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 8),
                            isCompleted
                                ? const Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: 18,
                                )
                                : const SizedBox(
                                  width: 18,
                                  height: 18,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.blue,
                                    ),
                                  ),
                                ),
                          ],
                        ),
                      );
                    },
                  ),
                );
              }),
            ],
          ),
        ),
      ),
    );
  }
}
