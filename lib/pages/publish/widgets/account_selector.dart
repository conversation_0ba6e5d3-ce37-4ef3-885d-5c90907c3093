import 'package:aitoearn_app/config/plat_config/plat_config.dart';
import 'package:aitoearn_app/pages/publish/account_select_dialog.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/pages/publish/widgets/platform_config_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AccountSelector extends StatelessWidget {
  final PublishLogic logic;
  final Function(List<Map<String, dynamic>>) onAccountsUpdated;
  final VoidCallback clearFocus;

  const AccountSelector({
    required this.logic,
    required this.onAccountsUpdated,
    required this.clearFocus,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16, bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Padding(
          //   padding: const EdgeInsets.only(left: 16, bottom: 8),
          //   child: Text(
          //     'video_publish.select_account'.tr,
          //     style: const TextStyle(
          //       fontSize: 15,
          //       fontWeight: FontWeight.w500,
          //       color: Colors.black87,
          //     ),
          //   ),
          // ),
          SizedBox(
            height: 80,
            child: Obx(() {
              if (logic.state.selectedAccounts.isEmpty) {
                return _buildEmptyAccountList(context);
              } else {
                return _buildAccountList(context);
              }
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAccountList(BuildContext context) {
    return ListView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      children: [_buildAddAccountButton(context)],
    );
  }

  Widget _buildAccountList(BuildContext context) {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: logic.state.selectedAccounts.length + 1, // +1 用于添加按钮
      itemBuilder: (context, index) {
        // 最后一个位置放添加按钮
        if (index == logic.state.selectedAccounts.length) {
          return _buildAddAccountButton(context);
        }

        // 其他位置显示账号
        final account = logic.state.selectedAccounts[index];
        return _buildAccountItem(account);
      },
    );
  }

  Widget _buildAccountItem(Map<String, dynamic> account) {
    // 获取平台图标
    Widget platformIcon;
    String platform = account['platform'] ?? 'default';

    var platformInfo = getPlatformInfoByString(platform);
    if (platformInfo?.icon != null) {
      platformIcon = Image.asset(platformInfo!.icon, width: 16, height: 16);
    } else {
      platformIcon = const Icon(Icons.account_circle, size: 16);
    }

    return GestureDetector(
      onTap: () => _showPlatformConfigDialog(account),
      child: Container(
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          children: [
            // 头像和平台图标
            Stack(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[200],
                    border: Border.all(color: Colors.white, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    image:
                        account['avatar'] != null &&
                                account['avatar'].toString().isNotEmpty
                            ? DecorationImage(
                              image: NetworkImage(account['avatar']),
                              fit: BoxFit.cover,
                            )
                            : null,
                  ),
                  child:
                      account['avatar'] == null ||
                              account['avatar'].toString().isEmpty
                          ? const Icon(
                            Icons.person,
                            color: Colors.grey,
                            size: 30,
                          )
                          : null,
                ),
                // 平台标识
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1.5),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                    child: platformIcon,
                  ),
                ),
                // 配置指示器
                Positioned(
                  top: 0,
                  right: 0,
                  child: _buildConfigIndicator(account),
                ),
              ],
            ),

            // 账号名称
            Text(
              account['nickname'] ??
                  account['name'] ??
                  'video_publish.unknown_account'.tr,
              style: const TextStyle(fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddAccountButton(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        clearFocus();
        final result = await AccountSelectDialog.show(
          context,
          initialSelectedAccounts: logic.state.selectedAccounts,
        );

        if (result != null && result['accounts'] != null) {
          final accounts = List<Map<String, dynamic>>.from(result['accounts']);
          if (accounts.isNotEmpty) {
            logic.setSelectedAccounts(accounts);
            onAccountsUpdated(accounts);
          } else {
            logic.clearSelectedAccounts();
            onAccountsUpdated([]);
          }
        }
      },
      child: Container(
        width: 70,
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.grey[100],
                border: Border.all(color: Colors.grey[300]!, width: 1),
              ),
              child: Icon(Icons.add, color: Colors.grey[600], size: 30),
            ),
            const SizedBox(height: 4),
            Text(
              'video_publish.add_account'.tr,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // 显示平台配置对话框
  void _showPlatformConfigDialog(Map<String, dynamic> account) async {
    clearFocus();

    final result = await PlatformConfigDialog.show(
      Get.context!,
      account: account,
      logic: logic,
    );

    if (result != null) {
      // 保存配置到PublishLogic
      logic.setAccountConfig(account['id'], result);

      // 显示保存成功的提示
      Get.snackbar(
        '配置已保存',
        '${account['nickname'] ?? account['name']} 的发布配置已更新',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    }
  }

  // 构建配置指示器
  Widget _buildConfigIndicator(Map<String, dynamic> account) {
    final config = logic.getAccountPlatformConfig(account);
    final hasCustomConfig =
        config.isNotEmpty &&
        ((config['custom_title'] as String?)?.isNotEmpty == true ||
            (config['topics'] as List?)?.isNotEmpty == true ||
            config['is_scheduled'] == true ||
            config['visibility'] != 'public');

    if (!hasCustomConfig) {
      return const SizedBox.shrink();
    }

    return Container(
      width: 8,
      height: 8,
      decoration: const BoxDecoration(
        color: Colors.orange,
        shape: BoxShape.circle,
      ),
    );
  }
}
