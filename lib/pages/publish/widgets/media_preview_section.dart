import 'dart:io';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MediaPreviewSection extends StatefulWidget {
  final PublishLogic logic;
  final VoidCallback onTap;

  const MediaPreviewSection({
    required this.logic, 
    required this.onTap, 
    super.key,
  });

  @override
  State<MediaPreviewSection> createState() => _MediaPreviewSectionState();
}

class _MediaPreviewSectionState extends State<MediaPreviewSection> {
  final RxInt currentPage = 0.obs;
  final PageController pageController = PageController();
  final ScrollController thumbnailScrollController = ScrollController();
  
  // 拖动排序相关状态
  bool _isDragging = false;
  int? _draggedItemIndex;
  
  @override
  void dispose() {
    pageController.dispose();
    thumbnailScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // 如果有选中的图片，显示图片预览
      if (widget.logic.state.selectedImages.isNotEmpty) {
        return _buildImagePreviewSection();
      }

      // 如果有选中的视频，显示视频预览
      if (widget.logic.state.videoPath.value.isNotEmpty) {
        return _buildVideoPreviewSection();
      }

      // 默认显示添加按钮
      return _buildEmptyPreviewSection();
    });
  }

  // 构建空状态的预览区域
  Widget _buildEmptyPreviewSection() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Center(
        child: Container(
          height: 220,
          width: 200,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.add_photo_alternate_outlined,
                  size: 60,
                  color: Colors.grey[400]
                ),
                const SizedBox(height: 16),
                Text(
                  'video_publish.add_image_or_video'.tr,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建图片预览区域
  Widget _buildImagePreviewSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // 主图预览区域
        Container(
          height:180,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Stack(
            children: [
              // 主图预览
              Positioned.fill(
                child: PageView.builder(
                  controller: pageController,
                  itemCount: widget.logic.state.selectedImages.length,
                  onPageChanged: (index) {
                    currentPage.value = index;
                    // 滚动缩略图到相应位置
                    if (thumbnailScrollController.hasClients) {
                      thumbnailScrollController.animateTo(
                        index * 60.0, // 每个缩略图宽度为 50 + 间距 10
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                  itemBuilder: (context, index) {
                    return Center(
                      child: Image.file(
                        widget.logic.state.selectedImages[index],
                        fit: BoxFit.contain,
                      ),
                    );
                  },
                ),
              ),
              
              // 右上角删除按钮
              Positioned(
                top: 10,
                right: 10,
                child: GestureDetector(
                  onTap: () {
                    if (widget.logic.state.selectedImages.length > 1) {
                      // 删除当前显示的图片
                      widget.logic.removeImage(currentPage.value);
                      // 如果删除的是最后一张，需要调整页面
                      if (currentPage.value >= widget.logic.state.selectedImages.length) {
                        pageController.jumpToPage(widget.logic.state.selectedImages.length - 1);
                      }
                    } else {
                      // 如果只有一张图片，清空所有图片
                      widget.logic.clearImages();
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.close, color: Colors.white, size: 20),
                  ),
                ),
              ),
              
              // 图片计数器
              Positioned(
                bottom: 10,
                right: 10,
                child: Obx(() => Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    '${currentPage.value + 1}/${widget.logic.state.selectedImages.length}',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                )),
              ),
            ],
          ),
        ),
        
        // 缩略图区域 - 修改这里，移除条件，使得只有一张图片时也显示
        Container(
          height: 50,
          margin: const EdgeInsets.only(top: 8),
          child: Row(
            children: [
              // 缩略图列表
              Expanded(
                child: widget.logic.state.selectedImages.length > 1 
                  ? _buildMultiImageThumbnails()
                  : _buildSingleImageThumbnail(),
              ),
              
              // 添加更多图片按钮
              GestureDetector(
                onTap: () async {
                  await widget.logic.pickMultiImages();
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(Icons.add, color: Colors.grey[600], size: 30),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 构建多图缩略图列表
  Widget _buildMultiImageThumbnails() {
    return Obx(() {
      // 在这里获取当前选中的索引，确保Obx能够监测到它的变化
      final currentIndex = currentPage.value;
      
      return ReorderableListView.builder(
        scrollDirection: Axis.horizontal,
        scrollController: thumbnailScrollController,
        onReorder: _onReorder,
        proxyDecorator: (widget, index, animation) {
          return AnimatedBuilder(
            animation: animation,
            builder: (BuildContext context, Widget? child) {
              return Material(
                elevation: 4.0 * animation.value,
                child: child,
              );
            },
            child: widget,
          );
        },
        itemCount: widget.logic.state.selectedImages.length,
        itemBuilder: (context, index) {
          // 直接在这里比较，不再通过方法传递
          final isSelected = index == currentIndex;
          
          return Container(
            key: ValueKey('thumbnail_$index'),
            width: 50,
            margin: const EdgeInsets.only(right: 4),
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.transparent,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: GestureDetector(
              onTap: () {
                // 更新当前页面索引
                currentPage.value = index;
                // 立即跳转到选中的页面
                pageController.jumpToPage(index);
              },
              child: ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: Image.file(
                  widget.logic.state.selectedImages[index],
                  fit: BoxFit.cover,
                ),
              ),
            ),
          );
        },
      );
    });
  }

  // 构建单图缩略图
  Widget _buildSingleImageThumbnail() {
    return ListView(
      shrinkWrap: true,
      scrollDirection: Axis.horizontal,
      physics: const NeverScrollableScrollPhysics(), // 禁用滚动
      children: [
        Container(
          width: 50,
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.blue,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(3),
            child: Image.file(
              widget.logic.state.selectedImages[0],
              fit: BoxFit.cover,
            ),
          ),
        ),
      ],
    );
  }

  // 构建缩略图项 - 这个方法已不再使用，但保留它以备将来需要
  Widget _buildThumbnailItem(int index, bool isSelected) {
    return Container(
      key: ValueKey('thumbnail_$index'),
      width: 50,
      margin: const EdgeInsets.only(right: 10),
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelected ? Colors.blue : Colors.transparent,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: GestureDetector(
        onTap: () {
          // 立即更新当前页面索引，解决需要点两次才能显示选中框的问题
          currentPage.value = index;
          pageController.jumpToPage(index); // 使用jumpToPage立即切换，不使用动画
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(3),
          child: Image.file(
            widget.logic.state.selectedImages[index],
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  // 重新排序图片
  void _onReorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    setState(() {
      // 获取当前所有图片
      final List<File> images = List.from(widget.logic.state.selectedImages);
      // 重新排序
      final File item = images.removeAt(oldIndex);
      images.insert(newIndex, item);
      
      // 更新图片列表
      widget.logic.state.selectedImages.clear();
      widget.logic.state.selectedImages.addAll(images);
      
      // 更新当前显示的图片索引
      if (currentPage.value == oldIndex) {
        currentPage.value = newIndex;
        pageController.jumpToPage(newIndex);
      } else if (currentPage.value > oldIndex && currentPage.value <= newIndex) {
        currentPage.value -= 1;
      } else if (currentPage.value < oldIndex && currentPage.value >= newIndex) {
        currentPage.value += 1;
      }
    });
  }

  // 构建视频预览区域
  Widget _buildVideoPreviewSection() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // 视频缩略图
          Positioned.fill(
            child: Obx(() {
              if (widget.logic.state.videoThumbnail.value != null) {
                return Center(
                  child: Image.file(
                    widget.logic.state.videoThumbnail.value!,
                    fit: BoxFit.contain,
                  ),
                );
              }
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.video_file, size: 60, color: Colors.grey[400]),
                    const SizedBox(height: 12),
                    Text(
                      'video_publish.video_selected'.tr,
                      style: TextStyle(color: Colors.grey[700]),
                    ),
                  ],
                ),
              );
            }),
          ),
          
          // 播放图标
          Positioned.fill(
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.3),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
          
          // 编辑封面按钮
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                width: 110,
                height: 30,
                child: ElevatedButton.icon(
                  onPressed: () async {
                    // 调用选择封面图片的方法
                    await widget.logic.pickVideoThumbnailFromGallery();
                  },
                  icon: const Icon(Icons.edit, size: 16),
                  label: Text('video_publish.edit_cover'.tr,style: const TextStyle(fontSize: 10),),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.black.withOpacity(0.6),
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ),
          ),
          
          // 自定义封面指示器
          if (widget.logic.state.isCustomThumbnail.value)
            Positioned(
              top: 10,
              left: 10,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green, size: 14),
                    const SizedBox(width: 4),
                    Text(
                      'video_publish.custom_cover'.tr,
                      style: const TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ],
                ),
              ),
            ),
          
          // 删除按钮
          Positioned(
            top: 10,
            right: 10,
            child: GestureDetector(
              onTap: () {
                widget.logic.clearVideo();
              },
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, color: Colors.white, size: 20),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 