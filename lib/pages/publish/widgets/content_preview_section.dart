
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ContentPreviewSection extends StatelessWidget {
  final PublishLogic logic;
  final VoidCallback onTap;

  const ContentPreviewSection({
    required this.logic, required this.onTap, super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 260,
        decoration: BoxDecoration(
          color: const Color(0xFFF5F7FA),
          border: Border.all(color: Colors.grey[200]!, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Obx(() {
          // 如果有选中的图片，显示图片网格
          if (logic.state.selectedImages.isNotEmpty) {
            return _buildSelectedImagesGrid();
          }

          // 如果有选中的视频，显示视频预览
          if (logic.state.videoPath.value.isNotEmpty) {
            return _buildVideoPreviewContent();
          }

          // 默认显示添加按钮
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 60,
                  height: 60,
                  child: Icon(Icons.add, size: 60, color: Colors.grey[300]),
                ),
                const SizedBox(height: 8),
                Text(
                  'video_publish.add_media'.tr,
                  style: TextStyle(color: Colors.grey[500], fontSize: 14),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  // 构建选中的图片预览
  Widget _buildSelectedImagesGrid() {
    // 使用控制器来管理当前页
    final RxInt currentPage = 0.obs;
    final PageController pageController = PageController();

    // 使用PageView实现左右滑动浏览图片
    return Stack(
      children: [
        // 图片翻页显示区域
        PageView.builder(
          controller: pageController,
          itemCount: logic.state.selectedImages.length,
          itemBuilder: (context, index) {
            return Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.black,
                image: DecorationImage(
                  image: FileImage(logic.state.selectedImages[index]),
                  fit: BoxFit.contain,
                ),
              ),
            );
          },
          onPageChanged: (index) {
            currentPage.value = index;
          },
        ),

        // 图片编号指示器
        Positioned(
          bottom: 16,
          right: 16,
          child: Obx(() {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Text(
                '${currentPage.value + 1}/${logic.state.selectedImages.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            );
          }),
        ),

        // 添加更多图片按钮
        Positioned(
          bottom: 16,
          left: 16,
          child: InkWell(
            onTap: () async {
              // 已有图片时，直接调用pickMultiImages打开相册
              await logic.pickMultiImages();
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.add_photo_alternate,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'video_publish.add'.tr,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // 删除当前图片按钮
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: () {
              // 获取当前显示的图片索引
              final currentIndex = currentPage.value;

              if (logic.state.selectedImages.length > 1) {
                // 如果多于一张图片，只删除当前图片
                logic.removeImage(currentIndex);
                // 如果删除的是最后一张，需要调整页面
                if (currentIndex >= logic.state.selectedImages.length) {
                  pageController.jumpToPage(
                    logic.state.selectedImages.length - 1,
                  );
                }
              } else {
                // 如果只有一张图片，清空所有图片
                logic.clearImages();
              }
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.black54,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, size: 18, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  // 视频预览区域显示视频内容
  Widget _buildVideoPreviewContent() {
    if (logic.state.videoPath.value.isEmpty) {
      return Center(
        child: SizedBox(
          width: 60,
          height: 60,
          child: Icon(Icons.add, size: 60, color: Colors.grey[300]),
        ),
      );
    }

    // 有视频时显示视频预览
    return Stack(
      children: [
        // 视频预览内容（显示缩略图或占位）
        Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.black,
          child: Obx(() {
            // 如果有缩略图，显示缩略图
            if (logic.state.videoThumbnail.value != null) {
              return Image.file(
                logic.state.videoThumbnail.value!,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              );
            }

            // 无缩略图时显示占位内容
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.play_circle_fill,
                    size: 50,
                    color: Colors.white.withOpacity(0.8),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'video_publish.video_selected'.tr,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    logic.state.videoPath.value.split('/').last,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            );
          }),
        ),

        // 播放图标覆盖层
        Positioned.fill(
          child: Center(
            child: Icon(
              Icons.play_circle_outline,
              size: 64,
              color: Colors.white.withOpacity(0.8),
            ),
          ),
        ),

        // 删除按钮
        Positioned(
          top: 8,
          right: 8,
          child: GestureDetector(
            onTap: () {
              logic.clearVideo();
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.black54,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, size: 18, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }
}
