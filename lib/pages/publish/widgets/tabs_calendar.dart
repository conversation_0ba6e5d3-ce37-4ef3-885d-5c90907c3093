import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/publish/publish_home/logic.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TabsCalendar extends StatefulWidget {
  TabsCalendar({
    required this.selectedIndex,
    super.key,
    this.selectedColor = AppColors.primaryColor,
    this.unselectedColor = Colors.grey,
    this.lineColor,
  });

  int selectedIndex = 0;

  /// 选中的颜色
  Color selectedColor;

  /// 未选中的颜色
  Color unselectedColor;

  /// 下方line颜色
  Color? lineColor;

  @override
  _TabsCalendarState createState() => _TabsCalendarState();
}

class _TabsCalendarState extends State<TabsCalendar> {
  final PublishHomeLogic publishHomeLogic = Get.put(PublishHomeLogic());

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildTabItem('publish_calender'.tr, index: 0),
        _buildTabItem('publish_hotName'.tr, index: 1),
        _buildTabItem('publish_account'.tr, index: 2),
      ],
    );
  }

  Widget _buildTabItem(String text, {int? index, String? path}) {
    bool isSelected = widget.selectedIndex == index;

    return InkWell(
      onTap: () {
        if (path != null) {
          Get.toNamed(path)?.then((_) {
            // 如果是从账号管理页面返回
            if (path == AppRouter.accountManagerPath) {
              // 刷新账号数据
              LoggerUtil.i('从账号管理页面返回，可以在这里刷新数据');
              // 通知其他页面刷新，可以使用GetX的消息机制
              Get.find<PublishHomeLogic>().refreshData();
            }
          });
          return;
        }
        if (index != null) {
          publishHomeLogic.currentIndex.value = index;
        }
      },
      child: Column(
        children: [
          // 上方文字
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Text(
              text,
              style: TextStyle(
                fontSize: Dimens.font_sp12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color:
                    isSelected ? widget.selectedColor : widget.unselectedColor,
              ),
            ),
          ),
          Gaps.vGap5,
          // 下方线条
          Container(
            height: 2,
            width: 20,
            decoration:
                widget.lineColor == null
                    ? BoxDecoration(
                      gradient: isSelected ? AppColors.blueAndPurple : null,
                    )
                    : BoxDecoration(
                      color: isSelected ? widget.lineColor : null,
                    ),
          ),
        ],
      ),
    );
  }
}
