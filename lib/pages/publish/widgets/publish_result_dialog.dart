import 'package:flutter/material.dart';

class PublishResultDialog extends StatelessWidget {
  final List<Map<String, dynamic>> tasks;
  final VoidCallback onClose;

  const PublishResultDialog({
    required this.tasks,
    required this.onClose,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '发布结果详情',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              IconButton(icon: const Icon(Icons.close), onPressed: onClose),
            ],
          ),
          const SizedBox(height: 8),
          const Divider(),
          Expanded(
            child: ListView.builder(
              itemCount: tasks.length,
              itemBuilder: (context, index) {
                final task = tasks[index];
                final platform = task['platform'] ?? '';
                final accountName = task['account']['nickname'] ?? platform;
                final isCompleted = task['completed'] ?? false;
                final status = task['status'] ?? '';
                final statusIcon = task['statusIcon'] ?? '';
                final error = task['error'];

                // 根据平台选择图标
                Widget platformIcon;
                switch (platform) {
                  case 'douyin':
                    platformIcon = Image.asset(
                      'assets/images/account/plat_icon/douyin.png',
                      width: 24,
                      height: 24,
                    );
                    break;
                  case 'xhs':
                    platformIcon = Image.asset(
                      'assets/images/account/plat_icon/xhs.png',
                      width: 24,
                      height: 24,
                    );
                    break;
                  case 'ks':
                  case 'kwai':
                    platformIcon = Image.asset(
                      'assets/images/account/plat_icon/ks.png',
                      width: 24,
                      height: 24,
                    );
                    break;
                  case 'wx-sph':
                    platformIcon = Image.asset(
                      'assets/images/account/plat_icon/wx-sph.png',
                      width: 24,
                      height: 24,
                    );
                    break;
                  default:
                    platformIcon = const Icon(Icons.account_circle, size: 24);
                }

                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    leading: platformIcon,
                    title: Text(accountName),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            if (statusIcon.isNotEmpty) ...[
                              Text(
                                statusIcon,
                                style: const TextStyle(fontSize: 16),
                              ),
                              const SizedBox(width: 4),
                            ],
                            Text(
                              status,
                              style: TextStyle(
                                color:
                                    isCompleted
                                        ? Colors.green
                                        : (error != null
                                            ? Colors.red
                                            : Colors.orange),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        if (error != null) ...[
                          const SizedBox(height: 4),
                          // Text(
                          //   '错误: $error',
                          //   style: const TextStyle(
                          //     color: Colors.red,
                          //     fontSize: 12,
                          //   ),
                          // ),
                        ],
                      ],
                    ),
                    trailing:
                        isCompleted
                            ? const Icon(
                              Icons.check_circle,
                              color: Colors.green,
                            )
                            : const Icon(Icons.error, color: Colors.red),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          // 添加完成按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onClose,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 14),
                backgroundColor: const Color(0xFF4776E6),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
              child: const Text(
                '完成',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
