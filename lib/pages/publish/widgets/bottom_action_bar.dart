import 'package:aitoearn_app/pages/publish/draft_box_select_dialog.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BottomActionBar extends StatelessWidget {
  final PublishLogic logic;
  final VoidCallback clearFocus;
  final VoidCallback showPublishOptions;
  final Function(List<Map<String, dynamic>>) showPublishResultDetails;

  const BottomActionBar({
    required this.logic, required this.clearFocus, required this.showPublishOptions, required this.showPublishResultDetails, super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: Row(
          children: [
            // 存草稿按钮
            InkWell(
              onTap: () => _handleSaveToDraft(context),
              child: Container(
                padding: const EdgeInsets.all(10),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.save_alt, color: Colors.grey[700], size: 20),
                    const SizedBox(height: 4),
                    Text(
                      'video_publish.save_draft'.tr,
                      style: TextStyle(color: Colors.grey[700], fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(width: 12),

            // 内容安全审查按钮
            Expanded(
              flex: 3,
              child: OutlinedButton(
                onPressed: () {
                  // 清除焦点
                  clearFocus();
                  logic.checkContentSafety();
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  side: BorderSide(color: Colors.grey[300]!),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: Obx(() {
                  return Text(
                    logic.state.isChecking.value ? 'video_publish.checking'.tr : 'video_publish.content_safety_check'.tr,
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w500,
                    ),
                  );
                }),
              ),
            ),

            const SizedBox(width: 12),

            // 发布按钮
            Expanded(
              flex: 3,
              child: ElevatedButton(
                onPressed: () => _handlePublish(context),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  backgroundColor: const Color(0xFF4776E6),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: Obx(() {
                  return Text(
                    logic.state.isPublishing.value ? 'video_publish.publishing'.tr : 'video_publish.one_click_publish'.tr,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 处理保存草稿
  void _handleSaveToDraft(BuildContext context) async {
    // 清除焦点
    clearFocus();

    // 检查是否有内容可保存
    if (logic.state.videoPath.value.isEmpty &&
        logic.state.selectedImages.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('video_publish.please_select_media'.tr)));
      return;
    }

    // 检查是否是更新现有草稿
    if (logic.state.draftId.value.isNotEmpty &&
        logic.state.draftBoxId.value.isNotEmpty) {
      // 更新现有草稿
      final success = await logic.saveToDraft(logic.state.draftBoxId.value);

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('video_publish.draft_updated'.tr), backgroundColor: Colors.green),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('video_publish.draft_update_failed'.tr), backgroundColor: Colors.red),
        );
      }
    } else {
      // 显示草稿箱选择弹窗
      final result = await DraftBoxSelectDialog.show(context);

      if (result != null && result['boxId'] != null) {
        // 保存草稿
        final success = await logic.saveToDraft(result['boxId']);

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('video_publish.saved_to_draft'.tr),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('video_publish.save_draft_failed'.tr),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // 处理发布
  void _handlePublish(BuildContext context) {
    // 清除焦点
    clearFocus();

    // 检查必要的数据
    if (logic.state.selectedAccounts.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请先选择发布账号')));
      return;
    }

    if (logic.state.videoPath.value.isEmpty &&
        logic.state.selectedImages.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请先选择视频或图片')));
      return;
    }

    // 显示发布选项
    showPublishOptions();
  }
}
