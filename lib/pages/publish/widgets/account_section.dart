import 'package:aitoearn_app/pages/publish/account_select_dialog.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AccountSection extends StatelessWidget {
  final PublishLogic logic;
  final Function(List<Map<String, dynamic>>) onAccountsUpdated;
  final VoidCallback clearFocus;

  const AccountSection({
    required this.logic, required this.onAccountsUpdated, required this.clearFocus, super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        clearFocus();
        final result = await AccountSelectDialog.show(
          context,
          initialSelectedAccounts: logic.state.selectedAccounts,
        );
        clearFocus();
        if (result != null && result['accounts'] != null) {
          final accounts = List<Map<String, dynamic>>.from(result['accounts']);
          if (accounts.isNotEmpty) {
            logic.setSelectedAccounts(accounts);
            onAccountsUpdated(accounts);
          } else {
            logic.clearSelectedAccounts();
            onAccountsUpdated([]);
          }
        }
      },
      child: Container(
        height: 260,
        decoration: BoxDecoration(
          color: const Color(0xFFF5F7FA),
          border: Border.all(color: Colors.grey[200]!, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Obx(() {
          if (logic.state.selectedAccounts.isEmpty) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 简单的用户头像占位图
                Icon(Icons.person, size: 60, color: Colors.grey[300]),
                Text(
                  'video_publish.select_account'.tr,
                  style: TextStyle(color: Colors.grey[500], fontSize: 14),
                ),
              ],
            );
          } else {
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children:
                      logic.state.selectedAccounts.map((account) {
                        return _buildAccountChip(account);
                      }).toList(),
                ),
              ),
            );
          }
        }),
      ),
    );
  }

  Widget _buildAccountChip(Map<String, dynamic> account) {
    Widget platformIcon;
    String platform = account['platform'] ?? 'default';

    switch (platform) {
      case 'douyin':
        platformIcon = Image.asset(
          'assets/images/account/plat_icon/douyin.png',
          width: 20,
          height: 20,
        );
        break;
      case 'xhs':
        platformIcon = Image.asset(
          'assets/images/account/plat_icon/xhs.png',
          width: 20,
          height: 20,
        );
        break;
      case 'ks':
        platformIcon = Image.asset(
          'assets/images/account/plat_icon/ks.png',
          width: 20,
          height: 20,
        );
        break;
      case 'wx-sph':
        platformIcon = Image.asset(
          'assets/images/account/plat_icon/wx-sph.png',
          width: 20,
          height: 20,
        );
        break;
      default:
        platformIcon = const Icon(Icons.account_circle, size: 20);
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          platformIcon,
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              account['nickname'] ?? account['name'] ?? 'video_publish.unknown_account'.tr,
              style: const TextStyle(fontSize: 14, color: Colors.black87),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
