import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/services/content_security_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 内容安全检查结果对话框
class ContentSecurityResultDialog extends StatelessWidget {
  final List<PublishItemSecurityResult> results;
  final VoidCallback? onConfirm;

  const ContentSecurityResultDialog({
    super.key,
    required this.results,
    this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    final sensitiveResults = results.where((r) => r.isSensitive).toList();
    final hasError = results.any((r) => r.error != null);

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            sensitiveResults.isNotEmpty ? Icons.warning : Icons.check_circle,
            color: sensitiveResults.isNotEmpty ? Colors.orange : Colors.green,
          ),
          const SizedBox(width: 8),
          Text(
            sensitiveResults.isNotEmpty ? '检测到敏感内容' : '内容安全检测通过',
            style: TextStyle(
              color: sensitiveResults.isNotEmpty ? Colors.orange : Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (sensitiveResults.isNotEmpty) ...[
              const Text(
                '以下内容可能包含敏感信息，建议修改后再发布：',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 12),
              ...sensitiveResults.map((result) => _buildResultItem(result, true)),
            ] else ...[
              const Text(
                '所有内容均通过安全检测，可以正常发布。',
                style: TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
            
            if (hasError) ...[
              const SizedBox(height: 16),
              const Text(
                '部分内容检测失败：',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              ...results.where((r) => r.error != null).map((result) => _buildResultItem(result, false)),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('关闭'),
        ),
        if (onConfirm != null)
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm!();
            },
            child: const Text('继续发布'),
          ),
      ],
    );
  }

  Widget _buildResultItem(PublishItemSecurityResult result, bool isSensitive) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isSensitive ? Colors.orange.withOpacity(0.1) : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSensitive ? Colors.orange.withOpacity(0.3) : Colors.red.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isSensitive ? Icons.warning : Icons.error,
                size: 16,
                color: isSensitive ? Colors.orange : Colors.red,
              ),
              const SizedBox(width: 4),
              Text(
                '项目 ${result.itemId}',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: isSensitive ? Colors.orange : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            result.content.length > 100 
                ? '${result.content.substring(0, 100)}...'
                : result.content,
            style: const TextStyle(fontSize: 12),
          ),
          if (result.error != null) ...[
            const SizedBox(height: 4),
            Text(
              '错误: ${result.error}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.red,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// 内容安全检查按钮组件
class ContentSecurityCheckButton extends StatefulWidget {
  /// 获取要检查的内容列表
  final Future<List<Map<String, String>>> Function() getContents;
  
  /// 检查完成后的回调
  final Function(List<PublishItemSecurityResult>)? onCheckComplete;
  
  /// 按钮文本
  final String? buttonText;
  
  /// 按钮样式
  final ButtonStyle? buttonStyle;
  
  /// 是否禁用
  final bool disabled;

  const ContentSecurityCheckButton({
    super.key,
    required this.getContents,
    this.onCheckComplete,
    this.buttonText,
    this.buttonStyle,
    this.disabled = false,
  });

  @override
  State<ContentSecurityCheckButton> createState() => _ContentSecurityCheckButtonState();
}

class _ContentSecurityCheckButtonState extends State<ContentSecurityCheckButton> {
  bool _isChecking = false;

  Future<void> _performSecurityCheck() async {
    if (_isChecking || widget.disabled) return;

    setState(() {
      _isChecking = true;
    });

    try {
      final contentMaps = await widget.getContents();
      
      if (contentMaps.isEmpty) {
        Get.snackbar('提示', '没有找到需要检查的内容');
        return;
      }

      LoggerUtil.i('【内容安全检查】开始检查 ${contentMaps.length} 个项目');

      final contents = <String>[];
      final itemIds = <String>[];

      for (int i = 0; i < contentMaps.length; i++) {
        final contentMap = contentMaps[i];
        final title = contentMap['title'] ?? '';
        final description = contentMap['description'] ?? '';
        final itemId = contentMap['itemId'] ?? i.toString();
        
        // 组合标题和描述
        final content = '$title\n$description'.trim();
        contents.add(content);
        itemIds.add(itemId);
      }

      // 执行安全检查（并发进行加载动画）
      final results = await Future.wait([
        ContentSecurityService.checkMultipleTextSecurity(contents, itemIds: itemIds),
        ContentSecurityService.simulateLoading(milliseconds: 800),
      ]);

      final securityResults = results[0] as List<PublishItemSecurityResult>;

      // 调用回调
      widget.onCheckComplete?.call(securityResults);

      // 显示结果
      _showSecurityResults(securityResults);

    } catch (e) {
      LoggerUtil.e('【内容安全检查】检查过程出错: $e');
      Get.snackbar('错误', '内容安全检查失败: $e');
    } finally {
      setState(() {
        _isChecking = false;
      });
    }
  }

  void _showSecurityResults(List<PublishItemSecurityResult> results) {
    final sensitiveResults = results.where((r) => r.isSensitive).toList();
    
    if (sensitiveResults.isEmpty) {
      // 所有内容都正常
      Get.snackbar(
        '检测完成',
        '所有内容均通过安全检测',
        backgroundColor: Colors.green.withOpacity(0.1),
        colorText: Colors.green,
        icon: const Icon(Icons.check_circle, color: Colors.green),
      );
    } else {
      // 有敏感内容，显示详细对话框
      showDialog(
        context: context,
        builder: (context) => ContentSecurityResultDialog(
          results: results,
          onConfirm: () {
            // 用户选择继续发布的逻辑可以在这里处理
          },
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: widget.disabled || _isChecking ? null : _performSecurityCheck,
      icon: _isChecking 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.security),
      label: Text(widget.buttonText ?? '内容安全检测'),
      style: widget.buttonStyle ?? ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
      ),
    );
  }
}
