import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/models/wx_sph_platform_model.dart';
import 'package:aitoearn_app/pages/publish/widgets/common/platform_config_base_widget.dart';
import 'package:aitoearn_app/pages/publish/widgets/common/topic_selector_widget.dart';
import 'package:aitoearn_app/pages/publish/widgets/common/location_selector_widget.dart';
import 'package:aitoearn_app/pages/publish/widgets/common/mix_selector_widget.dart';
import 'package:aitoearn_app/pages/publish/widgets/common/scheduled_time_widget.dart';

/// 视频号发布配置组件
class WxSphConfigWidget extends StatefulWidget {
  final WxSphPlatformModel model;
  final Function(WxSphPlatformModel) onChanged;

  const WxSphConfigWidget({
    Key? key,
    required this.model,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<WxSphConfigWidget> createState() => _WxSphConfigWidgetState();
}

class _WxSphConfigWidgetState extends State<WxSphConfigWidget> {
  late TextEditingController _shortTitleController;
  late TextEditingController _descController;
  late TextEditingController _extLinkController;
  late TextEditingController _activitySearchController;

  List<WxSphActivity> _activityOptions = [];
  List<WxSphUser> _userOptions = [];
  bool _isLoadingActivities = false;
  bool _isLoadingUsers = false;

  @override
  void initState() {
    super.initState();
    _shortTitleController = TextEditingController(text: widget.model.shortTitle);
    _descController = TextEditingController();
    _extLinkController = TextEditingController(text: widget.model.extLink);
    _activitySearchController = TextEditingController();
  }

  @override
  void dispose() {
    _shortTitleController.dispose();
    _descController.dispose();
    _extLinkController.dispose();
    _activitySearchController.dispose();
    super.dispose();
  }

  void _updateModel(WxSphPlatformModel newModel) {
    widget.onChanged(newModel);
  }

  /// 搜索活动
  Future<void> _searchActivities(String keyword) async {
    if (keyword.isEmpty) {
      setState(() {
        _activityOptions = [];
      });
      return;
    }

    setState(() {
      _isLoadingActivities = true;
    });

    try {
      // TODO: 调用实际的API搜索活动
      // final activities = await WxSphService.searchActivities(keyword);
      
      // 模拟数据
      await Future.delayed(const Duration(milliseconds: 500));
      final mockActivities = [
        WxSphActivity(
          eventTopicId: '1',
          eventName: '${keyword}相关活动1',
          eventCreatorNickname: '创建者1',
        ),
        WxSphActivity(
          eventTopicId: '2',
          eventName: '${keyword}相关活动2',
          eventCreatorNickname: '创建者2',
        ),
      ];

      setState(() {
        _activityOptions = mockActivities;
      });
    } catch (e) {
      LoggerUtil.e('搜索活动失败: $e');
      Get.snackbar('错误', '搜索活动失败: $e');
    } finally {
      setState(() {
        _isLoadingActivities = false;
      });
    }
  }

  /// 搜索用户
  Future<void> _searchUsers(String keyword) async {
    if (keyword.isEmpty) {
      setState(() {
        _userOptions = [];
      });
      return;
    }

    setState(() {
      _isLoadingUsers = true;
    });

    try {
      // TODO: 调用实际的API搜索用户
      // final users = await WxSphService.searchUsers(keyword);
      
      // 模拟数据
      await Future.delayed(const Duration(milliseconds: 500));
      final mockUsers = [
        WxSphUser(
          username: 'user1',
          headImgUrl: 'https://example.com/avatar1.jpg',
          nickName: '${keyword}用户1',
        ),
        WxSphUser(
          username: 'user2',
          headImgUrl: 'https://example.com/avatar2.jpg',
          nickName: '${keyword}用户2',
        ),
      ];

      setState(() {
        _userOptions = mockUsers;
      });
    } catch (e) {
      LoggerUtil.e('搜索用户失败: $e');
      Get.snackbar('错误', '搜索用户失败: $e');
    } finally {
      setState(() {
        _isLoadingUsers = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PlatformConfigBaseWidget(
      title: '视频号发布配置',
      children: [
        // 短标题
        _buildShortTitleSection(),
        const SizedBox(height: 16),

        // 描述
        _buildDescriptionSection(),
        const SizedBox(height: 16),

        // 话题
        TopicSelectorWidget(
          maxCount: 10, // 视频号话题限制
          onChanged: (topics) {
            // TODO: 更新话题到通用模型
          },
        ),
        const SizedBox(height: 16),

        // @视频号
        _buildMentionUsersSection(),
        const SizedBox(height: 16),

        // 位置选择
        LocationSelectorWidget(
          onChanged: (location) {
            // TODO: 更新位置到通用模型
          },
        ),
        const SizedBox(height: 16),

        // 参与活动
        _buildActivitySection(),
        const SizedBox(height: 16),

        // 合集选择
        MixSelectorWidget(
          platform: 'wxSph',
          onChanged: (mix) {
            // TODO: 更新合集到通用模型
          },
        ),
        const SizedBox(height: 16),

        // 扩展链接
        _buildExtLinkSection(),
        const SizedBox(height: 16),

        // 声明原创
        _buildOriginalSection(),
        const SizedBox(height: 16),

        // 定时发布
        ScheduledTimeWidget(
          onChanged: (time) {
            // TODO: 更新定时时间到通用模型
          },
        ),
      ],
    );
  }

  /// 构建短标题输入区域
  Widget _buildShortTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '短标题',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          '短标题会出现在搜索、话题、活动、地点、订阅号消息、发现页红点等场景',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _shortTitleController,
          maxLength: 16,
          decoration: const InputDecoration(
            hintText: '概况视频的主要内容。字数建议6-16个字符',
            border: OutlineInputBorder(),
            counterText: '',
          ),
          onChanged: (value) {
            _updateModel(widget.model.copyWith(shortTitle: value));
          },
        ),
      ],
    );
  }

  /// 构建描述输入区域
  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _descController,
          maxLines: 4,
          maxLength: 1000,
          decoration: const InputDecoration(
            hintText: '填写更全面的描述信息，让更多人看到你吧！',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            // TODO: 更新描述到通用模型
          },
        ),
      ],
    );
  }

  /// 构建@视频号区域
  Widget _buildMentionUsersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '@视频号',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          '您可以添加10个视频号',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        // TODO: 实现用户选择器
        Container(
          height: 48,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Center(
            child: Text(
              '点击添加@视频号',
              style: TextStyle(color: Colors.grey),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建活动选择区域
  Widget _buildActivitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '参与活动',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _activitySearchController,
          decoration: InputDecoration(
            hintText: '输入关键词搜索活动',
            border: const OutlineInputBorder(),
            suffixIcon: _isLoadingActivities
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.search),
          ),
          onChanged: _searchActivities,
        ),
        if (_activityOptions.isNotEmpty) ...[
          const SizedBox(height: 8),
          Container(
            constraints: const BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
            ),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _activityOptions.length,
              itemBuilder: (context, index) {
                final activity = _activityOptions[index];
                return ListTile(
                  title: Text(activity.eventName),
                  subtitle: Text('创建者: ${activity.eventCreatorNickname}'),
                  onTap: () {
                    _updateModel(widget.model.copyWith(activity: activity));
                    _activitySearchController.text = activity.eventName;
                    setState(() {
                      _activityOptions = [];
                    });
                  },
                );
              },
            ),
          ),
        ],
        if (widget.model.activity != null) ...[
          const SizedBox(height: 8),
          Chip(
            label: Text(widget.model.activity!.eventName),
            onDeleted: () {
              _updateModel(widget.model.copyWith(activity: null));
              _activitySearchController.clear();
            },
          ),
        ],
      ],
    );
  }

  /// 构建扩展链接区域
  Widget _buildExtLinkSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '扩展链接',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _extLinkController,
          decoration: const InputDecoration(
            hintText: '粘贴链接',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            _updateModel(widget.model.copyWith(extLink: value));
          },
        ),
      ],
    );
  }

  /// 构建声明原创区域
  Widget _buildOriginalSection() {
    return Row(
      children: [
        Checkbox(
          value: widget.model.isOriginal,
          onChanged: (value) {
            _updateModel(widget.model.copyWith(isOriginal: value ?? false));
          },
        ),
        const Expanded(
          child: Text(
            '声明原创\n声明后，作品将展示原创标记，有机会获得广告收入',
            style: TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }
}
