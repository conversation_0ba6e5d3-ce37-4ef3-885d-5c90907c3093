import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_types.dart';
import 'package:flutter/material.dart';

/// 视频号发布配置组件
class WxSphConfigWidget extends StatefulWidget {
  final Map<String, dynamic> account;
  final dynamic logic;
  final Map<String, dynamic> config;
  final Function(Map<String, dynamic>) onConfigChanged;

  const WxSphConfigWidget({
    required this.account,
    required this.logic,
    required this.config,
    required this.onConfigChanged,
    super.key,
  });

  @override
  State<WxSphConfigWidget> createState() => _WxSphConfigWidgetState();
}

class _WxSphConfigWidgetState extends State<WxSphConfigWidget> {
  late Map<String, dynamic> _config;
  late TextEditingController _shortTitleController;
  late TextEditingController _extLinkController;
  late TextEditingController _activitySearchController;
  late TextEditingController _userSearchController;

  List<WxSphActivity> _activityOptions = [];
  List<WxSphUser> _userOptions = [];
  bool _isLoadingActivities = false;
  bool _isLoadingUsers = false;

  @override
  void initState() {
    super.initState();
    _config = Map<String, dynamic>.from(widget.config);

    _shortTitleController = TextEditingController(
      text: _config['short_title'] ?? '',
    );
    _extLinkController = TextEditingController(text: _config['ext_link'] ?? '');
    _activitySearchController = TextEditingController();
    _userSearchController = TextEditingController();
  }

  @override
  void dispose() {
    _shortTitleController.dispose();
    _extLinkController.dispose();
    _activitySearchController.dispose();
    _userSearchController.dispose();
    super.dispose();
  }

  void _updateConfig(String key, dynamic value) {
    setState(() {
      _config[key] = value;
    });
    widget.onConfigChanged(_config);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 短标题配置
        _buildShortTitleSection(),
        const SizedBox(height: 24),

        // @视频号配置
        _buildMentionUsersSection(),
        const SizedBox(height: 24),

        // 参与活动配置
        _buildActivitySection(),
        const SizedBox(height: 24),

        // 扩展链接配置
        _buildExtLinkSection(),
        const SizedBox(height: 24),

        // 声明原创配置
        _buildOriginalSection(),
        const SizedBox(height: 24),
      ],
    );
  }

  /// 构建短标题输入区域
  Widget _buildShortTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '短标题',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          '短标题会出现在搜索、话题、活动、地点、订阅号消息、发现页红点等场景',
          style: TextStyle(fontSize: 12, color: Color(0xFF8C8C8C)),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _shortTitleController,
            maxLength: 16,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '概况视频的主要内容。字数建议6-16个字符',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) => _updateConfig('short_title', value),
          ),
        ),
      ],
    );
  }

  /// 构建@视频号区域
  Widget _buildMentionUsersSection() {
    final mentionedUsers =
        (_config['mentioned_users'] as List?)?.cast<Map<String, dynamic>>() ??
        [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '@视频号',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 4),
        const Text(
          '您可以添加10个视频号',
          style: TextStyle(fontSize: 12, color: Color(0xFF8C8C8C)),
        ),
        const SizedBox(height: 8),

        // 已选择的用户
        if (mentionedUsers.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                mentionedUsers.map((user) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF0F0F0),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          user['nickName'] ?? '',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF262626),
                          ),
                        ),
                        const SizedBox(width: 4),
                        GestureDetector(
                          onTap: () {
                            final newUsers = List<Map<String, dynamic>>.from(
                              mentionedUsers,
                            );
                            newUsers.remove(user);
                            _updateConfig('mentioned_users', newUsers);
                          },
                          child: const Icon(
                            Icons.close,
                            size: 14,
                            color: Color(0xFF8C8C8C),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
          const SizedBox(height: 8),
        ],

        // 搜索用户输入框
        if (mentionedUsers.length < 10) ...[
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFFFAFAFA),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: const Color(0xFFD9D9D9)),
            ),
            child: TextField(
              controller: _userSearchController,
              style: const TextStyle(fontSize: 14),
              decoration: InputDecoration(
                hintText: '搜索视频号用户',
                hintStyle: const TextStyle(color: Color(0xFFBFBFBF)),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 12,
                ),
                suffixIcon:
                    _isLoadingUsers
                        ? const Padding(
                          padding: EdgeInsets.all(12),
                          child: SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        )
                        : const Icon(Icons.search, color: Color(0xFF8C8C8C)),
              ),
              onChanged: _searchUsers,
            ),
          ),
        ],
      ],
    );
  }

  /// 构建参与活动区域
  Widget _buildActivitySection() {
    final activity = _config['activity'] as Map<String, dynamic>?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '参与活动',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _activitySearchController,
            style: const TextStyle(fontSize: 14),
            decoration: InputDecoration(
              hintText: '输入关键词搜索活动',
              hintStyle: const TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              suffixIcon:
                  _isLoadingActivities
                      ? const Padding(
                        padding: EdgeInsets.all(12),
                        child: SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                      : const Icon(Icons.search, color: Color(0xFF8C8C8C)),
            ),
            onChanged: _searchActivities,
          ),
        ),

        // 已选择的活动
        if (activity != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xFFF0F0F0),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  activity['eventName'] ?? '',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF262626),
                  ),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () {
                    _updateConfig('activity', null);
                    _activitySearchController.clear();
                  },
                  child: const Icon(
                    Icons.close,
                    size: 14,
                    color: Color(0xFF8C8C8C),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 搜索活动
  Future<void> _searchActivities(String keyword) async {
    if (keyword.isEmpty) {
      setState(() {
        _activityOptions = [];
      });
      return;
    }

    setState(() {
      _isLoadingActivities = true;
    });

    try {
      if (widget.account['accessToken'] != null ||
          widget.account['cookie'] != null) {
        final cookieString =
            widget.account['accessToken'] ?? widget.account['cookie'];
        final activities = await WxSphService.searchActivities(
          keyword,
          cookieString,
        );
        setState(() {
          _activityOptions = activities;
        });
      }
    } catch (e) {
      LoggerUtil.e('搜索活动失败: $e');
    } finally {
      setState(() {
        _isLoadingActivities = false;
      });
    }
  }

  /// 搜索用户
  Future<void> _searchUsers(String keyword) async {
    if (keyword.isEmpty) {
      setState(() {
        _userOptions = [];
      });
      return;
    }

    setState(() {
      _isLoadingUsers = true;
    });

    try {
      if (widget.account['accessToken'] != null ||
          widget.account['cookie'] != null) {
        final cookieString =
            widget.account['accessToken'] ?? widget.account['cookie'];
        final users = await WxSphService.searchUsers(keyword, cookieString);
        setState(() {
          _userOptions = users;
        });
      }
    } catch (e) {
      LoggerUtil.e('搜索用户失败: $e');
    } finally {
      setState(() {
        _isLoadingUsers = false;
      });
    }
  }

  /// 构建扩展链接区域
  Widget _buildExtLinkSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '扩展链接',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _extLinkController,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '粘贴链接',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
            ),
            onChanged: (value) => _updateConfig('ext_link', value),
          ),
        ),
      ],
    );
  }

  /// 构建声明原创区域
  Widget _buildOriginalSection() {
    final isOriginal = _config['is_original'] as bool? ?? false;

    return Row(
      children: [
        GestureDetector(
          onTap: () => _updateConfig('is_original', !isOriginal),
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color:
                    isOriginal
                        ? const Color(0xFF6366F1)
                        : const Color(0xFFD9D9D9),
                width: 2,
              ),
              color: isOriginal ? const Color(0xFF6366F1) : Colors.transparent,
            ),
            child:
                isOriginal
                    ? const Icon(Icons.check, size: 12, color: Colors.white)
                    : null,
          ),
        ),
        const SizedBox(width: 8),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '声明原创',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF262626),
                ),
              ),
              Text(
                '声明后，作品将展示原创标记，有机会获得广告收入',
                style: TextStyle(fontSize: 12, color: Color(0xFF8C8C8C)),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建可见性配置区域
  Widget _buildVisibilitySection() {
    final isPrivate = _config['is_private'] as bool? ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '谁可以看',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildVisibilityOption(
                value: false,
                groupValue: isPrivate,
                title: '公开',
                onChanged: (value) => _updateConfig('is_private', !value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildVisibilityOption(
                value: true,
                groupValue: isPrivate,
                title: '私密',
                onChanged: (value) => _updateConfig('is_private', value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 可见性选项组件
  Widget _buildVisibilityOption({
    required bool value,
    required bool groupValue,
    required String title,
    required ValueChanged<bool> onChanged,
  }) {
    final isSelected = value == groupValue;

    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF6366F1) : Colors.white,
          border: Border.all(
            color:
                isSelected ? const Color(0xFF6366F1) : const Color(0xFFD9D9D9),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Colors.white : const Color(0xFFD9D9D9),
                  width: 2,
                ),
                color: isSelected ? Colors.white : Colors.transparent,
              ),
              child:
                  isSelected
                      ? Center(
                        child: Container(
                          width: 6,
                          height: 6,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Color(0xFF6366F1),
                          ),
                        ),
                      )
                      : null,
            ),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? Colors.white : const Color(0xFF262626),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
