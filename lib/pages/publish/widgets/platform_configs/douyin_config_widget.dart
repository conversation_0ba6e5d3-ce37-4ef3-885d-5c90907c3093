import 'package:aitoearn_app/enums/ai_create_type.dart';
import 'package:aitoearn_app/pages/location/douyin_location_select_page.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_poi_model.dart';
import 'package:aitoearn_app/widgets/ai_create_button.dart';
import 'package:aitoearn_app/widgets/schedule_time_picker.dart';
import 'package:aitoearn_app/widgets/topic_selector.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 抖音平台配置组件
class DouyinConfigWidget extends StatefulWidget {
  final Map<String, dynamic> account;
  final PublishLogic logic;
  final Map<String, dynamic> config;
  final Function(Map<String, dynamic>) onConfigChanged;

  const DouyinConfigWidget({
    required this.account, required this.logic, required this.config, required this.onConfigChanged, super.key,
  });

  @override
  State<DouyinConfigWidget> createState() => _DouyinConfigWidgetState();
}

class _DouyinConfigWidgetState extends State<DouyinConfigWidget> {
  late Map<String, dynamic> _config;
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _config = Map<String, dynamic>.from(widget.config);
    
    // 初始化控制器
    _titleController.text = _config['custom_title'] ?? '';
    _descController.text = _config['description'] ?? '';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descController.dispose();
    super.dispose();
  }

  void _updateConfig(String key, dynamic value) {
    setState(() {
      _config[key] = value;
    });
    widget.onConfigChanged(_config);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题配置
        _buildTitleSection(),
        const SizedBox(height: 24),
        
        // 描述配置
        _buildDescriptionSection(),
        const SizedBox(height: 24),
        
        // 话题配置
        _buildTopicsSection(),
        const SizedBox(height: 24),
        
        // 自主声明
        _buildSelfDeclareSection(),
        const SizedBox(height: 24),
        
        // 谁可以看
        _buildVisibilitySection(),
        const SizedBox(height: 24),

        // // @好友
        // _buildMentionSection(),
        // const SizedBox(height: 24),
        //
        // // 活动参与
        // _buildActivitySection(),
        // const SizedBox(height: 24),
        //
        // // 热点关联
        // _buildHotspotSection(),
        // const SizedBox(height: 24),
        //
        // // 合集选择
        // _buildMixSection(),
        // const SizedBox(height: 24),

        // 定时发布
        _buildScheduleSection(),
        const SizedBox(height: 24),

        // 位置选择
        _buildLocationSection(),
      ],
    );
  }

  // 标题配置
  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标题',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _titleController,
            maxLength: 30,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '如果不填写则使用通用标题',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) => _updateConfig('custom_title', value),
          ),
        ),
        const SizedBox(height: 8),
        AiCreateButton(
          type: AiCreateType.title,
          tips: '基于视频内容智能生成标题',
          videoFilePath: widget.logic.state.videoPath.value.isNotEmpty 
              ? widget.logic.state.videoPath.value 
              : null,
          uploadedVideoUrl: widget.logic.state.uploadedVideoUrl.value.isNotEmpty
              ? widget.logic.state.uploadedVideoUrl.value
              : null,
          maxLength: 30,
          onAiCreateFinish: (text) {
            _titleController.text = text;
            _updateConfig('custom_title', text);
          },
        ),
      ],
    );
  }

  // 描述配置
  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _descController,
            maxLength: 1000,
            maxLines: 4,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '如果不填写则使用通用描述',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) => _updateConfig('description', value),
          ),
        ),
        const SizedBox(height: 8),
        AiCreateButton(
          type: AiCreateType.description,
          tips: '基于视频内容智能生成描述',
          videoFilePath: widget.logic.state.videoPath.value.isNotEmpty 
              ? widget.logic.state.videoPath.value 
              : null,
          uploadedVideoUrl: widget.logic.state.uploadedVideoUrl.value.isNotEmpty
              ? widget.logic.state.uploadedVideoUrl.value
              : null,
          maxLength: 1000,
          onAiCreateFinish: (text) {
            _descController.text = text;
            _updateConfig('description', text);
          },
        ),
      ],
    );
  }

  // 话题配置
  Widget _buildTopicsSection() {
    final accessToken = widget.account['accessToken'] ?? widget.account['cookie'] ?? '';
    final selectedTopics = (_config['topics'] as List<String>?) ?? [];
    
    return TopicSelector(
      platform: 'douyin',
      accessToken: accessToken,
      maxCount: 5,
      selectedTopics: selectedTopics,
      tips: '您可以添加5个话题',
      onChanged: (topics) => _updateConfig('topics', topics),
    );
  }

  // 自主声明配置
  Widget _buildSelfDeclareSection() {
    final selfDeclare = _config['self_declare'] as String?;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '自主声明',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selfDeclare,
              hint: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  '选择声明',
                  style: TextStyle(color: Color(0xFFBFBFBF)),
                ),
              ),
              isExpanded: true,
              icon: const Padding(
                padding: EdgeInsets.only(right: 12),
                child: Icon(Icons.keyboard_arrow_down, color: Color(0xFF8C8C8C)),
              ),
              items: const [
                DropdownMenuItem(
                  value: 'self_shoot',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('内容自行拍摄'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'from_net_v3',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('内容取材网络'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'aigc',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('内容由AI生成'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'maybe_unsuitable',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('可能引人不适'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'only_fun_new',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('虚构演绎，仅供娱乐'),
                  ),
                ),
                DropdownMenuItem(
                  value: 'dangerous_behavior',
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Text('危险行为，请勿模仿'),
                  ),
                ),
              ],
              onChanged: (value) => _updateConfig('self_declare', value),
            ),
          ),
        ),
      ],
    );
  }

  // 可见性配置
  Widget _buildVisibilitySection() {
    final visibilityType = _config['visibility_type'] as int? ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '谁可以看',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildVisibilityOption(
                value: 0,
                groupValue: visibilityType,
                title: '公开',
                onChanged: (value) => _updateConfig('visibility_type', value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildVisibilityOption(
                value: 2,
                groupValue: visibilityType,
                title: '好友可见',
                onChanged: (value) => _updateConfig('visibility_type', value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildVisibilityOption(
                value: 1,
                groupValue: visibilityType,
                title: '私密',
                onChanged: (value) => _updateConfig('visibility_type', value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 可见性选项组件
  Widget _buildVisibilityOption({
    required int value,
    required int groupValue,
    required String title,
    required ValueChanged<int?> onChanged,
  }) {
    final isSelected = value == groupValue;

    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF6366F1) : Colors.white,
          border: Border.all(
            color: isSelected ? const Color(0xFF6366F1) : const Color(0xFFD9D9D9),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Colors.white : const Color(0xFFD9D9D9),
                      width: 2,
                    ),
                    color: isSelected ? Colors.white : Colors.transparent,
                  ),
                  child: isSelected
                      ? Center(
                          child: Container(
                            width: 6,
                            height: 6,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Color(0xFF6366F1),
                            ),
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 6),
                Flexible(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Colors.white : const Color(0xFF262626),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 其他配置方法的占位符
  Widget _buildMentionSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '@好友功能开发中',
        style: TextStyle(color: Color(0xFF8C8C8C)),
      ),
    );
  }

  Widget _buildActivitySection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '活动参与功能开发中',
        style: TextStyle(color: Color(0xFF8C8C8C)),
      ),
    );
  }

  Widget _buildHotspotSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '热点关联功能开发中',
        style: TextStyle(color: Color(0xFF8C8C8C)),
      ),
    );
  }

  Widget _buildMixSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Text(
        '合集选择功能开发中',
        style: TextStyle(color: Color(0xFF8C8C8C)),
      ),
    );
  }

  // 定时发布
  Widget _buildScheduleSection() {
    final isScheduled = _config['is_scheduled'] as bool? ?? false;
    final scheduledTime = _config['scheduled_time'] as DateTime?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '定时发布',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '启用定时发布',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF262626),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '开启后可以设置发布时间',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: isScheduled,
                    onChanged: (value) {
                      _updateConfig('is_scheduled', value);
                      if (!value) {
                        _updateConfig('scheduled_time', null);
                      }
                    },
                    activeColor: const Color(0xFF6366F1),
                  ),
                ],
              ),
              if (isScheduled) ...[
                const SizedBox(height: 16),
                const Divider(height: 1),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () {
                    WidgetsBinding.instance.addPostFrameCallback((_) async {
                      await ScheduleTimePicker.show(
                        context: context,
                        initialDateTime: scheduledTime,
                        onTimeSelected: (selectedTime) {
                          _updateConfig('scheduled_time', selectedTime);
                        },
                      );
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: const Color(0xFFD9D9D9)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.schedule, color: Color(0xFF8C8C8C)),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            scheduledTime != null
                                ? '${scheduledTime.year}-${scheduledTime.month.toString().padLeft(2, '0')}-${scheduledTime.day.toString().padLeft(2, '0')} ${scheduledTime.hour.toString().padLeft(2, '0')}:${scheduledTime.minute.toString().padLeft(2, '0')}'
                                : '选择发布时间',
                            style: TextStyle(
                              color: scheduledTime != null
                                  ? const Color(0xFF262626)
                                  : const Color(0xFFBFBFBF),
                            ),
                          ),
                        ),
                        const Icon(Icons.chevron_right, color: Color(0xFF8C8C8C)),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // 位置选择
  Widget _buildLocationSection() {
    final selectedLocation = _config['selected_location'] as DouyinPoiItem?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '添加地点',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () {
            // 延迟到下一帧执行导航操作，避免在构建过程中导航
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              final cookies = widget.account['accessToken'] ?? widget.account['cookie'] ?? '';
              if (cookies.isEmpty) {
                Get.snackbar('提示', '账号Cookie无效');
                return;
              }

              final result = await Get.to(() => DouyinLocationSelectPage(
                cookies: cookies,
              ));

              if (result != null && result is DouyinPoiItem) {
                _updateConfig('selected_location', result);
              }
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFFAFAFA),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: const Color(0xFFD9D9D9)),
            ),
            child: Row(
              children: [
                const Icon(Icons.location_on, color: Color(0xFF8C8C8C)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    selectedLocation != null
                        ? selectedLocation.address ?? '未知位置'
                        : '点击添加地点',
                    style: TextStyle(
                      color: selectedLocation != null
                          ? const Color(0xFF262626)
                          : const Color(0xFFBFBFBF),
                    ),
                  ),
                ),
                if (selectedLocation != null) ...[
                  GestureDetector(
                    onTap: () => _updateConfig('selected_location', null),
                    child: const Icon(Icons.clear, color: Color(0xFF8C8C8C), size: 18),
                  ),
                  const SizedBox(width: 8),
                ],
                const Icon(Icons.chevron_right, color: Color(0xFF8C8C8C)),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
