import 'package:aitoearn_app/enums/ai_create_type.dart';
import 'package:aitoearn_app/pages/location/xhs_location_select_page.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_poi_model.dart';
import 'package:aitoearn_app/widgets/ai_create_button.dart';
import 'package:aitoearn_app/widgets/schedule_time_picker.dart';
import 'package:aitoearn_app/widgets/topic_selector.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 小红书平台配置组件
class XiaohongshuConfigWidget extends StatefulWidget {
  final Map<String, dynamic> account;
  final PublishLogic logic;
  final Map<String, dynamic> config;
  final Function(Map<String, dynamic>) onConfigChanged;

  const XiaohongshuConfigWidget({
    super.key,
    required this.account,
    required this.logic,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<XiaohongshuConfigWidget> createState() =>
      _XiaohongshuConfigWidgetState();
}

class _XiaohongshuConfigWidgetState extends State<XiaohongshuConfigWidget> {
  late Map<String, dynamic> _config;
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _config = Map<String, dynamic>.from(widget.config);

    // 初始化控制器
    _titleController.text = _config['custom_title'] ?? '';
    _descController.text = _config['description'] ?? '';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descController.dispose();
    super.dispose();
  }

  void _updateConfig(String key, dynamic value) {
    setState(() {
      _config[key] = value;
    });
    widget.onConfigChanged(_config);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题配置
        _buildTitleSection(),
        const SizedBox(height: 24),

        // 描述配置
        _buildDescriptionSection(),
        const SizedBox(height: 24),

        // 话题配置
        _buildTopicsSection(),
        const SizedBox(height: 24),

        // 可见性配置
        _buildVisibilitySection(),
        const SizedBox(height: 24),

        // 评论设置
        _buildCommentSection(),
        const SizedBox(height: 24),

        // 位置选择
        _buildLocationSection(),
        const SizedBox(height: 24),

        // 定时发布
        _buildScheduleSection(),
      ],
    );
  }

  // 标题配置
  Widget _buildTitleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '标题',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _titleController,
            maxLength: 20,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '填写笔记标题',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) => _updateConfig('custom_title', value),
          ),
        ),
        const SizedBox(height: 8),
        AiCreateButton(
          type: AiCreateType.title,
          tips: '基于视频内容智能生成标题',
          videoFilePath:
              widget.logic.state.videoPath.value.isNotEmpty
                  ? widget.logic.state.videoPath.value
                  : null,
          uploadedVideoUrl:
              widget.logic.state.uploadedVideoUrl.value.isNotEmpty
                  ? widget.logic.state.uploadedVideoUrl.value
                  : null,
          maxLength: 20,
          onAiCreateFinish: (text) {
            _titleController.text = text;
            _updateConfig('custom_title', text);
          },
        ),
      ],
    );
  }

  // 描述配置
  Widget _buildDescriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '描述',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: TextField(
            controller: _descController,
            maxLength: 1000,
            maxLines: 4,
            style: const TextStyle(fontSize: 14),
            decoration: const InputDecoration(
              hintText: '添加笔记正文...',
              hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              counterStyle: TextStyle(color: Color(0xFF8C8C8C)),
            ),
            onChanged: (value) => _updateConfig('description', value),
          ),
        ),
        const SizedBox(height: 8),
        AiCreateButton(
          type: AiCreateType.description,
          tips: '基于视频内容智能生成描述',
          videoFilePath:
              widget.logic.state.videoPath.value.isNotEmpty
                  ? widget.logic.state.videoPath.value
                  : null,
          uploadedVideoUrl:
              widget.logic.state.uploadedVideoUrl.value.isNotEmpty
                  ? widget.logic.state.uploadedVideoUrl.value
                  : null,
          maxLength: 1000,
          onAiCreateFinish: (text) {
            _descController.text = text;
            _updateConfig('description', text);
          },
        ),
      ],
    );
  }

  // 话题配置
  Widget _buildTopicsSection() {
    final accessToken =
        widget.account['accessToken'] ?? widget.account['cookie'] ?? '';
    final selectedTopics = (_config['topics'] as List<String>?) ?? [];

    return TopicSelector(
      platform: 'xhs',
      accessToken: accessToken,
      maxCount: 10,
      selectedTopics: selectedTopics,
      tips: '您可以添加10个话题',
      onChanged: (topics) => _updateConfig('topics', topics),
    );
  }

  // 可见性配置
  Widget _buildVisibilitySection() {
    final isPrivate = _config['is_private'] as bool? ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '谁可以看',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildVisibilityOption(
                value: false,
                groupValue: isPrivate,
                title: '公开',
                subtitle: '所有人可见',
                onChanged: (value) => _updateConfig('is_private', value),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildVisibilityOption(
                value: true,
                groupValue: isPrivate,
                title: '仅自己可见',
                subtitle: '私密笔记',
                onChanged: (value) => _updateConfig('is_private', value),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 可见性选项组件
  Widget _buildVisibilityOption({
    required bool value,
    required bool groupValue,
    required String title,
    required String subtitle,
    required ValueChanged<bool> onChanged,
  }) {
    final isSelected = value == groupValue;

    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? const Color(0xFFFF2442).withOpacity(0.1)
                  : Colors.white,
          border: Border.all(
            color:
                isSelected ? const Color(0xFFFF2442) : const Color(0xFFD9D9D9),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color:
                          isSelected
                              ? const Color(0xFFFF2442)
                              : const Color(0xFFD9D9D9),
                      width: 2,
                    ),
                    color:
                        isSelected
                            ? const Color(0xFFFF2442)
                            : Colors.transparent,
                  ),
                  child:
                      isSelected
                          ? const Center(
                            child: Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 10,
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color:
                        isSelected
                            ? const Color(0xFFFF2442)
                            : const Color(0xFF262626),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color:
                    isSelected
                        ? const Color(0xFFFF2442).withOpacity(0.7)
                        : const Color(0xFF8C8C8C),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 评论设置
  Widget _buildCommentSection() {
    final disableComment = _config['disable_comment'] as bool? ?? false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '评论设置',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '关闭评论',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF262626),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '开启后，其他用户无法评论此笔记',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Switch(
                value: disableComment,
                onChanged: (value) => _updateConfig('disable_comment', value),
                activeColor: const Color(0xFFFF2442),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 位置选择
  Widget _buildLocationSection() {
    final selectedLocation = _config['selected_location'] as XhsPoiItem?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '添加地点',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () {
            // 延迟到下一帧执行导航操作，避免在构建过程中导航
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              final cookies =
                  widget.account['accessToken'] ??
                  widget.account['cookie'] ??
                  '';
              if (cookies.isEmpty) {
                Get.snackbar('提示', '账号Cookie无效');
                return;
              }

              final result = await Get.to(
                () => XhsLocationSelectPage(cookies: cookies),
              );

              if (result != null && result is XhsPoiItem) {
                _updateConfig('selected_location', result);
              }
            });
          },
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFFAFAFA),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: const Color(0xFFD9D9D9)),
            ),
            child: Row(
              children: [
                const Icon(Icons.location_on, color: Color(0xFF8C8C8C)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    selectedLocation != null
                        ? selectedLocation.poiName ?? '未知位置'
                        : '点击添加地点',
                    style: TextStyle(
                      color:
                          selectedLocation != null
                              ? const Color(0xFF262626)
                              : const Color(0xFFBFBFBF),
                    ),
                  ),
                ),
                if (selectedLocation != null) ...[
                  GestureDetector(
                    onTap: () => _updateConfig('selected_location', null),
                    child: const Icon(
                      Icons.clear,
                      color: Color(0xFF8C8C8C),
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                const Icon(Icons.chevron_right, color: Color(0xFF8C8C8C)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 定时发布
  Widget _buildScheduleSection() {
    final isScheduled = _config['is_scheduled'] as bool? ?? false;
    final scheduledTime = _config['scheduled_time'] as DateTime?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '定时发布',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF262626),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '启用定时发布',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF262626),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '开启后可以设置发布时间',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: isScheduled,
                    onChanged: (value) {
                      _updateConfig('is_scheduled', value);
                      if (!value) {
                        _updateConfig('scheduled_time', null);
                      }
                    },
                    activeColor: const Color(0xFFFF2442),
                  ),
                ],
              ),
              if (isScheduled) ...[
                const SizedBox(height: 16),
                const Divider(height: 1),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () {
                    WidgetsBinding.instance.addPostFrameCallback((_) async {
                      await ScheduleTimePicker.show(
                        context: context,
                        initialDateTime: scheduledTime,
                        onTimeSelected: (selectedTime) {
                          _updateConfig('scheduled_time', selectedTime);
                        },
                      );
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: const Color(0xFFD9D9D9)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.schedule, color: Color(0xFF8C8C8C)),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            scheduledTime != null
                                ? '${scheduledTime.year}-${scheduledTime.month.toString().padLeft(2, '0')}-${scheduledTime.day.toString().padLeft(2, '0')} ${scheduledTime.hour.toString().padLeft(2, '0')}:${scheduledTime.minute.toString().padLeft(2, '0')}'
                                : '选择发布时间',
                            style: TextStyle(
                              color:
                                  scheduledTime != null
                                      ? const Color(0xFF262626)
                                      : const Color(0xFFBFBFBF),
                            ),
                          ),
                        ),
                        const Icon(
                          Icons.chevron_right,
                          color: Color(0xFF8C8C8C),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
