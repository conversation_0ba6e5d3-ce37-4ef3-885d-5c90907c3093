import 'dart:math' as math;

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/location/douyin_location_select_page.dart';
import 'package:aitoearn_app/pages/location/kwai_location_select_page.dart';
import 'package:aitoearn_app/pages/location/wx_sph_location_select_page.dart';
import 'package:aitoearn_app/pages/location/xhs_location_select_page.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_poi_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_kwai/models/kwai_poi_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/models/wx_sph_poi_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_poi_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LocationSection extends StatelessWidget {
  final PublishLogic logic;

  const LocationSection({required this.logic, super.key});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.location_on, color: Colors.black54),
      title: Text('video_publish.add_location'.tr, style: const TextStyle(color: Colors.black87)),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Obx(() {
            return Text(
              logic.state.location.value.isEmpty
                  ? 'video_publish.not_set'.tr
                  : logic.state.location.value,
              style: TextStyle(
                color:
                    logic.state.location.value.isEmpty
                        ? Colors.black45
                        : Colors.blue,
                fontSize: 14,
              ),
            );
          }),
          const SizedBox(width: 4),
          const Icon(Icons.chevron_right, color: Colors.black45),
        ],
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      onTap: () async {
        // 检查是否选择了账号
        if (logic.state.selectedAccounts.isEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('video_publish.please_select_account'.tr)));
          return;
        }

        // 获取第一个选中账号的平台
        final String platform =
            logic.state.selectedAccounts.first['platform'] ?? '';
        if (platform.isEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('video_publish.incomplete_account'.tr)));
          return;
        }

        // 打开对应平台的位置选择页面
        await _openLocationSelectPage(context);
      },
    );
  }

  // 打开位置选择页面
  Future<void> _openLocationSelectPage(BuildContext context) async {
    // 获取选中的平台账号
    String platform = '';

    // 从选中的账号列表中获取平台信息
    if (logic.state.selectedAccounts.isNotEmpty) {
      platform = logic.state.selectedAccounts[0]['platform'] ?? '';
    }

    LoggerUtil.i('选择的平台: $platform');

    // 根据平台打开不同的位置选择页面
    if (platform == 'douyin') {
      await _openDouyinLocationSelectPage(context);
    } else if (platform == 'ks' || platform == 'kwai') {
      await _openKwaiLocationSelectPage(context);
    } else if (platform == 'xhs') {
      await _openXhsLocationSelectPage(context);
    } else if (platform == 'wx-sph') {
      await _openWxSphLocationSelectPage(context);
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('video_publish.location_not_supported'.tr)));
    }
  }

  // 打开抖音位置选择页面
  Future<void> _openDouyinLocationSelectPage(BuildContext context) async {
    // 获取抖音accessToken和代理设置
    String accessToken = '';
    String? proxy;
    String actualPlatform = 'douyin'; // 默认平台标识

    // 从选中的账号中获取accessToken
    for (final account in logic.state.selectedAccounts) {
      if (account['platform'] == 'douyin' &&
          (account.containsKey('accessToken') || account.containsKey('cookie'))) {
        accessToken = account['accessToken'] ?? account['cookie'] ?? '';
        actualPlatform = account['platform']; // 保存实际的平台标识

        // 获取代理设置
        if (account.containsKey('proxy')) {
          proxy = account['proxy'];
        }

        LoggerUtil.i('找到抖音账号: ${account['nickname'] ?? '未知账号'}');
        LoggerUtil.i('平台: ${account['platform']}');
        break;
      }
    }

    // 检查是否有抖音账号
    if (accessToken.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('video_publish.please_select_platform_account'.trParams({'platform': '抖音'}))));
      return;
    }

    // 获取原始POI数据（如果有）
    Map<String, dynamic>? initialPoiData;
    if (logic.state.locationInfo.value.isNotEmpty &&
        logic.state.locationInfo.value['platform'] == 'douyin') {
      // 判断locationInfo是否包含current_loc或poi_list字段
      if (logic.state.locationInfo.value.containsKey('current_loc') ||
          logic.state.locationInfo.value.containsKey('poi_list')) {
        // 已经是抖音API格式的数据
        initialPoiData = logic.state.locationInfo.value;
      } else if (logic.state.locationInfo.value.containsKey('poi_object')) {
        // 包含poi_object字段，需要转换为API格式
        final poiObject = logic.state.locationInfo.value['poi_object'];
        initialPoiData = {
          'poi_list': [poiObject],
        };
      }

      if (initialPoiData != null) {
        LoggerUtil.i(
          '使用已有的POI数据: ${initialPoiData.toString().substring(0, math.min(100, initialPoiData.toString().length))}...',
        );
      }
    }

    // 使用抖音位置选择页面
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => DouyinLocationSelectPage(
              cookies: accessToken,
              proxy: proxy,
              initialPoiData: initialPoiData,
              onLocationSelected: (poi) {
                LoggerUtil.i('选择了位置: ${poi.poiName}');
              },
            ),
      ),
    );

    if (result != null && result is DouyinPoiItem) {
      logic.setLocation(result.poiName);

      // 保存完整的位置信息
      final locationInfo = {
        'id': result.poiId,
        'name': result.poiName,
        'address': result.address,
        'distance': '${(result.distance / 1000).toStringAsFixed(1)}km',
        'city': result.cityName,
        'district': result.districtName,
        'latitude': result.latitude.toString(),
        'longitude': result.longitude.toString(),
        'platform': actualPlatform, // 使用实际的平台标识
        // 保存原始POI对象
        'poi_object': result.toJson(),
      };

      logic.setLocationInfo(locationInfo);
    }
  }

  // 打开快手位置选择页面
  Future<void> _openKwaiLocationSelectPage(BuildContext context) async {
    // 获取快手accessToken和API PH
    String accessToken = '';
    String apiPh = '';
    String actualPlatform = 'kwai'; // 默认平台标识

    // 从选中的账号中获取accessToken和API PH
    for (final account in logic.state.selectedAccounts) {
      if ((account['platform'] == 'kwai' || account['platform'] == 'ks') &&
          (account.containsKey('accessToken') || account.containsKey('cookie'))) {
        accessToken = account['accessToken'] ?? account['cookie'] ?? '';
        actualPlatform = account['platform']; // 保存实际的平台标识

        // 从accessToken中提取apiPh
        final phRegex = RegExp(r'kuaishou\.web\.cp\.api_ph=([^;]+)');
        final match = phRegex.firstMatch(accessToken);
        if (match != null && match.groupCount >= 1) {
          apiPh = match.group(1) ?? '';
        }

        LoggerUtil.i('找到快手账号: ${account['nickname'] ?? '未知账号'}');
        LoggerUtil.i('平台: ${account['platform']}');
        LoggerUtil.i('API PH: $apiPh');
        break;
      }
    }

    // 检查是否有快手账号
    if (accessToken.isEmpty || apiPh.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('video_publish.please_select_platform_account'.trParams({'platform': '快手'}))));
      return;
    }

    // 获取原始POI数据（如果有）
    Map<String, dynamic>? initialPoiData;
    if (logic.state.locationInfo.value.isNotEmpty &&
        (logic.state.locationInfo.value['platform'] == 'kwai' ||
            logic.state.locationInfo.value['platform'] == 'ks')) {
      if (logic.state.locationInfo.value.containsKey('poi_object')) {
        initialPoiData = {
          'data': {
            'nearbyInfo': [logic.state.locationInfo.value['poi_object']],
          },
        };
      }

      if (initialPoiData != null) {
        LoggerUtil.i('使用已有的快手POI数据');
      }
    }

    // 使用快手位置选择页面
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => KwaiLocationSelectPage(
              cookies: accessToken,
              apiPh: apiPh,
              initialPoiData: initialPoiData,
              onLocationSelected: (poi) {
                LoggerUtil.i('选择了快手位置: ${poi.poiName}');
              },
            ),
      ),
    );

    if (result != null && result is KwaiPoiItem) {
      // 用户选择了位置
      logic.setLocation(result.poiName);

      // 保存完整的位置信息
      final locationInfo = {
        'id': result.poiId,
        'name': result.poiName,
        'address': result.address,
        'distance': '${(result.distance / 1000).toStringAsFixed(1)}km',
        'city': result.cityName,
        'district': result.districtName,
        'latitude': result.latitude.toString(),
        'longitude': result.longitude.toString(),
        'platform': actualPlatform, // 使用实际的平台标识
        // 保存原始POI对象
        'poi_object': result.toJson(),
      };

      logic.setLocationInfo(locationInfo);
    }
  }

  // 打开小红书位置选择页面
  Future<void> _openXhsLocationSelectPage(BuildContext context) async {
    String accessToken = '';

    // 从选中的账号中获取accessToken
    for (final account in logic.state.selectedAccounts) {
      if (account['platform'] == 'xhs' &&
          (account.containsKey('accessToken') || account.containsKey('cookie'))) {
        accessToken = account['accessToken'] ?? account['cookie'] ?? '';
        LoggerUtil.i('找到小红书账号: ${account['nickname'] ?? '未知账号'}');
        break;
      }
    }

    // 检查是否有小红书账号
    if (accessToken.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('video_publish.please_select_platform_account'.trParams({'platform': '小红书'}))));
      return;
    }

    // 获取原始POI数据（如果有）
    Map<String, dynamic>? initialPoiData;
    if (logic.state.locationInfo.value.isNotEmpty &&
        logic.state.locationInfo.value['platform'] == 'xhs') {
      if (logic.state.locationInfo.value.containsKey('poi_object')) {
        // 使用已保存的POI对象
        initialPoiData = {
          'data': {
            'poiList': [logic.state.locationInfo.value['poi_object']],
          },
        };
      }

      if (initialPoiData != null) {
        LoggerUtil.i('使用已有的小红书POI数据');
      }
    }

    // 使用小红书位置选择页面
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => XhsLocationSelectPage(
              cookies: accessToken,
              initialPoiData: initialPoiData,
              onLocationSelected: (poi) {
                LoggerUtil.i('选择了小红书位置: ${poi.poiName}');
              },
            ),
      ),
    );

    if (result != null && result is XhsPoiItem) {
      logic.setLocation(result.poiName);

      // 保存完整的位置信息
      final locationInfo = {
        'id': result.poiId,
        'name': result.poiName,
        'address': result.address,
        'distance': '${(result.distance / 1000).toStringAsFixed(1)}km',
        'city': result.cityName,
        'district': result.districtName,
        'latitude': result.latitude.toString(),
        'longitude': result.longitude.toString(),
        'platform': 'xhs', // 平台标识
        // 保存原始POI对象
        'poi_object': result.toJson(),
      };

      logic.setLocationInfo(locationInfo);
    }
  }

  Future<void> _openWxSphLocationSelectPage(BuildContext context) async {
    // 获取微信视频号accessToken
    String accessToken = '';
    Map<String, dynamic>? selectedAccount;

    // 从选中的账号中获取accessToken
    for (final account in logic.state.selectedAccounts) {
      if (account['platform'] == 'wx-sph' &&
          (account.containsKey('accessToken') || account.containsKey('cookie'))) {
        accessToken = account['accessToken'] ?? account['cookie'] ?? '';
        selectedAccount = account;
        LoggerUtil.i('找到微信视频号账号: ${account['nickname'] ?? '未知账号'}');
        break;
      }
    }

    // 检查是否有微信视频号账号
    if (accessToken.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('video_publish.please_select_platform_account'.trParams({'platform': '微信视频号'}))));
      return;
    }

    LoggerUtil.i('【微信视频号位置】获取到accessToken长度: ${accessToken.length}');

    // 检查accessToken是否包含重要字段
    final hasTokenRequired =
        accessToken.contains('wxuin=') ||
        accessToken.contains('passport_uin=') ||
        accessToken.contains('o_cookie=');

    if (!hasTokenRequired) {
      LoggerUtil.e('【微信视频号位置】accessToken缺少必要字段 wxuin 或 passport_uin');

      // 打印accessToken片段用于调试
      final shortToken =
          accessToken.length > 100
              ? '${accessToken.substring(0, 50)}...${accessToken.substring(accessToken.length - 50)}'
              : accessToken;
      LoggerUtil.i('【微信视频号位置】accessToken片段: $shortToken');

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('微信视频号访问令牌缺少必要字段，请重新登录账号')),
      );

      // 尽管有问题，但仍然继续，测试时可能会使用模拟数据
    }

    // 打印账号信息
    if (selectedAccount != null) {
      LoggerUtil.i(
        '【微信视频号位置】账号信息: nickname=${selectedAccount['nickname']}, platform=${selectedAccount['platform']}, online=${selectedAccount['online']}',
      );
    }

    // 获取原始POI数据（如果有）
    Map<String, dynamic>? initialPoiData;
    if (logic.state.locationInfo.value.isNotEmpty &&
        logic.state.locationInfo.value['platform'] == 'wx-sph') {
      if (logic.state.locationInfo.value.containsKey('poi_object')) {
        // 使用已保存的POI对象
        initialPoiData = {
          'searchLocationList': [logic.state.locationInfo.value['poi_object']],
        };
      }

      if (initialPoiData != null) {
        LoggerUtil.i('使用已有的微信视频号POI数据');
      }
    }

    // 使用微信视频号位置选择页面
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => WxSphLocationSelectPage(
              cookies: accessToken,
              initialPoiData: initialPoiData,
              onLocationSelected: (poi) {
                LoggerUtil.i('选择了微信视频号位置: ${poi.poiName}');
              },
            ),
      ),
    );

    if (result != null && result is WxSphPoiItem) {
      logic.setLocation(result.poiName);

      // 保存完整的位置信息
      final locationInfo = {
        'id': result.poiId,
        'name': result.poiName,
        'address': result.address,
        'distance': '${(result.distance / 1000).toStringAsFixed(1)}km',
        'city': result.cityName,
        'district': result.districtName,
        'latitude': result.latitude.toString(),
        'longitude': result.longitude.toString(),
        'platform': 'wx-sph', // 平台标识
        // 保存原始POI对象
        'poi_object': result.toJson(),
      };

      logic.setLocationInfo(locationInfo);
    }
  }
}
