import 'package:aitoearn_app/pages/publish/widgets/publish_progress_view.dart';

class PublishProgressMockData {
  // 获取平台数据
  static List<PublishPlatform> getPlatforms() {
    return [
      PublishPlatform(
        id: 'douyin',
        name: '抖音',
        icon: 'assets/images/account/plat_icon/douyin.png',
        count: 4,
      ),
      PublishPlatform(
        id: 'xiaohongshu',
        name: '红书',
        icon: 'assets/images/account/plat_icon/xhs.png',
        count: 4,
      ),
      PublishPlatform(
        id: 'weixin',
        name: '微信',
        icon: 'assets/images/account/plat_icon/wx-sph.png',
        count: 4,
      ),
      PublishPlatform(
        id: 'bilibili',
        name: '哔哩',
        icon: 'assets/images/account/plat_icon/bilibili.png',
        count: 4,
      ),
    ];
  }

  // 获取账号数据
  static List<PublishAccount> getAccounts() {
    return [
      PublishAccount(
        id: 'official',
        name: '官方账号',
        avatar: 'assets/images/account/plat_icon/douyin.png',
        progress: 86.4,
      ),
      PublishAccount(
        id: 'new',
        name: '我新建的账号',
        avatar: 'assets/images/account/plat_icon/douyin.png',
        progress: 0.0,
      ),
    ];
  }

  // 获取历史记录数据
  static List<PublishHistoryItem> getHistoryItems() {
    return [
      PublishHistoryItem(
        id: '1',
        time: '10:34',
        accountName: '账号昵称',
        accountAvatar: 'assets/images/account/plat_icon/douyin.png',
        title: '标题内容',
        topics: ['话题名称', '话题名称', '河山大好', '出去走走'],
        imageUrl: 'assets/images/publish/hotspot/hot_content.png',
        status: '已发布',
        stats: {'views': 100, 'comments': 24, 'likes': 13, 'shares': 68},
      ),
      PublishHistoryItem(
        id: '2',
        time: '12:48',
        accountName: '账号昵称',
        accountAvatar: 'assets/images/account/plat_icon/douyin.png',
        title: '标题内容',
        topics: ['话题名称', '话题名称', '河山大好', '出去走走'],
        imageUrl: 'assets/images/publish/hotspot/hot_content.png',
        status: '已发布',
        stats: {'views': 1000, 'comments': 240, 'likes': 130, 'shares': 680},
      ),
    ];
  }
} 