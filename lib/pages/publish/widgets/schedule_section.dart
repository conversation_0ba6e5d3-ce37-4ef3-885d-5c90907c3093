import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/widgets/schedule_time_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ScheduleSection extends StatelessWidget {
  final PublishLogic logic;

  const ScheduleSection({required this.logic, super.key});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.access_time, color: Colors.black54),
      title: Text('video_publish.scheduled_publish'.tr, style: const TextStyle(color: Colors.black87)),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Obx(() {
            return Text(
              logic.state.isScheduled.value
                  ? _formatScheduledTime(logic.state.scheduledTime.value)
                  : 'video_publish.not_set'.tr,
              style: TextStyle(
                color:
                    logic.state.isScheduled.value
                        ? Colors.blue
                        : Colors.black45,
                fontSize: 14,
              ),
            );
          }),
          const SizedBox(width: 4),
          const Icon(Icons.chevron_right, color: Colors.black45),
        ],
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      onTap: () {
        // 使用我们的自定义组件显示日期时间选择器
        ScheduleTimePicker.show(
          context: context,
          initialDateTime: logic.state.scheduledTime.value,
          onTimeSelected: (dateTime) {
            // 保存选择的时间
            logic.setScheduledTime(dateTime);
          },
        );
      },
    );
  }

  // 格式化日期时间显示
  String _formatScheduledTime(DateTime? dateTime) {
    if (dateTime == null) return 'video_publish.not_set'.tr;

    // 获取当前日期
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);

    String dayStr = '';
    // 判断是今天、明天还是具体日期
    if (dateTime.year == now.year &&
        dateTime.month == now.month &&
        dateTime.day == now.day) {
      dayStr = 'video_publish.today'.tr;
    } else if (dateTime.year == tomorrow.year &&
        dateTime.month == tomorrow.month &&
        dateTime.day == tomorrow.day) {
      dayStr = 'video_publish.tomorrow'.tr;
    } else {
      dayStr = 'video_publish.month_day'.trParams({
        'month': dateTime.month.toString(),
        'day': dateTime.day.toString()
      });
    }

    // 格式化时间，小时和分钟都显示两位数
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$dayStr $hour:$minute';
  }
}
