import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TagSection extends StatelessWidget {
  final PublishLogic logic;
  final VoidCallback clearFocus;

  const TagSection({required this.logic, required this.clearFocus, super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      child: Row(
        children: [
          // TextButton.icon(
          //   onPressed: () {
          //     clearFocus();
          //   },
          //   label: Text('video_publish.topic'.tr, style: const TextStyle(color: Colors.blue)),
          //   style: TextButton.styleFrom(
          //     padding: const EdgeInsets.symmetric(horizontal: 8),
          //     minimumSize: Size.zero,
          //   ),
          // ),
          // TextButton.icon(
          //   onPressed: () {
          //     // 清除焦点
          //     clearFocus();
          //     // 显示好友列表
          //   },
          //   label: const Text('@朋友', style: TextStyle(color: Colors.blue)),
          //   style: TextButton.styleFrom(
          //     padding: const EdgeInsets.symmetric(horizontal: 8),
          //     minimumSize: Size.zero,
          //   ),
          // ),
          const Spacer(),
          Visibility(
            visible: false,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFFEDE7F6),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.smart_toy, size: 16, color: Colors.purple[600]),
                  const SizedBox(width: 4),
                  Text(
                    'video_publish.ai_write'.tr,
                    style: TextStyle(
                      color: Colors.purple[600],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
