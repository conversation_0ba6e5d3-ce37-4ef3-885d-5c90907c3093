import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:math' as math;
import 'dart:async'; // 导入async库，以便使用TimeoutException

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/models/wx_sph_poi_model.dart';
import 'package:aitoearn_app/widgets/common_loading.dart';

class WxSphLocationSelectPage extends StatefulWidget {
  final String cookies;
  final Function(WxSphPoiItem)? onLocationSelected;
  final Map<String, dynamic>? initialPoiData;

  const WxSphLocationSelectPage({
    Key? key,
    required this.cookies,
    this.onLocationSelected,
    this.initialPoiData,
  }) : super(key: key);

  @override
  State<WxSphLocationSelectPage> createState() => _WxSphLocationSelectPageState();
}

class _WxSphLocationSelectPageState extends State<WxSphLocationSelectPage> {
  final TextEditingController _searchController = TextEditingController();
  
  List<WxSphPoiItem> _poiList = [];
  bool _isLoading = false;
  String _errorMessage = '';
  Position? _currentPosition;
  String? _searchCookies; // 存储搜索返回的cookies，用于分页加载
  Map<String, dynamic>? _lastAddress; // 存储上次搜索的地址信息
  bool _hasMoreData = false; // 是否有更多数据
  
  @override
  void initState() {
    super.initState();
    
    // 检查Cookie
    _checkCookie();
    
    // 如果提供了初始POI数据，则直接处理
    if (widget.initialPoiData != null) {
      _processInitialPoiData();
    } else {
      // 否则请求位置权限并获取当前位置
      _requestLocationPermission();
    }
  }
  
  // 检查Cookie是否有效
  void _checkCookie() {
    LoggerUtil.i('【微信视频号位置选择】检查Cookie');
    
    if (widget.cookies.isEmpty) {
      LoggerUtil.e('【微信视频号位置选择】Cookie为空');
      setState(() {
        _errorMessage = '登录信息不完整，请重新登录微信视频号';
      });
      return;
    }
    
    LoggerUtil.i('【微信视频号位置选择】Cookie长度: ${widget.cookies.length}');
    
    // 检查Cookie是否包含必要字段
    final hasCookieRequired = widget.cookies.contains('wxuin=') || 
                              widget.cookies.contains('passport_uin=') || 
                              widget.cookies.contains('o_cookie=');
    
    if (!hasCookieRequired) {
      LoggerUtil.e('【微信视频号位置选择】Cookie缺少必要字段 wxuin 或 passport_uin');
      
      // 打印Cookie片段用于调试
      final shortCookie = widget.cookies.length > 100 
          ? '${widget.cookies.substring(0, 50)}...${widget.cookies.substring(widget.cookies.length - 50)}'
          : widget.cookies;
      LoggerUtil.i('【微信视频号位置选择】Cookie片段: $shortCookie');
      
      setState(() {
        _errorMessage = 'Cookie缺少必要字段，请重新登录微信视频号';
      });
    } else {
      LoggerUtil.i('【微信视频号位置选择】Cookie包含必要字段');
    }
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  // 处理初始POI数据
  void _processInitialPoiData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      LoggerUtil.i('处理初始POI数据');
      
      // 使用WxSphPoiSearchResponse解析原始数据
      final poiResponse = WxSphPoiSearchResponse.fromRawApiResponse(widget.initialPoiData!);
      
      setState(() {
        _isLoading = false;
        _poiList = poiResponse.poiList;
        _searchCookies = poiResponse.cookies;
        _lastAddress = poiResponse.address;
        _hasMoreData = poiResponse.continueFlag == 1;
        
        if (_poiList.isEmpty) {
          _errorMessage = '未找到位置信息';
        } else {
          // 打印部分POI详情
          for (int i = 0; i < _poiList.length && i < 3; i++) {
            final poi = _poiList[i];
            LoggerUtil.i('POI ${i + 1}: ${poi.poiName} | 地址: ${poi.address}');
            LoggerUtil.i(' - 经纬度: (${poi.longitude}, ${poi.latitude}) | 城市: ${poi.cityName}');
            LoggerUtil.i(' - ID: ${poi.poiId}');
          }
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '处理位置数据失败: $e';
      });
      LoggerUtil.e('处理初始POI数据失败: $e');
    }
  }
  
  // 请求位置权限
  Future<void> _requestLocationPermission() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      final permissionStatus = await Permission.location.request();
      
      if (permissionStatus.isGranted) {
        await _getCurrentLocation();
      } else if (permissionStatus.isDenied) {
        setState(() {
          _isLoading = false;
          _errorMessage = '请授予位置权限以获取当前位置信息';
        });
      } else if (permissionStatus.isPermanentlyDenied) {
        setState(() {
          _isLoading = false;
          _errorMessage = '位置权限被永久拒绝，请在系统设置中启用';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '请求位置权限失败: $e';
      });
      LoggerUtil.e('请求位置权限失败: $e');
    }
  }
  
  // 获取当前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      LoggerUtil.i('【微信视频号位置选择】开始获取当前位置');
      
      // 添加超时处理
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),  // 添加10秒超时
      ).timeout(
        const Duration(seconds: 15),  // 整体操作15秒超时
        onTimeout: () {
          LoggerUtil.e('【微信视频号位置选择】获取位置超时，使用默认位置');
          throw TimeoutException('获取位置超时，使用默认位置');
        },
      );
      
      _currentPosition = position;
      LoggerUtil.i('【微信视频号位置选择】获取当前位置成功: 纬度=${_currentPosition!.latitude}, 经度=${_currentPosition!.longitude}');
      
      // 获取当前位置的POI信息
      await _searchPoi();
    } catch (e) {
      LoggerUtil.e('【微信视频号位置选择】获取当前位置失败: $e，使用默认位置');
      
      // 使用默认位置（北京市中心）
      _useDefaultLocation();
    }
  }
  
  // 使用默认位置
  void _useDefaultLocation() {
    LoggerUtil.i('【微信视频号位置选择】使用默认位置（北京市中心）');
    
    // 创建一个默认位置（北京市中心）
    _currentPosition = Position(
      latitude: 39.909187,
      longitude: 116.397451,
      timestamp: DateTime.now(),
      accuracy: 0,
      altitude: 0,
      heading: 0,
      speed: 0,
      speedAccuracy: 0,
      altitudeAccuracy: 0,
      headingAccuracy: 0,
    );
    
    setState(() {
      _errorMessage = '无法获取当前位置，使用默认位置（北京市中心）';
    });
    
    // 使用默认位置搜索POI
    _searchPoi();
  }
  
  // 使用示例POI数据（用于网络请求失败时的调试）
  void _useExamplePoiData() {
    LoggerUtil.i('【微信视频号位置选择】使用示例POI数据');
    
    // 创建示例POI数据
    final examplePoiList = [
      WxSphPoiItem(
        poiId: 'example_poi_1',
        poiName: '天安门广场',
        address: '北京市东城区天安门广场',
        latitude: 39.908722,
        longitude: 116.397501,
        cityName: '北京市',
        districtName: '东城区',
        distance: 100,
      ),
      WxSphPoiItem(
        poiId: 'example_poi_2',
        poiName: '故宫博物院',
        address: '北京市东城区景山前街4号',
        latitude: 39.916345,
        longitude: 116.397026,
        cityName: '北京市',
        districtName: '东城区',
        distance: 800,
      ),
      WxSphPoiItem(
        poiId: 'example_poi_3',
        poiName: '王府井步行街',
        address: '北京市东城区王府井大街',
        latitude: 39.915474,
        longitude: 116.408154,
        cityName: '北京市',
        districtName: '东城区',
        distance: 1200,
      ),
    ];
    
    setState(() {
      _poiList = examplePoiList;
      _isLoading = false;
      _errorMessage = '使用示例数据（仅用于测试）';
    });
    
    LoggerUtil.i('【微信视频号位置选择】已加载${_poiList.length}个示例POI数据');
  }
  
  // 搜索POI
  Future<void> _searchPoi() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // 检查位置是否获取到
      if (_currentPosition == null) {
        LoggerUtil.e('【微信视频号位置选择】当前位置未获取，无法进行搜索');
        setState(() {
          _errorMessage = '未能获取当前位置，请授予位置权限';
          _isLoading = false;
        });
        return;
      }
      
      LoggerUtil.i('【微信视频号位置选择】开始搜索POI，关键词: ${_searchController.text}，经纬度: ${_currentPosition!.latitude}, ${_currentPosition!.longitude}');
      
      // 检查账号信息和Cookie
      if (widget.cookies.isEmpty) {
        LoggerUtil.e('【微信视频号位置选择】Cookie为空，无法进行POI搜索');
        setState(() {
          _errorMessage = '登录信息不完整，请重新登录微信视频号';
          _isLoading = false;
        });
        return;
      }
      
      // 截取部分Cookie显示在日志中（仅用于调试）
      final shortCookie = widget.cookies.length > 30 
          ? '${widget.cookies.substring(0, 15)}...${widget.cookies.substring(widget.cookies.length - 15)}'
          : widget.cookies;
      LoggerUtil.i('【微信视频号位置选择】Cookie片段: $shortCookie');

      // 直接使用WxSphService中的searchPoi方法
      final response = await WxSphService.searchPoi(
        cookieStr: widget.cookies,
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        keyword: _searchController.text,
      );

      LoggerUtil.i('【微信视频号位置选择】搜索结果状态码: ${response.statusCode}, 消息: ${response.statusMsg}');
      LoggerUtil.i('【微信视频号位置选择】返回结果数量: ${response.poiList.length}');
      LoggerUtil.i('【微信视频号位置选择】是否有更多数据: ${response.continueFlag == 1}');

      // 检查状态码，允许0或200都表示成功
      if (response.statusCode != 0 && response.statusCode != 200) {
        // 处理错误情况
        LoggerUtil.e('【微信视频号位置选择】搜索POI失败: ${response.statusMsg}');
        
        // 如果网络请求失败且有调试标志，使用示例数据
        final bool useExampleData = true; // 设置为true以启用示例数据
        if (useExampleData) {
          LoggerUtil.i('【微信视频号位置选择】使用示例POI数据进行测试');
          _useExamplePoiData();
          return;
        }
        
        setState(() {
          _poiList = [];
          _errorMessage = '搜索失败: ${response.statusMsg}';
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _poiList = response.poiList;
        _searchCookies = response.cookies;
        _lastAddress = response.address;
        _hasMoreData = response.continueFlag == 1;
        _isLoading = false;
      });
      
      // 如果没有结果，显示提示
      if (_poiList.isEmpty) {
        LoggerUtil.i('【微信视频号位置选择】没有找到相关位置');
        setState(() {
          _errorMessage = '没有找到相关位置';
        });
      } else {
        // 打印获取到的POI详情
        for (int i = 0; i < math.min(3, _poiList.length); i++) {
          final poi = _poiList[i];
          LoggerUtil.i('【微信视频号位置选择】POI ${i + 1}: ${poi.poiName}, 地址: ${poi.fullAddress}');
        }
      }
    } catch (e) {
      LoggerUtil.e('【微信视频号位置选择】搜索异常: $e');
      setState(() {
        _poiList = [];
        _errorMessage = '搜索出错: $e';
        _isLoading = false;
      });
    }
  }
  
  // 处理搜索
  void _handleSearch() {
    if (_searchController.text.isNotEmpty) {
      _searchPoi();
    }
  }
  
  // 处理位置选择
  void _handlePoiSelected(WxSphPoiItem poi) {
    LoggerUtil.i('选择了位置: ${poi.poiName}');
    
    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(poi);
    }
    
    Navigator.pop(context, poi);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选择位置'),
        elevation: 0,
        actions: [
          // 添加刷新按钮
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _refreshData,
            tooltip: '刷新位置数据',
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索框
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索位置',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 8.0,
                  horizontal: 16.0,
                ),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                  },
                ),
              ),
              onSubmitted: (_) => _handleSearch(),
            ),
          ),
          
          // 内容区域
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }
  
  // 构建内容区域
  Widget _buildContent() {
    if (_isLoading) {
      return Center(child: CommonLoading(loadingText: '加载中...'));
    }
    
    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _requestLocationPermission,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }
    
    if (_poiList.isEmpty) {
      return const Center(
        child: Text(
          '未找到位置信息',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }
    
    return ListView.builder(
      itemCount: _poiList.length,
      itemBuilder: (context, index) {
        final poi = _poiList[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            title: Text(
              poi.poiName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  poi.fullAddress.isNotEmpty ? poi.fullAddress : poi.address,
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    if (poi.province.isNotEmpty)
                      _buildInfoChip(poi.province),
                    if (poi.cityName.isNotEmpty)
                      _buildInfoChip(poi.cityName),
                    if (poi.districtName.isNotEmpty)
                      _buildInfoChip(poi.districtName),
                  ],
                ),
              ],
            ),
            onTap: () => _handlePoiSelected(poi),
          ),
        );
      },
    );
  }
  
  // 构建信息标签
  Widget _buildInfoChip(String text) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[700],
        ),
      ),
    );
  }
  
  // 刷新数据
  void _refreshData() {
    LoggerUtil.i('【微信视频号位置选择】手动刷新数据');
    
    // 清空搜索框
    _searchController.clear();
    
    // 重新获取位置和POI数据
    _requestLocationPermission();
  }
} 