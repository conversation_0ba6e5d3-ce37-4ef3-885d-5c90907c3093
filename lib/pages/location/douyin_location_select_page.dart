import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_poi_model.dart';
import 'package:aitoearn_app/widgets/common_loading.dart';

class DouyinLocationSelectPage extends StatefulWidget {
  final String cookies;
  final String? proxy;
  final Function(DouyinPoiItem)? onLocationSelected;
  final Map<String, dynamic>? initialPoiData; // 初始POI数据，用于直接处理API返回的数据

  const DouyinLocationSelectPage({
    Key? key,
    required this.cookies,
    this.proxy,
    this.onLocationSelected,
    this.initialPoiData,
  }) : super(key: key);

  @override
  State<DouyinLocationSelectPage> createState() => _DouyinLocationSelectPageState();
}

class _DouyinLocationSelectPageState extends State<DouyinLocationSelectPage> {
  final TextEditingController _searchController = TextEditingController();
  final DouyinService _douyinService = DouyinService();
  
  List<DouyinPoiItem> _poiList = [];
  bool _isLoading = false;
  String _errorMessage = '';
  Position? _currentPosition;
  
  @override
  void initState() {
    super.initState();
    
    // 如果提供了初始POI数据，则直接处理
    if (widget.initialPoiData != null) {
      _processInitialPoiData();
    } else {
      // 否则请求位置权限并获取当前位置
      _requestLocationPermission();
    }
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  // 处理初始POI数据
  void _processInitialPoiData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      LoggerUtil.i('处理初始POI数据');
      
      // 使用DouyinPoiSearchResponse解析原始数据
      final poiResponse = DouyinPoiSearchResponse.fromRawApiResponse(widget.initialPoiData!);
      
      setState(() {
        _isLoading = false;
        _poiList = poiResponse.poiList;
        
        if (_poiList.isEmpty) {
          _errorMessage = '未找到位置信息';
        } else {
          // 打印部分POI详情
          for (int i = 0; i < _poiList.length && i < 3; i++) {
            final poi = _poiList[i];
            LoggerUtil.i('POI ${i + 1}: ${poi.poiName} | 地址: ${poi.address}');
            LoggerUtil.i(' - 经纬度: (${poi.longitude}, ${poi.latitude}) | 城市: ${poi.cityName}');
            LoggerUtil.i(' - 类型: ${poi.typeName} | ID: ${poi.poiId} | 距离: ${poi.distance}米');
          }
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '处理位置数据失败: $e';
      });
      LoggerUtil.e('处理初始POI数据失败: $e');
    }
  }
  
  // 请求位置权限
  Future<void> _requestLocationPermission() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      final permissionStatus = await Permission.location.request();
      
      if (permissionStatus.isGranted) {
        await _getCurrentLocation();
      } else if (permissionStatus.isDenied) {
        setState(() {
          _isLoading = false;
          _errorMessage = '请授予位置权限以获取当前位置信息';
        });
      } else if (permissionStatus.isPermanentlyDenied) {
        setState(() {
          _isLoading = false;
          _errorMessage = '位置权限被永久拒绝，请在系统设置中启用';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '请求位置权限失败: $e';
      });
      LoggerUtil.e('请求位置权限失败: $e');
    }
  }
  
  // 获取当前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      // 获取当前位置
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      LoggerUtil.i('获取当前位置成功: 纬度=${_currentPosition!.latitude}, 经度=${_currentPosition!.longitude}');
      
      // 获取当前位置的POI信息
      await _searchPoi();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '获取当前位置失败: $e';
      });
      LoggerUtil.e('获取当前位置失败: $e');
    }
  }
  
  // 搜索POI
  Future<void> _searchPoi({String? keywords}) async {
    if (_currentPosition == null) {
      setState(() {
        _isLoading = false;
        _errorMessage = '无法获取当前位置';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      // 使用DouyinService搜索POI
      final poiResponse = await _douyinService.searchPoi(
        cookies: widget.cookies,
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        keywords: keywords,
        proxy: widget.proxy,
      );
      
      setState(() {
        _isLoading = false;
        _poiList = poiResponse.poiList;
        if (_poiList.isEmpty) {
          _errorMessage = '未找到位置信息';
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '获取位置信息失败: $e';
      });
      LoggerUtil.e('获取位置信息失败: $e');
    }
  }
  
  // 处理搜索
  void _handleSearch() {
    if (_searchController.text.isNotEmpty) {
      _searchPoi(keywords: _searchController.text);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        title: const Text(
          '选择位置',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // 搜索框
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索位置',
                hintStyle: TextStyle(color: Colors.grey[400]),
                prefixIcon: Icon(Icons.search, color: Colors.grey[400]),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                  },
                ),
                fillColor: const Color(0xFFF5F7FA),
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onSubmitted: (_) => _handleSearch(),
            ),
          ),
          
          // 当前位置按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.my_location, color: Colors.blue),
                  label: const Text('刷新当前位置', style: TextStyle(color: Colors.blue)),
                  onPressed: _getCurrentLocation,
                ),
                if (_currentPosition != null)
                  Expanded(
                    child: Text(
                      '纬度: ${_currentPosition!.latitude.toStringAsFixed(6)}, 经度: ${_currentPosition!.longitude.toStringAsFixed(6)}',
                      style: const TextStyle(fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
              ],
            ),
          ),
          
          // 加载状态或错误信息
          if (_isLoading)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(child: CommonLoading(loadingText: '加载中...')),
            )
          else if (_errorMessage.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                _errorMessage,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          
          // POI列表
          Expanded(
            child: _poiList.isEmpty && !_isLoading && _errorMessage.isEmpty
                ? Center(child: Text('暂无位置数据，请搜索或刷新当前位置', style: TextStyle(color: Colors.grey[600])))
                : ListView.separated(
                    itemCount: _poiList.length,
                    separatorBuilder: (context, index) => const Divider(height: 1),
                    itemBuilder: (context, index) {
                      final poi = _poiList[index];
                      return ListTile(
                        title: Text(
                          poi.poiName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              poi.address,
                              style: const TextStyle(
                                fontSize: 13,
                                color: Colors.black54,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              '经纬度: (${poi.longitude.toStringAsFixed(6)}, ${poi.latitude.toStringAsFixed(6)})',
                              style: const TextStyle(
                                fontSize: 11,
                                color: Colors.black45,
                              ),
                            ),
                          ],
                        ),
                        trailing: Text(
                          '${(poi.distance / 1000).toStringAsFixed(1)}km',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black45,
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        onTap: () {
                          // 选择位置并返回
                          if (widget.onLocationSelected != null) {
                            widget.onLocationSelected!(poi);
                          }
                          Navigator.pop(context, poi);
                        },
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}