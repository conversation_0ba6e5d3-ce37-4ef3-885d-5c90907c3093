import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/location/location_select_logic.dart';
import 'package:aitoearn_app/pages/location/location_select_state.dart';

class LocationSelectPage extends StatefulWidget {
  const LocationSelectPage({super.key});

  @override
  State<LocationSelectPage> createState() => _LocationSelectPageState();
}

class _LocationSelectPageState extends State<LocationSelectPage> {
  final LocationSelectLogic logic = Get.put(LocationSelectLogic());
  final LocationSelectState state = Get.find<LocationSelectLogic>().state;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 初始化时加载默认城市的位置列表
    logic.loadLocationsForCity(state.selectedCity.value);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        title: const Text(
          '添加位置',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // 足迹地图横幅
          // _buildFootprintBanner(),
          
          // 城市选择区域
          _buildCitySelector(),
          
          // 搜索框
          _buildSearchBar(),
          
          // 当前位置
          _buildCurrentLocationItem(),
          
          // 位置列表
          Expanded(
            child: Obx(() {
              if (state.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              
              if (state.locationList.isEmpty) {
                return const Center(child: Text('没有找到相关位置'));
              }
              
              return ListView.separated(
                itemCount: state.locationList.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final location = state.locationList[index];
                  return _buildLocationItem(location);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  // 足迹地图横幅
  // Widget _buildFootprintBanner() {
  //   return Container(
  //     margin: const EdgeInsets.all(16),
  //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  //     decoration: BoxDecoration(
  //       color: const Color(0xFFF5F7FA),
  //       borderRadius: BorderRadius.circular(8),
  //     ),
  //     child: Row(
  //       children: [
  //         Container(
  //           padding: const EdgeInsets.all(4),
  //           decoration: const BoxDecoration(
  //             color: Color(0xFFFFF2D9),
  //             shape: BoxShape.circle,
  //           ),
  //           child: const Icon(
  //             Icons.directions_walk,
  //             color: Color(0xFFFFAA33),
  //             size: 18,
  //           ),
  //         ),
  //         const SizedBox(width: 8),
  //         const Text(
  //           '足迹地图',
  //           style: TextStyle(
  //             fontSize: 15,
  //             fontWeight: FontWeight.w500,
  //             color: Color(0xFFFFAA33),
  //           ),
  //         ),
  //         const SizedBox(width: 8),
  //         const Expanded(
  //           child: Text(
  //             '带定位视频 记录足迹领20元券',
  //             style: TextStyle(
  //               fontSize: 13,
  //               color: Colors.black54,
  //             ),
  //           ),
  //         ),
  //         const Icon(Icons.chevron_right, color: Colors.black45),
  //       ],
  //     ),
  //   );
  // }

  // 城市选择器
  Widget _buildCitySelector() {
    return GestureDetector(
      onTap: () {
        _showCityPicker();
      },
      onLongPress: () {
        // 长按打印抖音POI详细信息
        if (state.locationList.isNotEmpty) {
          LoggerUtil.i('========================');
          LoggerUtil.i('当前位置列表详情(共${state.locationList.length}个):');
          for (int i = 0; i < state.locationList.length && i < 5; i++) {
            final location = state.locationList[i];
            LoggerUtil.i('位置 ${i + 1}: ${location['name']}');
            LoggerUtil.i('  地址: ${location['address']}');
            LoggerUtil.i('  经纬度: (${location['longitude']}, ${location['latitude']})');
            LoggerUtil.i('  城市: ${location['city']} | 距离: ${location['distance']}');
            LoggerUtil.i('  ID: ${location['id']}');
          }
          LoggerUtil.i('========================');
          
          // 显示提示
          Get.snackbar(
            '调试信息',
            '位置信息已打印到控制台',
            snackPosition: SnackPosition.BOTTOM,
            duration: const Duration(seconds: 2),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Colors.black12, width: 0.5),
          ),
        ),
        child: Row(
          children: [
            const Icon(Icons.location_on, color: Colors.black87, size: 20),
            const SizedBox(width: 8),
            Obx(() => Text(
              state.selectedCity.value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            )),
            const SizedBox(width: 4),
            Icon(Icons.keyboard_arrow_down, color: Colors.grey[600], size: 18),
            const Spacer(),
            IconButton(
              onPressed: () {
                // 刷新当前位置
                logic.getCurrentLocation();
              },
              icon: Icon(Icons.my_location, color: Colors.grey[600], size: 24),
              tooltip: '定位到当前位置',
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ),
      ),
    );
  }

  // 搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        focusNode: _searchFocusNode,
        decoration: InputDecoration(
          hintText: '搜索位置',
          hintStyle: TextStyle(color: Colors.grey[400]),
          prefixIcon: Icon(Icons.search, color: Colors.grey[400]),
          fillColor: const Color(0xFFF5F7FA),
          filled: true,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(vertical: 12),
        ),
        onChanged: (value) {
          // 搜索位置
          logic.searchLocations(value);
        },
      ),
    );
  }
  
  // 当前位置项
  Widget _buildCurrentLocationItem() {
    return Obx(() {
      if (state.currentAddress.value.isEmpty) {
        return const SizedBox.shrink();
      }
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              '当前位置',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.my_location,
                color: Colors.blue,
                size: 20,
              ),
            ),
            title: const Text(
              '我的位置',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: Text(
              state.currentAddress.value,
              style: const TextStyle(
                fontSize: 13,
                color: Colors.black54,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            onTap: () {
              // 选择当前位置并返回
              final location = {
                'id': 'current_location',
                'name': '我的位置',
                'address': state.currentAddress.value,
                'distance': '0m',
                'city': state.selectedCity.value,
                'latitude': state.currentLat.value.toString(),
                'longitude': state.currentLng.value.toString(),
              };
              logic.selectLocation(location);
              Navigator.pop(context, location);
            },
          ),
          const Divider(height: 1),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              '附近地点',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
        ],
      );
    });
  }

  // 位置项
  Widget _buildLocationItem(Map<String, dynamic> location) {
    return ListTile(
      title: Text(
        location['name'] ?? '',
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
                            subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            location['address'] ?? '',
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.black54,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            '经纬度: (${location['longitude'] ?? '0.0'}, ${location['latitude'] ?? '0.0'})',
                            style: const TextStyle(
                              fontSize: 11,
                              color: Colors.black45,
                            ),
                          ),
                        ],
                      ),
      trailing: Text(
        location['distance'] ?? '',
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black45,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      onTap: () {
        // 选择位置并返回
        logic.selectLocation(location);
        Navigator.pop(context, location);
      },
    );
  }

  // 显示城市选择器
  void _showCityPicker() {
    // 使用中国主要城市
    final List<String> cities = [
      '北京', '上海', '广州', '深圳', '成都', 
      '重庆', '杭州', '武汉', '西安', '南京',
      '天津', '苏州', '郑州', '长沙', '东莞',
      '青岛', '沈阳', '宁波', '昆明', '福州',
    ];
    
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '选择城市',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: GridView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    childAspectRatio: 2.5,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: cities.length,
                  itemBuilder: (context, index) {
                    final city = cities[index];
                    final isSelected = city == state.selectedCity.value;
                    
                    return InkWell(
                      onTap: () {
                        logic.changeCity(city);
                        Navigator.pop(context);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: isSelected ? Colors.blue.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected ? Colors.blue : Colors.transparent,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          city,
                          style: TextStyle(
                            color: isSelected ? Colors.blue : Colors.black87,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
} 