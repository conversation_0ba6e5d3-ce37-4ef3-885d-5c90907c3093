import 'package:get/get.dart';

class LocationSelectState {
  // 当前选中的城市
  final RxString selectedCity = '成都'.obs;
  
  // 位置列表
  final RxList<Map<String, dynamic>> locationList = <Map<String, dynamic>>[].obs;
  
  // 搜索结果列表
  final RxList<Map<String, dynamic>> searchResults = <Map<String, dynamic>>[].obs;
  
  // 当前选中的位置
  final Rx<Map<String, dynamic>> selectedLocation = Rx<Map<String, dynamic>>({});
  
  // 当前位置的经纬度
  final RxDouble currentLat = 0.0.obs;
  final RxDouble currentLng = 0.0.obs;
  
  // 当前详细地址
  final RxString currentAddress = ''.obs;
  
  // 是否正在加载数据
  final RxBool isLoading = false.obs;
  
  // 是否正在搜索
  final RxBool isSearching = false.obs;
  
  // 搜索关键词
  final RxString searchKeyword = ''.obs;

  LocationSelectState() {
    ///Initialize variables
  }
} 