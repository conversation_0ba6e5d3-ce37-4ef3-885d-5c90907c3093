import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:math' as math;

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/xhs_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_poi_model.dart';
import 'package:aitoearn_app/widgets/common_loading.dart';

class XhsLocationSelectPage extends StatefulWidget {
  final String cookies;
  final Function(XhsPoiItem)? onLocationSelected;
  final Map<String, dynamic>? initialPoiData;

  const XhsLocationSelectPage({
    Key? key,
    required this.cookies,
    this.onLocationSelected,
    this.initialPoiData,
  }) : super(key: key);

  @override
  State<XhsLocationSelectPage> createState() => _XhsLocationSelectPageState();
}

class _XhsLocationSelectPageState extends State<XhsLocationSelectPage> {
  final TextEditingController _searchController = TextEditingController();
  
  List<XhsPoiItem> _poiList = [];
  bool _isLoading = false;
  String _errorMessage = '';
  Position? _currentPosition;
  
  @override
  void initState() {
    super.initState();
    
    // 如果提供了初始POI数据，则直接处理
    if (widget.initialPoiData != null) {
      _processInitialPoiData();
    } else {
      // 否则请求位置权限并获取当前位置
      _requestLocationPermission();
    }
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  // 处理初始POI数据
  void _processInitialPoiData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      LoggerUtil.i('处理初始POI数据');
      
      // 使用XhsPoiSearchResponse解析原始数据
      final poiResponse = XhsPoiSearchResponse.fromRawApiResponse(widget.initialPoiData!);
      
      setState(() {
        _isLoading = false;
        _poiList = poiResponse.poiList;
        
        if (_poiList.isEmpty) {
          _errorMessage = '未找到位置信息';
        } else {
          // 打印部分POI详情
          for (int i = 0; i < _poiList.length && i < 3; i++) {
            final poi = _poiList[i];
            LoggerUtil.i('POI ${i + 1}: ${poi.poiName} | 地址: ${poi.address}');
            LoggerUtil.i(' - 经纬度: (${poi.longitude}, ${poi.latitude}) | 城市: ${poi.cityName}');
            LoggerUtil.i(' - ID: ${poi.poiId}');
          }
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '处理位置数据失败: $e';
      });
      LoggerUtil.e('处理初始POI数据失败: $e');
    }
  }
  
  // 请求位置权限
  Future<void> _requestLocationPermission() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      final permissionStatus = await Permission.location.request();
      
      if (permissionStatus.isGranted) {
        await _getCurrentLocation();
      } else if (permissionStatus.isDenied) {
        setState(() {
          _isLoading = false;
          _errorMessage = '请授予位置权限以获取当前位置信息';
        });
      } else if (permissionStatus.isPermanentlyDenied) {
        setState(() {
          _isLoading = false;
          _errorMessage = '位置权限被永久拒绝，请在系统设置中启用';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '请求位置权限失败: $e';
      });
      LoggerUtil.e('请求位置权限失败: $e');
    }
  }
  
  // 获取当前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      // 获取当前位置
      _currentPosition = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      LoggerUtil.i('获取当前位置成功: 纬度=${_currentPosition!.latitude}, 经度=${_currentPosition!.longitude}');
      
      // 获取当前位置的POI信息
      await _searchPoi();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '获取当前位置失败: $e';
      });
      LoggerUtil.e('获取当前位置失败: $e');
    }
  }
  
  // 搜索POI
  Future<void> _searchPoi({String? keywords}) async {
    if (_currentPosition == null) {
      setState(() {
        _isLoading = false;
        _errorMessage = '无法获取当前位置';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });
    
    try {
      // 使用XhsService搜索POI
      final poiResponse = await XhsService.searchPoi(
        cookieStr: widget.cookies,
        latitude: _currentPosition!.latitude,
        longitude: _currentPosition!.longitude,
        keyword: keywords ?? '',
      );
      
      setState(() {
        _isLoading = false;
        _poiList = poiResponse.poiList;
        
        if (_poiList.isEmpty) {
          _errorMessage = '未找到位置信息';
        } else {
          LoggerUtil.i('获取到${_poiList.length}个位置');
          // 打印前几个POI位置信息
          for (int i = 0; i < math.min(3, _poiList.length); i++) {
            final poi = _poiList[i];
            LoggerUtil.i('POI ${i + 1}: ${poi.poiName} | ${poi.address}');
          }
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '获取位置信息失败: $e';
      });
      LoggerUtil.e('获取位置信息失败: $e');
    }
  }
  
  // 处理搜索
  void _handleSearch() {
    if (_searchController.text.isNotEmpty) {
      _searchPoi(keywords: _searchController.text);
    }
  }
  
  // 处理位置选择
  void _handlePoiSelected(XhsPoiItem poi) {
    LoggerUtil.i('选择了位置: ${poi.poiName}');
    
    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(poi);
    }
    
    Navigator.pop(context, poi);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选择位置'),
        elevation: 0,
      ),
      body: Column(
        children: [
          // 搜索框
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索位置',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 8.0,
                  horizontal: 16.0,
                ),
              ),
              onSubmitted: (_) => _handleSearch(),
            ),
          ),
          
          // 内容区域
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }
  
  // 构建内容区域
  Widget _buildContent() {
    if (_isLoading) {
      return Center(child: CommonLoading(loadingText: '加载中...'));
    }
    
    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _requestLocationPermission,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }
    
    if (_poiList.isEmpty) {
      return const Center(
        child: Text(
          '未找到位置信息',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }
    
    return ListView.builder(
      itemCount: _poiList.length,
      itemBuilder: (context, index) {
        final poi = _poiList[index];
        return ListTile(
          title: Text(
            poi.poiName,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(poi.address),
              if (poi.cityName.isNotEmpty)
                Text(
                  poi.cityName,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
            ],
          ),
          onTap: () => _handlePoiSelected(poi),
        );
      },
    );
  }
} 