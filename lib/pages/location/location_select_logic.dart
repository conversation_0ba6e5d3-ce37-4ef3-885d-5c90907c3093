import 'package:aitoearn_app/config/logger.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_poi_model.dart';
import 'location_select_state.dart';

class LocationSelectLogic extends GetxController {
  final LocationSelectState state = LocationSelectState();
  final DouyinService _douyinService = DouyinService();
  
  // 抖音Cookie，实际使用时应从应用配置或登录状态中获取
  String _cookies = '';
  String? _proxy;
  
  // 构造函数，接收必要的参数
  LocationSelectLogic({String? cookies, String? proxy}) 
      : _cookies = cookies ?? '',
        _proxy = proxy;
  
  @override
  void onInit() {
    super.onInit();
    
    // 初始化选中城市
    state.selectedCity.value = '成都';
    
    // 初始化时请求位置权限并获取位置
    _requestLocationPermission();
  }

  // 请求位置权限
  Future<void> _requestLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }
      
      if (permission == LocationPermission.whileInUse || 
          permission == LocationPermission.always) {
        await getCurrentLocation();
      }
    } catch (e) {
      LoggerUtil.i('请求位置权限失败: $e');
    }
  }

  // 获取当前位置
  Future<void> getCurrentLocation() async {
    try {
      state.isLoading.value = true;
      
      // 获取当前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high
      );
      
      // 更新状态
      state.currentLat.value = position.latitude;
      state.currentLng.value = position.longitude;
      
      // 获取当前城市和位置
      if (_cookies.isNotEmpty) {
        await _loadDouyinPOIs(position.latitude, position.longitude);
      }
    } catch (e) {
      LoggerUtil.i('获取位置失败: $e');
      Get.snackbar(
        '定位失败',
        '获取当前位置失败，请检查定位设置',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      state.isLoading.value = false;
    }
  }
  
  // 加载指定城市的位置列表
  Future<void> loadLocationsForCity(String city) async {
    state.isLoading.value = true;
    
    try {
      // 如果有当前位置，则使用当前位置获取附近POI
      if (state.currentLat.value != 0 && state.currentLng.value != 0) {
        await _loadDouyinPOIs(state.currentLat.value, state.currentLng.value);
      }
    } catch (e) {
      LoggerUtil.i('加载位置列表失败: $e');
      Get.snackbar(
        '加载失败',
        '无法获取位置数据，请检查网络连接',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      state.isLoading.value = false;
    }
  }

  // 加载抖音附近的POI
  Future<void> _loadDouyinPOIs(double latitude, double longitude) async {
    try {
      if (_cookies.isEmpty) {
        LoggerUtil.i('【位置选择】抖音Cookie不能为空');
        return;
      }
      
      LoggerUtil.i('【位置选择】开始获取附近POI - 纬度: $latitude, 经度: $longitude');
      LoggerUtil.i('【位置选择】Cookie长度: ${_cookies.length}, 代理: ${_proxy ?? '无'}');
      
      // 使用抖音API搜索附近POI
      final poiResponse = await _douyinService.searchPoi(
        cookies: _cookies,
        latitude: latitude,
        longitude: longitude,
        proxy: _proxy,
      );
      
      // 打印获取的POI信息
      LoggerUtil.i('【位置选择】获取抖音POI成功，状态码: ${poiResponse.statusCode}, 共 ${poiResponse.poiList.length} 个位置');
      
      // 打印部分POI详情
      for (int i = 0; i < poiResponse.poiList.length && i < 3; i++) {
        final poi = poiResponse.poiList[i];
        LoggerUtil.i('【位置选择】POI ${i + 1}: ${poi.poiName} | 地址: ${poi.address} | 距离: ${poi.distance}米');
        LoggerUtil.i('【位置选择】 - 经纬度: (${poi.longitude}, ${poi.latitude}) | 城市: ${poi.cityName}');
        LoggerUtil.i('【位置选择】 - 类型: ${poi.typeName} | ID: ${poi.poiId}');
      }
      
      // 更新当前地址(取第一个POI作为当前地址)
      if (poiResponse.poiList.isNotEmpty) {
        state.currentAddress.value = poiResponse.poiList.first.address;
      }
      
      // 转换为应用中使用的格式
      final locations = poiResponse.poiList.map((poi) {
        // 计算距离
        String distance = '未知';
        try {
          int distanceInt = poi.distance;
          // 转换为千米显示
          if (distanceInt >= 1000) {
            distance = '${(distanceInt / 1000).toStringAsFixed(1)}km';
          } else {
            distance = '${distanceInt}m';
          }
        } catch (e) {
          LoggerUtil.i('距离解析错误: $e');
        }
        
        return {
          'id': poi.poiId,
          'name': poi.poiName,
          'address': poi.address,
          'distance': distance,
          'city': poi.cityName.isNotEmpty ? poi.cityName : state.selectedCity.value,
          'latitude': poi.latitude.toString(),
          'longitude': poi.longitude.toString(),
        };
      }).toList();
      
      state.locationList.clear();
      state.locationList.addAll(locations);
    } catch (e) {
      LoggerUtil.i('加载抖音附近位置失败: $e');
    }
  }

  // 搜索位置
  Future<void> searchLocations(String keyword) async {
    state.searchKeyword.value = keyword;
    
    if (keyword.isEmpty) {
      // 如果关键词为空，显示附近位置
      LoggerUtil.i('【位置搜索】关键词为空，显示附近位置');
      getCurrentLocation();
      return;
    }
    
    state.isSearching.value = true;
    
    try {
      if (_cookies.isEmpty) {
        LoggerUtil.i('【位置搜索】抖音Cookie不能为空');
        state.isSearching.value = false;
        return;
      }
      
      if (state.currentLat.value == 0 || state.currentLng.value == 0) {
        LoggerUtil.i('【位置搜索】当前位置未获取，无法搜索');
        state.isSearching.value = false;
        return;
      }
      
      LoggerUtil.i('【位置搜索】开始搜索抖音POI，关键词: "$keyword", 位置: (${state.currentLat.value}, ${state.currentLng.value})');
      LoggerUtil.i('【位置搜索】Cookie长度: ${_cookies.length}, 代理: ${_proxy ?? '无'}');
      
      // 使用抖音API搜索POI
      final poiResponse = await _douyinService.searchPoi(
        cookies: _cookies,
        latitude: state.currentLat.value,
        longitude: state.currentLng.value,
        keywords: keyword,
        proxy: _proxy,
      );
      
      // 打印搜索结果
      LoggerUtil.i('【位置搜索】搜索抖音POI成功，状态码: ${poiResponse.statusCode}, 消息: ${poiResponse.statusMsg}');
      LoggerUtil.i('【位置搜索】共找到 ${poiResponse.poiList.length} 个位置结果');
      
      // 打印搜索结果详情
      for (int i = 0; i < poiResponse.poiList.length && i < 3; i++) {
        final poi = poiResponse.poiList[i];
        LoggerUtil.i('【位置搜索】结果 ${i + 1}: ${poi.poiName} | 地址: ${poi.address} | 距离: ${poi.distance}米');
        LoggerUtil.i('【位置搜索】 - 类型: ${poi.typeName} | 经纬度: (${poi.longitude}, ${poi.latitude}) | 城市: ${poi.cityName}');
        LoggerUtil.i('【位置搜索】 - POI ID: ${poi.poiId} | 区: ${poi.districtName}');
      }
      
      // 转换为应用中使用的格式
      final locations = poiResponse.poiList.map((poi) {
        // 计算距离
        String distance = '未知';
        try {
          int distanceInt = poi.distance;
          // 转换为千米显示
          if (distanceInt >= 1000) {
            distance = '${(distanceInt / 1000).toStringAsFixed(1)}km';
          } else {
            distance = '${distanceInt}m';
          }
        } catch (e) {
          LoggerUtil.i('距离解析错误: $e');
        }
        
        return {
          'id': poi.poiId,
          'name': poi.poiName,
          'address': poi.address,
          'distance': distance,
          'city': poi.cityName.isNotEmpty ? poi.cityName : state.selectedCity.value,
          'latitude': poi.latitude.toString(),
          'longitude': poi.longitude.toString(),
        };
      }).toList();
      
      state.locationList.clear();
      state.locationList.addAll(locations);
    } catch (e) {
      LoggerUtil.i('搜索位置失败: $e');
      Get.snackbar(
        '搜索失败',
        '无法获取搜索结果，请检查网络连接',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      state.isSearching.value = false;
    }
  }

  // 处理抖音API返回的POI数据
  Future<void> processDouyinPoiData(Map<String, dynamic> rawData) async {
    try {
      LoggerUtil.i('【位置处理】开始处理抖音POI原始数据');
      
      // 使用新增的方法解析抖音API返回的原始数据
      final poiResponse = DouyinPoiSearchResponse.fromRawApiResponse(rawData);
      
      // 打印解析结果
      LoggerUtil.i('【位置处理】解析抖音POI数据成功，状态码: ${poiResponse.statusCode}, 共 ${poiResponse.poiList.length} 个位置');
      
      // 打印部分POI详情
      for (int i = 0; i < poiResponse.poiList.length && i < 3; i++) {
        final poi = poiResponse.poiList[i];
        LoggerUtil.i('【位置处理】POI ${i + 1}: ${poi.poiName} | 地址: ${poi.address}');
        LoggerUtil.i('【位置处理】 - 经纬度: (${poi.longitude}, ${poi.latitude}) | 城市: ${poi.cityName}');
        LoggerUtil.i('【位置处理】 - 类型: ${poi.typeName} | ID: ${poi.poiId}');
      }
      
      // 转换为应用中使用的格式
      final locations = poiResponse.poiList.map((poi) {
        // 计算距离
        String distance = '未知';
        try {
          int distanceInt = poi.distance;
          if (distanceInt >= 1000) {
            distance = '${(distanceInt / 1000).toStringAsFixed(1)}km';
          } else {
            distance = '${distanceInt}m';
          }
        } catch (e) {
          LoggerUtil.i('距离解析错误: $e');
        }
        
        return {
          'id': poi.poiId,
          'name': poi.poiName,
          'address': poi.address,
          'distance': distance,
          'city': poi.cityName.isNotEmpty ? poi.cityName : state.selectedCity.value,
          'latitude': poi.latitude.toString(),
          'longitude': poi.longitude.toString(),
        };
      }).toList();
      
      state.locationList.clear();
      state.locationList.addAll(locations);
      
      // 如果有位置，更新当前地址
      if (poiResponse.poiList.isNotEmpty) {
        state.currentAddress.value = poiResponse.poiList.first.address;
      }
      
    } catch (e) {
      LoggerUtil.e('处理抖音POI数据失败: $e');
    }
  }

  // 切换城市(抖音API不需要切换城市，保留此方法兼容现有UI)
  Future<void> changeCity(String city) async {
    if (state.selectedCity.value != city) {
      state.selectedCity.value = city;
      await getCurrentLocation(); // 使用当前位置重新获取POI
    }
  }

  // 选择位置
  void selectLocation(Map<String, dynamic> location) {
    state.selectedLocation.value = location;
  }
} 