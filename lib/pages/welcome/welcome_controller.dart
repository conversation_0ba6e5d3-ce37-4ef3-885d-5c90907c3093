import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:get/get.dart';

class WelcomeController extends GetxController {
  @override
  Future<void> onInit() async {
    super.onInit();
    // 给初始化时间
    await Future.delayed(const Duration(milliseconds: 100));
    // 获取用户存储服务并检查token
    final userStore = Get.find<UserStoreService>();
    if (userStore.token.isNotEmpty) {
      // 记录检查的时间，要是没到1s, 添加剩余的延迟再跳转页面
      final checkTime = DateTime.now();
      final hasUserInfo = await userStore.getUserInfo();
      if (DateTime.now().difference(checkTime).inSeconds < 1) {
        await Future.delayed(
          const Duration(seconds: 1) - DateTime.now().difference(checkTime),
        );
      }
      goPage(hasUserInfo);
    } else {
      await Future.delayed(const Duration(seconds: 1));
      goPage(false);
    }
  }

  goPage(bool isToHome) {

    // 初始化账号持久化服务
    Get.put<AccountPersistentService>(
      AccountPersistentService(),
      permanent: true,
    );

    if (isToHome) {
      Get.offAndToNamed(AppRouter.homePath);
    } else {
      Get.offAndToNamed(AppRouter.loginEmailPath);
    }
  }
}
