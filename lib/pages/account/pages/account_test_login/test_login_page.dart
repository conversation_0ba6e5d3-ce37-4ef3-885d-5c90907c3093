import 'dart:math';

import 'package:aitoearn_app/api/account/account_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 测试登录页面
class TestLoginPage extends StatefulWidget {
  const TestLoginPage({super.key});

  @override
  State<TestLoginPage> createState() => _TestLoginPageState();
}

class _TestLoginPageState extends State<TestLoginPage> {
  // 平台选择
  PlatTypeEnum selectedPlatform = PlatTypeEnum.douyin;

  // 账号信息控制器
  final accountController = TextEditingController(text: 'test_account');
  final nicknameController = TextEditingController(text: '测试用户');
  final avatarController = TextEditingController(
    text: 'https://example.com/avatar.jpg',
  );

  // 日志信息
  final List<String> logs = [];

  // 是否正在加载
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('测试账号创建'), centerTitle: true),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 平台选择
            const Text('选择平台:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            DropdownButton<PlatTypeEnum>(
              value: selectedPlatform,
              isExpanded: true,
              items:
                  PlatTypeEnum.values.map((platform) {
                    return DropdownMenuItem<PlatTypeEnum>(
                      value: platform,
                      child: Text(platform.val),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    selectedPlatform = value;
                  });
                }
              },
            ),

            const SizedBox(height: 16),

            // 账号输入
            TextField(
              controller: accountController,
              decoration: const InputDecoration(
                labelText: '账号',
                border: OutlineInputBorder(),
              ),
            ),

            const SizedBox(height: 16),

            // 昵称输入
            TextField(
              controller: nicknameController,
              decoration: const InputDecoration(
                labelText: '昵称',
                border: OutlineInputBorder(),
              ),
            ),

            const SizedBox(height: 16),

            // 头像URL输入
            TextField(
              controller: avatarController,
              decoration: const InputDecoration(
                labelText: '头像URL',
                border: OutlineInputBorder(),
              ),
            ),

            const SizedBox(height: 24),

            // 登录按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handleLogin,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    isLoading
                        ? const CircularProgressIndicator()
                        : const Text('模拟登录并创建账号'),
              ),
            ),

            const SizedBox(height: 16),

            // 获取账号列表按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handleFetchAccounts,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Colors.green,
                ),
                child:
                    isLoading
                        ? const CircularProgressIndicator()
                        : const Text('获取账号列表'),
              ),
            ),

            const SizedBox(height: 16),

            // 测试删除账号按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handleTestDeleteAccount,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Colors.red,
                ),
                child:
                    isLoading
                        ? const CircularProgressIndicator()
                        : const Text('测试删除账号'),
              ),
            ),

            const SizedBox(height: 16),

            // 测试更新账号按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handleTestUpdateAccount,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Colors.orange,
                ),
                child:
                    isLoading
                        ? const CircularProgressIndicator()
                        : const Text('测试更新账号'),
              ),
            ),

            const SizedBox(height: 24),

            // 日志显示
            const Text('操作日志:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  itemCount: logs.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        logs[logs.length - 1 - index],
                        style: TextStyle(
                          color:
                              logs[logs.length - 1 - index].contains('错误')
                                  ? Colors.red
                                  : Colors.black87,
                          fontSize: 12,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 处理登录
  void _handleLogin() async {
    setState(() {
      isLoading = true;
    });

    try {
      _addLog('开始模拟登录...');

      // 生成随机账号信息
      final random = Random();
      final randomId = random.nextInt(100000);
      final uid = 'test_$randomId';
      final account =
          accountController.text.isNotEmpty
              ? accountController.text
              : 'test_account';

      // 初始默认值，将在获取平台信息后更新
      var nickname =
          nicknameController.text.isNotEmpty ? nicknameController.text : '测试用户';
      var avatar =
          avatarController.text.isNotEmpty
              ? avatarController.text
              : 'https://example.com/avatar.jpg';

      // 创建模拟cookie
      final sessionId = 'mock_session_${random.nextInt(10000)}';
      final accessToken = 'mock_access_token_${random.nextInt(10000)}';
      final refreshToken = 'mock_refresh_token_${random.nextInt(10000)}';
      final cookie =
          'sessionid=$sessionId; access_token=$accessToken; refresh_token=$refreshToken';

      _addLog('生成随机账号信息:');
      _addLog('平台: $selectedPlatform');
      _addLog('UID: $uid');
      _addLog('账号: $account');
      _addLog('初始昵称: $nickname');

      // 确保AccountPersistentService已注册
      if (!Get.isRegistered<AccountPersistentService>()) {
        Get.put(AccountPersistentService());
      }

      // 获取平台类型
      PlatTypeEnum platType = selectedPlatform;

      // 第一步：获取平台账号详细信息
      _addLog('开始获取平台账号详细信息...');

      // 初始化统计数据
      int fansCount = 0;
      int readCount = 0;
      int likeCount = 0;
      int collectCount = 0;
      int forwardCount = 0;
      int commentCount = 0;
      int workCount = 0;
      int income = 0;

      try {
        Map<String, dynamic>? platformInfo = await getPlatformAccountInfoApi(
          platType,
          cookie,
          uid,
        );

        if (platformInfo != null) {
          _addLog('成功获取平台账号信息');

          // 更新账号信息
          if (platformInfo['nickname'] != null &&
              platformInfo['nickname'].toString().isNotEmpty) {
            nickname = platformInfo['nickname'].toString();
            _addLog('使用平台昵称: $nickname');
          }

          if (platformInfo['avatar'] != null &&
              platformInfo['avatar'].toString().isNotEmpty) {
            avatar = platformInfo['avatar'].toString();
            _addLog('使用平台头像: $avatar');
          }

          // 更新统计数据
          if (platformInfo['fansCount'] != null) {
            fansCount =
                platformInfo['fansCount'] is int
                    ? platformInfo['fansCount']
                    : int.tryParse(
                          platformInfo['fansCount']?.toString() ?? '0',
                        ) ??
                        0;
            _addLog('粉丝数: $fansCount');
          }

          if (platformInfo['readCount'] != null) {
            readCount =
                platformInfo['readCount'] is int
                    ? platformInfo['readCount']
                    : int.tryParse(
                          platformInfo['readCount']?.toString() ?? '0',
                        ) ??
                        0;
            _addLog('阅读数: $readCount');
          }

          if (platformInfo['likeCount'] != null) {
            likeCount =
                platformInfo['likeCount'] is int
                    ? platformInfo['likeCount']
                    : int.tryParse(
                          platformInfo['likeCount']?.toString() ?? '0',
                        ) ??
                        0;
            _addLog('点赞数: $likeCount');
          }

          if (platformInfo['collectCount'] != null) {
            collectCount =
                platformInfo['collectCount'] is int
                    ? platformInfo['collectCount']
                    : int.tryParse(
                          platformInfo['collectCount']?.toString() ?? '0',
                        ) ??
                        0;
            _addLog('收藏数: $collectCount');
          }

          if (platformInfo['forwardCount'] != null) {
            forwardCount =
                platformInfo['forwardCount'] is int
                    ? platformInfo['forwardCount']
                    : int.tryParse(
                          platformInfo['forwardCount']?.toString() ?? '0',
                        ) ??
                        0;
            _addLog('转发数: $forwardCount');
          }

          if (platformInfo['commentCount'] != null) {
            commentCount =
                platformInfo['commentCount'] is int
                    ? platformInfo['commentCount']
                    : int.tryParse(
                          platformInfo['commentCount']?.toString() ?? '0',
                        ) ??
                        0;
            _addLog('评论数: $commentCount');
          }

          if (platformInfo['workCount'] != null) {
            workCount =
                platformInfo['workCount'] is int
                    ? platformInfo['workCount']
                    : int.tryParse(
                          platformInfo['workCount']?.toString() ?? '0',
                        ) ??
                        0;
            _addLog('作品数: $workCount');
          }

          if (platformInfo['income'] != null) {
            income =
                platformInfo['income'] is int
                    ? platformInfo['income']
                    : int.tryParse(platformInfo['income']?.toString() ?? '0') ??
                        0;
            _addLog('收入: $income');
          }
        } else {
          _addLog('无法获取平台账号信息，将使用默认值');
        }
      } catch (e) {
        _addLog('获取平台账号信息失败: $e');
        // 继续使用默认值
      }

      // 第二步：调用API创建账号
      _addLog('开始调用API创建账号...');

      // 直接调用API创建账号
      final accountService = Get.find<AccountPersistentService>();
      final createdAccount = await createAccountApi(
        cookie,
        platType,
        account,
        nickname,
        avatar,
        uid,
        accessToken: accessToken,
        refreshToken: refreshToken,
        fansCount: fansCount,
        readCount: readCount,
        likeCount: likeCount,
        collectCount: collectCount,
        forwardCount: forwardCount,
        commentCount: commentCount,
        workCount: workCount,
        income: income,
      );

      if (createdAccount != null) {
        // API创建成功
        _addLog('API创建账号成功');
        _addLog('账号ID: ${createdAccount.uid}');
        _addLog('空间ID: ${createdAccount.groupId}');

        // 将账号添加到本地
        await accountService.addOrUpdateAccount(createdAccount);

        _addLog('账号已添加到本地存储');

        // 显示成功提示
        Get.snackbar(
          '登录成功',
          '已创建测试账号',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        // API创建失败
        _addLog('错误: API创建账号失败');

        // 显示失败提示
        Get.snackbar(
          '登录失败',
          'API创建账号失败',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      _addLog('错误: 登录过程中出错: $e');
      LoggerUtil.e('模拟登录失败: $e');

      // 显示错误提示
      Get.snackbar(
        '登录失败',
        '登录过程中出错',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // 处理获取账号列表
  void _handleFetchAccounts() async {
    setState(() {
      isLoading = true;
    });

    try {
      _addLog('开始获取账号列表...');

      // 确保AccountPersistentService已注册
      if (!Get.isRegistered<AccountPersistentService>()) {
        Get.put(AccountPersistentService());
      }

      final accountService = Get.find<AccountPersistentService>();
      final success = await accountService.fetchAccountsFromApi();

      if (success) {
        _addLog('成功获取账号列表');
        _addLog('当前账号数量: ${accountService.accounts.length}');

        // 显示账号列表
        for (var account in accountService.accounts) {
          _addLog(
            '账号: ${account.account}, 昵称: ${account.nickname}, 平台: ${account.type}',
          );
        }

        // 显示成功提示
        Get.snackbar(
          '获取成功',
          '已获取账号列表',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        _addLog('错误: 获取账号列表失败');

        // 显示失败提示
        Get.snackbar(
          '获取失败',
          '无法获取账号列表',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      _addLog('错误: 获取账号列表时出错: $e');
      LoggerUtil.e('获取账号列表失败: $e');

      // 显示错误提示
      Get.snackbar(
        '获取失败',
        '获取账号列表时出错',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // 处理测试删除账号
  void _handleTestDeleteAccount() async {
    setState(() {
      isLoading = true;
    });

    try {
      _addLog('开始测试删除账号...');

      // 确保AccountPersistentService已注册
      if (!Get.isRegistered<AccountPersistentService>()) {
        Get.put(AccountPersistentService());
      }

      final accountService = Get.find<AccountPersistentService>();

      // 检查是否有账号可删除
      if (accountService.accounts.isEmpty) {
        _addLog('错误: 没有可删除的账号');

        // 显示提示
        Get.snackbar(
          '删除失败',
          '没有可删除的账号',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // 获取第一个账号进行删除测试
      final accountToDelete = accountService.accounts.first;

      _addLog(
        '准备删除账号: ${accountToDelete.account}, ID: ${accountToDelete.id ?? "无ID"}',
      );

      // 调用删除方法
      final success = await accountService.deleteAccount(accountToDelete);

      if (success) {
        _addLog('成功删除账号');
        _addLog('当前账号数量: ${accountService.accounts.length}');

        // 显示成功提示
        Get.snackbar(
          '删除成功',
          '已删除测试账号',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        _addLog('错误: 删除账号失败');

        // 显示失败提示
        Get.snackbar(
          '删除失败',
          '无法删除账号',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      _addLog('错误: 删除账号时出错: $e');
      LoggerUtil.e('删除账号失败: $e');

      // 显示错误提示
      Get.snackbar(
        '删除失败',
        '删除账号时出错',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // 处理测试更新账号
  void _handleTestUpdateAccount() async {
    setState(() {
      isLoading = true;
    });

    try {
      _addLog('开始测试更新账号...');

      // 确保AccountPersistentService已注册
      if (!Get.isRegistered<AccountPersistentService>()) {
        Get.put(AccountPersistentService());
      }

      final accountService = Get.find<AccountPersistentService>();

      // 检查是否有账号可更新
      if (accountService.accounts.isEmpty) {
        _addLog('错误: 没有可更新的账号');

        // 显示提示
        Get.snackbar(
          '更新失败',
          '没有可更新的账号',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // 获取第一个账号进行更新测试
      final accountToUpdate = accountService.accounts.first;

      _addLog(
        '准备更新账号: ${accountToUpdate.account}, ID: ${accountToUpdate.id ?? "无ID"}',
      );

      // 修改账号信息
      accountToUpdate.nickname = '已更新-${DateTime.now().millisecondsSinceEpoch}';
      accountToUpdate.avatar = 'https://example.com/updated-avatar.jpg';

      // 调用更新方法
      final success = await accountService.updateAccountInfo(accountToUpdate);

      if (success) {
        _addLog('成功更新账号');
        _addLog('新昵称: ${accountToUpdate.nickname}');

        // 显示成功提示
        Get.snackbar(
          '更新成功',
          '已更新测试账号',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        _addLog('错误: 更新账号失败');

        // 显示失败提示
        Get.snackbar(
          '更新失败',
          '无法更新账号',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      _addLog('错误: 更新账号时出错: $e');
      LoggerUtil.e('更新账号失败: $e');

      // 显示错误提示
      Get.snackbar(
        '更新失败',
        '更新账号时出错',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // 添加日志
  void _addLog(String message) {
    setState(() {
      logs.add('[${DateTime.now().toString().split('.')[0]}] $message');
    });
    LoggerUtil.i(message);
  }
}
