import 'dart:async';

import 'package:aitoearn_app/api/platform_apis/bilibili.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plats_login_official_base.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';

class PlatLoginOfficialBilibili extends PlatsLoginOfficialBase {
  late Timer timer;

  /// 任务ID
  var taskId = '';

  PlatLoginOfficialBilibili(super.onLoginSuccess);

  @override
  login() async {
    var webViewController = state.webViewController.value!;

    try {
      final cancelFunc = showProgress(text: '加载中...');
      var loginUrlRes = await apiGetBilibiliLoginUrl('h5');
      cancelFunc();
      if (loginUrlRes == null) {
        throw Exception('获取登录URL失败');
      }
      taskId = loginUrlRes['taskId'];
      await webViewController.loadUrl(
        urlRequest: URLRequest(url: WebUri(loginUrlRes['url'])),
      );
    } catch (e) {
      showError('登录失败: $e');
      Get.back();
    }
  }

  @override
  destroy() {
    timer.cancel();
  }
}
