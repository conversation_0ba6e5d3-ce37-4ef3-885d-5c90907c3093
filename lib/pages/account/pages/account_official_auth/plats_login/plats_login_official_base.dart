import 'package:aitoearn_app/pages/account/pages/account_official_auth/logic.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/state.dart';
import 'package:get/get.dart';

abstract class PlatsLoginOfficialBase extends GetxController {
  final AccountOfficialAuthState state =
      Get.find<AccountOfficialAuthLogic>().state;

  /// 登录成功回调
  late Function() onLoginSuccess;

  PlatsLoginOfficialBase(this.onLoginSuccess);

  /// 登录core
  login();

  /// 页面销毁执行的销毁方法
  destroy();
}
