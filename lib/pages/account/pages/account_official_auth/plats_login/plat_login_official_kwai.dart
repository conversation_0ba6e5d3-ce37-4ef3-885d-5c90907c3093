import 'dart:async';

import 'package:aitoearn_app/api/platform_apis/kwai.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plats_login_official_base.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';

class PlatLoginOfficialKwai extends PlatsLoginOfficialBase {
  late Timer timer;

  PlatLoginOfficialKwai(super.onLoginSuccess);

  @override
  login() async {
    var webViewController = state.webViewController.value!;

    try {
      final cancelFunc = showProgress(text: '加载中...');
      var loginUrlRes = await createKwaiAuth('h5');
      cancelFunc();
      if (loginUrlRes == null) {
        throw Exception('获取登录URL失败');
      }
      await webViewController.loadUrl(
        urlRequest: URLRequest(url: WebUri(loginUrlRes['url'])),
      );

      timer = Timer.periodic(const Duration(seconds: 2), (Timer timer) async {
        if (state.loadProgress.value < 100) {
          return;
        }
        final authStatusRes = await getKwaiAuthStatus(loginUrlRes['taskId']);
        if (authStatusRes?['status'] == 1) {
          onLoginSuccess();
        }
      });
    } catch (e) {
      showError('登录失败: $e');
      Get.back();
    }
  }

  @override
  destroy() {
    timer.cancel();
  }
}
