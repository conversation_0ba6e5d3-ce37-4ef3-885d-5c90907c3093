import 'dart:async';

import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plats_login_official_base.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class PlatLoginOfficialWxGzh extends PlatsLoginOfficialBase {
  late Timer timer;

  PlatLoginOfficialWxGzh(super.onLoginSuccess);

  @override
  login() async {
    var webViewController = state.webViewController.value!;

    await webViewController.loadUrl(
      urlRequest: URLRequest(
        url: WebUri('https://abrahamjuliot.github.io/creepjs/'),
      ),
    );
  }

  @override
  destroy() {
    timer.cancel();
  }
}
