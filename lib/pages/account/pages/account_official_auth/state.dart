import 'package:aitoearn_app/pages/account/pages/account_get_cookie/models/account_get_cookie_params.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plats_login_official_base.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';

class AccountOfficialAuthState {
  // 登录核心逻辑类
  late PlatsLoginOfficialBase loginController;

  /// page search参数
  var pageSearchParams = Rx<AccountGetCookieParams?>(null);

  /// webview控制器
  var webViewController = Rx<InAppWebViewController?>(null);

  /// webview 加载进度
  var loadProgress = 0.obs;

  /// 空间数据
  late Rx<AccountGroup?> spaceData = Rx(null);
}
