
import 'package:aitoearn_app/config/plat_config/plat_config.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/logic.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/state.dart';
import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';

class AccountOfficialAuthPage extends StatefulWidget {
  const AccountOfficialAuthPage({super.key});

  @override
  State<AccountOfficialAuthPage> createState() =>
      _AccountOfficialAuthPageState();
}

class _AccountOfficialAuthPageState extends State<AccountOfficialAuthPage> {
  final AccountOfficialAuthLogic logic = Get.put(AccountOfficialAuthLogic());
  final AccountOfficialAuthState state =
      Get.find<AccountOfficialAuthLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => BasePage(
        useDefaultBackground: false,
        backgroundColor: AppColors.white,

        title:
            '${platConfigMap[state.pageSearchParams.value?.platType]?.name}${'account.add.login'.tr} - ${state.spaceData.value?.name}',
        body: Stack(
          children: [
            // WebView组件
            InAppWebView(
              onWebViewCreated: (controller) {
                state.webViewController.value = controller;
                logic.getWebviewController();
              },
              onProgressChanged: (controller, progress) {
                state.loadProgress.value = progress;
              },
              initialSettings: InAppWebViewSettings(
                javaScriptEnabled: true,
                useShouldOverrideUrlLoading: true,
                mediaPlaybackRequiresUserGesture: false,
                useHybridComposition: false,
                isInspectable: true,
              ),
            ),
            // 顶部加载进度条
            state.loadProgress < 100
                ? Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.transparent,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      AppColors.primaryColor,
                    ),

                    minHeight: 2,
                    // 根据进度值显示不同样式
                    value:
                        state.loadProgress > 0 && state.loadProgress < 100
                            ? state.loadProgress / 100
                            : null,
                  ),
                )
                : const SizedBox(),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    state.webViewController.value!.stopLoading();
    state.webViewController.value!.dispose();
    Get.delete<AccountOfficialAuthLogic>();
    state.loginController.destroy();
    super.dispose();
  }
}
