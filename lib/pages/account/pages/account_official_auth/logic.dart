import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/models/account_get_cookie_params.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_Twitter.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_bilibili.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_facebook.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_instagram.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_kwai.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_pinterest.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_threads.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_tiktok.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_wxGzh.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plat_login_official_youtube.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/plats_login/plats_login_official_base.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/state.dart';
import 'package:aitoearn_app/pages/account/utils/webview_fingerprint.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:get/get.dart';

class AccountOfficialAuthLogic extends GetxController {
  final AccountOfficialAuthState state = AccountOfficialAuthState();
  var accountController = Get.put(AccountListController());

  @override
  void onReady() {
    super.onReady();
    state.pageSearchParams.value = Get.arguments as AccountGetCookieParams;

    /// 查找数据
    if (state.pageSearchParams.value!.spaceId.isEmpty) {
      state.pageSearchParams.value!.spaceId =
          accountController.groups.value
              .firstWhere((group) => group.isDefault == true)
              .id;
    }

    state.spaceData.value = accountController.groups.value.firstWhere(
      (group) => group.id == state.pageSearchParams.value!.spaceId,
    );
    LoggerUtil.i(state.spaceData.value!.name);
    LoggerUtil.i(state.spaceData.value!.browserConfig);
  }

  Future<void> getWebviewController() async {
    final browserConfig = state.spaceData.value?.browserConfig;
    if (browserConfig != null) {
      await WebviewFingerprint.setFingerprint(
        state.webViewController.value!,
        browserConfig,
      );
    }

    state.loginController = getPlatLoginIns();
    state.loginController.login();
  }

  loginSuccess() async {
    final accountListController = Get.find<AccountListController>();
    accountListController.refreshAccountsStatus();

    Get.back();
    showSuccess('登录成功！');
    state.loginController.destroy();
  }

  PlatsLoginOfficialBase getPlatLoginIns() {
    switch (state.pageSearchParams.value!.platType) {
      case PlatTypeEnum.bilibili:
        return PlatLoginOfficialBilibili(loginSuccess);
      case PlatTypeEnum.pinterest:
        return PlatLoginOfficialPinterest(loginSuccess);
      case PlatTypeEnum.kwai:
        return PlatLoginOfficialKwai(loginSuccess);
      case PlatTypeEnum.wxGzh:
        return PlatLoginOfficialWxGzh(loginSuccess);
      case PlatTypeEnum.tiktok:
        return PlatLoginOfficialTiktok(loginSuccess);
      case PlatTypeEnum.youTube:
        return PlatLoginOfficialYouTube(loginSuccess);
      case PlatTypeEnum.twitter:
        return PlatLoginOfficialTwitter(loginSuccess);
      case PlatTypeEnum.facebook:
        return PlatLoginOfficialFacebook(loginSuccess);
      case PlatTypeEnum.instagram:
        return PlatLoginOfficialInstagram(loginSuccess);
      case PlatTypeEnum.threads:
        return PlatLoginOfficialThreads(loginSuccess);
      default:
        LoggerUtil.e('不支持的登录平台: ${state.pageSearchParams.value!.platType}');
        throw Exception('不支持的登录平台: ${state.pageSearchParams.value!.platType}');
    }
  }
}
