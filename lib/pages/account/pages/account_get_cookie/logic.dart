import 'dart:io';

import 'package:aitoearn_app/api/account/account_api.dart';
import 'package:aitoearn_app/api/account/models/account_user_info_modle.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/models/account_get_cookie_params.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plat_login_douyin.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plat_login_kwai.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plat_login_wx_sph.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plat_login_xhs.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plats_login_base.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/state.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AccountGetCookieLogic extends GetxController {
  final AccountGetCookieState state = AccountGetCookieState();
  var accountController = Get.put(AccountListController());

  @override
  void onReady() {
    super.onReady();
    state.pageSearchParams.value = Get.arguments as AccountGetCookieParams;
    // 平台配置获取
    state.accountPlatInfo.value =
        platConfigMap[state.pageSearchParams.value!.platType]!;

    /// 查找数据
    if (state.pageSearchParams.value!.spaceId.isEmpty) {
      state.pageSearchParams.value!.spaceId =
          accountController.groups.value
              .firstWhere((group) => group.isDefault == true)
              .id;
    }
    state.spaceData.value = accountController.groups.value.firstWhere(
      (group) => group.id == state.pageSearchParams.value!.spaceId,
    );

    getWebviewController();
  }

  /// 获取webview 控制器，包含初始化
  getWebviewController() {
    // 将之前的所有cookie删了
    final cookieManager = WebViewCookieManager();
    cookieManager.clearCookies();
    state.loadProgress.value = 0;
    state.loginController = getPlatLoginIns();

    WebViewController? webViewController;
    webViewController =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                LoggerUtil.i('webview加载进度： $progress');
                state.loadProgress.value = progress;
              },
              onPageFinished: (url) async {
                if (state.spaceData.value?.browserConfig != null) {
                  // WebviewFingerprint.setFingerprint(
                  //   webViewController!,
                  //   state.spaceData.value!.browserConfig!,
                  // );
                }

                if (state.accountPlatInfo.value!.platType !=
                    PlatTypeEnum.douyin) {
                  return;
                }

                var privateKey = await webViewController!
                    .runJavaScriptReturningResult("""
                (function() {
                  try {
                    const sdkData = window.localStorage['security-sdk/s_sdk_crypt_sdk'];
                    const parsedData = JSON.parse(sdkData);
                    const parsedInnerData = JSON.parse(parsedData.data);
                    return parsedInnerData.ec_privateKey;
                  } catch (e) {
                    console.error('解析 privateKey 失败:', e);
                    return null;
                  }
                })()
                """);

                var webProtect = await webViewController
                    .runJavaScriptReturningResult("""
                (function() {
                  try {
                    const sdkData = window.localStorage['security-sdk/s_sdk_crypt_sdk'];
                    const parsedData = JSON.parse(sdkData);
                    const parsedInnerData = JSON.parse(parsedData.data);
                    return parsedInnerData.ec_privateKey;
                  } catch (e) {
                    console.error('解析 privateKey 失败:', e);
                    return null;
                  }
                })()
                """);

                LoggerUtil.i(privateKey);
                LoggerUtil.i(webProtect);
                LoggerUtil.i({
                  'privateKey': privateKey,
                  'webProtect': webProtect,
                });
              },
            ),
          );
    webViewController.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0',
    );
    state.webViewController.value = webViewController;
    state.loginController.login();
  }

  // 登录成功
  loginSuccess(List<Cookie> cookies) async {
    LoggerUtil.i('登录成功，获取到Cookie：$cookies');

    try {
      // 显示加载中对话框
      Get.dialog(
        const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      // 从cookie中提取基本用户信息
      String uid = '';
      String? accessToken;
      String? refreshToken;

      // 提取token信息
      for (var cookie in cookies) {
        if (cookie.name == 'access_token') {
          accessToken = cookie.value;
        } else if (cookie.name == 'refresh_token') {
          refreshToken = cookie.value;
        }
      }

      LoggerUtil.i(
        '提取的Token信息 - Access Token: $accessToken, Refresh Token: $refreshToken',
      );

      // 根据不同平台处理cookie，提取用户ID
      switch (state.pageSearchParams.value!.platType) {
        case PlatTypeEnum.xhs:
          // 小红书处理
          uid =
              _extractValueFromCookies(cookies, 'galaxy_creator_session_id') ??
              '';
          LoggerUtil.i('小红书登录 - UID: $uid');
          break;
        case PlatTypeEnum.douyin:
          // 抖音处理
          uid = _extractValueFromCookies(cookies, 'sessionid') ?? '';
          LoggerUtil.i('抖音登录 - UID: $uid');
          break;
        case PlatTypeEnum.wxWph:
          // 微信视频号处理
          uid = _extractValueFromCookies(cookies, 'wxuin') ?? '';
          LoggerUtil.i('微信视频号登录 - UID: $uid');
          break;
        case PlatTypeEnum.kwai:
          // 快手处理
          uid = _extractValueFromCookies(cookies, 'userId') ?? '';
          LoggerUtil.i('快手登录 - UID: $uid');
          break;
        default:
          LoggerUtil.w(
            '未知平台类型: ${state.pageSearchParams.value!.platType.name}，无法提取UID',
          );
          uid = '';
          break;
      }

      // 确保有有效的UID
      if (uid.isEmpty) {
        // 尝试生成一个临时UID
        uid = 'temp_${DateTime.now().millisecondsSinceEpoch}';
        LoggerUtil.w('无法获取有效UID，使用临时UID: $uid');
      }

      // 将cookie列表转换为字符串
      String cookieStr = '';
      for (var cookie in cookies) {
        cookieStr += '${cookie.name}=${cookie.value}; ';
      }

      // 确保AccountPersistentService已注册
      if (!Get.isRegistered<AccountPersistentService>()) {
        Get.put(AccountPersistentService());
      }

      final accountService = Get.find<AccountPersistentService>();

      // 第一步：获取平台账号详细信息
      LoggerUtil.i('开始获取平台账号详细信息');

      String nickname = '未知用户';
      String avatar = '';
      int fansCount = 0;
      int readCount = 0;
      int likeCount = 0;
      int collectCount = 0;
      int forwardCount = 0;
      int commentCount = 0;
      int workCount = 0;
      int income = 0;

      try {
        // 使用新的统一接口获取平台账号信息
        AccountUserInfoModle? platformAccount = await accountService
            .getPlatformAccountInfo(
              state.pageSearchParams.value!.platType,
              cookieStr,
              uid,
            );

        if (platformAccount != null) {
          LoggerUtil.i('成功获取平台账号信息: ${platformAccount.nickname}');

          // 使用平台信息更新账号信息
          nickname = platformAccount.nickname ?? '未知用户';
          avatar = platformAccount.avatar ?? '';
          fansCount = platformAccount.fansCount ?? 0;
          readCount = platformAccount.readCount ?? 0;
          likeCount = platformAccount.likeCount ?? 0;
          collectCount = platformAccount.collectCount ?? 0;
          forwardCount = platformAccount.forwardCount ?? 0;
          commentCount = platformAccount.commentCount ?? 0;
          workCount = platformAccount.workCount ?? 0;
          income = platformAccount.income ?? 0;

          // 如果平台返回了更准确的UID，使用平台UID
          if (platformAccount.uid.isNotEmpty &&
              platformAccount.uid != 'temp_') {
            uid = platformAccount.uid;
            LoggerUtil.i('使用平台返回的UID: $uid');
          }

          LoggerUtil.i('平台账号信息: 昵称=$nickname, 粉丝数=$fansCount, 阅读数=$readCount');
        } else {
          LoggerUtil.w('无法获取平台账号信息，将使用默认值');
        }
      } catch (e) {
        LoggerUtil.e('获取平台账号信息失败: $e');
        // 继续使用默认值
      }

      // 第二步：创建账号对象（使用获取到的平台信息）
      LoggerUtil.i(
        '创建账号对象 - 平台: ${state.pageSearchParams.value!.platType.name}, UID: $uid',
      );

      // 创建基础账号对象
      final account = accountService.createAccountFromCookie(
        state.pageSearchParams.value!.platType,
        cookies,
        uid,
        nickname,
        avatar,
      );

      // 更新账号对象的统计数据
      account.fansCount = fansCount;
      account.readCount = readCount;
      account.likeCount = likeCount;
      account.collectCount = collectCount;
      account.forwardCount = forwardCount;
      account.commentCount = commentCount;
      account.workCount = workCount;
      account.income = income;

      // 第三步：调用API创建账号
      LoggerUtil.i('开始调用API创建账号');

      // 尝试最多3次
      bool success = false;
      String errorMessage = '';

      for (int attempt = 1; attempt <= 3; attempt++) {
        try {
          LoggerUtil.i('创建账号尝试 #$attempt');

          // 调用createAccountApi创建账号
          final createdAccount = await createAccountApi(
            account.cookie ?? '',
            account.type,
            account.account,
            account.nickname ?? '',
            account.avatar ?? '',
            account.uid,
            groupId: account.groupId,
            accessToken: account.accessToken,
            refreshToken: account.refreshToken,
            fansCount: account.fansCount ?? 0,
            readCount: account.readCount ?? 0,
            likeCount: account.likeCount ?? 0,
            collectCount: account.collectCount ?? 0,
            forwardCount: account.forwardCount ?? 0,
            commentCount: account.commentCount ?? 0,
            workCount: account.workCount ?? 0,
            income: account.income ?? 0,
          );

          if (createdAccount != null) {
            LoggerUtil.i('API创建账号成功: ID=${createdAccount.id}');

            // 使用API返回的账号信息更新本地
            await accountService.addOrUpdateAccount(createdAccount);
            success = true;
            break;
          } else {
            errorMessage = '服务器未返回有效的账号数据';
            LoggerUtil.e('API创建账号失败: $errorMessage');

            // 等待一段时间后重试
            if (attempt < 3) {
              await Future.delayed(Duration(seconds: 1 * attempt));
            }
          }
        } catch (e) {
          errorMessage = e.toString();
          LoggerUtil.e('API创建账号异常: $e');

          // 等待一段时间后重试
          if (attempt < 3) {
            await Future.delayed(Duration(seconds: 1 * attempt));
          }
        }
      }

      // 如果API创建失败，尝试本地保存
      if (!success) {
        LoggerUtil.w('API创建账号失败，尝试本地保存');
        success = await accountService.addOrUpdateAccount(account);
      }

      // 立即更新账号信息
      await accountService.updateAccountsUserInfo();

      // 关闭加载对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      if (success) {
        // 显示成功提示
        Get.snackbar(
          '登录成功',
          '已保存账号信息',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );

        // 如果账号列表控制器已注册，通知它刷新数据
        if (Get.isRegistered<AccountListController>()) {
          LoggerUtil.i('通知账号列表控制器刷新数据');
          final accountListController = Get.find<AccountListController>();
          await accountListController.loadGroupsFromApi();
        }

        // 延迟返回上一页
        Future.delayed(const Duration(seconds: 1), () {
          Get.back(); // 返回账号列表页
        });
      } else {
        // 显示失败提示
        Get.snackbar(
          '保存失败',
          '无法保存账号信息: $errorMessage',
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );

        LoggerUtil.e('账号创建/更新失败');
      }
    } catch (e) {
      LoggerUtil.e('保存账号信息失败: $e');
      // 关闭可能存在的加载对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }
      Get.snackbar(
        '登录失败',
        '保存账号信息时出错: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // 从Cookie列表中提取指定名称的值
  String? _extractValueFromCookies(List<Cookie> cookies, String name) {
    for (var cookie in cookies) {
      if (cookie.name == name) {
        return cookie.value;
      }
    }
    return null;
  }

  PlatsLoginBase getPlatLoginIns() {
    switch (state.pageSearchParams.value!.platType) {
      case PlatTypeEnum.xhs:
        return PlatLoginXhs(loginSuccess);
      case PlatTypeEnum.douyin:
        return PlatLoginDouyin(loginSuccess);
      case PlatTypeEnum.wxWph:
        return PlatLoginWxSph(loginSuccess);
      case PlatTypeEnum.kwai:
        return PlatLoginKwai(loginSuccess);
      default:
        LoggerUtil.e(
          '不支持的登录平台: ${state.pageSearchParams.value!.platType.name}',
        );
        throw Exception(
          '不支持的登录平台: ${state.pageSearchParams.value!.platType.name}',
        );
    }
  }
}
