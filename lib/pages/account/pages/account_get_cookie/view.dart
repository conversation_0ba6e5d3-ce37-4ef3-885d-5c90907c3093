import 'package:aitoearn_app/config/plat_config/plat_config.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/logic.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/state.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AccountGetCookiePage extends StatefulWidget {
  const AccountGetCookiePage({super.key});

  @override
  State<AccountGetCookiePage> createState() => _AccountGetCookiePageState();
}

class _AccountGetCookiePageState extends State<AccountGetCookiePage> {
  final AccountGetCookieLogic logic = Get.put(AccountGetCookieLogic());
  final AccountGetCookieState state = Get.find<AccountGetCookieLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TDNavBar(
        titleWidget: Obx(() {
          return Text(
            '${platConfigMap[state.pageSearchParams.value?.platType]!.name} ${'account.add.login'.tr}',
            style: const TextStyle(fontSize: Dimens.font_sp18),
          );
        }),
        backgroundColor: TDTheme.of(context).grayColor1,
      ),
      body: Stack(
        children: [
          Obx(
            () =>
                state.webViewController.value != null
                    ? WebViewWidget(controller: state.webViewController.value!)
                    : const SizedBox(),
          ),
          Obx(
            () =>
                state.loadProgress < 100
                    ? Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: LinearProgressIndicator(
                        backgroundColor: Colors.transparent,
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          AppColors.primaryColor,
                        ),
                        minHeight: 2,
                        // 根据进度值显示不同样式
                        value:
                            state.loadProgress > 0 && state.loadProgress < 100
                                ? state.loadProgress / 100
                                : null,
                      ),
                    )
                    : const SizedBox(),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    Get.delete<AccountGetCookieLogic>();
    state.loginController.destroy();
    super.dispose();
  }
}
