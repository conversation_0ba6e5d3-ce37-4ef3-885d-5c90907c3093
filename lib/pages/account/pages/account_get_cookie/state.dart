import 'package:aitoearn_app/config/plat_config/plat_config_models.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/models/account_get_cookie_params.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plats_login_base.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AccountGetCookieState {
  // 登录核心逻辑类
  late PlatsLoginBase loginController;

  /// page search参数
  var pageSearchParams = Rx<AccountGetCookieParams?>(null);

  /// 平台配置
  var accountPlatInfo = Rx<AccountPlatInfo?>(null);

  /// webview控制器
  var webViewController = Rx<WebViewController?>(null);

  /// webview 加载进度
  var loadProgress = 0.obs;

  /// 空间数据
  late Rx<AccountGroup?> spaceData = Rx(null);
}
