import 'dart:async';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plats_login_base.dart';
import 'package:get/get.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';

class PlatLoginXhs extends PlatsLoginBase {
  late Timer timer;

  /// 用户小红书登录判断，判断 session 的变化
  var prevWebSession = '';

  /// 小红书主页登录yrl
  final loginHomeUrl = "https://www.xiaohongshu.com/";

  /// 小红书视频后台登录url
  final loginCreatorUrl = "https://creator.xiaohongshu.com/";

  PlatLoginXhs(super.onLoginSuccess);

  @override
  login() {
    var webViewController = state.webViewController.value!;
    webViewController.loadRequest(Uri.parse(loginHomeUrl));
    final cookieManager = WebviewCookieManager();

    timer = Timer.periodic(Duration(seconds: 1), (Timer timer) async {
      if (state.loadProgress.value < 100) return;

      var currUrl = await webViewController.currentUrl();
      final cookiesHome = await cookieManager.getCookies(loginHomeUrl);
      final cookiesCreator = await cookieManager.getCookies(loginCreatorUrl);

      // 前台cookie检测
      if (currUrl!.contains(loginHomeUrl)) {
        final webSession = cookiesHome.firstWhereOrNull(
              (cookie) => cookie.name == 'web_session',
        );
        if (prevWebSession.isEmpty) {
          prevWebSession = webSession?.value ?? '';
        }
        if (prevWebSession == (webSession?.value ?? '')) return;
        await webViewController.loadRequest(
          Uri.parse('${loginCreatorUrl}login?source=official'),
        );
      } else if (currUrl.contains(loginCreatorUrl)) {
        // 开发者后台cookie检测
        LoggerUtil.d(cookiesCreator.toString());
        if (cookiesCreator.any(
              (cookie) => cookie.name.contains("access-token"),
        )) {
          // cookie获取成功！
          onLoginSuccess(cookiesHome + cookiesCreator);
          destroy();
        }
      }
    });
  }

  @override
  destroy() {
    timer.cancel();
  }
}
