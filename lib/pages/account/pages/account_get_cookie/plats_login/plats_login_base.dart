import 'dart:io';

import 'package:aitoearn_app/pages/account/pages/account_get_cookie/logic.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/state.dart';
import 'package:get/get.dart';

abstract class PlatsLoginBase extends GetxController {
  final AccountGetCookieState state = Get.find<AccountGetCookieLogic>().state;

  /// 登录成功回调，返回cookie
  late Function(List<Cookie>) onLoginSuccess;

  PlatsLoginBase(this.onLoginSuccess);

  /// 登录core
  login();

  /// 页面销毁执行的销毁方法
  destroy();
}
