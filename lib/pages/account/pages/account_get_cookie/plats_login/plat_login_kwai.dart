import 'dart:async';

import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plats_login_base.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';

class Plat<PERSON>ogin<PERSON>wai extends PlatsLoginBase {
  Timer? timer1;
  Timer? timer2;

  /// 首页登录
  final loginHomeUrl = 'https://passport.kuaishou.com/pc/account/login';

  /// 创作者中心
  final loginCreatorUrl =
      'https://cp.kuaishou.com/article/publish/video?origin=www.kuaishou.com';

  PlatLoginKwai(super.onLoginSuccess);

  @override
  login() {
    var webViewController = state.webViewController.value!;
    webViewController.loadRequest(Uri.parse(loginCreatorUrl));
    final cookieManager = WebviewCookieManager();

    // 前台cookie检测
    timer1 = Timer.periodic(const Duration(seconds: 1), (Timer timer) async {
      if (state.loadProgress.value < 100) return;
      final cookies = await cookieManager.getCookies(loginCreatorUrl);

      if (cookies.any(
        (cookie) => cookie.name == 'kuaishou.web.cp.api_st',
      )) {
        timer1?.cancel();
        onLoginSuccess(cookies);
        // timer1?.cancel();
        // // 创作者中心
        // await webViewController.loadRequest(Uri.parse(loginCreatorUrl));
        //
        // // 开发者后台cookie检测
        // timer2 = Timer.periodic(Duration(seconds: 1), (Timer timer) async {
        //   if (state.loadProgress.value < 100) return;
        //   final cookies = await cookieManager.getCookies(null);
        //
        //   if (cookies.any((cookie) => cookie.name == "ks_onvideo_token")) {
        //     onLoginSuccess(cookies);
        //   }
        // });
      }
    });
  }

  @override
  destroy() {
    timer1?.cancel();
    timer2?.cancel();
  }
}
