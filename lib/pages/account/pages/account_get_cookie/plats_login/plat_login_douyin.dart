import 'dart:async';

import 'package:aitoearn_app/pages/account/pages/account_get_cookie/plats_login/plats_login_base.dart';
import 'package:webview_cookie_manager/webview_cookie_manager.dart';

class PlatLoginDouyin extends PlatsLoginBase {
  final loginUrl = 'https://creator.douyin.com/';
  late Timer timer;

  PlatLoginDouyin(super.onLoginSuccess);

  @override
  login() {
    var webViewController = state.webViewController.value!;
    webViewController.loadRequest(Uri.parse(loginUrl));
    final cookieManager = WebviewCookieManager();

    timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) async {
      if (state.loadProgress.value < 100) return;
      final cookies = await cookieManager.getCookies(loginUrl);

      if (cookies.any((cookie) => cookie.name.contains('sessionid')) &&
          cookies.any((cookie) => cookie.name.contains('x-web-secsdk-uid'))) {
        onLoginSuccess(cookies);
        destroy();
      }
    });
  }

  @override
  destroy() {
    timer.cancel();
  }
}
