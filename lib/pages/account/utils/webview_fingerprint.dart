import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebviewFingerprint {
  static Future<void> setFingerprint(
    InAppWebViewController controller,
    Map<String, dynamic> fingerprint,
  ) async {
    await controller.setSettings(
      settings: InAppWebViewSettings(
        userAgent: fingerprint['fingerprint']['navigator']['userAgent'],
        javaScriptEnabled: true,
        cacheEnabled: true,
        isInspectable: true,
      ),
    );
    // 注入 utils.js
    final utilsJs = await rootBundle.loadString('assets/fingerprint/utils.js');

    // 构造指纹注入脚本
    final fp = fingerprint['fingerprint'] ?? {};
    final headers = fingerprint['headers'] ?? {};
    final historyLength = Random().nextInt(5) + 2;

    final injectScript = '''
      (function() {
        const fp = ${jsonEncode(fp)};
        console.log(fp);
        
        runHeadlessFixes();
        if (fp.mockWebRTC) blockWebRTC();
        if (fp.slim) window.slim = true;
        if (fp.navigator) {
          overrideIntlAPI(fp.navigator.language);
          overrideStatic();
          overrideInstancePrototype(window.navigator, fp.navigator);
          if (fp.navigator.userAgentData) {
            overrideUserAgentData(fp.navigator.userAgentData);
          }
          if (window.navigator.webdriver) {
              fp.navigator.webdriver = false;
          }
          overrideInstancePrototype(window.navigator, fp.navigator);
        }
        
        if (fp.screen) {
          overrideInstancePrototype(window.screen, fp.screen);
          overrideWindowDimensionsProps({
            innerHeight: fp.screen.innerHeight,
            outerHeight: fp.screen.outerHeight,
            outerWidth: fp.screen.outerWidth,
            innerWidth: fp.screen.innerWidth,
            screenX: fp.screen.screenX,
            pageXOffset: fp.screen.pageXOffset,
            pageYOffset: fp.screen.pageYOffset,
            devicePixelRatio: fp.screen.devicePixelRatio
          });
          overrideDocumentDimensionsProps({
            clientHeight: fp.screen.clientHeight,
            clientWidth: fp.screen.clientWidth
          })
          overrideInstancePrototype(window.history, {
            length: $historyLength,
          });
        }
        if (fp.battery) {
          overrideBattery(fp.battery);
        }
        if (fp.videoCard) {
          overrideWebGl(fp.videoCard);
        }
        if (fp.audioCodecs && fp.videoCodecs) {
          overrideCodecs(fp.audioCodecs, fp.videoCodecs);
        }
        if (fp.pluginsData) {
          fixPluginArray(fp.pluginsData);
        }
        if (fp.multimediaDevices) {
           navigator.mediaDevices.enumerateDevices = function() {
            const devices = [
              ...(fp.multimediaDevices.speakers || []),
              ...(fp.multimediaDevices.micros || []),
              ...(fp.multimediaDevices.webcams || [])
            ];
            return Promise.resolve(devices);
          };
        }
      })();
    ''';

    await controller.addUserScript(
      userScript: UserScript(
        source: '''(()=>{
          $utilsJs; 
          $injectScript;
         })()''',
        injectionTime: UserScriptInjectionTime.AT_DOCUMENT_START,
      ),
    );
  }
}
