import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/login/register_email/register_email_controller.dart';
import 'package:aitoearn_app/pages/login/register_email/widgets/agreement_checkbox.dart';
import 'package:aitoearn_app/pages/login/register_email/widgets/confirm_hint_item.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/input_box.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RegisterEmailPage extends GetView<RegisterEmailController> {
  const RegisterEmailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'register_account'.tr,
      body: SingleChildScrollView(
        child: ResponsiveLayout(
          mobile: getContentWidget(),
          desktop: Center(
            child: SizedBox(width: 400, child: getContentWidget()),
          ),
        ),
      ),
    );
  }

  getContentWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Gaps.vGap8,
        'verification_sent'.tr.toNormalText(
          style: const TextStyle(fontSize: 14, color: AppColors.textColor),
        ),
        controller.email.toNormalText(
          style: const TextStyle(fontSize: 16, color: AppColors.primaryColor),
        ),
        'check_mail_to_verify'.tr.toNormalText(
          style: const TextStyle(fontSize: 14, color: AppColors.textColor),
        ),

        Gaps.vGap24,

        CustomInputBox(
          title: 'set_password'.tr,
          titleStyle: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            decoration: TextDecoration.none,
          ),
          hintText: 'input_password'.tr,
          controller: controller.passwordController,
          obscureText: true,
          textInputType: TextInputType.visiblePassword,
        ).marginOnly(bottom: 10),

        Obx(
          () => ConfirmHintItem(
            text: 'password_at_least_8'.tr,
            isConfirmed: controller.passwordLength.value,
          ),
        ).marginOnly(bottom: 2),

        Obx(
          () => ConfirmHintItem(
            text: 'password_uppercase_required'.tr,
            isConfirmed: controller.passwordContainsCaps.value,
          ),
        ).marginOnly(bottom: 2),

        Obx(
          () => ConfirmHintItem(
            text: 'password_number_or_symbol'.tr,
            isConfirmed: controller.passwordContainsNumberOrSign.value,
          ),
        ).marginOnly(bottom: 2),

        Gaps.vGap10,

        CustomInputBox(
          title: 'confirm_password'.tr,
          titleStyle: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            decoration: TextDecoration.none,
          ),
          hintText: 'input_password'.tr,
          controller: controller.passwordConformController,
          obscureText: true,
          textInputType: TextInputType.visiblePassword,
        ).marginOnly(bottom: 10),

        CustomInputBox(
          title: 'invite_code_optional'.tr,
          titleStyle: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            decoration: TextDecoration.none,
          ),
          hintText: 'input_invite_code'.tr,
          controller: controller.inviteCodeController,
        ).marginOnly(bottom: 15),

        Gaps.vGap24,

        Obx(
          () => CustomButton(
            text: controller.registerEnable.value ? 'register_account'.tr : 'waiting_for_verification'.tr,
            gradient: AppColors.blueAndPurple,
            textColor: Colors.white,
            onPressed: () => controller.registerEmail(),
            color: Colors.purple[900]?.withValues(alpha: 0.65) ?? Colors.purple,
            enable: controller.registerEnable.value,
            showLoading: !controller.registerEnable.value,
          ),
        ),

        Gaps.vGap16,

        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            'already_have_account'.tr.toNormalText(),
            'login_now'.tr
                .toNormalText(
                  color:
                      Colors.purple[900]?.withValues(alpha: 0.6) ??
                      Colors.purple,
                )
                .gestures(onTap: () => Get.back()),
          ],
        ),

        Gaps.vGap16,
      ],
    ).paddingSymmetric(horizontal: 20).safeArea();
  }
}
