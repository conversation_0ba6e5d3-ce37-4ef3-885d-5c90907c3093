import 'package:aitoearn_app/pages/login/register_email/register_email_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ConfirmHintItem extends GetView<RegisterEmailController> {
  final String text;
  final bool isConfirmed;

  const ConfirmHintItem({
    required this.text, super.key,
    this.isConfirmed = false,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 勾选图标
        isConfirmed
            ? const Icon(Icons.check, color: Colors.green, size: 14)
            : const Icon(Icons.cancel, color: Colors.red, size: 14),

        text.toNormalText(style: const TextStyle(
          color: AppColors.textHintColor,
          fontSize: 13,
        )).marginOnly(left: 4),
      ],
    );
  }
}
