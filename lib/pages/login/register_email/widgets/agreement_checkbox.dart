import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AgreementCheckbox extends StatelessWidget {
  final Function? openServiceAgreement;
  final Function? openPrivacyPolicy;
  final RxBool agreementChecked;
  const AgreementCheckbox({
    required this.agreementChecked, super.key,
    this.openServiceAgreement,
    this.openPrivacyPolicy,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 复选框
        Obx(
          () => Checkbox(
            value: agreementChecked.value,
            onChanged: (value) {
              agreementChecked.value = value ?? false;
            },
            activeColor: Colors.purple[600],
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(3),
            ),
          ),
        ),

        // 文本和链接
        Expanded(
          child: RichText(
            text: TextSpan(
              style: const TextStyle(fontSize: 13, color: Colors.black87),
              children: [
                TextSpan(text: 'agree_prefix'.tr),
                TextSpan(
                  text: 'terms_of_service'.tr,
                  style: TextStyle(
                    color: Colors.purple[600],
                    fontWeight: FontWeight.bold,
                  ),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          // 处理服务条款点击事件
                          openServiceAgreement?.call();
                        },
                ),
                TextSpan(text: 'privacy_and'.tr),
                TextSpan(
                  text: 'privacy_policy'.tr,
                  style: TextStyle(
                    color: Colors.purple[700],
                    fontWeight: FontWeight.bold,
                  ),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          // 处理隐私政策点击事件
                          openPrivacyPolicy?.call();
                        },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
