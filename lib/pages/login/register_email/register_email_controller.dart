import 'dart:async';
import 'dart:convert';

import 'package:aitoearn_app/api/login/login_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/network/response_yk.dart';
import 'package:aitoearn_app/pages/login/bean/login_info.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RegisterEmailController extends GetxController {
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController inviteCodeController = TextEditingController();
  final TextEditingController passwordConformController = TextEditingController();

  final passwordLength = false.obs;
  final passwordContainsCaps = false.obs;
  final passwordContainsNumberOrSign = false.obs;

  String email = '';
  String code = '';
  Timer? _emailPollingTimer;

  final registerEnable = true.obs;

  /// 在 widget 内存中分配后立即调用。
  @override
  void onInit() {
    super.onInit();
    final args = Get.arguments;
    email = args['email'];
    code = args['code'];
    final password = args['password'];

    passwordController.addListener(() {
      validatePassword(passwordController.text);
    });

    Future.delayed(const Duration(milliseconds: 300), () {
      passwordController.text = password;
    });
  }

  @override
  void onClose() {
    _emailPollingTimer?.cancel();
    super.onClose();
  }

  /// dispose 释放内存
  @override
  void dispose() {
    passwordController.dispose();
    inviteCodeController.dispose();
    passwordConformController.dispose();
    super.dispose();
  }

  bool validatePassword(String password) {
    bool validata = true;
    // 至少8个字符
    if (password.length < 8) {
      passwordLength.value = false;
      validata = false;
    } else {
      passwordLength.value = true;
    }

    // 包含大写字母
    if (!password.contains(RegExp(r'[A-Z]'))) {
      passwordContainsCaps.value = false;
      validata = false;
    } else {
      passwordContainsCaps.value = true;
    }

    // 包含数字或符号
    if (!password.contains(RegExp(r'[0-9!@#$%^&*(),.?":{}|<>]'))) {
      passwordContainsNumberOrSign.value = false;
      validata = false;
    } else {
      passwordContainsNumberOrSign.value = true;
    }

    return validata;
  }

  Future<void> registerEmail() async {
    final password = passwordController.text;

    if (password.isEmpty) {
      showToast('请填写密码');
      return;
    }

    if (!validatePassword(password)) {
      showToast('密码不符合要求');
      return;
    }

    final passwordConform = passwordConformController.text;
    if (password != passwordConform) {
      showToast('两次输入的密码不一致');
      return;
    }

    registerEnable.value = false;

    // 清除旧的 Timer
    _emailPollingTimer?.cancel();
    final isSuccess = await fetchRegisterStatus(password);

    const interval = Duration(seconds: 3);
    if (!isSuccess) {
      _emailPollingTimer = Timer.periodic(interval, (timer) async {
        final isSuccess = await fetchRegisterStatus(password);
        if (isSuccess) {
          timer.cancel();
          Get.offAllNamed(AppRouter.homePath);
        }
      });
    } else {
      Get.offAllNamed(AppRouter.homePath);
    }
  }

  Future<bool> fetchRegisterStatus(String password) async {
    try {
      final response = await pollEmailRegisterResultApi(
        mail: email,
        code: code,
        password: md5.convert(utf8.encode(password)).toString(),
        inviteCode: inviteCodeController.text,
      );

      final result = ResponseYK<LoginInfoModel>.fromJson(
        response?.data,
        (data) => LoginInfoModel.fromJson(response?.data?['data']),
      );

      if (result.data?.token?.isNotEmpty ?? false) {
        final userStore = Get.find<UserStoreService>();
        await userStore.saveToken(result.data?.token ?? '');
        final hasUser = await userStore.getUserInfo();
        if (hasUser) {
          registerEnable.value = true;
          showSuccess('注册成功');
        } else {
          showError('注册失败');
        }
        return true;
      }
    } catch (e) {
      registerEnable.value = true;
      print('注册轮询出错: $e');
    }
    return false;
  }
}
