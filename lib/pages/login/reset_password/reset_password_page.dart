import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/login/reset_password/reset_password_controller.dart';
import 'package:aitoearn_app/pages/login/reset_password/widgets/reset_password_email_widget.dart';
import 'package:aitoearn_app/pages/login/reset_password/widgets/reset_password_reset_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetPasswordPage extends GetView<ResetPasswordController> {
  const ResetPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'reset_password'.tr,
      body: SingleChildScrollView(
        child: ResponsiveLayout(
          mobile: getContentWidget(),
          desktop: Center(
            child: SizedBox(width: 400, child: getContentWidget()),
          ),
        ),
      ),
    );
  }

  getContentWidget() {
    return Obx(
      () =>
          controller.resetPasswordStep.value == 1
              ? const ResetPasswordEmailWidget()
              : const ResetPasswordResetWidget(),
    );
  }
}
