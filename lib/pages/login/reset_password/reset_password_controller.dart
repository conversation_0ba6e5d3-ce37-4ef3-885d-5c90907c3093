import 'dart:async';
import 'dart:convert';

import 'package:aitoearn_app/api/login/login_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/network/response_yk.dart';
import 'package:aitoearn_app/pages/login/bean/login_info.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetPasswordController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController passwordConformController =
      TextEditingController();

  final passwordLength = false.obs;
  final passwordContainsCaps = false.obs;
  final passwordContainsNumberOrSign = false.obs;

  String email = '';
  String code = '';
  Timer? _emailPollingTimer;

  final resetEmailEnable = true.obs;
  final resetPasswordEnable = true.obs;

  // 步骤1 - 输入邮箱    步骤2 - 输入密码
  final resetPasswordStep = 1.obs;

  /// 在 widget 内存中分配后立即调用。
  @override
  void onInit() {
    super.onInit();
    passwordController.addListener(() {
      validatePassword(passwordController.text);
    });
  }

  @override
  void onClose() {
    _emailPollingTimer?.cancel();
    super.onClose();
  }

  /// dispose 释放内存
  @override
  void dispose() {
    passwordController.dispose();
    passwordConformController.dispose();
    super.dispose();
  }

  bool validatePassword(String password) {
    bool validata = true;
    // 至少8个字符
    if (password.length < 8) {
      passwordLength.value = false;
      validata = false;
    } else {
      passwordLength.value = true;
    }

    // 包含大写字母
    if (!password.contains(RegExp(r'[A-Z]'))) {
      passwordContainsCaps.value = false;
      validata = false;
    } else {
      passwordContainsCaps.value = true;
    }

    // 包含数字或符号
    if (!password.contains(RegExp(r'[0-9!@#$%^&*(),.?":{}|<>]'))) {
      passwordContainsNumberOrSign.value = false;
      validata = false;
    } else {
      passwordContainsNumberOrSign.value = true;
    }

    return validata;
  }

  Future<void> resetPassword() async {
    final password = passwordController.text;
    if (!validatePassword(password)) {
      showToast('密码不符合要求');
      return;
    }

    resetPasswordEnable.value = false;

    // 清除旧的 Timer
    _emailPollingTimer?.cancel();
    final isSuccess = await _fetchResetPassword(password);

    const interval = Duration(seconds: 3);
    if (!isSuccess) {
      _emailPollingTimer = Timer.periodic(interval, (timer) async {
        final isSuccess = await _fetchResetPassword(password);
        if (isSuccess) {
          timer.cancel();
          Get.back();
        }
      });
    } else {
      Get.back();
    }
  }

  Future<bool> _fetchResetPassword(String password) async {
    try {
      final response = await pollResetPasswordResultApi(
        mail: email,
        code: code,
        password: md5.convert(utf8.encode(password)).toString(),
        inviteCode: '',
      );

      final result = ResponseYK<LoginInfoModel>.fromJson(
        response?.data,
        (data) => LoginInfoModel.fromJson(response?.data?['data']),
      );

      if (result.data?.token?.isNotEmpty ?? false) {
        // final userStore = Get.find<UserStoreService>();
        // await userStore.saveToken(result.data?.token ?? '');
        // final hasUser = await userStore.getUserInfo();
        // if (hasUser) {
        //   resetPasswordEnable.value = true;
        //   showSuccess('密码重置成功');
        // } else {
        //   showError('密码重置失败');
        // }
        showSuccess('密码重置成功');
        return true;
      }
    } catch (e) {
      resetPasswordEnable.value = true;
      print('密码重置轮询出错: $e');
    }
    return false;
  }

  void startSendResetEmail() async {
    try {
      final mail = emailController.text;
      if (mail.isEmpty) {
        showToast('请填写邮箱');
        return;
      }

      if (!GetUtils.isEmail(mail)) {
        showToast('请填写正确的邮箱');
        return;
      }

      resetEmailEnable.value = false;
      final response = await sendResetPasswordMailApi(mail: mail);
      final result = ResponseYK<String>.fromJson(
        response?.data,
        (data) => response?.data?['data'],
      );
      code = result.data ?? '';
      resetEmailEnable.value = true;
      if (code.isNotEmpty && code.length == 6) {
        showSuccess('发送邮件成功');
        email = mail;
        resetPasswordStep.value = 2;
      } else {
        showError(result.message ?? '发送邮件失败');
      }
    } catch (e) {
      showError('发送邮件失败');
      resetEmailEnable.value = true;
    }
  }
}
