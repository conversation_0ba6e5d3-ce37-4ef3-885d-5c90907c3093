import 'package:aitoearn_app/pages/login/reset_password/reset_password_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/input_box.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetPasswordEmailWidget extends GetView<ResetPasswordController> {
  const ResetPasswordEmailWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Gaps.vGap50,

        CustomInputBox(
          title: 'account_email'.tr,
          hintText: 'input_email'.tr,
          controller: controller.emailController,
          textInputType: TextInputType.emailAddress,
        ).marginOnly(bottom: 15),

        Gaps.vGap32,

        Obx(
              () => CustomButton(
            text: controller.resetEmailEnable.value ? 'send_reset_link'.tr : 'sending'.tr,
            gradient: AppColors.blueAndPurple,
            textColor: Colors.white,
            onPressed: () => controller.startSendResetEmail(),
            color:
            Colors.purple[900]?.withValues(alpha: 0.65) ??
                Colors.purple,
            enable: controller.resetEmailEnable.value,
            showLoading: !controller.resetEmailEnable.value,
          ),
        ),

      ],
    ).paddingSymmetric(horizontal: 20).safeArea();
  }


}