import 'package:aitoearn_app/pages/login/register_email/widgets/confirm_hint_item.dart';
import 'package:aitoearn_app/pages/login/reset_password/reset_password_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/input_box.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetPasswordResetWidget extends GetView<ResetPasswordController> {
  const ResetPasswordResetWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Gaps.vGap8,
        'verification_sent'.tr.toNormalText(
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textColor,
          ),
        ),
        controller.email.toNormalText(
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.primaryColor,
          ),
        ),
        'send_code_to_reset'.tr.toNormalText(
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textColor,
          ),
        ),

        Gaps.vGap24,

        CustomInputBox(
          title: 'new_password'.tr,
          titleStyle: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            decoration: TextDecoration.none,
          ),
          hintText: 'input_password'.tr,
          controller: controller.passwordController,
          obscureText: true,
          textInputType: TextInputType.visiblePassword,
        ).marginOnly(bottom: 10),

        Obx(
              () => ConfirmHintItem(
            text: 'password_at_least_8'.tr,
            isConfirmed: controller.passwordLength.value,
          ),
        ).marginOnly(bottom: 2),

        Obx(
              () => ConfirmHintItem(
            text: 'password_uppercase_required'.tr,
            isConfirmed: controller.passwordContainsCaps.value,
          ),
        ).marginOnly(bottom: 2),

        Obx(
              () => ConfirmHintItem(
            text: 'password_number_or_symbol'.tr,
            isConfirmed: controller.passwordContainsNumberOrSign.value,
          ),
        ).marginOnly(bottom: 2),

        Gaps.vGap10,

        CustomInputBox(
          title: 'confirm_password'.tr,
          titleStyle: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            decoration: TextDecoration.none,
          ),
          hintText: 'input_password'.tr,
          controller: controller.passwordConformController,
          obscureText: true,
          textInputType: TextInputType.visiblePassword,
        ).marginOnly(bottom: 10),

        Gaps.vGap24,

        Obx(
              () => CustomButton(
            text: controller.resetPasswordEnable.value ? 'confirm_reset'.tr : 'waiting_for_verification'.tr,
            gradient: AppColors.blueAndPurple,
            textColor: Colors.white,
            onPressed: () => controller.resetPassword(),
            color:
            Colors.purple[900]?.withValues(alpha: 0.65) ??
                Colors.purple,
            enable: controller.resetPasswordEnable.value,
            showLoading: !controller.resetPasswordEnable.value,
          ),
        ),

      ],
    ).paddingSymmetric(horizontal: 20).safeArea();
  }


}