class LoginInfoModel {
  final String? type;
  final String? code;
  final String? token;
  final Map? userInfo;

  LoginInfoModel({
    this.type,
    this.code,
    this.token,
    this.userInfo,
  });

  factory LoginInfoModel.fromJson(Map<String, dynamic> json) {
    return LoginInfoModel(
      type: json['type'] ?? '',
      code: json['code'] ?? '',
      token: json['token'] ?? '',
      userInfo: json['userInfo'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'code': code,
      'token': token,
      'userInfo': userInfo,
    };
  }
}