import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/res/google_signIn_config.dart';
import 'package:google_sign_in/google_sign_in.dart' as mobile;
import 'package:google_sign_in_all_platforms/google_sign_in_all_platforms.dart'
    as desktop;


/// 谷歌登录服务
class GoogleSignInService {
  static final GoogleSignInService _instance = GoogleSignInService._internal();

  factory GoogleSignInService() => _instance;

  GoogleSignInService._internal();

  mobile.GoogleSignIn? _mobileSignIn;
  desktop.GoogleSignIn? _desktopSignIn;

  Future<String?> signIn() async {
    final (clientId, secretOrServerId) =
        GoogleSignInConfig.getGoogleSignInParams();

    try {
      if (Platform.isAndroid || Platform.isIOS) {
        _mobileSignIn ??= mobile.GoogleSignIn(
          clientId: clientId,
          serverClientId: secretOrServerId,
        );
        await _mobileSignIn!.signOut();
        final account = await _mobileSignIn!.signIn();
        return (await account?.authentication)?.idToken;
      } else {
        _desktopSignIn ??= desktop.GoogleSignIn(
          params: desktop.GoogleSignInParams(
            clientId: clientId,
            clientSecret: secretOrServerId,
            redirectPort: 7007,
            customPostAuthPage: '''
      <!DOCTYPE html>
      <html>
        <head>
          <title>登录成功</title>
          <style>
            body { font-family: sans-serif; text-align: center; margin-top: 50px; }
            .box { display: inline-block; padding: 20px; border: 1px solid #ccc; border-radius: 8px; }
          </style>
        </head>
        <body>
          <div class="box">
            <h2>哎呦赚 - 登录成功 🎉</h2>
            <p>你可以关闭此页面并回到应用。</p>
          </div>
        </body>
      </html>
    ''',
          ),
        );
        await _desktopSignIn!.signOut();
        final account = await _desktopSignIn!.signIn();
        return account?.idToken;
      }
    } catch (e) {
      LoggerUtil.e('Google SignIn failed $e');
      return null;
    }
  }
}
