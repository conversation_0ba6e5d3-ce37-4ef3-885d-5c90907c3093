// 优化点：
// 1. 抽出统一 GoogleSignInService 供 Controller 调用
// 2. 明确变量命名，便于阅读
// 3. 避免重复初始化 googleSignIn 实例
// 4. 加入错误日志

import 'dart:convert';
import 'dart:io';

import 'package:aitoearn_app/api/login/login_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/network/response_yk.dart';
import 'package:aitoearn_app/pages/login/bean/login_info.dart';
import 'package:aitoearn_app/pages/login/login_email/google_sign_service.dart';
import 'package:aitoearn_app/pages/login/register_email/register_email_controller.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/google_signIn_config.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/dialog/dialog_helper.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginEmailController extends GetxController {
  final String title = '欢迎来到哎呦赚';
  final String subtitle = '请使用邮箱号登陆';

  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  final agreementChecked = false.obs;
  final loginEnable = true.obs;

  Future<void> startGoogleLogin() async {
    if (!agreementChecked.value) {
      await _showAgreementDialog();
      if (!agreementChecked.value) {
        return;
      }
    }

    final cancel = showProgress(cancelable: false, cancelButton: '取消');
    try {
      final idToken = await GoogleSignInService().signIn();
      if (idToken == null || idToken.isEmpty) {
        cancel();
        showError('Google登录失败');
        return;
      }
      LoggerUtil.i('Google登录成功，ID Token: $idToken');

      final (clientId, _) = GoogleSignInConfig.getGoogleSignInParams();
      final apiClientId =
          Platform.isAndroid || Platform.isIOS
              ? GoogleSignInConfig.webServerClientId
              : clientId;

      final response = await googleLoginApi(
        clientId: apiClientId,
        credential: idToken,
      );

      final result = ResponseYK<LoginInfoModel>.fromJson(
        response?.data,
        (data) => LoginInfoModel.fromJson(response?.data?['data']),
      );

      if (result.data?.type == 'login' &&
          (result.data?.token?.isNotEmpty ?? false)) {
        final userStore = Get.find<UserStoreService>();
        await userStore.saveToken(result.data?.token ?? '');
        final hasUser = await userStore.getUserInfo();
        if (hasUser) {
          cancel();
          Get.offAllNamed(AppRouter.homePath);
          return;
        }
      }

      cancel();
      showError(result.message ?? '登录失败');
    } catch (e) {
      cancel();
      LoggerUtil.e('Google登录异常 $e');
      showToast('Google登录失败');
    }
  }

  Future<void> startEmailLogin() async {
    final email = emailController.text;
    final password = passwordController.text;

    if (!agreementChecked.value) {
      await _showAgreementDialog();
      if (!agreementChecked.value) return;
    }
    if (email.isEmpty || password.isEmpty) {
      showToast('请填写邮箱和密码');
      return;
    }
    if (!GetUtils.isEmail(email)) {
      showToast('请填写正确的邮箱');
      return;
    }

    loginEnable.value = false;
    try {
      final response = await loginOrRegisterByEmailApi(
        mail: email,
        password: md5.convert(utf8.encode(password)).toString(),
      );

      final result = ResponseYK<LoginInfoModel>.fromJson(
        response?.data,
        (data) => LoginInfoModel.fromJson(response?.data?['data']),
      );

      if (result.data?.type == 'regist') {
        loginEnable.value = true;
        await Get.toNamed(
          AppRouter.registerEmailPath,
          arguments: {
            'email': email,
            'password': password,
            'code': result.data?.code,
          },
        );
        Get.delete<RegisterEmailController>();
      } else if (result.data?.type == 'login' &&
          (result.data?.token?.isNotEmpty ?? false)) {
        final userStore = Get.find<UserStoreService>();
        await userStore.saveToken(result.data?.token ?? '');
        final hasUser = await userStore.getUserInfo();
        if (hasUser) {
          loginEnable.value = true;
          Get.offAllNamed(AppRouter.homePath);
        }
      } else {
        showError(result.message ?? '登录失败');
        loginEnable.value = true;
      }
    } catch (e) {
      showError('网络错误');
      loginEnable.value = true;
      LoggerUtil.e('邮箱登录异常 $e');
    }
  }

  void toResetPassword() => Get.toNamed(AppRouter.resetPasswordPath);

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  // 显示协议确认弹窗
  Future<void> _showAgreementDialog() async {
    await DialogHelper.showAlertDialog(
      title: '请阅读并同意协议',
      contentWidget: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'agree_prefix'.tr,
                  style: const TextStyle(color: AppColors.textColor),
                ),
                TextSpan(
                  text: 'terms_of_service'.tr,
                  style: const TextStyle(
                    color: AppColors.primaryHighLightColor,
                  ),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          Get.toNamed(
                            AppRouter.webPath,
                            arguments: {
                              'url': '/en/websit/terms-of-service',
                              'title': 'setting.terms'.tr,
                            },
                          );
                        },
                ),
                TextSpan(
                  text: 'privacy_and'.tr,
                  style: const TextStyle(color: AppColors.textColor),
                ),
                TextSpan(
                  text: 'privacy_policy'.tr,
                  style: const TextStyle(
                    color: AppColors.primaryHighLightColor,
                  ),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          Get.toNamed(
                            AppRouter.webPath,
                            arguments: {
                              'url': '/en/websit/privacy-policy',
                              'title': 'setting.privacy'.tr,
                            },
                          );
                        },
                ),
              ],
            ),
          ),
        ],
      ),
      confirmText: '同意',
      onConfirm: () {
        agreementChecked.value = true;
        Get.back();
      },
      cancelText: '取消',
      onCancel: () {
        showToast('请先勾选同意协议');
        Get.back();
      },
      barrierDismissible: false,
    );
  }
}
