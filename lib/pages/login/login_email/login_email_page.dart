import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/login/login_email/login_email_controller.dart';
import 'package:aitoearn_app/pages/login/register_email/widgets/agreement_checkbox.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/widgets/custom_button.dart';
import 'package:aitoearn_app/widgets/input_box.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginEmailPage extends GetView<LoginEmailController> {
  const LoginEmailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      useSafeArea: false,
      body: ResponsiveLayout(
        mobile: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [...getAppInfoWidget(), ...getLoginWidget()],
          ).paddingSymmetric(horizontal: 20, vertical: 50),
        ),
        desktop: Row(
          children: [
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [...getAppInfoWidget()],
              ),
            ),
            Expanded(
              flex: 3,
              child: Container(
                alignment: Alignment.center,
                child: SizedBox(
                  width: 350,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [...getLoginWidget()],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  getAppInfoWidget() {
    return [
      Image.asset('assets/images/logo.png', width: 50, height: 50)
          .paddingAll(5)
          .decorated(
            gradient: AppColors.blueAndPurple,
            borderRadius: BorderRadius.circular(15),
          )
          .marginOnly(top: 10, bottom: 15),

      'Hello!'.toNormalText(
        fontWeight: FontWeight.w600,
        fontSize: 24,
        color: Colors.black,
      ),
      'welcome_title'.tr
          .toNormalText(
            fontWeight: FontWeight.w600,
            fontSize: 24,
            color: Colors.black,
          )
          .marginOnly(bottom: 30),
    ];
  }

  getLoginWidget() {
    return [
      CustomInputBox(
        hintText: 'input_email'.tr,
        controller: controller.emailController,
        textInputType: TextInputType.emailAddress,
      ).marginOnly(bottom: 15),

      CustomInputBox(
        hintText: 'input_password'.tr,
        controller: controller.passwordController,
        textInputType: TextInputType.visiblePassword,
        obscureText: true,
        textInputAction: TextInputAction.done, // 设置为“完成”
        onSubmitted: (_) => controller.startEmailLogin(), // 按回车触发登录
      ),

      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const SizedBox(),
          MouseRegion(
            cursor: SystemMouseCursors.click, // 设置为点击样式（手指）
            child: 'forgot_password'.tr
                .toNormalText(color: AppColors.textHintColor, fontSize: 13)
                .marginSymmetric(vertical: 10)
                .padding(all: 5)
                .gestures(onTap: controller.toResetPassword),
          ),
        ],
      ),

      Obx(
        () => CustomButton(
          text: controller.loginEnable.value ? 'login_or_register'.tr : 'waiting_for_verification'.tr,
          gradient: AppColors.blueAndPurple,
          textColor: Colors.white,
          onPressed: controller.startEmailLogin,
          enable: controller.loginEnable.value,
          showLoading: !controller.loginEnable.value,
        ),
      ).marginOnly(top: 15),

      AgreementCheckbox(
        agreementChecked: controller.agreementChecked,
        openServiceAgreement: () {
          Get.toNamed(AppRouter.webPath, arguments: {
            'url': '/en/websit/terms-of-service',
            'title': 'setting.terms'.tr,
          });
        },
        openPrivacyPolicy: () {
          Get.toNamed(AppRouter.webPath, arguments: {
            'url': '/en/websit/privacy-policy',
            'title': 'setting.privacy'.tr,
          });
        },
      ).marginOnly(bottom: 40, top: 10),

      Center(
        child: 'third_party_login'.tr.toNormalText(
          fontSize: 11,
          color: AppColors.textHintColor,
        ),
      ),

      CustomButton(
        text: 'login_with_google'.tr,
        color: const Color(0xFFF0E9FF),
        textColor: AppColors.primaryHighLightColor,
        onPressed: () => controller.startGoogleLogin(),
      ).marginOnly(top: 10),
    ];
  }
}
