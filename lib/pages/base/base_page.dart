import 'package:aitoearn_app/pages/base/nav_back_button.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class BasePage extends StatelessWidget {
  final Widget body;
  final String? title;
  final Widget? titleWidget;
  final bool centerTitle;
  final TextStyle? titleTextStyle;
  final Widget? action;
  final Widget? leading;
  final VoidCallback? backFuc;

  final bool useSafeArea;
  final bool resizeToAvoidBottomInset;
  final bool showBackBtnWhenNoLeading;

  final Color backgroundColor;

  final Widget? drawer;

  final Widget? backgroundWidget; // 自定义背景
  final bool useDefaultBackground; // 是否使用默认背景
  final Brightness statusBarIconBrightness; // 状态栏图标颜色

  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final FloatingActionButtonAnimator? floatingActionButtonAnimator;

  const BasePage({
    required this.body,
    super.key,
    this.title,
    this.titleWidget,
    this.centerTitle = true,
    this.titleTextStyle,
    this.action,
    this.leading,
    this.backFuc,
    this.useSafeArea = true,
    this.resizeToAvoidBottomInset = true,
    this.showBackBtnWhenNoLeading = true,
    this.backgroundColor = AppColors.backgroundColor,
    this.drawer,
    this.backgroundWidget,
    this.useDefaultBackground = true,
    this.statusBarIconBrightness = Brightness.dark,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.floatingActionButtonAnimator,
  });

  @override
  Widget build(BuildContext context) {
    final showBackground = backgroundWidget != null || useDefaultBackground;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: statusBarIconBrightness,
        statusBarBrightness:
            statusBarIconBrightness == Brightness.light
                ? Brightness.dark
                : Brightness.light,
      ),
      child: Stack(
        children: [
          if (backgroundWidget != null)
            Positioned.fill(child: backgroundWidget!)
          else if (useDefaultBackground)
            Positioned.fill(child: _defaultBackgroundWidget()),

          Scaffold(
            backgroundColor:
                showBackground ? Colors.transparent : backgroundColor,
            resizeToAvoidBottomInset: resizeToAvoidBottomInset,
            drawer: drawer,
            appBar: _buildAppBar(),
            floatingActionButton: floatingActionButton,
            floatingActionButtonLocation: floatingActionButtonLocation,
            floatingActionButtonAnimator: floatingActionButtonAnimator,
            body: PopScope<Object?>(
              canPop: backFuc == null, // 有回调则禁止默认 pop
              onPopInvokedWithResult: (bool didPop, Object? result) {
                if (!didPop && backFuc != null) {
                  backFuc!(); // 拦截后执行自定义逻辑
                }
              },
              child: useSafeArea ? SafeArea(child: body) : body,
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget? _buildAppBar() {
    if (title == null &&
        titleWidget == null &&
        leading == null &&
        action == null) {
      return null;
    }

    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: statusBarIconBrightness,
        statusBarBrightness:
            statusBarIconBrightness == Brightness.light
                ? Brightness.dark
                : Brightness.light,
      ),
      titleSpacing: 0,
      leading: leading ?? _buildBackBtn(),
      title:
          titleWidget ??
          title?.toNormalText(fontSize: 18, fontWeight: FontWeight.w500),
      actions: [action ?? const SizedBox()],
      centerTitle: centerTitle,
      titleTextStyle: titleTextStyle,
      automaticallyImplyLeading: false,
    );
  }

  Widget? _buildBackBtn() {
    return showBackBtnWhenNoLeading
        ? NavBackButton(
          onPressed: () {
            if (backFuc == null) {
              Get.back();
              return;
            }
            backFuc?.call();
          },
        )
        : null;
  }

  /// 默认顶部渐变 + 背景图
  Widget _defaultBackgroundWidget() {
    return Container(
      color: backgroundColor,
      child: Column(
        children: [
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryColor.withAlpha(100),
                  AppColors.primaryColor.withAlpha(50),
                  AppColors.primaryColor.withAlpha(0),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Image.asset(
              'assets/images/login/login_bg.png',
              fit: BoxFit.fill,
            ),
          ),
        ],
      ),
    );
  }
}
