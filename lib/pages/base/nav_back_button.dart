import 'package:aitoearn_app/res/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class NavBackButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Color color;

  const NavBackButton({
    required this.onPressed,
    super.key,
    this.color = AppColors.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 40,
      height: 40,
      child: Material(
        color: Colors.transparent, // 背景透明
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(100), // 点击时水波纹的圆角
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: SvgPicture.asset(
              'assets/svg/back_icon.svg',
              colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
            ),
          ),
        ),
      ),
    ).marginAll(6);
  }
}
