import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/draft_models/draft_model.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_detail_page.dart';
import 'package:aitoearn_app/store/draft/draft_store_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DraftBoxesPage extends StatefulWidget {
  const DraftBoxesPage({Key? key}) : super(key: key);

  @override
  State<DraftBoxesPage> createState() => _DraftBoxesPageState();
}

class _DraftBoxesPageState extends State<DraftBoxesPage> with SingleTickerProviderStateMixin {
  // 草稿箱服务
  late final DraftStoreService _draftStoreService;
  
  // 草稿箱列表
  late final RxList<DraftBox> draftBoxes;
  
  // 新草稿箱名称控制器
  final TextEditingController _newBoxNameController = TextEditingController();
  
  // 标签控制器
  late TabController _tabController;
  
  // 当前选中的标签索引
  final RxInt _currentTabIndex = 0.obs;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化标签控制器
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      _currentTabIndex.value = _tabController.index;
    });
    
    // 获取草稿箱服务
    _draftStoreService = Get.find<DraftStoreService>();
    
    // 获取草稿箱列表
    draftBoxes = _draftStoreService.draftBoxes;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次依赖变化时刷新数据
    _refreshData();
  }
  
  // 刷新数据
  void _refreshData() {
    LoggerUtil.i('刷新草稿箱数据');
    // 强制刷新草稿箱列表
    draftBoxes.refresh();
  }

  @override
  void dispose() {
    _newBoxNameController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text('草稿箱'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          indicatorSize: TabBarIndicatorSize.label,
          tabs: const [
            Tab(text: '图片库'),
            Tab(text: '草稿箱'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // 图片库标签页
          const Center(
            child: Text('图片库功能开发中'),
          ),
          
          // 草稿箱标签页
          _buildDraftBoxesTab(),
        ],
      ),
    );
  }
  
  // 构建草稿箱标签页
  Widget _buildDraftBoxesTab() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: CustomScrollView(
          slivers: [
            // 创建草稿箱按钮
            SliverToBoxAdapter(
              child: _buildCreateDraftBoxButton(),
            ),
            
            SliverToBoxAdapter(
              child: const SizedBox(height: 16),
            ),
            
            // 草稿箱网格列表
            Obx(() {
              if (draftBoxes.isEmpty) {
                return const SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.only(top: 32.0),
                      child: Text('暂无草稿箱，请先创建'),
                    ),
                  ),
                );
              }
              
              return SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final box = draftBoxes[index];
                    return _buildDraftBoxCard(box);
                  },
                  childCount: draftBoxes.length,

                ),
              );
            }),
          ],
        ),
      ),
    );
  }
  
  // 创建草稿箱按钮
  Widget _buildCreateDraftBoxButton() {
    return InkWell(
      onTap: () {
        _showCreateDraftBoxDialog();
      },
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_circle_outline, color: Colors.blue[400], size: 24),
            const SizedBox(width: 8),
            Text(
              '创建草稿箱',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.purple[400],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建草稿箱卡片
  Widget _buildDraftBoxCard(DraftBox box) {
    // 每次构建时重新获取草稿缩略图，避免缓存问题
    final List<String?> thumbnails = _getDraftThumbnails(box.drafts);
    
    return InkWell(
      onTap: () {
        _openDraftBox(box);
      },
      onLongPress: () {
        // 长按显示操作菜单
        _showDraftBoxOptions(box);
      },
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 缩略图区域
                ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                  child: Container(
                    height: 120, // 固定高度
                    width: double.infinity,
                    color: Colors.grey[200], // 背景色
                    child: _buildThumbnailGrid(thumbnails),
                  ),
                ),
                
                // 草稿箱名称和数量
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            box.name,
                            style: const TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            '(${box.drafts.length})',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const Spacer(),
                          if (box.isDefault)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                                vertical: 1,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue[100],
                                borderRadius: BorderRadius.circular(2),
                              ),
                              child: Text(
                                '默认',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue[800],
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 2),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 添加更多操作按钮
          // Positioned(
          //   top: 4,
          //   right: 4,
          //   child: GestureDetector(
          //     onTap: () => _showDraftBoxOptions(box),
          //     child: Container(
          //       padding: const EdgeInsets.all(4),
          //       decoration: BoxDecoration(
          //         color: Colors.black.withOpacity(0.5),
          //         shape: BoxShape.circle,
          //       ),
          //       child: const Icon(
          //         Icons.more_horiz,
          //         color: Colors.white,
          //         size: 16,
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
  
  // 构建缩略图网格
  Widget _buildThumbnailGrid(List<String?> thumbnails) {
    // 如果没有缩略图，显示默认图标
    if (thumbnails.isEmpty) {
      LoggerUtil.i('没有缩略图，显示默认图标');
      return Container(
        color: Colors.grey[200],
        child: Center(
          child: Icon(
            Icons.folder,
            size: 48,
            color: Colors.grey[400],
          ),
        ),
      );
    }

    LoggerUtil.i('构建缩略图网格，缩略图数量: ${thumbnails.length}');
    
    // 根据缩略图数量决定布局
    if (thumbnails.length == 1) {
      LoggerUtil.i('单张图片布局');
      return _buildThumbnailItem(thumbnails[0], isFullSize: true);
    } else if (thumbnails.length == 2) {
      LoggerUtil.i('两张图片布局');
      // 两张图片并排显示
      return Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(child: _buildThumbnailItem(thumbnails[0])),
          const SizedBox(width: 1), // 添加1像素间隔
          Expanded(child: _buildThumbnailItem(thumbnails[1])),
        ],
      );
    } else if (thumbnails.length == 3) {
      LoggerUtil.i('三张图片布局');
      // 三张图片：左侧一张大图，右侧两张小图上下排列
      return Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 左侧大图
          Expanded(
            flex: 3,
            child: _buildThumbnailItem(thumbnails[0]),
          ),
          const SizedBox(width: 1), // 添加1像素间隔
          // 右侧两张小图
          Expanded(
            flex: 2,
            child: Column(
              children: [
                Expanded(child: _buildThumbnailItem(thumbnails[1])),
                const SizedBox(height: 1), // 添加1像素间隔
                Expanded(child: _buildThumbnailItem(thumbnails[2])),
              ],
            ),
          ),
        ],
      );
    } else {
      LoggerUtil.i('四张或更多图片布局');
      // 四张或更多图片：2x2网格布局
      return LayoutBuilder(
        builder: (context, constraints) {
          return Column(
            children: [
              // 第一行
              Expanded(
                child: Row(
                  children: [
                    Expanded(child: _buildThumbnailItem(thumbnails[0])),
                    const SizedBox(width: 1), // 添加1像素间隔
                    Expanded(child: _buildThumbnailItem(thumbnails[1])),
                  ],
                ),
              ),
              const SizedBox(height: 1), // 添加1像素间隔
              // 第二行
              Expanded(
                child: Row(
                  children: [
                    Expanded(child: _buildThumbnailItem(thumbnails[2])),
                    const SizedBox(width: 1), // 添加1像素间隔
                    Expanded(
                      child: thumbnails.length > 3
                          ? _buildThumbnailItem(thumbnails[3])
                          : Container(color: Colors.grey[200]),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      );
    }
  }
  
  // 构建单个缩略图项
  Widget _buildThumbnailItem(String? path, {bool isFullSize = false}) {
    if (path == null || path.isEmpty) {
      LoggerUtil.i('路径为空，显示灰色占位符');
      return Container(color: Colors.grey[200]);
    }

    LoggerUtil.i('构建缩略图项: $path');
    final file = File(path);
    if (!file.existsSync()) {
      LoggerUtil.i('文件不存在: $path');
      return Container(
        color: Colors.grey[200],
        child: Center(
          child: Icon(
            Icons.broken_image,
            color: Colors.grey[400],
          ),
        ),
      );
    }

    LoggerUtil.i('文件存在，显示图片');
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
      ),
      clipBehavior: Clip.hardEdge,
      child: FittedBox(
        fit: BoxFit.cover,
        clipBehavior: Clip.hardEdge,
        child: Image.file(
          file,
          fit: BoxFit.cover,
          cacheWidth: isFullSize ? 300 : 150, // 添加缓存宽度以提高性能
          errorBuilder: (context, error, stackTrace) {
            LoggerUtil.i('图片加载错误: $error');
            return Center(
              child: Icon(
                Icons.broken_image,
                color: Colors.grey[400],
              ),
            );
          },
        ),
      ),
    );
  }
  
  // 获取草稿缩略图
  List<String?> _getDraftThumbnails(List<DraftItem> drafts) {
    List<String?> thumbnails = [];

    LoggerUtil.i('获取草稿缩略图，草稿数量: ${drafts.length}');
    
    for (var draft in drafts) {
      // 优先使用视频缩略图
      if (draft.thumbnailPath != null && draft.thumbnailPath!.isNotEmpty) {
        LoggerUtil.i('添加视频缩略图: ${draft.thumbnailPath}');
        thumbnails.add(draft.thumbnailPath);
      }
      // 其次使用图片，添加所有图片而不仅仅是第一张
      else if (draft.imagePaths != null && draft.imagePaths!.isNotEmpty) {
        LoggerUtil.i('添加图片，数量: ${draft.imagePaths!.length}');
        for (var imagePath in draft.imagePaths!) {
          LoggerUtil.i('图片路径: $imagePath');
          // 检查文件是否存在
          final file = File(imagePath);
          if (file.existsSync()) {
            LoggerUtil.i('图片文件存在');
            thumbnails.add(imagePath);
          } else {
            LoggerUtil.i('图片文件不存在: $imagePath');
          }
        }
      }
      
      // 最多获取4个缩略图
      if (thumbnails.length >= 4) {
        print('缩略图数量已达到4个，截断');
        thumbnails = thumbnails.sublist(0, 4);
        break;
      }
    }
    
    print('最终获取的缩略图数量: ${thumbnails.length}');
    return thumbnails;
  }

  // 打开草稿箱
  void _openDraftBox(DraftBox box) async {
    // 使用 Get.to 跳转到草稿详情页
    await Get.to(
      () => DraftDetailPage(draftBox: box),
      transition: Transition.rightToLeft,
    );
    
    // 页面返回后刷新数据
    print('从草稿详情页返回，刷新数据');
    _refreshData();
  }

  // 显示草稿箱选项菜单
  void _showDraftBoxOptions(DraftBox box) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('重命名'),
                onTap: () {
                  Navigator.pop(context);
                  _showRenameDraftBoxDialog(box);
                },
              ),
              ListTile(
                leading: const Icon(Icons.star),
                title: const Text('设为默认'),
                onTap: () {
                  Navigator.pop(context);
                  _setDefaultDraftBox(box);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('删除草稿箱'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteDraftBoxDialog(box);
                },
              ),
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  // 显示创建草稿箱对话框
  void _showCreateDraftBoxDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('创建草稿箱'),
          content: TextField(
            controller: _newBoxNameController,
            decoration: const InputDecoration(
              hintText: '输入草稿箱名称',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _newBoxNameController.clear();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                final name = _newBoxNameController.text.trim();
                
                if (name.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('请输入草稿箱名称')),
                  );
                  return;
                }
                
                // 创建新草稿箱
                await _draftStoreService.createDraftBox(
                  name: name,
                  description: '自定义草稿箱',
                );
                
                // 关闭对话框
                Navigator.pop(context);
                _newBoxNameController.clear();
              },
              child: const Text('创建'),
            ),
          ],
        );
      },
    );
  }

  // 显示重命名草稿箱对话框
  void _showRenameDraftBoxDialog(DraftBox box) {
    _newBoxNameController.text = box.name;
    
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('重命名草稿箱'),
          content: TextField(
            controller: _newBoxNameController,
            decoration: const InputDecoration(
              hintText: '输入新名称',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _newBoxNameController.clear();
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                final name = _newBoxNameController.text.trim();
                
                if (name.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('请输入草稿箱名称')),
                  );
                  return;
                }
                
                // 更新草稿箱
                final updatedBox = box.copyWith(name: name);
                await _draftStoreService.updateDraftBox(updatedBox);
                
                // 关闭对话框
                Navigator.pop(context);
                _newBoxNameController.clear();
              },
              child: const Text('保存'),
            ),
          ],
        );
      },
    );
  }

  // 设置默认草稿箱
  void _setDefaultDraftBox(DraftBox box) async {
    await _draftStoreService.setDefaultDraftBox(box.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已将"${box.name}"设为默认草稿箱')),
    );
  }

  // 显示删除草稿箱对话框
  void _showDeleteDraftBoxDialog(DraftBox box) {
    // 检查是否是默认草稿箱
    if (box.isDefault) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('默认草稿箱不能删除'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('删除草稿箱'),
          content: Text('确定要删除"${box.name}"吗？此操作将删除其中的所有草稿，且无法恢复。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                // 删除草稿箱
                final success = await _draftStoreService.deleteDraftBox(box.id);
                
                // 关闭对话框
                Navigator.pop(context);
                
                // 根据删除结果显示不同提示
                if (success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('草稿箱已删除')),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('删除草稿箱失败'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  // 获取草稿箱颜色
  Color _getBoxColor(String? colorStr) {
    if (colorStr == null || colorStr.isEmpty) {
      return Colors.blue;
    }
    
    try {
      return Color(int.parse(colorStr.replaceAll('#', '0xFF')));
    } catch (e) {
      return Colors.blue;
    }
  }

  // 获取草稿箱图标
  IconData _getBoxIcon(String? iconStr) {
    if (iconStr == null || iconStr.isEmpty) {
      return Icons.folder;
    }
    
    switch (iconStr) {
      case 'folder':
        return Icons.folder;
      case 'description':
        return Icons.description;
      case 'videocam':
        return Icons.videocam;
      case 'image':
        return Icons.image;
      case 'favorite':
        return Icons.favorite;
      case 'star':
        return Icons.star;
      default:
        return Icons.folder;
    }
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return '刚刚';
        }
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }
}