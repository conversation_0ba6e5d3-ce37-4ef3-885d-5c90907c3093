import 'package:aitoearn_app/config/app_config.dart';

class DraftInfoModel {
  final String id;
  final String userId;
  final String groupId;
  final String type;
  final String coverUrl;
  final List<DraftMediaItem> mediaList;
  final String title;
  final String desc;
  final List<int> location;
  final int status;
  final String message;

  DraftInfoModel({
    required this.id,
    required this.userId,
    required this.groupId,
    required this.type,
    required this.coverUrl,
    required this.mediaList,
    required this.title,
    required this.desc,
    required this.location,
    required this.status,
    required this.message,
  });

  factory DraftInfoModel.fromJson(Map<String, dynamic> json) {
    return DraftInfoModel(
      id: json['_id'] ?? '',
      userId: json['userId'] ?? '',
      groupId: json['groupId'] ?? '',
      type: json['type'] ?? '',
      coverUrl: json['coverUrl'],
      mediaList: (json['mediaList'] as List<dynamic>?)
          ?.map((e) => DraftMediaItem.fromJson(e))
          .toList() ??
          [],
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      location: (json['location'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList() ??
          [],
      status: json['status'] ?? 0,
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    '_id': id,
    'userId': userId,
    'groupId': groupId,
    'type': type,
    'coverUrl': coverUrl,
    'mediaList': mediaList.map((e) => e.toJson()).toList(),
    'title': title,
    'desc': desc,
    'location': location,
    'status': status,
    'message': message,
  };
}


class DraftMediaItem {
  final String id;
  final String url;
  final String type;
  final String content;

  DraftMediaItem({
    required this.id,
    required this.url,
    required this.type,
    required this.content,
  });

  factory DraftMediaItem.fromJson(Map<String, dynamic> json) {
    return DraftMediaItem(
      id: json['_id'] ?? '',
      url: json['url'],
      type: json['type'] ?? '',
      content: json['content'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    '_id': id,
    'url': url,
    'type': type,
    'content': content,
  };
}
