
import 'package:aitoearn_app/pages/draft_and_media/model/media_info.dart';

class MediaGroupInfoModel {
  final String id;
  final String userId;
  final String type;
  final String title;
  final String desc;
  final List<MediaInfoModel> mediaList;
  final int mediaTotal;

  MediaGroupInfoModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.title,
    required this.desc,
    required this.mediaList,
    required this.mediaTotal,
  });

  factory MediaGroupInfoModel.fromJson(Map<String, dynamic> json) {
    final mediaData = json['mediaList'] ?? {};
    final mediaList = (mediaData['list'] as List<dynamic>?)
        ?.map((e) => MediaInfoModel.fromJson(e))
        .toList() ??
        [];

    final total = mediaData['total'] ?? mediaList.length;

    final id = json['_id'] ?? '';
    final type = json['type'] ?? '';
    String title = '';
    if (id == '') {
      title = type == 'video' ? '默认视频库' : '默认图片库';
    } else {
      title = json['title'] ?? '';
    }

    return MediaGroupInfoModel(
      id: id,
      userId: json['userId'] ?? '',
      type: type,
      title: title,
      desc: json['desc'] ?? '',
      mediaList: mediaList,
      mediaTotal: total,
    );
  }

  Map<String, dynamic> toJson() => {
    '_id': id,
    'userId': userId,
    'type': type,
    'title': title,
    'desc': desc,
    'mediaList': {
      'total': mediaTotal,
      'list': mediaList.map((e) => e.toJson()).toList(),
    }
  };
}