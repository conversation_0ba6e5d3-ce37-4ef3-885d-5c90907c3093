import 'package:aitoearn_app/config/app_config.dart';

class MediaInfoModel {
  final String id;
  final String userId;
  final String groupId;
  final String type;
  final String url;
  final String title;
  final String desc;

  MediaInfoModel({
    required this.id,
    required this.userId,
    required this.groupId,
    required this.type,
    required this.url,
    required this.title,
    required this.desc,
  });

  factory MediaInfoModel.fromJson(Map<String, dynamic> json) {
    return MediaInfoModel(
      id: json['_id'] ?? '',
      userId: json['userId'] ?? '',
      groupId: json['groupId'] ?? '',
      type: json['type'] ?? '',
      url: json['url'],
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    '_id': id,
    'userId': userId,
    'groupId': groupId,
    'type': type,
    'url': url,
    'title': title,
    'desc': desc,
  };
}