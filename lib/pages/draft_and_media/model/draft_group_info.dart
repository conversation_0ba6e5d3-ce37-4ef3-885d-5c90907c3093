class DraftGroupInfoModel {
  final String id;
  final String userId;
  final String name;
  final String type;

  DraftGroupInfoModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.type,
  });

  factory DraftGroupInfoModel.fromJson(Map<String, dynamic> json) {
    final id = json['_id'] ?? '';
    String name = '';
    if (id == '') {
      name = '默认草稿库';
    } else {
      name = json['name'] ?? '';
    }
    return DraftGroupInfoModel(
      id: id,
      userId: json['userId'] ?? '',
      name: name,
      type: json['type'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    '_id': id,
    'userId': userId,
    'name': name,
    'type': type,
  };
}
