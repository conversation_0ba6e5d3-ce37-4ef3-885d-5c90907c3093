import 'dart:io';

import 'package:aitoearn_app/models/draft_models/draft_model.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_create_page.dart';
import 'package:aitoearn_app/pages/publish/video_publish_page.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/draft/draft_store_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 草稿箱详情页面
class DraftDetailPage extends StatefulWidget {
  final DraftBox draftBox;

  const DraftDetailPage({Key? key, required this.draftBox}) : super(key: key);

  @override
  State<DraftDetailPage> createState() => _DraftDetailPageState();
}

class _DraftDetailPageState extends State<DraftDetailPage> {
  // 草稿箱服务
  late final DraftStoreService _draftStoreService;
  
  // 草稿列表
  late final RxList<DraftItem> drafts;
  
  // 是否处于选择模式
  final RxBool isSelectionMode = false.obs;
  
  // 选中的草稿IDs
  final RxSet<String> selectedDraftIds = <String>{}.obs;
  
  @override
  void initState() {
    super.initState();
    
    // 获取草稿箱服务
    _draftStoreService = Get.find<DraftStoreService>();
    
    // 获取草稿列表
    drafts = RxList<DraftItem>.from(widget.draftBox.drafts);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.draftBox.name),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        actions: [
          // 选择模式下显示取消按钮
          Obx(() {
            if (isSelectionMode.value) {
              return TextButton(
                onPressed: () {
                  isSelectionMode.value = false;
                  selectedDraftIds.clear();
                },
                child: const Text('取消'),
              );
            }
            
            return IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () {
                _showMoreOptions();
              },
            );
          }),
        ],
      ),
      body: Column(
        children: [
          // 草稿箱信息
          // _buildDraftBoxInfo(),
          
          // 草稿列表
          Expanded(
            child: Obx(() {
              if (drafts.isEmpty) {
                return const Center(
                  child: Text('暂无草稿，点击右下角按钮创建'),
                );
              }
              
              return ListView.builder(
                itemCount: drafts.length,
                padding: const EdgeInsets.all(16),
                itemBuilder: (context, index) {
                  final draft = drafts[index];
                  return _buildDraftCard(draft);
                },
              );
            }),
          ),
        ],
      ),
      // 选择模式下显示底部操作栏
      bottomNavigationBar: Obx(() {
        if (isSelectionMode.value && selectedDraftIds.isNotEmpty) {
          return _buildSelectionModeBottomBar();
        }
        return const SizedBox.shrink();
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // 跳转到草稿创建页面
          Get.toNamed(
            AppRouter.draftCreatePath,
            arguments: {
              'boxId': widget.draftBox.id, // 传递当前草稿箱ID
            },
          )?.then((_) {
            // 当从草稿创建页面返回时，刷新草稿列表
            _refreshDraftList();
          });
        },
        backgroundColor: Colors.blue,
        heroTag: 'draft_detail_fab',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  // 构建草稿箱信息
  Widget _buildDraftBoxInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
      child: Row(
        children: [
          // 草稿箱图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getBoxColor(widget.draftBox.color),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Icon(
                _getBoxIcon(widget.draftBox.icon),
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
          const SizedBox(width: 16),
          // 草稿箱信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      widget.draftBox.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (widget.draftBox.isDefault)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '默认',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue[800],
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Obx(() => Text(
                  '共${drafts.length}个草稿',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建草稿卡片
  Widget _buildDraftCard(DraftItem draft) {
    // 确定草稿类型图标和预览内容
    IconData typeIcon;
    Widget previewContent;
    
    if (draft.videoPath != null && draft.videoPath!.isNotEmpty) {
      typeIcon = Icons.videocam;
      
      // 视频预览
      if (draft.thumbnailPath != null && draft.thumbnailPath!.isNotEmpty) {
        // 使用缩略图
        final thumbnailFile = File(draft.thumbnailPath!);
        if (thumbnailFile.existsSync()) {
          previewContent = Image.file(
            thumbnailFile,
            width: 80,
            height: 80,
            fit: BoxFit.cover,
          );
        } else {
          previewContent = Container(
            width: 80,
            height: 80,
            color: Colors.grey[300],
            child: const Icon(Icons.videocam, size: 32, color: Colors.white),
          );
        }
      } else {
        // 无缩略图时显示占位图
        previewContent = Container(
          width: 80,
          height: 80,
          color: Colors.grey[300],
          child: const Icon(Icons.videocam, size: 32, color: Colors.white),
        );
      }
    } else if (draft.imagePaths != null && draft.imagePaths!.isNotEmpty) {
      typeIcon = Icons.photo;
      
      // 图片预览
      final imageFile = File(draft.imagePaths!.first);
      if (imageFile.existsSync()) {
        previewContent = Image.file(
          imageFile,
          width: 80,
          height: 80,
          fit: BoxFit.cover,
        );
      } else {
        previewContent = Container(
          width: 80,
          height: 80,
          color: Colors.grey[300],
          child: const Icon(Icons.image, size: 32, color: Colors.white),
        );
      }
    } else {
      typeIcon = Icons.description;
      previewContent = Container(
        width: 80,
        height: 80,
        color: Colors.grey[300],
        child: const Icon(Icons.description, size: 32, color: Colors.white),
      );
    }
    
    return Obx(() {
      final isSelected = selectedDraftIds.contains(draft.id);
      
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        elevation: isSelected ? 2 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: isSelected
              ? const BorderSide(color: Colors.blue, width: 2)
              : BorderSide.none,
        ),
        child: InkWell(
          onTap: () {
            if (isSelectionMode.value) {
              // 选择模式下，切换选中状态
              if (isSelected) {
                selectedDraftIds.remove(draft.id);
              } else {
                selectedDraftIds.add(draft.id);
              }
            } else {
              // 非选择模式下，打开草稿编辑页面
              _openDraftEdit(draft);
            }
          },
          onLongPress: () {
            // 长按进入选择模式
            if (!isSelectionMode.value) {
              isSelectionMode.value = true;
              selectedDraftIds.add(draft.id);
            }
          },
          borderRadius: BorderRadius.circular(10),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // 选择模式下显示选择框
                if (isSelectionMode.value)
                  Padding(
                    padding: const EdgeInsets.only(right: 12),
                    child: isSelected
                        ? const Icon(Icons.check_circle, color: Colors.blue)
                        : const Icon(Icons.circle_outlined, color: Colors.grey),
                  ),
                
                // 草稿预览
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: previewContent,
                ),
                
                const SizedBox(width: 16),
                
                // 草稿信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        draft.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        draft.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(typeIcon, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            _getDraftTypeText(draft),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Icon(Icons.access_time, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            _formatDate(draft.updateTime),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // 非选择模式下显示更多按钮
                if (!isSelectionMode.value)
                  IconButton(
                    icon: const Icon(Icons.more_vert, color: Colors.grey),
                    onPressed: () {
                      _showDraftOptions(draft);
                    },
                  ),
              ],
            ),
          ),
        ),
      );
    });
  }

  // 构建选择模式底部操作栏
  Widget _buildSelectionModeBottomBar() {
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 12,
        bottom: 12 + MediaQuery.of(context).padding.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          // 选中数量
          Obx(() => Text(
            '已选择${selectedDraftIds.length}个草稿',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          )),
          const Spacer(),
          // 删除按钮
          TextButton.icon(
            onPressed: () {
              _showDeleteSelectedDraftsDialog();
            },
            icon: const Icon(Icons.delete, color: Colors.red),
            label: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
          // 移动按钮
          TextButton.icon(
            onPressed: () {
              _showMoveDraftsDialog();
            },
            icon: const Icon(Icons.drive_file_move),
            label: const Text('移动'),
          ),
        ],
      ),
    );
  }

  // 显示更多选项菜单
  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.select_all),
                title: const Text('选择草稿'),
                onTap: () {
                  Navigator.pop(context);
                  isSelectionMode.value = true;
                },
              ),
              ListTile(
                leading: const Icon(Icons.sort),
                title: const Text('排序'),
                onTap: () {
                  Navigator.pop(context);
                  _showSortOptions();
                },
              ),
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  // 显示排序选项
  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Padding(
                padding: EdgeInsets.all(16),
                child: Text(
                  '排序方式',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ListTile(
                leading: const Icon(Icons.access_time),
                title: const Text('最近更新'),
                onTap: () {
                  Navigator.pop(context);
                  _sortDrafts('updateTime');
                },
              ),
              ListTile(
                leading: const Icon(Icons.calendar_today),
                title: const Text('创建时间'),
                onTap: () {
                  Navigator.pop(context);
                  _sortDrafts('createTime');
                },
              ),
              ListTile(
                leading: const Icon(Icons.sort_by_alpha),
                title: const Text('标题'),
                onTap: () {
                  Navigator.pop(context);
                  _sortDrafts('title');
                },
              ),
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  // 排序草稿
  void _sortDrafts(String sortBy) {
    switch (sortBy) {
      case 'updateTime':
        drafts.sort((a, b) => b.updateTime.compareTo(a.updateTime));
        break;
      case 'createTime':
        drafts.sort((a, b) => b.createTime.compareTo(a.createTime));
        break;
      case 'title':
        drafts.sort((a, b) => a.title.compareTo(b.title));
        break;
    }
  }

  // 显示草稿选项菜单
  void _showDraftOptions(DraftItem draft) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('编辑'),
                onTap: () {
                  Navigator.pop(context);
                  _openDraftEdit(draft);
                },
              ),
              ListTile(
                leading: const Icon(Icons.drive_file_move),
                title: const Text('移动到其他草稿箱'),
                onTap: () {
                  Navigator.pop(context);
                  _showMoveDraftDialog(draft);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('删除', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteDraftDialog(draft);
                },
              ),
              const SizedBox(height: 8),
            ],
          ),
        );
      },
    );
  }

  // 打开草稿编辑页面
  void _openDraftEdit(DraftItem draft) {
    // 跳转到发布页面而不是草稿创建页面
    Get.toNamed(
      AppRouter.videoPublishPath,
      arguments: {
        'draftId': draft.id,
        'boxId': widget.draftBox.id,
        'draft': draft,  // 传递完整的草稿对象，方便发布页面直接使用
        'sourceType': 'draft', // 指定来源类型为草稿
      },
    )?.then((_) {
      // 当从发布页面返回时，刷新草稿列表
      _refreshDraftList();
    });
  }
  
  // 刷新草稿列表
  void _refreshDraftList() {
    print('刷新草稿列表');
    drafts.assignAll(_draftStoreService.getDraftsInBox(widget.draftBox.id));
  }

  // 显示移动草稿对话框
  void _showMoveDraftDialog(DraftItem draft) {
    _showMoveDraftsDialog(draftIds: [draft.id]);
  }

  // 显示移动草稿对话框（支持批量）
  void _showMoveDraftsDialog({List<String>? draftIds}) {
    final ids = draftIds ?? selectedDraftIds.toList();
    if (ids.isEmpty) return;
    
    // 获取其他草稿箱列表（排除当前草稿箱）
    final otherBoxes = _draftStoreService.draftBoxes
        .where((box) => box.id != widget.draftBox.id)
        .toList();
    
    if (otherBoxes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有其他草稿箱可移动')),
      );
      return;
    }
    
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('移动${ids.length}个草稿到'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: otherBoxes.length,
              itemBuilder: (context, index) {
                final box = otherBoxes[index];
                return ListTile(
                  leading: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: _getBoxColor(box.color),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Center(
                      child: Icon(
                        _getBoxIcon(box.icon),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
                  title: Text(box.name),
                  subtitle: Text('${box.drafts.length}个草稿'),
                  onTap: () async {
                    Navigator.pop(context);
                    
                    // 移动草稿
                    bool allSuccess = true;
                    for (final id in ids) {
                      final success = await _draftStoreService.moveDraft(
                        fromBoxId: widget.draftBox.id,
                        toBoxId: box.id,
                        draftId: id,
                      );
                      
                      if (!success) {
                        allSuccess = false;
                      }
                    }
                    
                    // 更新草稿列表
                    drafts.assignAll(_draftStoreService.getDraftsInBox(widget.draftBox.id));
                    
                    // 退出选择模式
                    isSelectionMode.value = false;
                    selectedDraftIds.clear();
                    
                    // 显示结果提示
                    if (allSuccess) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('已移动${ids.length}个草稿到"${box.name}"')),
                      );
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('部分草稿移动失败')),
                      );
                    }
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  // 显示删除草稿对话框
  void _showDeleteDraftDialog(DraftItem draft) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('删除草稿'),
          content: Text('确定要删除"${draft.title}"吗？此操作无法恢复。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                // 删除草稿
                final success = await _draftStoreService.deleteDraft(
                  boxId: widget.draftBox.id,
                  draftId: draft.id,
                );
                
                // 关闭对话框
                Navigator.pop(context);
                
                if (success) {
                  // 更新草稿列表
                  drafts.assignAll(_draftStoreService.getDraftsInBox(widget.draftBox.id));
                  
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('草稿已删除')),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('删除草稿失败')),
                  );
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  // 显示删除选中草稿对话框
  void _showDeleteSelectedDraftsDialog() {
    if (selectedDraftIds.isEmpty) return;
    
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('删除草稿'),
          content: Text('确定要删除${selectedDraftIds.length}个草稿吗？此操作无法恢复。'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.pop(context);
                
                // 删除选中的草稿
                int successCount = 0;
                for (final id in selectedDraftIds) {
                  final success = await _draftStoreService.deleteDraft(
                    boxId: widget.draftBox.id,
                    draftId: id,
                  );
                  
                  if (success) {
                    successCount++;
                  }
                }
                
                // 更新草稿列表
                drafts.assignAll(_draftStoreService.getDraftsInBox(widget.draftBox.id));
                
                // 退出选择模式
                isSelectionMode.value = false;
                selectedDraftIds.clear();
                
                // 显示结果提示
                if (successCount > 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('已删除$successCount个草稿')),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('删除草稿失败')),
                  );
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  // 获取草稿类型文本
  String _getDraftTypeText(DraftItem draft) {
    if (draft.videoPath != null && draft.videoPath!.isNotEmpty) {
      return '视频';
    } else if (draft.imagePaths != null && draft.imagePaths!.isNotEmpty) {
      return '${draft.imagePaths!.length}张图片';
    } else {
      return '文本';
    }
  }

  // 获取草稿箱颜色
  Color _getBoxColor(String? colorStr) {
    if (colorStr == null || colorStr.isEmpty) {
      return Colors.blue;
    }
    
    try {
      return Color(int.parse(colorStr.replaceAll('#', '0xFF')));
    } catch (e) {
      return Colors.blue;
    }
  }

  // 获取草稿箱图标
  IconData _getBoxIcon(String? iconStr) {
    if (iconStr == null || iconStr.isEmpty) {
      return Icons.folder;
    }
    
    switch (iconStr) {
      case 'folder':
        return Icons.folder;
      case 'description':
        return Icons.description;
      case 'videocam':
        return Icons.videocam;
      case 'image':
        return Icons.image;
      case 'favorite':
        return Icons.favorite;
      case 'star':
        return Icons.star;
      default:
        return Icons.folder;
    }
  }

  // 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return '刚刚';
        }
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }
}