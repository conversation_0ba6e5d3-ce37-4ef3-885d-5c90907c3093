import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_bucket/draft_bucket_controller.dart';
import 'package:aitoearn_app/pages/draft_and_media/model/draft_info.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_option.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_pop_helper.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 草稿箱详情页面
/// 展示特定草稿组下的所有草稿内容
class DraftBucketPage extends GetView<DraftBucketController> {
  const DraftBucketPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      titleWidget: Obx(
        () => '${controller.bucketTitle.value}（${controller.draftTotal.value}）'
            .toNormalText(fontSize: 18, fontWeight: FontWeight.w500),
      ),
      backFuc: () {
        Get.back(result: controller.isEdit.value);
      },
      action: _buildMoreAction(context),
      floatingActionButton: FloatingActionButton(
        onPressed: () => controller.showCreateDraftDialog(),
        child: const Icon(Icons.add),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Obx(() {
              return GridView.builder(
                shrinkWrap: true,
                // 高度根据内容适应（但不能滚动）
                physics: const NeverScrollableScrollPhysics(),
                // 禁用自身滚动，避免嵌套冲突
                itemCount: controller.draftList.length,
                gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                  maxCrossAxisExtent: 280, // 每项最大宽度（自动计算列数）
                  mainAxisSpacing: 12, // 行间距
                  crossAxisSpacing: 12, // 列间距
                  childAspectRatio: 1.4, // 控制宽高比，可根据实际内容调整
                ),
                itemBuilder: (context, index) {
                  return _buildDraftItem(controller.draftList[index]);
                },
              );
            }),
            Obx(() {
              Widget infoWidget = const SizedBox();
              if (!controller.hasMoreDraft()) {
                infoWidget = Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: '没有更多数据'.toNormalText(),
                );
              } else if (!isMobileMode() && controller.hasMoreDraft()) {
                infoWidget = Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: '点击加载更多'.toNormalText().gestures(
                    onTap: () => controller.loadMoreDraft(),
                  ),
                );
              }
              return infoWidget;
            }),
            Gaps.vGap16,
          ],
        ),
      ).marginSymmetric(horizontal: 16),
    );
  }

  /// 构建草稿列表项
  Widget _buildDraftItem(DraftInfoModel draft) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Stack(
        children: [
          // 背景封面图
          Positioned.fill(
            child:
                draft.coverUrl.isNotEmpty
                    ? NetworkImageWidget(draft.coverUrl, fit: BoxFit.cover)
                    : Container(color: Colors.grey[200]),
          ),

          // 遮罩 + 文本信息区域
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.white.withOpacity(0.6),
                    Colors.white.withOpacity(0.8),
                    Colors.white.withOpacity(1.0),
                  ],
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  draft.title.toNormalText(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textColor,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Gaps.vGap4,
                  draft.desc.toNormalText(
                    fontSize: 11,
                    color: AppColors.textSecondColor,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),

          Positioned(
            right: 6,
            top: 6,
            child: Container(
              padding: const EdgeInsets.all(3),
              margin: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: AppColors.white.withOpacity(0.4),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.delete_outline,
                color: AppColors.errorColor,
                size: 20,
              ),
            ).gestures(onTap: () => controller.deleteDraft(draft.id)),
          ),
        ],
      ),
    );
  }

  _buildMoreAction(BuildContext context) {
    if (controller.bucketId == '') {
      return null;
    }
    final menuKey = GlobalKey();
    return IconButton(
      key: menuKey,
      onPressed: () {
        MenuPopHelper.showMenuByKey(
          context: context,
          key: menuKey,
          options: [
            MenuOption(
              icon: Icons.edit,
              name: '编辑',
              onTap: () => controller.editDraftBucket(),
            ),
            MenuOption(
              icon: Icons.delete,
              name: '删除',
              onTap: () => controller.deleteDraftBucket(),
            ),
          ],
        );
      },
      icon: const Icon(Icons.more_horiz),
    ).marginOnly(right: 8);
  }
}
