import 'package:aitoearn_app/api/media_material/media_material_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/draft_and_media/model/draft_info.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/utils/dialog/dialog_helper.dart';
import 'package:aitoearn_app/utils/dialog/form_dialog_helper.dart';
import 'package:aitoearn_app/utils/dialog/models/form_item_model.dart';
import 'package:aitoearn_app/utils/dialog/widgets/bottom_dialog_widget.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

/// 草稿箱控制器
/// 负责草稿数据的加载、删除、编辑等操作
class DraftBucketController extends GetxController {
  final isLoading = false.obs;
  final bucketTitle = ''.obs;
  late final String bucketId;
  late final String bucketType;

  final draftList = <DraftInfoModel>[].obs;
  final draftPageNo = 1.obs;
  final draftTotal = 0.obs;
  final draftPageLimit = 20;
  final isEdit = false.obs;

  final scrollController = ScrollController();

  @override
  void onInit() {
    super.onInit();
    // 获取传递的参数
    bucketTitle.value = Get.arguments['title'] as String? ?? '';
    bucketId = Get.arguments['bucketId'] as String? ?? '';
    bucketType = Get.arguments['type'] as String? ?? '';

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
              scrollController.position.maxScrollExtent &&
          isMobileMode()) {
        // 滚动到了底部
        if (hasMoreDraft() && !isLoading.value) {
          loadMoreDraft();
        }
      }
    });

    initList();
  }

  Future<void> initList() async {
    final cancel = showProgress();
    await getDraftList(draftPageNo.value, draftPageLimit);
    cancel();
  }

  /// 获取草稿列表数据
  Future<void> getDraftList(int pageNo, int pageSize) async {
    isLoading.value = true;
    try {
      final response = await getMaterialListApi(
        pageNo: pageNo,
        pageSize: pageSize,
        groupId: bucketId,
      );

      final rawData = response?.data?['data'];
      final total = rawData?['total'] ?? 0;
      final List<dynamic> list = rawData?['list'] ?? [];

      final List<DraftInfoModel> drafts =
          list.map((e) => DraftInfoModel.fromJson(e)).toList();

      if (pageNo == 1) {
        draftList.clear();
      }
      draftList.addAll(drafts);
      draftTotal.value = total;
      draftPageNo.value += 1;
    } catch (e) {
      showError('加载草稿失败');
      LoggerUtil.e('加载草稿失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 删除草稿
  void deleteDraft(String draftId) async {
    DialogHelper.showSimpleDialog(title: '确定要删除该草稿吗？').then((res) async {
      if (res == true) {
        final cancel = showProgress();
        try {
          final response = await deleteMaterialApi(draftId);
          if (response?['code'] == 0) {
            showSuccess('删除成功');
            draftPageNo.value = 1;
            getDraftList(draftPageNo.value, draftPageLimit);
            isEdit.value = true;
          } else {
            showError(response?['message'] ?? '删除失败');
          }
        } catch (e) {
          showError('删除失败');
        } finally {
          cancel();
        }
      }
    });
  }

  /// 加载更多草稿
  Future<void> loadMoreDraft() async {
    if (hasMoreDraft()) {
      await getDraftList(draftPageNo.value, draftPageLimit);
    }
  }

  /// 是否还有更多草稿
  bool hasMoreDraft() {
    return (draftPageNo.value - 1) * draftPageLimit < draftTotal.value;
  }

  editDraftBucket() {
    const name = '草稿箱标题';
    FormDialogHelper.showFormDialog(
      title: '更新草稿箱',
      formItems: [
        InputFormItem(
          name: name,
          hint: '请输入草稿箱名称',
          value: bucketTitle.value,
          required: true,
        ),
      ],
    ).then((value) async {
      if (value == null) {
        return;
      }
      final cancel = showProgress();
      final res = await updateMaterialGroupInfoApi(
        id: bucketId,
        name: value[name],
      );
      if (res?['code'] == 0) {
        showSuccess('更新成功');
        bucketTitle.value = value[name];
        isEdit.value = true;
      } else {
        showError(res?['message'] ?? '更新失败');
      }
      cancel();
    });
  }

  deleteDraftBucket() {
    DialogHelper.showSimpleDialog(title: '确定要删除草稿么？').then((res) async {
      if (res == true) {
        final cancel = showProgress();
        final res = await deleteMaterialGroupApi(bucketId);
        if (res?['code'] == 0) {
          showSuccess('删除成功');
          Get.back(result: true);
        } else {
          showError(res?['message'] ?? '删除失败');
        }
        cancel();
      }
    });
  }

  showCreateDraftDialog() {
    DialogHelper.showBottomSheetDialog(
      items: [
        MenuItem(
          text: '批量生成草稿',
          onTap: () {
            Get.toNamed(
              AppRouter.draftCreatePath,
              arguments: {
                'boxId': bucketId, // 传递当前草稿箱ID
              },
            );
          },
        ),
        MenuItem(text: '手动创建草稿', onTap: () {}),
        MenuItem(text: '导入历史记录', onTap: () {}),
      ],
    );
  }
}
