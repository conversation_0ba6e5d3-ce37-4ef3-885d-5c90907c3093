import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/draft_models/draft_model.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_create_logic.dart';
import 'package:aitoearn_app/pages/publish/account_select_dialog.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DraftCreatePage extends StatefulWidget {
  const DraftCreatePage({super.key});

  @override
  State<DraftCreatePage> createState() => _DraftCreatePageState();
}

class _DraftCreatePageState extends State<DraftCreatePage> {
  // 通过Get.find获取控制器
  late final DraftCreateLogic logic;

  // 标题和简介的文本控制器
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  // 焦点节点
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _descriptionFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    logic = Get.find<DraftCreateLogic>();
    
    // 设置文本控制器的监听
    _titleController.addListener(() {
      logic.state.title.value = _titleController.text;
    });

    _descriptionController.addListener(() {
      logic.state.description.value = _descriptionController.text;
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _titleFocusNode.dispose();
    _descriptionFocusNode.dispose();
    super.dispose();
  }

  // 清除所有焦点
  void _clearFocus() {
    _titleFocusNode.unfocus();
    _descriptionFocusNode.unfocus();
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _clearFocus,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          centerTitle: true,
          title: const Text(
            '草稿生成',
            style: TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.black,
              size: 20,
            ),
            onPressed: () => Get.back(),
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 媒体预览区域
                    _buildMediaPreviewSection(),
                    
                    // 模型选择区域
                    _buildModelSelectionSection(),

                    // 标题输入
                    _buildInputField(
                      controller: _titleController,
                      focusNode: _titleFocusNode,
                      hint: '添加标题',
                    ),

                    // 简介输入
                    _buildInputField(
                      controller: _descriptionController,
                      focusNode: _descriptionFocusNode,
                      hint: '添加作品简介',
                    ),

                    // 话题和@好友
                    _buildTagSection(),

                    // 位置信息
                    _buildLocationSection(),

                    // 定时发布
                    _buildScheduleSection(),
                    
                    // 高级设置
                    _buildAdvancedSettingsSection(),
                  ],
                ),
              ),
            ),

            // 底部操作栏
            _buildBottomActionBar(),
          ],
        ),
      ),
    );
  }

  // 媒体预览区域
  Widget _buildMediaPreviewSection() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 左侧预览区域
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: const Color(0xFFE6EAF2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Obx(() {
              // 如果有选中的图片，显示第一张图片
              if (logic.state.selectedImages.isNotEmpty) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Image.file(
                    logic.state.selectedImages.first,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                );
              }
              
              // 如果有选中的视频，显示视频缩略图
              if (logic.state.videoPath.value.isNotEmpty && logic.state.videoThumbnail.value != null) {
                return ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: Image.file(
                    logic.state.videoThumbnail.value!,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  ),
                );
              }
              
              // 默认显示空白区域
              return Container();
            }),
          ),
          
          // 添加按钮
          Container(
            width: 100,
            height: 100,
            margin: const EdgeInsets.only(left: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey[200]!, width: 1),
            ),
            child: InkWell(
              onTap: () {
                _clearFocus();
                _selectContent();
              },
              child: Center(
                child: Icon(
                  Icons.add,
                  size: 40,
                  color: Colors.purple[200],
                ),
              ),
            ),
          ),
          
          // 右侧积分图标
          const Spacer(),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.orange,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '158+',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // 模型选择区域
  Widget _buildModelSelectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
          child: Text(
            '选择大模型',
            style: TextStyle(
              color: Colors.black54,
              fontSize: 14,
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F7FA),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(Icons.image, size: 20, color: Colors.black54),
              const SizedBox(width: 8),
              Text(
                '图片1.0',
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: 15,
                ),
              ),
              const Spacer(),
              Icon(Icons.keyboard_arrow_down, color: Colors.black54),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Text(
            '大模型生成提示词',
            style: TextStyle(
              color: Colors.black38,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  // 话题和@好友区域
  Widget _buildTagSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Text(
                  '#话题',
                  style: TextStyle(
                    color: Colors.blue,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Text(
                  '@朋友',
                  style: TextStyle(
                    color: Colors.blue,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.purple[50],
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.remove_red_eye, size: 16, color: Colors.purple),
                const SizedBox(width: 4),
                Text(
                  '预览生成结果',
                  style: TextStyle(
                    color: Colors.purple,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  // 位置信息区域
  Widget _buildLocationSection() {
    return ListTile(
      leading: const Icon(Icons.location_on_outlined, color: Colors.black54),
      title: const Text('你在哪里', style: TextStyle(color: Colors.black87)),
      trailing: const Icon(Icons.chevron_right, color: Colors.black45),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      onTap: () {
        _clearFocus();
        // 显示位置选择
      },
    );
  }

  // 定时发布区域
  Widget _buildScheduleSection() {
    return ListTile(
      leading: const Icon(Icons.access_time_outlined, color: Colors.black54),
      title: const Text('定时发布', style: TextStyle(color: Colors.black87)),
      trailing: const Icon(Icons.chevron_right, color: Colors.black45),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      onTap: () {
        _clearFocus();
        // 显示时间选择
      },
    );
  }
  
  // 高级设置区域
  Widget _buildAdvancedSettingsSection() {
    return ListTile(
      leading: const Icon(Icons.settings_outlined, color: Colors.black54),
      title: const Text('高级设置', style: TextStyle(color: Colors.black87)),
      trailing: const Icon(Icons.chevron_right, color: Colors.black45),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      onTap: () {
        _clearFocus();
        // 显示高级设置
      },
    );
  }

  // 底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 草稿数量
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.remove, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    '4篇草稿',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(Icons.add, size: 16, color: Colors.grey[600]),
                ],
              ),
            ),
            
            const Spacer(),
            
            // 生成按钮
            Expanded(
              child: Container(
                height: 44,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue, Colors.purple],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(22),
                ),
                child: ElevatedButton(
                  onPressed: () async {
                    _clearFocus();
                    
                    // 检查必要的数据
                    if (logic.state.videoPath.value.isEmpty && logic.state.selectedImages.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('请先选择视频或图片')),
                      );
                      return;
                    }
                    
                    // 保存草稿
                    final success = await logic.saveDraft();
                    if (success) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('草稿保存成功'),
                          backgroundColor: Colors.green,
                        ),
                      );
                      Navigator.of(context).pop(true); // 返回true表示保存成功
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('保存草稿失败'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(22),
                    ),
                  ),
                  child: const Text(
                    '生成',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 选择内容（图片或视频）
  void _selectContent() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  '选择素材',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ListTile(
                leading: const Icon(Icons.photo_library, color: Colors.blue),
                title: const Text('从相册选择图片'),
                onTap: () async {
                  Navigator.pop(context);
                  logic.clearVideo();
                  await logic.pickMultiImages();
                },
              ),
              ListTile(
                leading: const Icon(Icons.videocam, color: Colors.red),
                title: const Text('从相册选择视频'),
                onTap: () async {
                  Navigator.pop(context);
                  logic.clearImages();
                  await logic.pickVideoFromGallery();
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  // 输入字段
  Widget _buildInputField({
    required TextEditingController controller,
    required FocusNode focusNode,
    required String hint,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        autofocus: false,
        decoration: InputDecoration(
          hintText: hint,
          hintStyle: TextStyle(color: Colors.grey[400]),
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        style: const TextStyle(fontSize: 16, color: Colors.black87),
      ),
    );
  }
} 