import 'package:aitoearn_app/pages/ai_tools/global_media_generation_controller.dart';
import 'package:aitoearn_app/pages/base/base_page.dart';
import 'package:aitoearn_app/pages/draft_and_media/media_bucket/media_bucket_controller.dart';
import 'package:aitoearn_app/pages/draft_and_media/model/media_info.dart';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_option.dart';
import 'package:aitoearn_app/utils/bubble_menu/menu_pop_helper.dart';
import 'package:aitoearn_app/utils/image_preview_viewer.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MediaBucketPage extends GetView<MediaBucketController> {
  const MediaBucketPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BasePage(
      titleWidget: Obx(
        () => Column(
          children: [
            [
              '${controller.bucketTitle.value}（${controller.mediaTotal.value}）'
                  .toNormalText(
                    fontSize: controller.bucketDesc.value.isNotEmpty ? 16 : 18,
                    fontWeight: FontWeight.w500,
                  ),
              Gaps.hGap4,
              Container(
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                child:
                    (controller.bucketType == 'video')
                        ? '视频库'.toNormalText(
                          fontSize: 10,
                          color: AppColors.white,
                        )
                        : '图片库'.toNormalText(
                          fontSize: 10,
                          color: AppColors.white,
                        ),
              ),
              Gaps.hGap4,
            ].toRow(mainAxisSize: MainAxisSize.min),
            if (controller.bucketDesc.value.isNotEmpty)
              controller.bucketDesc.value.toNormalText(
                fontSize: 11,
                color: AppColors.textSecondColor,
              ),
          ],
        ),
      ),
      action: _buildMoreAction(context),
      backFuc: () {
        Get.back(result: controller.isEdit.value);
      },
      floatingActionButton: FloatingActionButton(
        onPressed: () => controller.addMedia(),
        child: const Icon(Icons.add),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Obx(() {
              // 添加按钮
              final items = <Widget>[];

              // 带 index 地生成媒体组件
              final mediaWidgets = List.generate(
                controller.mediaList.length,
                (index) => _buildMediaItem(controller.mediaList[index], index),
              );

              // 添加生成状态项 - 使用过滤后的列表
              final generationList = controller.filteredGenerationStatus;

              // 遍历生成状态列表，添加所有状态项
              for (var status in generationList) {
                items.add(_buildGenerationStatusItem(status));
              }

              // 添加媒体项
              items.addAll(mediaWidgets);

              return GridView.builder(
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                  maxCrossAxisExtent: 150, // 最大宽度为200，超过后自动换行
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  childAspectRatio: 1, // 宽高比1:1，保持高度固定
                ),
                itemCount: items.length,
                itemBuilder: (context, index) => items[index],
              );
            }),
            Obx(() {
              Widget infoWidget = const SizedBox();
              if (!controller.hasMoreMedia()) {
                infoWidget = Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: '没有更多数据'.toNormalText(),
                );
              } else if (!isMobileMode() && controller.hasMoreMedia()) {
                infoWidget = Container(
                  height: 50,
                  alignment: Alignment.center,
                  child: '点击加载更多'.toNormalText().gestures(
                    onTap: () => controller.loadMoreMedia(),
                  ),
                );
              }

              return infoWidget;
            }),
            Gaps.vGap16,
          ],
        ),
      ).marginSymmetric(horizontal: 16),
    );
  }

  // 媒体项Widget
  Widget _buildMediaItem(MediaInfoModel media, int index) {
    return Stack(
      children: [
        Positioned.fill(
          child: NetworkImageWidget(
            media.url,
            width: double.infinity,
            height: double.infinity,
            heroTag: media.url,
            fit: BoxFit.cover,
            borderRadius: BorderRadius.circular(8),
            onTap: () => controller.preViewMedia(media, index),
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.7),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: media.title.toNormalText(
              fontSize: 11,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              color: AppColors.textColor,
            ),
          ),
        ),
        Positioned(
          right: 5,
          top: 5,
          child: Container(
            padding: const EdgeInsets.all(3),
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.4),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.delete_outline,
              color: AppColors.errorColor,
              size: 20,
            ),
          ).gestures(onTap: () => controller.deleteMedia(media)),
        ),
      ],
    );
  }

  _buildMoreAction(BuildContext context) {
    if (controller.bucketId == '') {
      return null;
    }

    final menuKey = GlobalKey();
    return IconButton(
      key: menuKey,
      onPressed: () {
        MenuPopHelper.showMenuByKey(
          context: context,
          key: menuKey,
          options: [
            MenuOption(
              icon: Icons.edit,
              name: '编辑',
              onTap: () => controller.editMediaBucket(),
            ),
            MenuOption(
              icon: Icons.delete,
              name: '删除',
              onTap: () => controller.deleteMediaBucket(),
            ),
          ],
        );
      },
      icon: const Icon(Icons.more_horiz),
    ).marginOnly(right: 8);
  }

  // 添加生成状态项Widget
  // 添加状态展示组件
  Widget _buildGenerationStatusItem(
      GenerationStatus status,
      ) {
    // 实现与media_bucket_page中相同的展示逻辑
    Widget content;

    switch (status.status) {
      case GenerationStatusType.error:
        content = Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: AppColors.errorColor, size: 32),
            Gaps.vGap12,
            status.message.toNormalText(
              color: AppColors.textHintColor,
              fontSize: 12,
            ),
          ],
        );
        break;
      default:
        content = Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(),
            ),
            Gaps.vGap12,
            status.message.toNormalText(
              color: AppColors.textHintColor,
              fontSize: 12,
            ),
          ],
        );
    }

    return Stack(
      children: [
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: content,
          ),
        ),
      ],
    );
  }
}
