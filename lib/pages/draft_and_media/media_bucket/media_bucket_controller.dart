import 'dart:async';
import 'dart:io';

import 'package:aitoearn_app/api/media_material/media_material_api.dart';
import 'package:aitoearn_app/config/app_config.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/pages/ai_tools/global_media_generation_controller.dart';
import 'package:aitoearn_app/pages/draft_and_media/model/media_info.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/utils/dialog/dialog_helper.dart';
import 'package:aitoearn_app/utils/dialog/form_dialog_helper.dart';
import 'package:aitoearn_app/utils/dialog/models/form_item_model.dart';
import 'package:aitoearn_app/utils/dialog/widgets/bottom_dialog_widget.dart';
import 'package:aitoearn_app/utils/image_preview_viewer.dart';
import 'package:aitoearn_app/utils/media_picker.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:aitoearn_app/utils/video_preview_viewer.dart';
import 'package:aitoearn_app/widgets/responsive_layout.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class MediaBucketController extends GetxController {
  final isLoading = false.obs;
  final bucketTitle = ''.obs;
  final bucketDesc = ''.obs;
  late final String bucketId;
  late final String bucketType;

  final mediaList = <MediaInfoModel>[].obs;
  final mediaPageNo = 1.obs;
  final mediaTotal = 0.obs;

  // 添加过滤后的生成状态列表
  final filteredGenerationStatus = <GenerationStatus>[].obs;

  // 每页数量
  final mediaPageLimit = 20;

  // 记录有没有编辑过
  final isEdit = false.obs;

  final scrollController = ScrollController();

  Timer? _refreshTimer;

  @override
  void onInit() {
    super.onInit();
    bucketTitle.value = Get.arguments['title'] as String? ?? '';
    bucketDesc.value = Get.arguments['desc'] as String? ?? '';
    bucketId = Get.arguments['bucketId'] as String? ?? '';
    bucketType = Get.arguments['bucketType'] as String ?? '';

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
              scrollController.position.maxScrollExtent &&
          isMobileMode()) {
        // 滚动到了底部
        if (hasMoreMedia() && !isLoading.value) {
          loadMoreMedia();
        }
      }
    });

    // 监听全局生成状态变化
    ever(GlobalMediaGenerationController.to.generationStatus, (_) {
      _handleGenerationStatusChange();
    });

    initList();
  }

  Future<void> initList() async {
    final cancel = showProgress();
    await getMediaList(mediaPageNo.value, mediaPageLimit);
    cancel();
  }

  /// 处理生成状态变化 - 添加过滤逻辑
  void _handleGenerationStatusChange() {
    final statusList =
        GlobalMediaGenerationController.to.generationStatus[bucketId] ?? [];

    // 根据桶类型过滤生成状态
    filteredGenerationStatus.value =
        statusList.where((s) {
          // 如果是视频桶，只显示视频类型的生成状态
          // 如果是图片桶，只显示图片类型的生成状态
          bool isCorrectMediaType =
              bucketType == 'video'
                  ? s.mediaType == MediaType.video
                  : s.mediaType == MediaType.image;

          // 过滤掉已完成状态，只保留生成中、保存中和错误状态
          bool isActiveStatus = s.status != GenerationStatusType.completed;

          return isCorrectMediaType && isActiveStatus;
        }).toList();

    final hasCompleted = statusList.any(
      (s) =>
          s.status == GenerationStatusType.completed ||
          s.status == GenerationStatusType.error,
    );

    if (hasCompleted) {
      // 防抖：500ms 内只刷新一次
      _refreshTimer?.cancel();
      _refreshTimer = Timer(const Duration(milliseconds: 500), () {
        mediaPageNo.value = 1;
        getMediaList(mediaPageNo.value, mediaPageLimit);
      });
    }
  }

  getMediaList(int pageNo, int pageSize) async {
    isLoading.value = true;
    final response = await getMediaListApi(
      pageNo: pageNo,
      pageSize: pageSize,
      groupId: bucketId,
    );

    final rawData = response?.data?['data'];
    final total = rawData?['total'] ?? 0;

    final List<dynamic> list = rawData?['list'] ?? [];

    final List<MediaInfoModel> mediaGroups =
        list.map((e) => MediaInfoModel.fromJson(e)).toList();

    // 你可以使用 mediaGroups 和 total 做接下来的逻辑
    if (pageNo == 1) {
      mediaList.clear();
    }
    mediaList.addAll(mediaGroups);
    mediaTotal.value = total;
    mediaPageNo.value += 1;
    isLoading.value = false;
  }

  deleteMediaBucket() {
    final isVideo = bucketType == 'video';
    DialogHelper.showSimpleDialog(
      title: isVideo ? '确定要删除视频库么？' : '确定要删除图片库么？',
    ).then((res) async {
      if (res == true) {
        final cancel = showProgress();
        final res = await deleteMediaGroupApi(bucketId);
        if (res?['code'] == 0) {
          showSuccess('删除成功');
          Get.back(result: true);
        } else {
          showError(res?['message'] ?? '删除失败');
        }
        cancel();
      }
    });
  }

  editMediaBucket() {
    final isVideo = bucketType == 'video';
    final name = isVideo ? '视频库标题' : '图片库标题';
    final desc = isVideo ? '视频库描述' : '图片库描述';
    FormDialogHelper.showFormDialog(
      title: isVideo ? '更新视频库' : '更新图片库',
      formItems: [
        InputFormItem(
          name: name,
          hint: isVideo ? '请输入视频库名称' : '请输入图片库名称',
          value: bucketTitle.value,
          required: true,
        ),
        InputFormItem(
          name: desc,
          hint: isVideo ? '请输入视频库描述' : '请输入图片库描述',
          value: bucketDesc.value,
        ),
      ],
    ).then((value) async {
      if (value == null) {
        return;
      }
      final cancel = showProgress();
      final res = await updateMediaGroupInfoApi(
        id: bucketId,
        title: value[name],
        desc: value[desc],
      );
      if (res?['code'] == 0) {
        showSuccess('更新成功');
        bucketTitle.value = value[name];
        bucketDesc.value = value[desc];
        isEdit.value = true;
      } else {
        showError(res?['message'] ?? '更新失败');
      }
      cancel();
    });
  }

  void deleteMedia(MediaInfoModel media) {
    DialogHelper.showSimpleDialog(title: '确定要删除该图片么？').then((res) async {
      if (res == true) {
        final cancel = showProgress();
        final res = await deleteMediaApi(media.id);
        if (res?['code'] == 0) {
          showSuccess('删除成功');
          mediaPageNo.value = 1;
          getMediaList(mediaPageNo.value, mediaPageLimit);
          isEdit.value = true;
        } else {
          showError(res?['message'] ?? '删除失败');
        }
        cancel();
      }
    });
  }

  addMedia() {
    final isVideo = bucketType == 'video';
    if (isVideo) {
      _addVideoMedia();
    } else {
      _addPicMedia();
    }
  }

  // 修改添加媒体方法，使用全局控制器
  void _addPicMedia() {
    DialogHelper.showBottomSheetDialog(
      items: [
        MenuItem(
          text: '本地上传',
          onTap: () async {
            final res = await MediaPicker.pickImages(maxCount: 9);
            final cancel = showProgress();
            try {
              for (File item in res) {
                final fileName = item.path.split('/').last;
                final url = await _uploadMedia(item, fileName);
                if (url.isNotEmpty) {
                  _createMedia(fileName, url);
                } else {
                  showError('上传失败');
                }
              }
            } catch (e) {
              LoggerUtil.e(e.toString());
            }
            cancel();
          },
        ),
        MenuItem(
          text: '图片生成',
          onTap: () {
            // 跳转到AI图片生成页面时传递bucketId
            Get.toNamed(
              AppRouter.aiToImagePath,
              arguments: {'bucketId': bucketId},
            );
          },
        ),
        MenuItem(text: '图文生成', onTap: () {}),
      ],
    );
  }

  void _addVideoMedia() {
    DialogHelper.showBottomSheetDialog(
      items: [
        MenuItem(
          text: '本地上传',
          onTap: () async {
            final res = await MediaPicker.pickVideos(maxCount: 1);
            final cancel = showProgress();
            try {
              for (VideoInfo item in res) {
                final fileName = item.file.path.split('/').last;
                final url = await _uploadMedia(item.file, fileName);
                if (url.isNotEmpty) {
                  _createMedia(fileName, url);
                } else {
                  showError('上传失败');
                }
              }
            } catch (e) {
              LoggerUtil.e(e.toString());
            }
            cancel();
          },
        ),
        MenuItem(
          text: '视频生成',
          onTap: () {
            // 跳转到AI视频生成页面时传递bucketId
            Get.toNamed(AppRouter.aiToVideo, arguments: {'bucketId': bucketId});
          },
        ),
      ],
    );
  }

  // 上传媒体（图片 / 视频）
  Future<String> _uploadMedia(File file, String fileName) async {
    final res = await updateOSSFile(file: file, fileName: fileName);
    final mediaUrl =
        res?['data']?['key'] != null
            ? '${AppConfig.ossHost}/${res?['data']?['key']}'
            : '';
    LoggerUtil.i('上传结果：$res');
    return mediaUrl;
  }

  bool hasMoreMedia() {
    return (mediaPageNo.value - 1) * mediaPageLimit < mediaTotal.value;
  }

  _createMedia(String fileName, String url) async {
    final res = await createMediaApi(
      groupId: bucketId,
      title: fileName,
      url: url,
      type: bucketType,
      desc: '',
    );
    if (res?['code'] == 0) {
      showSuccess('上传成功');
      mediaPageNo.value = 1;
      getMediaList(mediaPageNo.value, mediaPageLimit);
    } else {
      showError(res?['message'] ?? '上传失败');
    }
    isEdit.value = true;
  }

  Future<void> loadMoreMedia() async {
    await getMediaList(mediaPageNo.value, mediaPageLimit);
  }

  preViewMedia(MediaInfoModel media, int index) {
    if (bucketType == 'video') {
      VideoPreviewViewer.show(
        context: Get.context!,
        videoUrl: media.url,
        heroTag: media.url,
      );
    } else {
      List<String> imageUrls = mediaList.map((e) => e.url).toList();
      ImagePreviewViewer.show(
        context: Get.context!,
        imageUrls: imageUrls,
        heroTags: imageUrls,
        initialIndex: index,
      );
    }
  }
}
