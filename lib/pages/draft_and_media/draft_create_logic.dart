import 'dart:io';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/draft_models/draft_model.dart';
import 'package:aitoearn_app/store/draft/draft_store_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

/// 草稿创建页面的状态
class DraftCreateState {
  // 标题
  final RxString title = ''.obs;
  
  // 描述
  final RxString description = ''.obs;
  
  // 标签列表
  final RxList<String> tags = <String>[].obs;
  
  // 位置信息
  final RxMap<String, dynamic> locationInfo = <String, dynamic>{}.obs;
  
  // 位置名称（显示用）
  final RxString location = ''.obs;
  
  // 是否定时发布
  final RxBool isScheduled = false.obs;
  
  // 定时发布时间
  final Rx<DateTime?> scheduledTime = Rx<DateTime?>(null);
  
  // 视频路径
  final RxString videoPath = ''.obs;
  
  // 视频缩略图
  final Rx<File?> videoThumbnail = Rx<File?>(null);
  
  // 选中的图片列表
  final RxList<File> selectedImages = <File>[].obs;
  
  // 选中的账号列表
  final RxList<Map<String, dynamic>> selectedAccounts = <Map<String, dynamic>>[].obs;
  
  // 草稿ID（用于更新草稿）
  final RxString draftId = ''.obs;
  
  // 草稿箱ID（用于保存草稿）
  final RxString draftBoxId = ''.obs;
  
  // 是否正在保存
  final RxBool isSaving = false.obs;
}

/// 草稿创建页面的控制器
class DraftCreateLogic extends GetxController {
  final state = DraftCreateState();
  final ImagePicker _imagePicker = ImagePicker();
  final Uuid _uuid = Uuid();
  
  // 获取草稿箱服务
  late final DraftStoreService _draftStoreService;
  
  @override
  void onInit() {
    super.onInit();
    _draftStoreService = Get.find<DraftStoreService>();
    
    // 从参数中获取草稿箱ID和草稿ID
    final Map<String, dynamic>? arguments = Get.arguments;
    if (arguments != null) {
      if (arguments['boxId'] != null) {
        state.draftBoxId.value = arguments['boxId'];
      }
      if (arguments['draftId'] != null) {
        state.draftId.value = arguments['draftId'];
      }
      if (arguments['draft'] != null) {
        final DraftItem draft = arguments['draft'];
        _loadDraft(draft);
      }
    }
  }
  
  /// 加载草稿数据
  void _loadDraft(DraftItem draft) {
    state.title.value = draft.title;
    state.description.value = draft.description;
    state.tags.assignAll(draft.tags ?? []);
    state.locationInfo.assignAll(draft.locationInfo ?? {});
    state.location.value = draft.locationInfo?['name'] ?? '';
    state.isScheduled.value = draft.isScheduled ?? false;
    state.scheduledTime.value = draft.scheduledTime;
    
    if (draft.videoPath != null && draft.videoPath!.isNotEmpty) {
      state.videoPath.value = draft.videoPath!;
      if (draft.thumbnailPath != null && draft.thumbnailPath!.isNotEmpty) {
        state.videoThumbnail.value = File(draft.thumbnailPath!);
      }
    }
    
    if (draft.imagePaths != null && draft.imagePaths!.isNotEmpty) {
      state.selectedImages.assignAll(
        draft.imagePaths!.map((path) => File(path)).toList(),
      );
    }
    
    if (draft.selectedAccounts != null && draft.selectedAccounts!.isNotEmpty) {
      state.selectedAccounts.assignAll(draft.selectedAccounts!);
    }
  }
  
  /// 设置草稿标题
  void setDraftTitle(String title) {
    state.title.value = title;
  }
  
  /// 设置草稿描述
  void setDraftDescription(String description) {
    state.description.value = description;
  }
  
  /// 添加标签
  void addTag(String tag) {
    if (!state.tags.contains(tag)) {
      state.tags.add(tag);
    }
  }
  
  /// 移除标签
  void removeTag(String tag) {
    state.tags.remove(tag);
  }
  
  /// 设置位置名称
  void setLocation(String location) {
    state.location.value = location;
  }
  
  /// 设置位置信息
  void setLocationInfo(Map<String, dynamic> locationInfo) {
    state.locationInfo.value = locationInfo;
    
    // 如果locationInfo中包含name字段，同步更新location
    if (locationInfo.containsKey('name')) {
      state.location.value = locationInfo['name'];
    }
  }
  
  /// 设置定时发布时间
  void setScheduledTime(DateTime dateTime) {
    state.scheduledTime.value = dateTime;
    state.isScheduled.value = true;
  }
  
  /// 取消定时发布
  void cancelScheduledTime() {
    state.scheduledTime.value = null;
    state.isScheduled.value = false;
  }
  
  /// 设置视频路径
  void setVideoPath(String path) {
    state.videoPath.value = path;
    
    // 异步生成视频缩略图
    _generateVideoThumbnail(path);
  }
  
  /// 生成视频缩略图
  Future<void> _generateVideoThumbnail(String videoPath) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final thumbnailPath = await VideoThumbnail.thumbnailFile(
        video: videoPath,
        thumbnailPath: tempDir.path,
        imageFormat: ImageFormat.JPEG,
        quality: 50,
      );
      
      if (thumbnailPath != null) {
        state.videoThumbnail.value = File(thumbnailPath);
      }
    } catch (e) {
      LoggerUtil.e('生成视频缩略图失败: $e');
    }
  }
  
  /// 清除视频
  void clearVideo() {
    state.videoPath.value = '';
    state.videoThumbnail.value = null;
  }
  
  /// 清除图片
  void clearImages() {
    state.selectedImages.clear();
  }
  
  /// 移除指定索引的图片
  void removeImage(int index) {
    if (index >= 0 && index < state.selectedImages.length) {
      state.selectedImages.removeAt(index);
    }
  }
  
  /// 设置选中的账号
  void setSelectedAccounts(List<Map<String, dynamic>> accounts) {
    state.selectedAccounts.clear();
    state.selectedAccounts.addAll(accounts);
  }
  
  /// 清除选中的账号
  void clearSelectedAccounts() {
    state.selectedAccounts.clear();
  }
  
  /// 清除所有内容
  void clearAllContent() {
    state.title.value = '';
    state.description.value = '';
    state.tags.clear();
    state.locationInfo.clear();
    state.location.value = '';
    state.isScheduled.value = false;
    state.scheduledTime.value = null;
    state.videoPath.value = '';
    state.videoThumbnail.value = null;
    state.selectedImages.clear();
    state.selectedAccounts.clear();
    state.draftId.value = '';
    state.draftBoxId.value = '';
  }
  
  /// 从相册选择多张图片
  Future<void> pickMultiImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        state.selectedImages.addAll(
          images.map((image) => File(image.path)).toList(),
        );
      }
    } catch (e) {
      LoggerUtil.e('选择图片失败：$e');
    }
  }
  
  /// 从相册选择视频
  Future<void> pickVideoFromGallery() async {
    try {
      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
      );

      if (video != null) {
        state.videoPath.value = video.path;
        
        // 生成视频缩略图
        final tempDir = await getTemporaryDirectory();
        final thumbnailPath = await VideoThumbnail.thumbnailFile(
          video: video.path,
          thumbnailPath: tempDir.path,
          imageFormat: ImageFormat.JPEG,
          quality: 50,
        );
        
        if (thumbnailPath != null) {
          state.videoThumbnail.value = File(thumbnailPath);
        }
      }
    } catch (e) {
      LoggerUtil.e('选择视频失败：$e');
    }
  }
  
  /// 保存草稿
  Future<bool> saveDraft() async {
    // 标记正在保存
    state.isSaving.value = true;
    
    try {
      // 检查必要的字段
      if ((state.videoPath.value.isEmpty && state.selectedImages.isEmpty) ||
          state.draftBoxId.value.isEmpty) {
        LoggerUtil.e('保存草稿失败：缺少必要的内容或草稿箱ID');
        state.isSaving.value = false;
        return false;
      }
      
      // 准备草稿数据
      final draft = DraftItem(
        id: state.draftId.value.isEmpty ? _uuid.v4() : state.draftId.value,
        title: state.title.value.isNotEmpty ? state.title.value : '未命名草稿',
        description: state.description.value,
        tags: state.tags.toList(),
        videoPath: state.videoPath.value.isNotEmpty ? state.videoPath.value : null,
        thumbnailPath: state.videoThumbnail.value?.path,
        imagePaths: state.selectedImages.isNotEmpty 
            ? state.selectedImages.map((file) => file.path).toList() 
            : null,
        selectedAccounts: state.selectedAccounts.isNotEmpty 
            ? state.selectedAccounts.toList() 
            : null,
        locationInfo: state.locationInfo.isNotEmpty 
            ? Map<String, dynamic>.from(state.locationInfo) 
            : null,
        isScheduled: state.isScheduled.value,
        scheduledTime: state.scheduledTime.value,
        createTime: DateTime.now(),
        updateTime: DateTime.now(),
      );
      
      // 检查是更新还是创建新草稿
      if (state.draftId.value.isNotEmpty) {
        // 更新现有草稿
        final success = await _draftStoreService.updateDraft(
          boxId: state.draftBoxId.value,
          draftId: state.draftId.value,
          draft: draft,
        );
        
        state.isSaving.value = false;
        return success;
      } else {
        // 创建新草稿
        final success = await _draftStoreService.createDraft(
          boxId: state.draftBoxId.value,
          draft: draft,
        );
        
        if (success) {
          state.draftId.value = draft.id;
        }
        
        state.isSaving.value = false;
        return success;
      }
    } catch (e) {
      LoggerUtil.e('保存草稿异常: $e');
      state.isSaving.value = false;
      return false;
    }
  }
} 