class ResponseYK<T> {
  final T? data;
  final dynamic code;
  final String? message;
  final String? url;

  ResponseYK({
    this.code,
    this.message,
    this.data,
    this.url,
  });

  factory ResponseYK.fromJson(
      Map<String, dynamic> json,
      T Function(dynamic json)? fromJsonT,
      ) {
    return ResponseYK<T>(
      code: json['code'] ?? -1,
      message: json['message'] ?? '',
      url: json['url'] ?? '',
      data: (fromJsonT != null && json['data'] != null) ? fromJsonT(json['data']) : null,
    );
  }
}
