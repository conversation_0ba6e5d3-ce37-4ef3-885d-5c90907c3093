import 'dart:io';
import 'package:aitoearn_app/config/app_config.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/network/dio_interceptors.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';

/// 抓包主机
const String PROXY_INFO = '';
// const String PROXY_INFO = 'PROXY *************:8888';

class DioClient {
  static final DioClient _instance = DioClient._internal();
  late final Dio dio;

  factory DioClient() => _instance;

  DioClient._internal() {
    dio = Dio(
      BaseOptions(
        baseUrl: AppConfig.appHostApi,
        connectTimeout: const Duration(seconds: 15),
        receiveTimeout: const Duration(seconds: 15),
        sendTimeout: const Duration(seconds: 15),
      ),
    );

    // Charles 抓包代理配置（仅 PROXY_INFO 不为空 生效）
    if (PROXY_INFO != '') {
      // 使用 IOHttpClientAdapter，此适配器替代了被弃用的 DefaultHttpClientAdapter
      (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        final HttpClient client = HttpClient();
        // 设置 findProxy，返回代理服务器地址；格式形如："PROXY {host};"
        client.findProxy = (Uri uri) {
          return PROXY_INFO;
        };
        // 为了方便调试，信任所有自签名证书（仅限调试环境），生产环境请勿启用
        client.badCertificateCallback =
            (X509Certificate cert, String host, int port) => true;
        return client;
      };
    }

    dio.interceptors.addAll([AppInterceptors()]);
  }

  Future<Response<T>> request<T>(
    String path, {
    required Options options,
    dynamic data,
    Map<String, dynamic>? query,
    bool withToken = true,
  }) {
    try {
      if (!withToken) {
        options.extra = {'isToken': false};
      }
      return dio.request<T>(
        path,
        data: data,
        queryParameters: query,
        options: options,
      );
    } on DioException catch (e) {
      LoggerUtil.e('网络错误：${e.message}');
      rethrow;
    }
  }
}
