import 'package:aitoearn_app/network/dio_client.dart';
import 'package:dio/dio.dart';
import 'package:get/utils.dart';

class Http {
  /// GET 请求，仅返回 data
  static Future<T?> get<T>(
    String url, {
    Map<String, dynamic>? query,
    bool withToken = true,
    Options? options,
  }) async {
    try {
      options ??= Options();
      options.method = 'GET';

      final res = await request<T>(
        url,
        query: query,
        withToken: withToken,
        options: options,
      );
      return res?.data;
    } catch (e) {
      e.printError();
      return null;
    }
  }

  /// POST 请求，仅返回 data
  static Future<T?> post<T>(
    String url, {
    dynamic data,
    bool withToken = true,
    Options? options,
  }) async {
    try {
      options ??= Options();
      options.method = 'POST';

      final res = await request<T>(
        url,
        data: data,
        withToken: withToken,
        options: options,
      );
      return res?.data;
    } catch (e) {
      e.printError();
      return null;
    }
  }

  /// DELETE 请求，仅返回 data
  static Future<T?> delete<T>(
    String url, {
    dynamic data,
    Map<String, dynamic>? query,
    bool withToken = true,
    Options? options,
  }) async {
    try {
      options ??= Options();
      options.method = 'DELETE';

      final res = await request<T>(
        url,
        data: data,
        query: query,
        withToken: withToken,
        options: options,
      );
      return res?.data;
    } catch (e) {
      e.printError();
      return null;
    }
  }

  /// GET 请求，返回完整 Response
  static Future<Response<T>?> getResponse<T>(
    String url, {
    Map<String, dynamic>? query,
    bool withToken = true,
    Options? options,
  }) async {
    try {
      options ??= Options();
      options.method = 'GET';

      return await request<T>(
        url,
        query: query,
        withToken: withToken,
        options: options,
      );
    } catch (e) {
      e.printError();
      return null;
    }
  }

  /// POST 请求，返回完整 Response
  static Future<Response<T>?> postResponse<T>(
    String url, {
    dynamic data,
    bool withToken = true,
    Options? options,
  }) async {
    try {
      options ??= Options();
      options.method = 'POST';

      return await request<T>(
        url,
        data: data,
        withToken: withToken,
        options: options,
      );
    } catch (e) {
      e.printError();
      return null;
    }
  }

  /// DELETE 请求，返回完整 Response
  static Future<Response<T>?> deleteResponse<T>(
    String url, {
    dynamic data,
    Map<String, dynamic>? query,
    bool withToken = true,
    Options? options,
  }) async {
    try {
      options ??= Options();
      options.method = 'DELETE';

      return await request<T>(
        url,
        data: data,
        query: query,
        withToken: withToken,
        options: options,
      );
    } catch (e) {
      e.printError();
      return null;
    }
  }

  /// 封装的通用请求方法（底层）
  static Future<Response<T>?> request<T>(
    String url, {
    required Options options,
    dynamic data,
    Map<String, dynamic>? query,
    bool withToken = true,
  }) async {
    try {
      // 过滤掉值为 null 的 query 参数
      Map<String, dynamic>? filteredQuery;
      if (query != null) {
        filteredQuery = Map.fromEntries(
          query.entries.where((entry) => entry.value != null),
        );
        // 如果过滤后为空，则设为 null
        if (filteredQuery.isEmpty) {
          filteredQuery = null;
        }
      }

      return await DioClient().request<T>(
        url,
        data: data,
        query: filteredQuery,
        options: options,
        withToken: withToken,
      );
    } catch (e) {
      e.printError();
      return null;
    }
  }
}
