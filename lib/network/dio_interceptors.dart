import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;

class AppInterceptors extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    LoggerUtil.i(
      '----- net ${options.uri} 请求：${options.method} | params: ${options.queryParameters} | data: ${options.data}',
    );
    if (options.extra['isToken'] != false) {
      final token = getx.Get.find<UserStoreService>().token;
      if (token.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    }
    // 语言
    options.headers['accept-language'] = getx.Get.locale?.languageCode;
    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    LoggerUtil.i('----- net ${response.requestOptions.uri}【${response.statusCode}】响应：${response.data}');
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    LoggerUtil.e('----- net ${err.requestOptions.uri} 网络异常：${err.message}');
    handler.next(err);
  }
}
