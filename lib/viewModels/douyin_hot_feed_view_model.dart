import 'package:aitoearn_app/api/douyin_hot_feed_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/douyin_models/douyin_hot_feed_model.dart';
import 'package:get/get.dart';

/// 抖音热门Feed视图模型
class DouyinHotFeedViewModel extends GetxController {
  /// API服务
  final DouyinHotFeedApi _api = DouyinHotFeedApi();

  /// 热门Feed列表
  final RxList<DouyinHotFeedItem> hotFeedList = <DouyinHotFeedItem>[].obs;

  /// 加载状态
  final RxBool isLoading = false.obs;

  /// 错误信息
  final RxString errorMessage = ''.obs;

  /// 获取抖音热门Feed
  ///
  /// [cookie] 用户Cookie
  /// [refresh] 是否刷新，默认为false
  Future<bool> getHotFeed({
    required String cookie,
    bool refresh = false,
  }) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      // 如果是刷新，清空列表
      if (refresh) {
        hotFeedList.clear();
      }

      LoggerUtil.i('【DouyinHotFeedViewModel】开始获取抖音热门Feed');

      // 调用API获取热门Feed
      final response = await _api.getHotFeed(cookie: cookie);

      // 检查响应状态
      if (response.success && response.data != null) {
        // 根据是否刷新决定是覆盖还是追加数据
        if (refresh) {
          hotFeedList.value = response.data!;
        } else {
          hotFeedList.addAll(response.data!);
        }
        LoggerUtil.i(
          '【DouyinHotFeedViewModel】获取抖音热门Feed成功，数据条数: ${response.data!.length}',
        );
        return true;
      } else {
        errorMessage.value = response.msg ?? '获取抖音热门Feed失败';
        LoggerUtil.e('【DouyinHotFeedViewModel】获取抖音热门Feed失败: ${response.msg}');
        return false;
      }
    } catch (e, stackTrace) {
      errorMessage.value = '获取抖音热门Feed异常: $e';
      LoggerUtil.e('【DouyinHotFeedViewModel】获取抖音热门Feed异常: $e\n$stackTrace');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载更多内容
  ///
  /// [cookie] 用户Cookie
  Future<bool> loadMore(String cookie) async {
    // 调用getHotFeed加载更多内容，不刷新
    return getHotFeed(cookie: cookie, refresh: false);
  }

  /// 清空数据
  void clear() {
    hotFeedList.clear();
    errorMessage.value = '';
  }
}
