import 'package:aitoearn_app/api/platform_service.dart';
import 'package:aitoearn_app/models/hot_events_models/hot_events_models.dart';
import 'package:aitoearn_app/models/ranking_date_model.dart';
import 'package:flutter/foundation.dart';

class PlatViewModel extends ChangeNotifier {
  final PlatformService _platformService = PlatformService();

  List<CustomPlatform> _platforms = [];
  CustomPlatform? _selectedPlatform;
  final List<String> _labels = [];
  final List<RankingDate> _rankingDates = [];
  bool _isLoading = false;
  bool _isLoadingDetails = false;
  final bool _isLoadingRankings = false;
  final bool _isLoadingRankingDetails = false;
  final bool _isLoadingLabels = false;
  final bool _isLoadingDates = false;
  String _error = '';
  String _detailsError = '';
  final String _rankingsError = '';
  final String _rankingDetailsError = '';
  final String _labelsError = '';
  final String _datesError = '';
  String _currentLanguage = 'zh';

  List<CustomPlatform> get platforms => _platforms;
  CustomPlatform? get selectedPlatform => _selectedPlatform;
  List<String> get labels => _labels;
  List<RankingDate> get rankingDates => _rankingDates;
  bool get isLoading => _isLoading;
  bool get isLoadingDetails => _isLoadingDetails;
  bool get isLoadingRankings => _isLoadingRankings;
  bool get isLoadingRankingDetails => _isLoadingRankingDetails;
  bool get isLoadingLabels => _isLoadingLabels;
  bool get isLoadingDates => _isLoadingDates;
  String get error => _error;
  String get detailsError => _detailsError;
  String get rankingsError => _rankingsError;
  String get rankingDetailsError => _rankingDetailsError;
  String get labelsError => _labelsError;
  String get datesError => _datesError;
  String get currentLanguage => _currentLanguage;

  // Future<void> fetchPlatforms({String? language}) async {
  //   try {
  //     _isLoading = true;
  //     _error = '';
  //     notifyListeners();
  //
  //     if (language != null) {
  //       _currentLanguage = language;
  //     }
  //
  //     _platforms = await _platformService.getPlatforms(
  //       language: _currentLanguage,
  //     );
  //     _error = '';
  //   } catch (e) {
  //     _error = e.toString();
  //     _platforms = [];
  //   } finally {
  //     _isLoading = false;
  //     notifyListeners();
  //   }
  // }
  //
  // Future<void> fetchPlatformDetails(String platformId) async {
  //   try {
  //     _isLoadingDetails = true;
  //     _detailsError = '';
  //     notifyListeners();
  //
  //     _selectedPlatform = await _platformService.getPlatformDetails(
  //       platformId,
  //       language: _currentLanguage,
  //     );
  //   } catch (e) {
  //     _detailsError = e.toString();
  //     _selectedPlatform = null;
  //   } finally {
  //     _isLoadingDetails = false;
  //     notifyListeners();
  //   }
  // }
}
