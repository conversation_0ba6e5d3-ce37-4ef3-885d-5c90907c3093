import 'package:aitoearn_app/api/models/base_response.dart';
import 'package:aitoearn_app/api/platform_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_home_feed_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 小红书内容视图模型
class XhsContentViewModel extends GetxController {
  /// 平台服务
  final PlatformService _platformService = PlatformService();

  /// 加载状态
  final RxBool isLoading = false.obs;

  /// 错误信息
  final RxString errorMessage = ''.obs;

  /// 搜索关键词
  final RxString keyword = ''.obs;

  /// 当前页码
  final RxInt currentPage = 1.obs;

  /// 是否有更多数据
  final RxBool hasMore = true.obs;

  /// 内容列表
  final RxList<XhsNoteItem> contentList = <XhsNoteItem>[].obs;

  /// 首页热门作品列表
  final RxList<XhsHomeFeedItem> homeFeedList = <XhsHomeFeedItem>[].obs;

  /// 首页热门作品光标，用于分页
  final RxString cursorScore = ''.obs;

  /// 设置关键词
  void setKeyword(String value) {
    keyword.value = value;
  }

  /// 重置搜索
  void resetSearch() {
    currentPage.value = 1;
    contentList.clear();
    hasMore.value = true;
    errorMessage.value = '';
  }

  /// 重置首页热门作品
  void resetHomeFeed() {
    homeFeedList.clear();
    cursorScore.value = '';
    errorMessage.value = '';
  }

  /// 搜索小红书内容
  ///
  /// [accountId] 账号ID
  /// [refresh] 是否刷新（重新加载第一页）
  /// 返回是否成功
  Future<bool> searchContent({
    required String accountId,
    required String keyword,
    bool reset = true,
  }) async {
    try {
      // 设置加载状态
      isLoading.value = true;

      // 重置状态
      if (reset) {
        contentList.clear();
        currentPage.value = 1;
        hasMore.value = true;
        this.keyword.value = keyword;
      }

      // 调用API搜索内容
      final response = await _platformService.searchXhsContent(
        accountId: accountId,
        keyword: keyword,
        page: currentPage.value,
        pageSize: 20,
      );

      // 处理响应
      if (response.code == 0 && response.data != null) {
        // 添加内容到列表
        contentList.addAll(response.data!.items);

        // 更新是否有更多内容
        hasMore.value = response.data!.hasMore;

        // 返回成功
        return true;
      } else {
        // 设置错误信息
        errorMessage.value = response.msg ?? '搜索失败';
        return false;
      }
    } catch (e) {
      // 设置错误信息
      errorMessage.value = '搜索异常: $e';
      return false;
    } finally {
      // 重置加载状态
      isLoading.value = false;
    }
  }

  /// 获取小红书首页热门作品列表
  ///
  /// [accountId] 账号ID
  /// [refresh] 是否刷新（重新加载第一页）
  /// 返回是否成功
  Future<bool> getHomeFeed({
    required String accountId,
    bool refresh = false,
  }) async {
    try {
      // 设置加载状态
      isLoading.value = true;

      // 重置状态
      if (refresh) {
        homeFeedList.clear();
        cursorScore.value = '';
      }

      // 调用API获取首页热门作品
      final response = await _platformService.getXhsHomeFeed(
        accountId: accountId,
        cursorScore: cursorScore.value,
        num: 18,
        refreshType: refresh ? 1 : 2,
      );

      // 处理响应
      if (response.code == 0 && response.data != null) {
        // 添加内容到列表
        homeFeedList.addAll(response.data!.items);

        // 更新光标值用于分页
        if (response.data!.cursorScore.isNotEmpty) {
          cursorScore.value = response.data!.cursorScore;
        }

        // 返回成功
        return true;
      } else {
        // 设置错误信息
        errorMessage.value = response.msg ?? '获取热门作品失败';
        return false;
      }
    } catch (e) {
      // 设置错误信息
      errorMessage.value = '获取热门作品异常: $e';
      return false;
    } finally {
      // 重置加载状态
      isLoading.value = false;
    }
  }

  /// 加载更多内容
  ///
  /// [accountId] 账号ID
  Future<bool> loadMore(String accountId) async {
    // 如果没有更多内容或者正在加载，则返回
    if (!hasMore.value || isLoading.value) {
      return false;
    }

    // 如果有关键词，加载搜索结果的更多内容
    if (keyword.value.isNotEmpty) {
      // 增加页码
      currentPage.value++;

      // 搜索内容
      return await searchContent(
        accountId: accountId,
        keyword: keyword.value,
        reset: false,
      );
    }
    // 否则，加载更多首页热门作品
    else {
      return await getHomeFeed(accountId: accountId, refresh: false);
    }
  }
}
