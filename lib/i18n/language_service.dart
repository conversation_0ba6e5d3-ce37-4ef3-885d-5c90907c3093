import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

// i18n/language_service.dart
class LanguageService {
  static const _storageKey = 'selected_locale';

  static const supportedLocales = [
    Locale('zh', 'CN'),
    Locale('en', 'US'),
  ];

  static bool isSystemLocale = true;

  /// 初始化语言（首次启动时调用）
  static Future<void> initLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final langCode = prefs.getString(_storageKey);

    if (langCode == null || langCode == 'system') {
      isSystemLocale = true;
      final systemLocale = Get.deviceLocale ?? const Locale('zh', 'CN');
      Get.updateLocale(systemLocale);
    } else {
      isSystemLocale = false;
      final parts = langCode.split('_');
      Get.updateLocale(Locale(parts[0], parts.length > 1 ? parts[1] : ''));
    }
  }

  /// 切换语言并保存（"system" 表示跟随系统）
  static Future<void> switchLocale(String langCode) async {
    final prefs = await SharedPreferences.getInstance();
    if (langCode == 'system') {
      isSystemLocale = true;
      await prefs.remove(_storageKey);
      final systemLocale = Get.deviceLocale ?? const Locale('zh', 'CN');
      Get.updateLocale(systemLocale);
    } else {
      isSystemLocale = false;
      await prefs.setString(_storageKey, langCode);
      final parts = langCode.split('_');
      Get.updateLocale(Locale(parts[0], parts.length > 1 ? parts[1] : ''));
    }
  }

  static String getCurrentLanguageText() {
    if (isSystemLocale) {
      return 'followSystem'.tr;
    }
    final locale = Get.locale;
    if (locale?.languageCode == 'zh') {
      return '简体中文';
    }
    if (locale?.languageCode == 'en') {
      return 'English';
    }
    return 'followSystem'.tr;
  }
}