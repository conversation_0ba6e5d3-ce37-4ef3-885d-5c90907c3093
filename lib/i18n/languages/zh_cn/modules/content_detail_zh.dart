/// 内容详情页相关翻译
const Map<String, String> contentDetailZhCnLangs = {
  // 通用
  'content.back': '返回',
  
  // 内容详情页
  'content.comments': '评论',
  'content.comments.count': '评论 (@count)',
  'content.no.comments': '暂无评论',
  'content.no.filtered.comments': '没有符合筛选条件的评论',
  'content.no.more.comments': '没有更多评论了',
  'content.comment.placeholder': '说点什么',
  'content.reply.to': '回复 @user',
  'content.filtered.comments': '已筛选 @count 条评论',
  'content.clear.filter': '清除筛选',
  'content.filter': '筛选',
  'content.reply': '回复',
  
  // 表情选择器
  'content.emoji.title': '表情',
  'content.emoji.tab.common': '常用',
  'content.emoji.tab.all': '表情',
  'content.emoji.tab.popular': '热门',
  'content.emoji.tab.animals': '动物',
  'content.emoji.close': '关闭',
  
  // 表情分类
  'content.emoji.category.face': '表情',
  'content.emoji.category.gesture': '手势',
  'content.emoji.category.symbol': '符号',

  // 评论输入
  'content.send': '发送',

  // 时间格式
  'content.unknown_time': '未知时间',
  'content.date.month_day': '@month月@day日',
  'content.date.year_month_day': '@year年@month月@day日',
  'content.count.wan': '@value万',
  'content.time.years_ago': '@count年前',
  'content.time.months_ago': '@count个月前',
  'content.time.days_ago': '@count天前',
  'content.time.hours_ago': '@count小时前',
  'content.time.minutes_ago': '@count分钟前',
  'content.time.just_now': '刚刚',
  
  // 错误信息
  'content.error.invalid_note': '无效的笔记数据',
  'content.error.no_xhs_account': '未找到小红书账号',
  'content.error.get_detail_failed': '获取详情失败',
  
  // 评论操作
  'content.leave_comment': '留下你的想法吧',
  'content.comment.success': '评论发送成功',
  'content.comment.failed': '评论发送失败: @message',
  'content.like.success': '点赞成功',
  'content.unlike.success': '取消点赞成功',
  'content.operation.failed': '操作失败: @message',
  'content.like.failed': '点赞操作失败: @message',
  'content.like.exception': '点赞异常: @message',
  'content.hint': '提示',
  
  // 抖音内容详情页
  'content.douyin.one_click_reply': '一键回复',
  'content.douyin.load_more': '加载更多',
  'content.douyin.loading_replies': '加载回复中...',
  'content.douyin.view_all_replies': '查看全部 @count 条回复',
  
  // 抖音评论筛选
  'content.douyin.filter.last_sync_time': '上次同步时间',
  'content.douyin.filter.sync_all': '同步全部评论',
  'content.douyin.filter.only_unreplied': '只看未回复',
  'content.douyin.filter.comment_time': '评论时间',
  'content.douyin.filter.to': '至',
  'content.douyin.filter.likes_count': '点赞数量',
  'content.douyin.filter.min_value': '最小值',
  'content.douyin.filter.max_value': '最大值',
  'content.douyin.filter.keywords': '关键字匹配',
  'content.douyin.filter.add_keyword': '添加关键词',
  'content.douyin.filter.add_keyword_title': '添加关键词',
  'content.douyin.filter.enter_keyword': '请输入关键词',
  'content.douyin.filter.cancel': '取消',
  'content.douyin.filter.add': '添加',
  'content.douyin.filter.reset': '重置',
  'content.douyin.filter.confirm': '确定',
  'content.douyin.filter.start_time': '开始时间',
  'content.douyin.filter.end_time': '结束时间',
  
  // 抖音一键回复
  'content.douyin.quick_reply.title': '一键回复',
  'content.douyin.quick_reply.cancel': '取消',
  'content.douyin.quick_reply.confirm': '确定',
  'content.douyin.quick_reply.time_interval': '回复时间间隔',
  'content.douyin.quick_reply.comment_selection': '回复评论选择',
  'content.douyin.quick_reply.select_comment': '选择评论',
  'content.douyin.quick_reply.selected_comments': '已选择 @count 条评论',
  'content.douyin.quick_reply.reply_content': '回复内容',
  'content.douyin.quick_reply.time.seconds': '@count秒',
  'content.douyin.quick_reply.time.minute': '1分钟',
  'content.douyin.quick_reply.time.minutes': '@count分钟',
  'content.douyin.quick_reply.time.hour': '1小时',
  'content.douyin.quick_reply.select_time_interval': '选择回复时间间隔',
  'content.douyin.quick_reply.select_comments_title': '选择要回复的评论',
  'content.douyin.quick_reply.select_all': '全选',
  'content.douyin.quick_reply.unselect_all': '取消全选',
  'content.douyin.quick_reply.selected_count': '已选择 @selected/@total',
  'content.douyin.quick_reply.batch_ai_reply': '批量AI回复',
  'content.douyin.quick_reply.batch_ai_desc': '为每条选中的评论生成不同的AI回复内容',
  'content.douyin.quick_reply.generating': 'AI生成中...',
  'content.douyin.quick_reply.batch_generate': '批量生成AI回复',
  'content.douyin.quick_reply.no_comments': '暂无评论可选择',
  'content.douyin.quick_reply.user': '用户',
  'content.douyin.quick_reply.comment_content': '评论内容',
  'content.douyin.quick_reply.ai_reply_content': 'AI回复内容:',
  'content.douyin.quick_reply.replied': '已回复: ',
  'content.douyin.quick_reply.edit_reply': '编辑回复内容',
  'content.douyin.quick_reply.enter_reply': '请输入回复内容',
  'content.douyin.quick_reply.ai_smart_reply': 'AI智能回复',
  'content.douyin.quick_reply.ai_generated': 'AI生成的回复',
  'content.douyin.quick_reply.quick_templates': '快速模板',
  'content.douyin.quick_reply.save': '保存',
  'content.douyin.quick_reply.confirm_batch': '确认批量回复',
  'content.douyin.quick_reply.confirm_batch_message': '您将回复@count条评论，每条评论间隔@interval，确定继续吗？',
  'content.douyin.quick_reply.enter_reply_first': '请先输入回复内容',
  'content.douyin.quick_reply.batch_replying': '批量回复中',
  'content.douyin.quick_reply.replying_progress': '正在回复 (@current/@total)',
  'content.douyin.quick_reply.batch_complete': '批量回复完成',
  'content.douyin.quick_reply.success_fail': '成功: @success, 失败: @fail',
  'content.douyin.quick_reply.ai_batch_title': 'AI批量生成回复中',
  'content.douyin.quick_reply.generating_progress': '正在生成 (@current/@total)',
  'content.douyin.quick_reply.generate_success': '成功为@count条评论生成了AI回复',
  'content.douyin.quick_reply.generate_complete': '生成完成',
  'content.douyin.quick_reply.generate_failed': '生成失败',
  'content.douyin.quick_reply.ai_generate_failed': '生成AI回复失败',
  'content.douyin.quick_reply.generate_success_single': '已为该评论生成AI回复',
  'content.douyin.quick_reply.default_reply': '感谢支持！',
  
  // 抖音一键回复 - 回复模板
  'content.douyin.quick_reply.reply_template_1': '感谢支持！',
  'content.douyin.quick_reply.reply_template_2': '谢谢观看，欢迎点赞关注',
  'content.douyin.quick_reply.reply_template_3': '感谢评论，后续会有更多精彩内容',
  'content.douyin.quick_reply.reply_template_4': '您的建议我们会认真考虑',
  'content.douyin.quick_reply.reply_template_5': '感谢反馈，我们会继续努力',
  'content.douyin.quick_reply.reply_template_6': '感谢关注，更多精彩内容敬请期待',
  'content.douyin.quick_reply.reply_template_7': '已收到您的反馈，我们会尽快处理',
  'content.douyin.quick_reply.reply_template_8': '谢谢支持，我们会做得更好',
  'content.douyin.quick_reply.reply_template_9': '欢迎继续关注我们的更新',
  'content.douyin.quick_reply.reply_template_10': '谢谢，我们会持续创作优质内容',
  'content.douyin.quick_reply.input_reply_hint': '请输入回复内容',
  'content.douyin.quick_reply.no_comments_to_select': '暂无评论可选择',
  'content.douyin.quick_reply.ai_generating': 'AI生成中...',
  'content.douyin.quick_reply.batch_generate_ai_reply': '批量生成AI回复',
  'content.douyin.quick_reply.generate_ai_reply_failed': '生成AI回复失败',
  'content.douyin.quick_reply.batch_ai_reply_desc': '为每条选中的评论生成不同的AI回复内容',
  'content.douyin.quick_reply.edit_reply_content': '编辑回复内容',
}; 