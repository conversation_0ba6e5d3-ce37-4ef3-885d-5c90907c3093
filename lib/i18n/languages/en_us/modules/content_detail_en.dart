/// 内容详情页相关翻译
const Map<String, String> contentDetailEnUsLangs = {
  // Common
  'content.back': 'Back',
  
  // Content Detail Page
  'content.comments': 'Comments',
  'content.comments.count': 'Comments (@count)',
  'content.no.comments': 'No comments yet',
  'content.no.filtered.comments': 'No comments match your filter',
  'content.no.more.comments': 'No more comments',
  'content.comment.placeholder': 'Say something...',
  'content.reply.to': 'Reply to @user',
  'content.filtered.comments': 'Filtered @count comments',
  'content.clear.filter': 'Clear filter',
  'content.filter': 'Filter',
  'content.reply': 'Reply',
  
  // Emoji Picker
  'content.emoji.title': 'Emoji',
  'content.emoji.tab.common': 'Common',
  'content.emoji.tab.all': 'All',
  'content.emoji.tab.popular': 'Popular',
  'content.emoji.tab.animals': 'Animals',
  'content.emoji.close': 'Close',
  
  // Emoji Categories
  'content.emoji.category.face': 'Face',
  'content.emoji.category.gesture': 'Gesture',
  'content.emoji.category.symbol': 'Symbol',

  // Comment Input
  'content.send': 'Send',

  // Time Format
  'content.unknown_time': 'Unknown time',
  'content.date.month_day': '@month/@day',
  'content.date.year_month_day': '@month/@day/@year',
  'content.count.wan': '@value0K',
  'content.time.years_ago': '@count years ago',
  'content.time.months_ago': '@count months ago',
  'content.time.days_ago': '@count days ago',
  'content.time.hours_ago': '@count hours ago',
  'content.time.minutes_ago': '@count minutes ago',
  'content.time.just_now': 'Just now',
  
  // Error Messages
  'content.error.invalid_note': 'Invalid note data',
  'content.error.no_xhs_account': 'No Xiaohongshu account found',
  'content.error.get_detail_failed': 'Failed to get details',
  
  // Comment Operations
  'content.leave_comment': 'Leave your thoughts...',
  'content.comment.success': 'Comment sent successfully',
  'content.comment.failed': 'Failed to send comment: @message',
  'content.like.success': 'Like success',
  'content.unlike.success': 'Unlike success',
  'content.operation.failed': 'Operation failed: @message',
  'content.like.failed': 'Like failed: @message',
  'content.like.exception': 'Exception: @message',
  'content.hint': 'Hint',
  
  // Douyin Content Detail Page
  'content.douyin.one_click_reply': 'Quick Reply',
  'content.douyin.load_more': 'Load More',
  'content.douyin.loading_replies': 'Loading replies...',
  'content.douyin.view_all_replies': 'View all @count replies',
  
  // Douyin Comment Filter
  'content.douyin.filter.last_sync_time': 'Last Sync Time',
  'content.douyin.filter.sync_all': 'Sync All Comments',
  'content.douyin.filter.only_unreplied': 'Unreplied Only',
  'content.douyin.filter.comment_time': 'Comment Time',
  'content.douyin.filter.to': 'to',
  'content.douyin.filter.likes_count': 'Likes Count',
  'content.douyin.filter.min_value': 'Min',
  'content.douyin.filter.max_value': 'Max',
  'content.douyin.filter.keywords': 'Keywords',
  'content.douyin.filter.add_keyword': 'Add keyword',
  'content.douyin.filter.add_keyword_title': 'Add Keyword',
  'content.douyin.filter.enter_keyword': 'Enter keyword',
  'content.douyin.filter.cancel': 'Cancel',
  'content.douyin.filter.add': 'Add',
  'content.douyin.filter.reset': 'Reset',
  'content.douyin.filter.confirm': 'Confirm',
  'content.douyin.filter.start_time': 'Start time',
  'content.douyin.filter.end_time': 'End time',
  
  // Douyin Quick Reply
  'content.douyin.quick_reply.title': 'Quick Reply',
  'content.douyin.quick_reply.cancel': 'Cancel',
  'content.douyin.quick_reply.confirm': 'Confirm',
  'content.douyin.quick_reply.time_interval': 'Reply Interval',
  'content.douyin.quick_reply.comment_selection': 'Comment Selection',
  'content.douyin.quick_reply.select_comment': 'Select comments',
  'content.douyin.quick_reply.selected_comments': 'Selected @count comments',
  'content.douyin.quick_reply.reply_content': 'Reply Content',
  'content.douyin.quick_reply.time.seconds': '@count seconds',
  'content.douyin.quick_reply.time.minute': '1 minute',
  'content.douyin.quick_reply.time.minutes': '@count minutes',
  'content.douyin.quick_reply.time.hour': '1 hour',
  'content.douyin.quick_reply.select_time_interval': 'Select Reply Interval',
  'content.douyin.quick_reply.select_comments_title': 'Select Comments to Reply',
  'content.douyin.quick_reply.select_all': 'Select All',
  'content.douyin.quick_reply.unselect_all': 'Unselect All',
  'content.douyin.quick_reply.selected_count': 'Selected @selected/@total',
  'content.douyin.quick_reply.batch_ai_reply': 'Batch AI Reply',
  'content.douyin.quick_reply.batch_ai_desc': 'Generate unique AI reply for each selected comment',
  'content.douyin.quick_reply.generating': 'Generating...',
  'content.douyin.quick_reply.batch_generate': 'Generate AI Replies',
  'content.douyin.quick_reply.no_comments': 'No comments available',
  'content.douyin.quick_reply.user': 'User',
  'content.douyin.quick_reply.comment_content': 'Comment content',
  'content.douyin.quick_reply.ai_reply_content': 'AI Reply Content:',
  'content.douyin.quick_reply.replied': 'Replied: ',
  'content.douyin.quick_reply.edit_reply': 'Edit Reply Content',
  'content.douyin.quick_reply.enter_reply': 'Enter reply content',
  'content.douyin.quick_reply.ai_smart_reply': 'AI Smart Reply',
  'content.douyin.quick_reply.ai_generated': 'AI Generated Replies',
  'content.douyin.quick_reply.quick_templates': 'Quick Templates',
  'content.douyin.quick_reply.save': 'Save',
  'content.douyin.quick_reply.confirm_batch': 'Confirm Batch Reply',
  'content.douyin.quick_reply.confirm_batch_message': 'You will reply to @count comments with @interval intervals. Continue?',
  'content.douyin.quick_reply.enter_reply_first': 'Please enter reply content first',
  'content.douyin.quick_reply.batch_replying': 'Batch Replying',
  'content.douyin.quick_reply.replying_progress': 'Replying (@current/@total)',
  'content.douyin.quick_reply.batch_complete': 'Batch Reply Complete',
  'content.douyin.quick_reply.success_fail': 'Success: @success, Failed: @fail',
  'content.douyin.quick_reply.ai_batch_title': 'AI Batch Generation',
  'content.douyin.quick_reply.generating_progress': 'Generating (@current/@total)',
  'content.douyin.quick_reply.generate_success': 'Successfully generated AI replies for @count comments',
  'content.douyin.quick_reply.generate_complete': 'Generation Complete',
  'content.douyin.quick_reply.generate_failed': 'Generation Failed',
  'content.douyin.quick_reply.ai_generate_failed': 'Failed to generate AI reply',
  'content.douyin.quick_reply.generate_success_single': 'AI reply generated for this comment',
  'content.douyin.quick_reply.default_reply': 'Thank you for your support!',
  
  // Douyin Quick Reply - Reply Templates
  'content.douyin.quick_reply.reply_template_1': 'Thank you for your support!',
  'content.douyin.quick_reply.reply_template_2': 'Thanks for watching, please like and follow',
  'content.douyin.quick_reply.reply_template_3': 'Thank you for your comment, more great content coming soon',
  'content.douyin.quick_reply.reply_template_4': 'We will seriously consider your suggestion',
  'content.douyin.quick_reply.reply_template_5': 'Thanks for your feedback, we will keep improving',
  'content.douyin.quick_reply.reply_template_6': 'Thanks for following, stay tuned for more exciting content',
  'content.douyin.quick_reply.reply_template_7': 'We have received your feedback and will address it soon',
  'content.douyin.quick_reply.reply_template_8': 'Thank you for your support, we will do better',
  'content.douyin.quick_reply.reply_template_9': 'Welcome to continue following our updates',
  'content.douyin.quick_reply.reply_template_10': 'Thanks, we will continue to create quality content',
  'content.douyin.quick_reply.input_reply_hint': 'Enter reply content',
  'content.douyin.quick_reply.no_comments_to_select': 'No comments available for selection',
  'content.douyin.quick_reply.ai_generating': 'AI generating...',
  'content.douyin.quick_reply.batch_generate_ai_reply': 'Generate AI replies in batch',
  'content.douyin.quick_reply.generate_ai_reply_failed': 'Failed to generate AI reply',
  'content.douyin.quick_reply.batch_ai_reply_desc': 'Generate different AI replies for each selected comment',
  'content.douyin.quick_reply.edit_reply_content': 'Edit Reply Content',
}; 