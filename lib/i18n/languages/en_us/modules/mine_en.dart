/// Mine Page
const Map<String, String> mineEnUs = {
  // UserAction
  'mine.action.qr_code': 'QR',
  'mine.action.share': 'Share',
  'mine.action.message': 'Message',
  'mine.action.settings': 'Settings',

  // VipCard
  'mine.vip.total_earning': 'Total Earning',
  'mine.vip.currency_unit': 'USD',
  'mine.vip.renew': 'Renew',
  'mine.vip.upgrade': 'Upgrade',
  'mine.vip.balance': 'Balance',
  'mine.vip.points': 'Points',
  'mine.vip.coupon': 'Coupon',

  // UserBuckets
  'mine.buckets.no_more_data': 'No more data',
  'mine.buckets.load_more': 'Tap to load more',
  'mine.buckets.image_library': 'Images',
  'mine.buckets.video_library': 'Videos',
  'mine.buckets.draft_box': 'Drafts',
  'mine.buckets.create_image_library': 'Create Image Library',
  'mine.buckets.create_video_library': 'Create Video Library',
  'mine.buckets.create_draft_box': 'Create Draft Box',
  
  // Form Fields
  'mine.form.image_library_title': 'Image Library Title',
  'mine.form.image_library_desc': 'Image Library Description',
  'mine.form.video_library_title': 'Video Library Title',
  'mine.form.video_library_desc': 'Video Library Description',
  'mine.form.draft_box_title': 'Draft Box Title',
  'mine.form.enter_image_library_name': 'Enter image library name',
  'mine.form.enter_image_library_desc': 'Enter image library description',
  'mine.form.enter_video_library_name': 'Enter video library name',
  'mine.form.enter_video_library_desc': 'Enter video library description',
  'mine.form.enter_draft_box_name': 'Enter draft box name',
  
  // Dialog Titles
  'mine.dialog.create_image_library': 'Create Image Library',
  'mine.dialog.create_video_library': 'Create Video Library',
  'mine.dialog.create_draft_box': 'Create Draft Box',
  'mine.dialog.update_image_library': 'Update Image Library',
  'mine.dialog.update_video_library': 'Update Video Library',
  'mine.dialog.update_draft_box': 'Update Draft Box',
  'mine.dialog.confirm_delete_image_library': 'Are you sure you want to delete this image library?',
  'mine.dialog.confirm_delete_video_library': 'Are you sure you want to delete this video library?',
  'mine.dialog.confirm_delete_draft_box': 'Are you sure you want to delete this draft?',
  
  // Operation Tips
  'mine.toast.create_success': 'Created successfully',
  'mine.toast.update_success': 'Updated successfully',
  'mine.toast.delete_success': 'Deleted successfully',
  'mine.toast.create_failed': 'Create failed',
  'mine.toast.update_failed': 'Update failed',
  'mine.toast.delete_failed': 'Delete failed',
}; 