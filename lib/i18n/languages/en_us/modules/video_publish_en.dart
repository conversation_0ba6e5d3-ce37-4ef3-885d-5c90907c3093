/// 视频发布页面相关翻译
const Map<String, String> videoPublishEnUsLangs = {
  // Page title and navigation
  'video_publish.title': 'Publish',
  'video_publish.back': 'Back',
  
  // Media selection
  'video_publish.select_from_album': 'Select Photos from Album',
  'video_publish.select_video_from_album': 'Select Video from Album',
  'video_publish.select_from_draft': 'Select from Drafts',
  
  // Input prompts
  'video_publish.add_video_title': 'Add video title',
  'video_publish.add_video_description': 'Add video description',
  
  // Publishing options
  'video_publish.publish_options': 'Select Publishing Method',
  'video_publish.publish_now': 'Publish Now',
  'video_publish.publish_now_desc': 'Stay on this page and view publishing progress',
  'video_publish.publish_background': 'Publish in Background',
  'video_publish.publish_background_desc': 'Return to home and continue publishing in background',
  'video_publish.cancel': 'Cancel',
  
  // Advanced settings
  'video_publish.advanced_settings': 'Advanced Settings',
  
  // Errors and notifications
  'video_publish.video_file_not_exist': 'Video file does not exist or has been deleted',
  'video_publish.image_file_not_exist': 'Image file does not exist or has been deleted',
  'video_publish.load_draft_failed': 'Failed to load draft: @error',
  
  // ContentPreviewSection
  'video_publish.add_media': 'Tap to add photos or video',
  'video_publish.add': 'Add',
  'video_publish.video_selected': 'Video selected',
  
  // MediaPreviewSection - New
  'video_publish.add_image_or_video': 'Tap to add photos or video',
  'video_publish.edit_cover': 'Edit Cover',
  'video_publish.custom_cover': 'Custom Cover',
  'video_publish.reorder_tip': 'Press and hold to drag and reorder',
  
  // AccountSection
  'video_publish.select_account': 'Select publishing account',
  'video_publish.unknown_account': 'Unknown account',
  'video_publish.add_account': 'Add account',
  
  // TagSection
  'video_publish.topic': '#Topic',
  'video_publish.ai_write': 'AI Write',
  
  // LocationSection
  'video_publish.add_location': 'Add location',
  'video_publish.not_set': 'Not set',
  'video_publish.please_select_account': 'Please select a publishing account first',
  'video_publish.incomplete_account': 'Selected account has incomplete information',
  'video_publish.location_not_supported': 'Location selection not supported for this platform',
  'video_publish.please_select_platform_account': 'Please select a @platform account first',
  
  // ScheduleSection
  'video_publish.scheduled_publish': 'Schedule publishing',
  'video_publish.today': 'Today',
  'video_publish.tomorrow': 'Tomorrow',
  'video_publish.month_day': '@month/@day',
  
  // BottomActionBar
  'video_publish.save_draft': 'Save Draft',
  'video_publish.content_safety_check': 'Safety Check',
  'video_publish.checking': 'Checking...',
  'video_publish.one_click_publish': 'Publish',
  'video_publish.publishing': 'Publishing...',
  'video_publish.please_select_media': 'Please select a video or photos first',
  'video_publish.draft_updated': 'Draft updated',
  'video_publish.draft_update_failed': 'Failed to update draft',
  'video_publish.saved_to_draft': 'Saved to drafts',
  'video_publish.save_draft_failed': 'Failed to save draft',
  
  // ScheduleTimePicker
  'video_publish.schedule.cancel': 'Cancel',
  'video_publish.schedule.title': 'Publish Time (Beijing Time)',
  'video_publish.schedule.confirm': 'Confirm',
  'video_publish.schedule.note': '* Content will be reviewed by the platform before scheduled publishing.',
  'video_publish.schedule.today': 'Today',
  'video_publish.schedule.tomorrow': 'Tomorrow',
  'video_publish.schedule.day_after': 'Day after tomorrow',
  'video_publish.schedule.month_day': '@month/@day',
  
  // PublishLogic
  'video_publish.logic.error': 'Error',
  'video_publish.logic.please_select_media': 'Please select a video or photos first',
  'video_publish.logic.please_select_account': 'Please select a publishing account first',
  'video_publish.logic.please_add_title': 'Please add a video title',
  'video_publish.logic.notice': 'Notice',
  'video_publish.logic.background_publish': 'Background Publishing',
  'video_publish.logic.background_notice': 'Content will be published in the background while you continue using the app',
  'video_publish.logic.publish_failed': 'Publishing Failed',
  'video_publish.logic.publishing': 'Publishing',
  'video_publish.logic.publish_progress': 'Publishing content to @count platforms, please wait...',
  'video_publish.logic.publish_result': 'Publishing Result',
  'video_publish.logic.total_tasks': 'Total: @count tasks',
  'video_publish.logic.success': 'Success: @count',
  'video_publish.logic.failed': 'Failed: @count',
  'video_publish.logic.close': 'Close',
  'video_publish.logic.view_details': 'View Details',
  'video_publish.logic.background_complete': 'Publishing Complete',
  'video_publish.logic.publish_success_count': '@count items published successfully',
  'video_publish.logic.publish_partial': 'Partially Published',
  'video_publish.logic.success_failed': 'Success: @success, Failed: @failed',
  'video_publish.logic.publish_success': 'Publishing Success',
  'video_publish.logic.publish_platform_count': 'Content published successfully to @count platforms',
  'video_publish.logic.ai_write': 'AI Writing',
  'video_publish.logic.ai_generating': 'AI is generating content...',
  'video_publish.logic.use': 'Use',
  'video_publish.logic.generate_thumbnail_failed': 'Failed to generate video thumbnail: @error',
  'video_publish.logic.untitled_draft': 'Untitled Draft',
  'video_publish.logic.update_draft': 'Updating draft: @id',
  'video_publish.logic.create_new_draft': 'Creating new draft',
  'video_publish.logic.save_draft_failed': 'Failed to save draft: @error',
  'video_publish.logic.image_draft_title': 'Image Draft @date',
  'video_publish.logic.image_draft_desc': 'Image draft saved from test page',
  'video_publish.logic.image_draft_failed': 'Failed to save image draft: @error',
  'video_publish.logic.clear_all': 'Clearing all content',
  
  // AccountSelectDialog
  'video_publish.account_select.cancel': 'Cancel',
  'video_publish.account_select.title': 'Select Publishing Accounts',
  'video_publish.account_select.confirm': 'Confirm',
  'video_publish.account_select.selected_count': '@count accounts selected',
  'video_publish.account_select.clear': 'Clear',
  'video_publish.account_select.search': 'Search accounts',
  'video_publish.account_select.no_groups': 'No groups',
  'video_publish.account_select.no_accounts': 'No accounts',
  'video_publish.account_select.online': 'Online',
  'video_publish.account_select.offline': 'Offline',
  'video_publish.account_select.status': '[@status]',
}; 