import 'package:aitoearn_app/i18n/languages/en_us/modules/content_detail_en.dart';
import 'package:aitoearn_app/i18n/languages/en_us/modules/global_en.dart';
import 'package:aitoearn_app/i18n/languages/en_us/modules/hot_en.dart';
import 'package:aitoearn_app/i18n/languages/en_us/modules/mine_en.dart';
import 'package:aitoearn_app/i18n/languages/en_us/modules/publish_en.dart';
import 'package:aitoearn_app/i18n/languages/en_us/modules/video_publish_en.dart';

const Map<String, String> enUs = {
  'appName': 'AiToEarn',
  'welcome_title': 'Welcome to AiToEarn',

  // Form Dialog -----------------------------------
  'form_dialog.confirm': 'Confirm',
  'form_dialog.cancel': 'Cancel',
  'form_dialog.field_required': '@field cannot be empty',

  'dataInfo': 'Data',
  'homePageTitle': 'Home',
  'BNBAccount': 'Account',
  'BNBInteraction': 'Interaction',
  'BNBPub': 'Publish',
  'BNBMine': 'Mine',
  'setting.language': 'Language',
  'selectLanguage': 'Select Language',
  'cancel': 'cancel',
  'confirm': 'confirm',
  'permissionDeniedTitle': 'Permission Denied',
  'permissionRequestTitle': 'Request Permission',
  'permissionDeniedMessage':
      'The following permissions were denied, some features may not work:',
  'permissionPermanentlyDeniedMessage':
      'You have permanently denied this permission. Please go to the system settings page to manually enable it.',
  'permissionAcknowledge': 'Got it',
  'permissionGoSettings': 'Go to Settings',
  'permissionCamera': 'Camera Permission',
  'permissionStorage': 'Storage Permission',
  'permissionPhotos': 'Photos Permission',
  'permissionLocation': 'Location Permission',
  'permissionLocationAlways': 'Location Permission (Always)',
  'permissionLocationWhenInUse': 'Location Permission (When in Use)',
  'permissionMicrophone': 'Microphone Permission',
  'permissionContacts': 'Contacts Permission',
  'permissionCalendar': 'Calendar Permission',
  'permissionNotification': 'Notification Permission',
  'permissionSensors': 'Sensors Permission',
  'permissionSms': 'SMS Permission',
  'permissionSpeech': 'Speech Recognition Permission',
  'permissionIgnoreBatteryOptimizations':
      'Ignore Battery Optimizations Permission',
  'permissionBluetooth': 'Bluetooth Permission',
  'permissionBluetoothScan': 'Bluetooth Scan Permission',
  'permissionBluetoothAdvertise': 'Bluetooth Advertise Permission',
  'permissionBluetoothConnect': 'Bluetooth Connect Permission',
  'permissionAppTrackingTransparency': 'App Tracking Transparency Permission',
  'permissionCriticalAlerts': 'Critical Alerts Permission',
  'permissionAccessMediaLocation': 'Access Media Location Permission',
  'permissionActivityRecognition': 'Activity Recognition Permission',
  'followSystem': 'Follow System',
  'notice': 'Notice',

  // 登录相关
  'agree_prefix': 'I have read and agree to',
  'terms_of_service': ' Terms of Service ',
  'privacy_and': 'and',
  'privacy_policy': ' Privacy Policy ',
  'input_email': 'Enter Email',
  'input_password': 'Enter Password',
  'forgot_password': 'Forgot Password',
  'account_email': 'Email',
  'login_or_register': 'Login / Register',
  'third_party_login': 'Third-party Login',
  'login_with_google': 'Login with Google',
  'waiting_for_verification': 'Waiting for Verification...',
  'register_account': 'Register Account',
  'verification_sent': 'Verification sent to your email',
  'check_mail_to_verify': 'Check your email to complete registration',
  'set_password': 'Set Password',
  'password_at_least_8': 'At least 8 characters',
  'password_uppercase_required': 'Contains uppercase letters',
  'password_number_or_symbol': 'Contains number or symbol',
  'confirm_password': 'Confirm Password',
  'invite_code_optional': 'Invite Code (Optional)',
  'input_invite_code': 'Enter Invite Code',
  'already_have_account': 'Already have an account?',
  'login_now': 'Login Now',
  'reset_password': 'Reset Password',
  'send_code_to_reset': 'Send verification code and reset password',
  'new_password': 'New Password',
  'confirm_reset': 'Confirm Reset',
  'send_reset_link': 'Send Reset Link',
  'sending': 'Sending...',
  'setting.title': 'Settings',
  'setting.logout': 'Logout',
  'setting.cancelAccount': 'Cancel Account',
  'setting.feedback': 'Feedback',
  'setting.notification': 'Notifications',
  'setting.terms': 'Terms of Service',
  'setting.privacy': 'Privacy Policy',
  'setting.rateUs': 'Rate Us',
  'setting.about': 'About AiToEarn',
  'userInfo.avatar': 'Avatar',
  'userInfo.nickname': 'Nickname',
  'userInfo.account': 'Account',
  'userInfo.defaultName': 'AiToEarn User',

  // Interaction Page
  'interaction.douyinSearch': 'Douyin Search',
  'interaction.startInteraction': 'Start Interaction',
  'interaction.interactionStarted': 'Interaction Started',
  'interaction.selectedContent':
      'Content selected, frequency: {frequency} times/min, duration: {duration} min',
  'interaction.closeFilterPanel': 'Click mask to close filter panel',

  // Interaction Controller
  'platform.xiaohongshu': 'Xiaohongshu',
  'platform.douyin': 'Douyin',
  'platform.wechat': 'WeChat Channels',
  'platform.kuaishou': 'Kuaishou',
  'platform.doupinping': 'Hualihuali',

  'dialog.selectAccount': 'Select {platform} Account',
  'dialog.unnamedAccount': 'Unnamed Account',

  'notice.title': 'Notice',
  'notice.noAccount': 'No available {platform} account, please add one first',
  'notice.selectValidDouyinAccount':
      'Please select a valid Douyin account first',

  'error.douyinTokenEmpty':
      'Douyin account Cookie is empty, cannot load content',
  'error.douyinHotFeedFailed': 'Failed to load Douyin hot feed: {error}',
  'error.searchContentFailed': 'Exception while searching content: {error}',
  'error.douyinSearchFailed': 'Douyin search failed',

  // Search Bar
  'search.placeholder': 'Search @platform content',

  // Filter Tabs
  'filter.category.all': 'All',
  'filter.category.hot': 'Hot',
  'filter.category.latest': 'Latest',

  // Filter Controller
  'filter.dateRange.all': 'All',
  'filter.dateRange.today': 'Today',
  'filter.dateRange.thisWeek': 'This Week',
  'filter.dateRange.thisMonth': 'This Month',
  'filter.dateRange.custom': 'Custom',

  'filter.contentType.all': 'All',
  'filter.contentType.image': 'Image & Text',
  'filter.contentType.video': 'Video',
  'filter.contentType.live': 'Live',

  'filter.interactionType.all': 'All',
  'filter.interactionType.like': 'Like',
  'filter.interactionType.favorite': 'Favorite',
  'filter.interactionType.comment': 'Comment',
  'filter.interactionType.share': 'Share',

  // Filter Panel
  'filter.panel.sortBy': 'Sort By',
  'filter.panel.noteType': 'Note Type',
  'filter.panel.publishTime': 'Publish Time',
  'filter.panel.likesCount': 'Likes Count',
  'filter.panel.to': 'to',
  'filter.panel.startTime': 'Start Time',
  'filter.panel.endTime': 'End Time',
  'filter.panel.minValue': 'Min',
  'filter.panel.maxValue': 'Max',
  'filter.panel.reset': 'Reset',
  'filter.panel.confirm': 'Confirm',

  // Filter Panel Controller
  'filter.sort.comprehensive': 'Comprehensive',
  'filter.sort.latest': 'Latest',
  'filter.sort.mostLikes': 'Most Likes',
  'filter.sort.mostComments': 'Most Comments',
  'filter.sort.mostFavorites': 'Most Favorites',

  'filter.noteType.unlimited': 'All',
  'filter.noteType.video': 'Video',
  'filter.noteType.imageText': 'Image & Text',
  'filter.noteType.live': 'Live',

  'filter.timeRange.unlimited': 'All Time',
  'filter.timeRange.today': 'Today',
  'filter.timeRange.thisWeek': 'This Week',
  'filter.timeRange.thisMonth': 'This Month',
  'filter.timeRange.threeMonths': 'Last 3 Months',
  'filter.timeRange.sixMonths': 'Last 6 Months',
  'filter.timeRange.oneYear': 'Last Year',
  'filter.timeRange.custom': 'Custom',

  'filter.likesRange.unlimited': 'Unlimited',
  'filter.likesRange.zeroToHundred': '0-100',
  'filter.likesRange.hundredToThousand': '100-1000',
  'filter.likesRange.thousandToTenThousand': '1000-10000',
  'filter.likesRange.tenThousandPlus': '10000+',

  // Account Management
  'account.list': 'Account List',
  'account.add': 'Add Account',
  'account.cancel': 'Cancel',
  'account.confirm': 'Confirm',
  'account.empty': 'No Accounts',
  'account.empty.hint': 'Click "+" in top left to add account',
  'account.default': 'Default',
  'account.online': 'Online',
  'account.offline': 'Offline',
  'account.more': 'More Options',
  'account.group.manage': 'Space Management',
  'account.batch.manage': 'Batch Management',
  'account.edit.note': 'Edit Note',
  'account.move.group': 'Move to Other Space',
  'account.delete': 'Delete Account',
  'account.delete.confirm':
      'Are you sure to delete this account? This action cannot be undone.',
  'account.select.group': 'Select Target Space',
  'account.operation.success': 'Operation Success',
  'account.update.note.success': 'Account note updated',
  'account.operation.failed': 'Operation Failed',
  'account.update.note.failed': 'Failed to update account note',
  'account.move.failed': 'Move Failed',
  'account.move.group.notfound': 'Target group not found',
  'account.move.failed.retry':
      'Failed to move account, please try again later: {error}',

  // Add Account Dialog
  'account.add.oneclick': 'One-Click Login',
  'account.add.login.now': 'Login Now',
  'account.add.notice':
      'After clicking, you will be redirected to {platform} login page where you can choose QR code or phone login',
  'account.add.platform': 'Platform',
  'account.add.select.platform': 'Please select a platform first',
  'account.add.success': 'Added Successfully',
  'account.add.success.message': 'New account added',
  'account.add.phone': 'Enter phone number',
  'account.add.verification': 'Enter verification code',
  'account.add.get.verification': 'Get Code',
  'account.add.qr.login': 'QR Code Login',
  'account.add.cookie.login': 'Cookie Login',
  'account.add.support': 'Supports {platform} QR code or phone login',
  'account.add.login': 'login',

  // Platform names
  'account.platform.douyin': 'Douyin',
  'account.platform.weixin': 'WeChat Video',
  'account.platform.xhs': 'Xiaohongshu',
  'account.platform.kuaishou': 'Kuaishou',
  'account.platform.wxGzh': 'WeChat Official Account',

  // Subscription page related
  'subscription.plusMember': 'PLUS Member',
  'subscription.unlockAllFeatures':
      'Activate membership to unlock all features and enjoy 8 benefits',
  'subscription.membershipExpireTime': 'Expires: @expireTime',
  'subscription.selectPlan': 'Select Plan',
  'subscription.renewPlan': 'Renew Plan',
  'subscription.monthlyPlan': 'Monthly',
  'subscription.annualPlan': 'Annual',
  'onceMonthlyPlan': 'One-time Monthly',
  'subscription.mostPopular': 'Most Popular',
  'subscription.bestValue': 'Best Value',
  'subscription.flexibleChoice': 'Flexible',
  'subscription.autoRenewCancelAnytime': 'Auto-renew, cancel anytime',
  'subscription.activateNow': 'Activate Now',
  'subscription.renewNow': 'Renew Now',
  'subscription.paymentTitle': 'Payment Notice',
  'subscription.paymentMessage':
      'You have been redirected to the payment page. Please click the button below after completing payment',
  'subscription.completePayment': 'Complete Payment',
  'subscription.userInfoError': 'User information error',
  'subscription.createOrderFailed': 'Failed to create payment order',
  'subscription.getPaymentUrlFailed': 'Failed to get payment URL',
  'subscription.getOrderIdFailed': 'Failed to get order ID',
  'subscription.cannotOpenPaymentPage': 'Cannot open payment page',
  'subscription.paymentFailed': 'Payment failed: @error',
  'subscription.orderQueryFailed': 'Order query failed',
  'subscription.orderRefunded': 'Order has been refunded',
  'subscription.orderCanceled': 'Order has been canceled or expired',
  'subscription.paymentTimeout': 'Payment timeout, please try again',
  'subscription.unknownOrderStatus': 'Unknown order status: @status',
  'subscription.queryOrderError': 'Order status query error: @error',
  'subscription.success': 'Payment successfully',
  'subscription.priceOff': 'OFF',

  // Time formatting
  'time.year_ago': '@year year(s) ago',
  'time.month_ago': '@month month(s) ago',
  'time.day_ago': '@day day(s) ago',
  'time.hour_ago': '@hour hour(s) ago',
  'time.minute_ago': '@minute minute(s) ago',
  'time.just_now': 'Just now',

  // Time interval picker
  'time.interval.title': 'Select Time Interval',
  'time.interval.5s': '5 seconds',
  'time.interval.10s': '10 seconds',
  'time.interval.30s': '30 seconds',
  'time.interval.1m': '1 minute',
  'time.interval.5m': '5 minutes',
  'time.interval.10m': '10 minutes',
  'time.interval.30m': '30 minutes',
  'time.interval.1h': '1 hour',
  'error.xhsTokenExpired': 'TokenExpired',
  'error.retry': 'Retry',

  // ContentList page
  'content.no_account': 'No @platform Account',
  'content.add_account': 'Add Account',
  'content.search_hint': 'Enter keywords to search content',
  'content.anonymous_user': 'Anonymous User',

  // Platform names
  'platform.name.xhs': 'Xiaohongshu',
  'platform.name.douyin': 'Douyin',
  'platform.name.wxsph': 'WeChat Channels',
  'platform.name.kwai': 'Kuaishou',
  'platform.name.default': 'This platform',

  // Member benefits
  'subscription.benefit1': 'Exclusive Badge',
  'subscription.benefit2': 'Premium Features',
  'subscription.benefit3': 'Member Gifts',
  'subscription.benefit4': 'Priority Support',
  'subscription.benefit5': 'Discounts',
  'subscription.benefit6': 'Unlimited Duration',
  'subscription.benefit7': 'Fast Experience',
  'subscription.benefit8': 'More Privileges',

  ...hotEnUsLangs,
  ...publishEnUsLangs,
  ...contentDetailEnUsLangs,
  ...videoPublishEnUsLangs,
  ...mineEnUs,
  ...globalEnUsLangs,
};
