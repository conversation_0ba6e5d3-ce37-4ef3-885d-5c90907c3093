import 'package:aitoearn_app/config/app_config.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/utils/dio_util/dio_util.dart';
import 'package:dio/dio.dart';

/// 内容安全检查结果
class ContentSecurityResult {
  /// 是否敏感 true=敏感 false=正常
  final bool isSensitive;
  
  /// 检查的内容
  final String content;
  
  /// 检查结果详情
  final String result;
  
  /// 错误信息（如果有）
  final String? error;

  ContentSecurityResult({
    required this.isSensitive,
    required this.content,
    required this.result,
    this.error,
  });

  factory ContentSecurityResult.fromResponse(String content, String response) {
    return ContentSecurityResult(
      isSensitive: response != 'Normal',
      content: content,
      result: response,
    );
  }

  factory ContentSecurityResult.error(String content, String error) {
    return ContentSecurityResult(
      isSensitive: false,
      content: content,
      result: 'Error',
      error: error,
    );
  }
}

/// 发布项目的安全检查结果
class PublishItemSecurityResult {
  /// 发布项目ID或索引
  final String itemId;
  
  /// 是否敏感
  final bool isSensitive;
  
  /// 检查的内容
  final String content;
  
  /// 检查结果
  final String result;
  
  /// 错误信息
  final String? error;

  PublishItemSecurityResult({
    required this.itemId,
    required this.isSensitive,
    required this.content,
    required this.result,
    this.error,
  });

  factory PublishItemSecurityResult.fromContentResult(
    String itemId,
    ContentSecurityResult contentResult,
  ) {
    return PublishItemSecurityResult(
      itemId: itemId,
      isSensitive: contentResult.isSensitive,
      content: contentResult.content,
      result: contentResult.result,
      error: contentResult.error,
    );
  }
}

/// 内容安全检查服务
class ContentSecurityService {
  static final DioUtil _dioUtil = DioUtil(AppConfig.appHostApi);

  /// 检查文本内容安全性
  ///
  /// [content] 要检查的文本内容
  /// 返回检查结果，'Normal' 表示正常，其他值表示敏感
  static Future<ContentSecurityResult> checkTextSecurity(String content) async {
    try {
      LoggerUtil.i('【内容安全检查】开始检查文本: ${content.length > 50 ? '${content.substring(0, 50)}...' : content}');

      final response = await _dioUtil.request(
        '/tools/common/text/moderation',
        data: {
          'content': content,
        },
        options: Options(
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response != null && response.statusCode == 200 && response.data != null) {
        final result = response.data.toString();
        LoggerUtil.i('【内容安全检查】检查结果: $result');

        return ContentSecurityResult.fromResponse(content, result);
      } else {
        LoggerUtil.w('【内容安全检查】API响应异常: ${response?.statusCode}');
        return ContentSecurityResult.error(content, 'API响应异常');
      }
    } catch (e) {
      LoggerUtil.e('【内容安全检查】检查失败: $e');
      return ContentSecurityResult.error(content, e.toString());
    }
  }

  /// 批量检查多个文本内容的安全性
  /// 
  /// [contents] 要检查的文本内容列表
  /// [itemIds] 对应的项目ID列表（可选）
  /// 返回检查结果列表
  static Future<List<PublishItemSecurityResult>> checkMultipleTextSecurity(
    List<String> contents, {
    List<String>? itemIds,
  }) async {
    try {
      LoggerUtil.i('【内容安全检查】开始批量检查 ${contents.length} 个文本');
      final contentSet = <String>{};
      final uniqueContents = <String>[];
      final contentIndexMap = <String, List<int>>{};

      for (int i = 0; i < contents.length; i++) {
        final content = contents[i].trim();
        if (content.isNotEmpty && !contentSet.contains(content)) {
          contentSet.add(content);
          uniqueContents.add(content);
          contentIndexMap[content] = [i];
        } else if (content.isNotEmpty) {
          contentIndexMap[content]!.add(i);
        }
      }

      LoggerUtil.i('【内容安全检查】去重后需要检查 ${uniqueContents.length} 个唯一文本');

      // 并发检查所有唯一内容
      final futures = uniqueContents.map((content) => checkTextSecurity(content));
      final results = await Future.wait(futures);
      final finalResults = <PublishItemSecurityResult>[];
      
      for (int i = 0; i < contents.length; i++) {
        final content = contents[i].trim();
        final itemId = itemIds != null && i < itemIds.length ? itemIds[i] : i.toString();
        
        if (content.isEmpty) {
          finalResults.add(PublishItemSecurityResult(
            itemId: itemId,
            isSensitive: false,
            content: content,
            result: 'Normal',
          ));
        } else {
          final uniqueIndex = uniqueContents.indexOf(content);
          if (uniqueIndex >= 0 && uniqueIndex < results.length) {
            final contentResult = results[uniqueIndex];
            finalResults.add(PublishItemSecurityResult.fromContentResult(itemId, contentResult));
          } else {
            finalResults.add(PublishItemSecurityResult(
              itemId: itemId,
              isSensitive: false,
              content: content,
              result: 'Error',
              error: '未找到检查结果',
            ));
          }
        }
      }

      LoggerUtil.i('【内容安全检查】批量检查完成，共 ${finalResults.length} 个结果');
      return finalResults;
    } catch (e) {
      LoggerUtil.e('【内容安全检查】批量检查失败: $e');
      
      // 返回错误结果
      final errorResults = <PublishItemSecurityResult>[];
      for (int i = 0; i < contents.length; i++) {
        final itemId = itemIds != null && i < itemIds.length ? itemIds[i] : i.toString();
        errorResults.add(PublishItemSecurityResult(
          itemId: itemId,
          isSensitive: false,
          content: contents[i],
          result: 'Error',
          error: e.toString(),
        ));
      }
      return errorResults;
    }
  }

  /// 模拟延迟（用于显示加载状态）
  static Future<void> simulateLoading({int milliseconds = 1000}) async {
    await Future.delayed(Duration(milliseconds: milliseconds));
  }
}
