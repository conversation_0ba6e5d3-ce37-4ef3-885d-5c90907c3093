import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';

class DouyinUploader {
  final String defaultUserAgent =
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3';
  final Dio _dio = Dio();

  // 上传封面文件
  Future<String> uploadCoverFile(
      String filePath, String cookieString, String userUid, String proxy) async {
    final authRes = await _request(
      'https://creator.douyin.com/web/api/media/upload/auth/v5/',
      method: 'POST',
      headers: {
        'Cookie': cookieString,
        'User-Agent': defaultUserAgent,
      },
      body: {
        'upload_type': 2, // 2=图片
        'uid': userUid,
      },
    );
    if (authRes['status_code'] != 0) {
      throw Exception(authRes['status_msg'] ?? '获取上传凭证失败');
    }
    final uploadUrl = authRes['data']['upload_url'];
    final fileBytes = await _readFileBytes(filePath);
    final uploadRes = await _dio.put(
      uploadUrl,
      data: Stream.fromIterable([fileBytes]),
      options: Options(
        headers: {
          'Content-Type': 'image/jpeg',
          'User-Agent': defaultUserAgent,
        },
      ),
    );
    if (uploadRes.statusCode != 200) {
      throw Exception('封面上传失败: ${uploadRes.statusCode}');
    }
    return authRes['data']['uri'] ?? '';
  }

  // 上传视频文件
  Future<String> uploadVideo(
      String filePath, String cookieString, String userUid, String proxy) async {
    final authRes = await _request(
      'https://creator.douyin.com/web/api/media/upload/auth/v5/',
      method: 'POST',
      headers: {
        'Cookie': cookieString,
        'User-Agent': defaultUserAgent,
      },
      body: {
        'upload_type': 1, // 1=视频
        'uid': userUid,
      },
    );
    if (authRes['status_code'] != 0) {
      throw Exception(authRes['status_msg'] ?? '获取上传凭证失败');
    }
    final uploadUrl = authRes['data']['upload_url'];
    final fileBytes = await _readFileBytes(filePath);
    final uploadRes = await _dio.put(
      uploadUrl,
      data: Stream.fromIterable([fileBytes]),
      options: Options(
        headers: {
          'Content-Type': 'video/mp4',
          'User-Agent': defaultUserAgent,
        },
      ),
    );
    if (uploadRes.statusCode != 200) {
      throw Exception('视频上传失败: ${uploadRes.statusCode}');
    }
    return authRes['data']['uri'] ?? '';
  }

  // 获取CSRF Token
  Future<String> getSecsdkCsrfToken(String cookies) async {
    final res = await _dio.get(
      'https://creator.douyin.com/web/api/media/anchor/search',
      options: Options(
        headers: {
          'Cookie': cookies,
          'User-Agent': defaultUserAgent,
        },
      ),
    );
    final setCookie = res.headers['set-cookie']?.join(';') ?? '';
    final reg = RegExp(r'x-secsdk-csrf-token=([^;]+)');
    final match = reg.firstMatch(setCookie);
    if (match != null) {
      return match.group(1)!;
    }
    final bodyJson = res.data is String ? jsonDecode(res.data) : res.data;
    if (bodyJson is Map && bodyJson.containsKey('token')) {
      return bodyJson['token'];
    }
    throw Exception('CSRF Token获取失败');
  }

  // 读取文件为字节
  Future<List<int>> _readFileBytes(String filePath) async {
    final file = File(filePath);
    return await file.readAsBytes();
  }

  // 封装请求方法（Dio实现）
  Future<Map<String, dynamic>> _request(
    String url, {
    String method = 'GET',
    Map<String, String>? headers,
    dynamic body,
  }) async {
    Response res;
    if (method == 'POST') {
      res = await _dio.post(
        url,
        data: body,
        options: Options(headers: headers),
      );
    } else {
      res = await _dio.get(
        url,
        options: Options(headers: headers),
      );
    }
    if (res.data is String) {
      return jsonDecode(res.data);
    }
    return Map<String, dynamic>.from(res.data);
  }

  // 获取活动详情
  Future<Map<String, dynamic>> getActivityDetails(String cookies, String activityId) async {
    final res = await _request(
      'https://creator.douyin.com/web/api/media/activity/detail/?activity_id=$activityId',
      method: 'GET',
      headers: {'Cookie': cookies},
    );
    if (res['status_code'] != 0) throw Exception(res['status_msg'] ?? '未知错误');
    return res;
  }

  // 获取活动标签
  Future<List<dynamic>> getActivityTags(String cookies) async {
    final res = await _request(
      'https://creator.douyin.com/web/api/media/activity/tags/query',
      method: 'GET',
      headers: {'Cookie': cookies},
    );
    if (res['status_code'] != 0) throw Exception(res['status_msg'] ?? '未知错误');
    return res['tags'] ?? [];
  }

  // 获取作品列表
  Future<Map<String, dynamic>> getCreatorItems(String cookies, {String? cursor}) async {
    final url = 'https://creator.douyin.com/aweme/v1/creator/item/list/' + (cursor != null ? '?cursor=$cursor' : '');
    final res = await _request(
      url,
      method: 'GET',
      headers: {'Cookie': cookies},
    );
    if (res['status_code'] != 0) throw Exception(res['status_msg'] ?? '未知错误');
    return res;
  }

  // 获取评论列表
  Future<Map<String, dynamic>> getCreatorCommentList(String cookies, String itemId, {String? cursor, int? count}) async {
    final url = 'https://creator.douyin.com/aweme/v1/creator/comment/list/?item_id=$itemId&sort=TIME' + (cursor != null ? '&cursor=$cursor' : '') + (count != null ? '&count=$count' : '');
    final res = await _request(
      url,
      method: 'GET',
      headers: {'Cookie': cookies},
    );
    if (res['status_code'] != 0) throw Exception(res['status_msg'] ?? '未知错误');
    return res;
  }

  // 获取评论回复列表
  Future<Map<String, dynamic>> getCreatorCommentReplyList(String cookies, String commentId, {String? cursor, int? count}) async {
    final url = 'https://creator.douyin.com/aweme/v1/creator/comment/reply/list/?comment_id=$commentId' + (cursor != null ? '&cursor=$cursor' : '') + (count != null ? '&count=$count' : '');
    final res = await _request(
      url,
      method: 'GET',
      headers: {'Cookie': cookies},
    );
    if (res['status_code'] != 0) throw Exception(res['status_msg'] ?? '未知错误');
    return res;
  }

  // 点赞作品
  Future<Map<String, dynamic>> creatorDianzanOther(String cookies, String awemeId) async {
    final url = 'https://www.douyin.com/aweme/v1/web/commit/item/digg/?aid=6383';
    final csrfToken = await getSecsdkCsrfToken(cookies);
    final res = await _dio.post(
      url,
      data: jsonEncode({'aweme_id': awemeId}),
      options: Options(
        headers: {
          'Cookie': cookies,
          'X-Secsdk-Csrf-Token': csrfToken,
          'referer': 'https://www.douyin.com/video/$awemeId',
          'user-agent': defaultUserAgent,
          'Content-Type': 'application/json',
        },
      ),
    );
    if (res.statusCode != 200) throw Exception('点赞失败: ${res.statusCode}');
    return res.data is String ? jsonDecode(res.data) : res.data;
  }

  // 收藏作品
  Future<Map<String, dynamic>> creatorShoucangOther(String cookies, String awemeId) async {
    final url = 'https://www.douyin.com/aweme/v1/web/aweme/collect/?aid=6383';
    final csrfToken = await getSecsdkCsrfToken(cookies);
    final res = await _dio.post(
      url,
      data: jsonEncode({'aweme_id': awemeId}),
      options: Options(
        headers: {
          'Cookie': cookies,
          'X-Secsdk-Csrf-Token': csrfToken,
          'referer': 'https://www.douyin.com/video/$awemeId',
          'user-agent': defaultUserAgent,
          'Content-Type': 'application/json',
        },
      ),
    );
    if (res.statusCode != 200) throw Exception('收藏失败: ${res.statusCode}');
    return res.data is String ? jsonDecode(res.data) : res.data;
  }

  // 回复评论
  Future<Map<String, dynamic>> creatorCommentReplyOther(String cookies, String awemeId, String text) async {
    final url = 'https://www.douyin.com/aweme/v1/web/comment/publish/?aid=6383';
    final csrfToken = await getSecsdkCsrfToken(cookies);
    final res = await _dio.post(
      url,
      data: jsonEncode({'aweme_id': awemeId, 'text': text}),
      options: Options(
        headers: {
          'Cookie': cookies,
          'X-Secsdk-Csrf-Token': csrfToken,
          'referer': 'https://www.douyin.com/video/$awemeId',
          'user-agent': defaultUserAgent,
          'Content-Type': 'application/json',
        },
      ),
    );
    if (res.statusCode != 200) throw Exception('评论失败: ${res.statusCode}');
    return res.data is String ? jsonDecode(res.data) : res.data;
  }

  // 作品评论回复（作者回复）
  Future<Map<String, dynamic>> creatorCommentReply(String cookies, String commentId, String itemId, String text) async {
    final url = 'https://creator.douyin.com/aweme/v1/creator/comment/reply/';
    final csrfToken = await getSecsdkCsrfToken(cookies);
    final res = await _dio.post(
      url,
      data: jsonEncode({'comment_Id': commentId, 'item_id': itemId, 'text': text}),
      options: Options(
        headers: {
          'Cookie': cookies,
          'X-Secsdk-Csrf-Token': csrfToken,
          'Content-Type': 'application/json',
        },
      ),
    );
    if (res.statusCode != 200) throw Exception('回复失败: ${res.statusCode}');
    return res.data is String ? jsonDecode(res.data) : res.data;
  }

  // 检查用户登录是否过期
  Future<bool> checkLoginStatus(String cookies) async {
    final res = await _request(
      'https://creator.douyin.com/web/api/media/user/info/',
      method: 'GET',
      headers: {'Cookie': cookies},
    );
    if (res['status_code'] == 0) return true;
    throw Exception(res['status_msg'] ?? '未知错误');
  }

  // 获取用户信息
  Future<Map<String, dynamic>> getUserInfo(String cookies) async {
    final res = await _request(
      'https://creator.douyin.com/web/api/media/user/info/',
      method: 'GET',
      headers: {'Cookie': cookies},
    );
    if (res['status_code'] == 0) {
      final user = res['user'];
      return {
        'uid': user['sec_uid'],
        'authorId': user['unique_id'] != '' ? user['unique_id'] : user['uid'],
        'nickname': user['nickname'] ?? '',
        'avatar': user['avatar_thumb']['url_list'][0] ?? '',
        'fansCount': user['follower_count'] ?? 0,
      };
    } else {
      throw Exception(res['status_msg'] ?? '未知错误');
    }
  }
}