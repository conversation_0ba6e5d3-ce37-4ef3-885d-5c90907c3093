import 'package:aitoearn_app/network/http.dart';
import 'package:dio/dio.dart';

/// 邮箱登录 / 注册
Future<Response?> loginOrRegisterByEmailApi({
  required String mail,
  required String password,
}) async {
  return await Http.postResponse(
    '/login/mail',
    withToken: false,
    data: {
      'mail': mail,
      'password': password,
    },
  );
}

/// 轮询获取邮箱注册结果（POST）
Future<Response?> pollEmailRegisterResultApi({
  required String mail,
  required String code,
  required String password,
  required String inviteCode,
}) async {
  return await Http.postResponse(
    '/login/mail/regist/back',
    withToken: false,
    data: {
      'mail': mail,
      'code': code,
      'password': password,
      'inviteCode': inviteCode
    },
  );
}

/// 邮箱重置发送邮件（POST）
Future<Response?> sendResetPasswordMailApi({
  required String mail,
}) async {
  return await Http.postResponse(
    '/login/repassword/mail',
    withToken: false,
    data: {
      'mail': mail,
    },
  );
}

/// 轮询获取邮箱重设密码结果（POST）
Future<Response?> pollResetPasswordResultApi({
  required String mail,
  required String code,
  required String password,
  required String inviteCode,
}) async {
  return await Http.postResponse(
    '/login/repassword/mail/back',
    withToken: false,
    data: {
      'mail': mail,
      'code': code,
      'password': password,
      'inviteCode': inviteCode,
    },
  );
}

/// Google 登录
Future<Response?> googleLoginApi({
  required String clientId,
  required String credential,
}) async {
  return await Http.postResponse(
    '/login/google',
    withToken: false,
    data: {
      'clientId': clientId,
      'credential': credential,
    },
  );
}
