import 'package:json_annotation/json_annotation.dart';

part 'base_response.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class BaseResponse<T> {
  String? msg;
  dynamic code;
  T? data;
  bool success;

  BaseResponse({
    this.msg,
    this.code = 0,
    this.data,
    this.success = false,
  });

  /// 是否成功
  bool get isSuccess => success || code == 0;

  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic json) fromJsonT,
  ) => _$BaseResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$BaseResponseToJson(this, toJsonT);

  @override
  String toString() {
    return 'BaseResponse{msg: $msg, code: $code, data: $data, success: $success}';
  }
}
