import 'package:aitoearn_app/network/http.dart';

/// 创建支付订单
/// [mode] 支付模式，固定为"payment"
/// [payment] 支付类型：month(月度), year(年度), onceMonth(一次性月度)
/// [userId] 用户ID，用于metadata字段
Future<Map?> createOrder({
  required String mode,
  required String payment,
  required String userId,
}) async {
  return await Http.post(
    '/payment/checkout', // 修正为用户指定的POST路径
    data: {
      'mode': mode,
      'payment': payment,
      'metadata': {'userId': userId},
    },
  );
}

/// 查询支付订单
/// [id] 订单ID，作为路径参数
Future<Map?> queryOrder({required String id}) async {
  return await Http.get(
    '/payment/checkout/$id', // 修正为用户指定的GET路径
  );
}
