import 'package:aitoearn_app/network/http.dart';
import 'package:dio/dio.dart';

/// AI 生成图片接口
/// [prompt] 描述内容（必填）
/// [model] 模型名称（可选）
/// [quality] 图片质量，standard | hd（默认 standard）
/// [responseFormat] 返回格式，url | b64_json（默认 url）
/// [size] 图片尺寸（默认 1024x1024）
/// [style] 图片风格，vivid | natural（默认 vivid）
/// [user] 用户标识（可选）
Future<Map?> generateAiImageApi({
  required String prompt,
  String? model,
  String quality = '',
  String responseFormat = 'url',
  String size = '',
  String style = '',
  String? user,
}) async {
  final Map<String, dynamic> data = {
    'prompt': prompt,
    'n': 1,
    'response_format': responseFormat,
    'size': size,
    if (quality.isNotEmpty) 'quality': quality,
    if (style.isNotEmpty) 'style': style,
    if (model != null) 'model': model,
    if (user != null) 'user': user,
  };

  return await Http.post(
    '/ai/image/generate',
    data: data,
    options: Options(
      sendTimeout: const Duration(minutes: 5),
      receiveTimeout: const Duration(minutes: 5),
    ),
  );
}

/// 编辑 AI 图片（图像编辑接口）
///
/// [image] 原始图片（base64 编码，必填）
/// [prompt] 编辑描述词（必填）
/// [model] 编辑使用的模型（可选）
/// [size] 图片尺寸（默认 1024x1024）
/// [responseFormat] 返回格式 url | b64_json（默认 url）
/// [user] 用户标识符（可选）
Future<Map?> editAiImageApi({
  required String image,
  required String prompt,
  String? model,
  String size = '',
  String responseFormat = 'url',
  String? user,
}) async {
  final Map<String, dynamic> data = {
    'image': image,
    'prompt': prompt,
    'n': 1,
    'size': size,
    'response_format': responseFormat,
    if (model != null) 'model': model,
    if (user != null) 'user': user,
  };

  return await Http.post(
    '/ai/image/edit',
    data: data,
    options: Options(
      sendTimeout: const Duration(minutes: 5),
      receiveTimeout: const Duration(minutes: 5),
    ),
  );
}

/// 提交 AI 视频生成请求（Midjourney Video）
///
/// [base64] 图片的 base64 或 prompt 中的图片 URL
/// [prompt] 提示词（必填）最多长度不超过 4000 个字符
/// [mode] 模式，fast | relax（默认 fast）
/// [motion] 动作强度，high | low（默认 high）
/// [videoType] 视频分辨率类型（可选）
/// [animateMode] 动画模式，manual | auto（默认 auto）
/// [action] 动作执行类型（可选）
/// [taskId] 如果是衍生视频，指定原任务 ID（可选）
/// [index] 衍生视频使用的索引（0-3，默认 null 表示不传）
Future<Map?> submitAiMjVideoApi({
  required String prompt,
  String? base64,
  String mode = 'fast',
  String motion = 'high',
  String? videoType,
  String animateMode = 'auto',
  String? action,
  String? taskId,
  int? index,
}) async {
  final Map<String, dynamic> data = {
    'prompt': prompt,
    'mode': mode,
    'motion': motion,
    'animate_mode': animateMode,
    if (base64 != null) 'base64': base64,
    if (videoType != null) 'video_type': videoType,
    if (action != null) 'action': action,
    if (taskId != null) 'taskId': taskId,
    if (index != null) 'index': index,
  };

  return await Http.post(
    '/ai/mj/submit/video',
    data: data,
    options: Options(
      sendTimeout: const Duration(minutes: 2),
      receiveTimeout: const Duration(minutes: 2),
    ),
  );
}

/// 获取 AI 视频任务状态（通过 taskId 查询）
///
/// [taskId] 视频任务 ID（必填）
/// 返回任务详情 Map（如 status、video_url 等）
Future<Map?> getAiMjTaskStatusApi(String taskId) async {
  return await Http.get('/ai/mj/task/$taskId/fetch');
}

/// 提交 AI 视频生成任务
///
/// [model] 模型名称（必填）
/// [prompt] 提示词（必填）
/// [image] 起始图（可选，base64 编码）
/// [mode] 模式（可选）
/// [size] 尺寸（可选）
/// [duration] 视频时长（单位秒，可选）
/// [metadata] 附加参数（可选 Map）
/// 返回生成任务信息（含 taskId 等）
Future<Map?> submitAiVideoGenerationApi({
  required String model,
  required String prompt,
  String? image,
  String? mode,
  String? size,
  int? duration,
  Map<String, dynamic>? metadata,
}) async {
  final Map<String, dynamic> data = {
    'model': model,
    'prompt': prompt,
    if (image != null) 'image': image,
    if (mode != null && mode != '') 'mode': mode,
    if (size != null) 'size': size,
    if (duration != null) 'duration': duration,
    if (metadata != null && mode != '') 'metadata': metadata,
  };

  return await Http.post(
    '/ai/video/generations',
    data: data,
    options: Options(
      sendTimeout: const Duration(minutes: 5),
      receiveTimeout: const Duration(minutes: 5),
    ),
  );
}

/// 获取 AI 视频任务状态（通过 taskId 查询）
///
/// [taskId] 任务 ID（必填）
/// 返回任务详情 Map（如状态、视频地址等）
Future<Map?> getAiVideoGenerationStatusApi(String taskId) async {
  return await Http.get('/ai/video/generations/$taskId');
}

/// 获取图片生成模型及参数
///
/// 返回模型配置列表（包含模型名、支持尺寸、风格等）
Future<Map?> getImageGenerationModelsApi() async {
  return await Http.get('/ai/models/image/generation');
}

/// 获取图片编辑模型及参数
///
/// 返回模型配置列表（包含模型名、支持遮罩等）
Future<Map?> getImageEditModelsApi() async {
  return await Http.get('/ai/models/image/edit');
}

/// 获取视频生成模型及参数
///
/// 返回模型配置列表（如支持的视频大小、时长等）
Future<Map?> getVideoGenerationModelsApi() async {
  return await Http.get('/ai/models/video/generation');
}

/// AI生成视频标题和描述
///
/// [url] 视频URL地址（必填）
/// [type] 生成类型：1=标题，2=描述（必填）
/// [max] 最大字数限制（必填）
/// 返回生成的文本内容
Future<String?> generateVideoAiTitleApi({
  required String url,
  required int type,
  required int max,
}) async {
  final Map<String, dynamic> data = {'url': url, 'type': type, 'max': max - 10};

  final response = await Http.post('/tools/ai/video/title', data: data);

  if (response != null && response['code'] == 0) {
    return response['data'] as String?;
  }

  return null;
}
