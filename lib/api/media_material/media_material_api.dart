import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/network/http.dart';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';

/// ********** 素材部分 **********

/// 创建素材
Future<Response?> createMaterialApi({
  required String groupId,
  required String coverUrl,
  required List<Map<String, dynamic>> mediaList,
  required String title,
  required String desc,
  required List<dynamic> location,
  Map<String, dynamic>? option,
}) async {
  return await Http.postResponse(
    '/material',
    data: {
      'groupId': groupId,
      'coverUrl': coverUrl,
      'mediaList': mediaList,
      'title': title,
      'desc': desc,
      'location': location,
      'option': option ?? {},
    },
  );
}

/// 创建批量生成素材任务
Future<Response?> createMaterialTaskApi({
  required String groupId,
  required int num,
  required String aiModelTag,
  required String prompt,
  required String title,
  required String desc,
  required List<String> location,
  required List<String> mediaGroups,
  required String coverGroup,
  required int textMax,
  required String language,
  Map<String, dynamic>? option,
}) async {
  return await Http.postResponse(
    '/material/task/create',
    data: {
      'groupId': groupId,
      'num': num,
      'aiModelTag': aiModelTag,
      'prompt': prompt,
      'title': title,
      'desc': desc,
      'location': location,
      'mediaGroups': mediaGroups,
      'coverGroup': coverGroup,
      'option': option ?? {},
      'textMax': textMax,
      'language': language,
    },
  );
}

/// 预览草稿生成任务
Future<Response?> previewMaterialTaskApi(String id) async {
  return await Http.getResponse('/material/task/preview/$id');
}

/// 开始素材生成任务
Future<Response?> startMaterialTaskApi(String id) async {
  return await Http.getResponse('/material/task/start/$id');
}

/// 删除素材
Future<Map?> deleteMaterialApi(String id) async {
  return await Http.delete('/material/$id');
}

/// 获取素材列表（分页 + 过滤）
Future<Response?> getMaterialListApi({
  required int pageNo,
  required int pageSize,
  String? title,
  String? status, // "0","1","-1"
  String? groupId,
}) async {
  return await Http.getResponse(
    '/material/list/$pageNo/$pageSize',
    query: {
      if (title != null) 'title': title,
      if (status != null) 'status': status,
      if (groupId != null) 'groupId': groupId,
    },
  );
}

/// 创建素材组
Future<Map?> createMaterialGroupApi({
  required String type,
  required String name,
}) async {
  return await Http.post('/material/group', data: {'type': type, 'name': name});
}

/// 删除素材组
Future<Map?> deleteMaterialGroupApi(String id) async {
  return await Http.delete('/material/group/$id');
}

/// 更新素材组信息
Future<Map?> updateMaterialGroupInfoApi({
  required String id,
  required String name,
}) async {
  return await Http.post('/material/group/info/$id', data: {'name': name});
}

/// 获取素材组列表（分页）
Future<Response?> getMaterialGroupListApi({
  required int pageNo,
  required int pageSize,
}) async {
  return await Http.getResponse('/material/group/list/$pageNo/$pageSize');
}

/// ********** 媒体部分 **********

/// 创建媒体
Future<Map?> createMediaApi({
  required String groupId,
  required String type, // "video"、"img" 等
  required String url,
  required String title,
  required String desc,
}) async {
  return await Http.post(
    '/media',
    data: {
      'groupId': groupId,
      'type': type,
      'url': url,
      'title': title,
      'desc': desc,
    },
  );
}

/// 删除媒体
Future<Map?> deleteMediaApi(String id) async {
  return await Http.delete('/media/$id');
}

/// 获取媒体列表（分页 + 过滤）
Future<Response?> getMediaListApi({
  required int pageNo,
  required int pageSize,
  String? groupId,
}) async {
  return await Http.getResponse(
    '/media/list/$pageNo/$pageSize',
    query: {if (groupId != null) 'groupId': groupId},
  );
}

/// 创建媒体组
Future<Map?> createMediaGroupApi({
  required String type, // "video"、"img"
  required String title,
  required String desc,
}) async {
  return await Http.post(
    '/media/group',
    data: {'type': type, 'title': title, 'desc': desc},
  );
}

/// 删除媒体库空间
Future<Map?> deleteMediaGroupApi(String id) async {
  return await Http.delete('/media/group/$id');
}

/// 更新媒体组信息
Future<Map?> updateMediaGroupInfoApi({
  required String id,
  required String title,
  required String desc,
}) async {
  return await Http.post(
    '/media/group/info/$id',
    data: {'title': title, 'desc': desc},
  );
}

/// 获取媒体组列表（分页 + 过滤）
Future<Response?> getMediaGroupListApi({
  required int pageNo,
  required int pageSize,
  String? type, // "video"、"img"
}) async {
  return await Http.getResponse(
    '/media/group/list/$pageNo/$pageSize',
    query: {if (type != null) 'type': type},
  );
}

/// 上传文件
Future<Map?> updateOSSFile({
  required File file,
  required String fileName,
}) async {
  final mimeType = lookupMimeType(file.path) ?? 'application/octet-stream';
  LoggerUtil.i('上传文件：$fileName ${file.path} $mimeType');
  final formData = FormData.fromMap({
    'file': await MultipartFile.fromFile(
      file.path,
      filename: fileName,
      contentType: MediaType.parse(mimeType),
    ),
  });

  return await Http.post<Map>(
    '/file/upload',
    data: formData,
    options: Options(
      contentType: 'multipart/form-data',
      sendTimeout: const Duration(minutes: 60),
      receiveTimeout: const Duration(minutes: 60),
    ),
  );

  // return await Http.post<Map>(
  //   '/oss/upload',
  //   data: formData,
  //   options: Options(
  //     contentType: 'multipart/form-data',
  //     sendTimeout: const Duration(minutes: 60),
  //     receiveTimeout: const Duration(minutes: 60),
  //   ),
  // );
}
