// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hot_topics_module.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HotTopicsModule _$HotTopicsModuleFromJson(Map<String, dynamic> json) =>
    HotTopicsModule(
      (json['items'] as List<dynamic>)
          .map((e) => Items.fromJson(e as Map<String, dynamic>))
          .toList(),
      Meta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$HotTopicsModuleToJson(HotTopicsModule instance) =>
    <String, dynamic>{'items': instance.items, 'meta': instance.meta};

Items _$ItemsFromJson(Map<String, dynamic> json) => Items(
  json['title'] as String?,
  json['description'] as String?,
  json['msgType'] as String?,
  json['category'] as String?,
  json['subCategory'] as String?,
  json['originalId'] as String?,
  json['author'] as String?,
  json['avatar'] as String?,
  json['cover'] as String?,
  json['authorId'] as String?,
  (json['fans'] as num?)?.toInt(),
  (json['topics'] as List<dynamic>?)?.map((e) => e as String).toList(),
  (json['rank'] as num?)?.toDouble(),
  (json['shareCount'] as num?)?.toInt(),
  (json['likeCount'] as num?)?.toInt(),
  (json['readCount'] as num).toInt(),
  (json['collectCount'] as num).toInt(),
  (json['commentCount'] as num).toInt(),
  json['publishTime'] as String,
  json['platformId'] as String,
  json['update_time'] as String,
  json['label'] as String?,
  json['secondLabel'] as String?,
  json['timeType'] as String?,
  json['platform_name'] as String?,
  json['url'] as String?,
  json['authorUrl'] as String?,
  json['id'] as String,
  (json['rankingPosition'] as num).toInt(),
);

Map<String, dynamic> _$ItemsToJson(Items instance) => <String, dynamic>{
  'title': instance.title,
  'description': instance.description,
  'msgType': instance.msgType,
  'category': instance.category,
  'subCategory': instance.subCategory,
  'originalId': instance.originalId,
  'author': instance.author,
  'avatar': instance.avatar,
  'cover': instance.cover,
  'authorId': instance.authorId,
  'fans': instance.fans,
  'topics': instance.topics,
  'rank': instance.rank,
  'shareCount': instance.shareCount,
  'likeCount': instance.likeCount,
  'readCount': instance.readCount,
  'collectCount': instance.collectCount,
  'commentCount': instance.commentCount,
  'publishTime': instance.publishTime,
  'platformId': instance.platformId,
  'update_time': instance.updateTime,
  'label': instance.label,
  'secondLabel': instance.secondLabel,
  'timeType': instance.timeType,
  'platform_name': instance.platformName,
  'url': instance.url,
  'authorUrl': instance.authorUrl,
  'id': instance.id,
  'rankingPosition': instance.rankingPosition,
};
