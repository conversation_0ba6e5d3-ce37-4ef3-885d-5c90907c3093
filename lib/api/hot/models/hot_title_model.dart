import 'package:aitoearn_app/api/hot/models/hot_ranking_contents_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'hot_title_model.g.dart';

@JsonSerializable()
class HotTitleModel extends Object {
  @Json<PERSON>ey(name: 'items')
  List<Items> items;

  @Json<PERSON>ey(name: 'meta')
  Meta meta;

  HotTitleModel(this.items, this.meta);

  factory HotTitleModel.fromJson(Map<String, dynamic> srcJson) =>
      _$HotTitleModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotTitleModelToJson(this);
}

@JsonSerializable()
class Items extends Object {
  @Json<PERSON>ey(name: 'category')
  String category;

  @JsonKey(name: 'title')
  String title;

  @Json<PERSON>ey(name: 'rank')
  int rank;

  @JsonKey(name: 'engagement')
  int engagement;

  @Json<PERSON>ey(name: 'originalId')
  String originalId;

  @Json<PERSON><PERSON>(name: 'update_time')
  String updateTime;

  @Json<PERSON><PERSON>(name: 'timeType')
  String timeType;

  @Json<PERSON>ey(name: 'url')
  String url;

  @Json<PERSON>ey(name: 'platformId')
  String platformId;

  @JsonKey(name: 'id')
  String id;

  Items(
    this.category,
    this.title,
    this.rank,
    this.engagement,
    this.originalId,
    this.updateTime,
    this.timeType,
    this.url,
    this.platformId,
    this.id,
  );

  factory Items.fromJson(Map<String, dynamic> srcJson) =>
      _$ItemsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ItemsToJson(this);
}

List<HotTitleTopBModel> getHotTitleTopBModelList(List<dynamic> list) {
  List<HotTitleTopBModel> result = [];
  for (var item in list) {
    result.add(HotTitleTopBModel.fromJson(item));
  }
  return result;
}

@JsonSerializable()
class HotTitleTopBModel extends Object {
  @JsonKey(name: 'category')
  String category;

  @JsonKey(name: 'titles')
  List<Items> titles;

  HotTitleTopBModel(this.category, this.titles);

  factory HotTitleTopBModel.fromJson(Map<String, dynamic> srcJson) =>
      _$HotTitleTopBModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotTitleTopBModelToJson(this);
}
