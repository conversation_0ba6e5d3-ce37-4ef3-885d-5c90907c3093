import 'package:aitoearn_app/api/hot/models/hot_platform_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'hot_event_model.g.dart';

List<HotEventModel> getHotEventModelList(List<dynamic> list) {
  List<HotEventModel> result = [];
  for (var item in list) {
    result.add(HotEventModel.fromJson(item));
  }
  return result;
}

@JsonSerializable()
class HotEventModel extends Object {
  @JsonKey(name: 'platform')
  HotPlatformModel platform;

  @Json<PERSON>ey(name: 'topics')
  List<Topics> topics;

  HotEventModel(this.platform, this.topics);

  factory HotEventModel.fromJson(Map<String, dynamic> srcJson) =>
      _$HotEventModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotEventModelToJson(this);
}

@JsonSerializable()
class Topics extends Object {
  @J<PERSON><PERSON><PERSON>(name: 'originalId')
  String? originalId;

  @JsonKey(name: 'title')
  String? title;

  @JsonKey(name: 'hotValue')
  String? hotValue;

  @JsonKey(name: 'rank')
  int? rank;

  @JsonKey(name: 'rankChange')
  int? rankChange;

  @JsonKey(name: 'hotValueHistory')
  List<HotValueHistory>? hotValueHistory;

  @JsonKey(name: 'rankHistory')
  List<dynamic>? rankHistory;

  @JsonKey(name: 'fetchTime')
  String? fetchTime;

  @JsonKey(name: 'createdAt')
  String? createdAt;

  @JsonKey(name: 'updatedAt')
  String? updatedAt;

  @JsonKey(name: 'plat_name')
  String? platName;

  @JsonKey(name: 'platformId')
  String platformId;

  @JsonKey(name: 'url')
  String url;

  @JsonKey(name: 'id')
  String id;

  Topics(
    this.originalId,
    this.title,
    this.hotValue,
    this.rank,
    this.rankChange,
    this.hotValueHistory,
    this.rankHistory,
    this.fetchTime,
    this.createdAt,
    this.updatedAt,
    this.platName,
    this.platformId,
    this.url,
    this.id,
  );

  factory Topics.fromJson(Map<String, dynamic> srcJson) =>
      _$TopicsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$TopicsToJson(this);
}

@JsonSerializable()
class HotValueHistory extends Object {
  @JsonKey(name: 'hotValue')
  double? hotValue;

  @JsonKey(name: 'sentence')
  String sentence;

  @JsonKey(name: 'updateTime')
  String updateTime;

  HotValueHistory(this.hotValue, this.sentence, this.updateTime);

  factory HotValueHistory.fromJson(Map<String, dynamic> srcJson) =>
      _$HotValueHistoryFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotValueHistoryToJson(this);
}
