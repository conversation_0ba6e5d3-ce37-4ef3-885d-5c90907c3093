import 'package:json_annotation/json_annotation.dart';

part 'hot_ranking_dates_model.g.dart';

List<HotRankingDatesModel> getHotRankingDatesModelList(List<dynamic> list) {
  List<HotRankingDatesModel> result = [];
  for (var item in list) {
    result.add(HotRankingDatesModel.fromJson(item));
  }
  return result;
}

@JsonSerializable()
class HotRankingDatesModel extends Object {
  @Json<PERSON>ey(name: '_id')
  String id;

  @J<PERSON><PERSON><PERSON>(name: 'spiderTime')
  String spiderTime;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'rankingId')
  String rankingId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'showDate')
  String showDate;

  @J<PERSON><PERSON><PERSON>(name: 'queryDate')
  String queryDate;

  @J<PERSON><PERSON>ey(name: 'value')
  String value;

  HotRankingDatesModel(
    this.id,
    this.spiderTime,
    this.rankingId,
    this.showDate,
    this.queryDate,
    this.value,
  );

  factory HotRankingDatesModel.fromJson(Map<String, dynamic> srcJson) =>
      _$HotRankingDatesModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotRankingDatesModelToJson(this);
}
