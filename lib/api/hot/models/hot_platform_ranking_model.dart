import 'package:json_annotation/json_annotation.dart';

part 'hot_platform_ranking_model.g.dart';

List<HotPlatformRankingModel> getHotPlatformRankingModelList(
  List<dynamic> list,
) {
  List<HotPlatformRankingModel> result = [];
  for (var item in list) {
    result.add(HotPlatformRankingModel.fromJson(item));
  }
  return result;
}

@JsonSerializable()
class HotPlatformRankingModel extends Object {
  @Json<PERSON><PERSON>(name: 'createTime')
  String createTime;

  @Json<PERSON>ey(name: 'description')
  String? description;

  @Json<PERSON>ey(name: 'description_en')
  String descriptionEn;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'icon')
  String icon;

  @Json<PERSON>ey(name: 'name')
  String name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'name_en')
  String nameEn;

  @JsonKey(name: 'platformId')
  String platformId;

  @Json<PERSON>ey(name: 'rules')
  String rules;

  @JsonKey(name: 'sort')
  int sort;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'status')
  int status;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'type')
  String type;

  @Json<PERSON>ey(name: 'updateFrequency')
  String updateFrequency;

  @Json<PERSON>ey(name: 'updateFrequency_en')
  String updateFrequencyEn;

  @JsonKey(name: 'updateTime')
  String updateTime;

  @JsonKey(name: 'labels_show')
  String labelsShow;

  @JsonKey(name: 'id')
  String id;

  @JsonKey(name: 'platform')
  Platform platform;

  HotPlatformRankingModel(
    this.createTime,
    this.description,
    this.descriptionEn,
    this.icon,
    this.name,
    this.nameEn,
    this.platformId,
    this.rules,
    this.sort,
    this.status,
    this.type,
    this.updateFrequency,
    this.updateFrequencyEn,
    this.updateTime,
    this.labelsShow,
    this.id,
    this.platform,
  );

  factory HotPlatformRankingModel.fromJson(Map<String, dynamic> srcJson) =>
      _$HotPlatformRankingModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotPlatformRankingModelToJson(this);
}

@JsonSerializable()
class Platform extends Object {
  @JsonKey(name: 'name')
  String name;

  @JsonKey(name: 'type')
  String type;

  @JsonKey(name: 'description')
  String? description;

  @JsonKey(name: 'icon')
  String icon;

  @JsonKey(name: 'status')
  int status;

  @JsonKey(name: 'sort')
  int sort;

  @JsonKey(name: 'hotinfo')
  String hotinfo;

  @JsonKey(name: 'language')
  String language;

  @JsonKey(name: 'name_en')
  String nameEn;

  @JsonKey(name: 'description_en')
  String descriptionEn;

  @JsonKey(name: 'id')
  String id;

  Platform(
    this.name,
    this.type,
    this.description,
    this.icon,
    this.status,
    this.sort,
    this.hotinfo,
    this.language,
    this.nameEn,
    this.descriptionEn,
    this.id,
  );

  factory Platform.fromJson(Map<String, dynamic> srcJson) =>
      _$PlatformFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PlatformToJson(this);
}
