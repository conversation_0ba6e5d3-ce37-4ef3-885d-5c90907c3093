// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hot_ranking_contents_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HotRankingContentsModel _$HotRankingContentsModelFromJson(
  Map<String, dynamic> json,
) => HotRankingContentsModel(
  (json['items'] as List<dynamic>)
      .map((e) => Items.fromJson(e as Map<String, dynamic>))
      .toList(),
  Meta.fromJson(json['meta'] as Map<String, dynamic>),
);

Map<String, dynamic> _$HotRankingContentsModelToJson(
  HotRankingContentsModel instance,
) => <String, dynamic>{'items': instance.items, 'meta': instance.meta};

Items _$ItemsFromJson(Map<String, dynamic> json) =>
    Items(
        json['rankingId'] as String?,
        json['col_name'] as String?,
        (json['status'] as num?)?.toDouble(),
        (json['videoDuration'] as num?)?.toInt(),
        json['anaAdd'] == null
            ? null
            : AnaAdd.fromJson(json['anaAdd'] as Map<String, dynamic>),
        json['url'] as String?,
        json['photoId'] as String?,
        json['category'] as String?,
        json['subCategory'] as String?,
        json['type'] as String?,
        json['cover'] as String?,
        json['title'] as String?,
        json['createTime'] as String?,
        json['updateTime'] as String?,
        json['author'] == null
            ? null
            : Author.fromJson(json['author'] as Map<String, dynamic>),
        json['stats'] == null
            ? null
            : Stats.fromJson(json['stats'] as Map<String, dynamic>),
        json['publishTime'] as String?,
        (json['rankingPosition'] as num?)?.toInt(),
        json['spider_time'] as String?,
        json['platformId'] as String?,
        json['originalId'] as String?,
        json['label'] as String?,
        json['secondLabel'] as String?,
        json['id'] as String,
      )
      ..shareCount = (json['shareCount'] as num?)?.toInt()
      ..collectCount = (json['collectCount'] as num?)?.toInt();

Map<String, dynamic> _$ItemsToJson(Items instance) => <String, dynamic>{
  'rankingId': instance.rankingId,
  'col_name': instance.colName,
  'status': instance.status,
  'videoDuration': instance.videoDuration,
  'anaAdd': instance.anaAdd,
  'url': instance.url,
  'photoId': instance.photoId,
  'category': instance.category,
  'subCategory': instance.subCategory,
  'type': instance.type,
  'cover': instance.cover,
  'title': instance.title,
  'createTime': instance.createTime,
  'updateTime': instance.updateTime,
  'author': instance.author,
  'stats': instance.stats,
  'publishTime': instance.publishTime,
  'rankingPosition': instance.rankingPosition,
  'shareCount': instance.shareCount,
  'collectCount': instance.collectCount,
  'spider_time': instance.spiderTime,
  'platformId': instance.platformId,
  'originalId': instance.originalId,
  'label': instance.label,
  'secondLabel': instance.secondLabel,
  'id': instance.id,
};

AnaAdd _$AnaAddFromJson(Map<String, dynamic> json) => AnaAdd(
  addCollectCount: AnaAdd._intFromJson(json['addCollectCount']),
  addCommentCount: AnaAdd._intFromJson(json['addCommentCount']),
  addLikeCount: AnaAdd._intFromJson(json['addLikeCount']),
  addShareCount: AnaAdd._intFromJson(json['addShareCount']),
  useCollectCount: AnaAdd._intFromJson(json['useCollectCount']),
  useCommentCount: AnaAdd._intFromJson(json['useCommentCount']),
  useLikeCount: AnaAdd._intFromJson(json['useLikeCount']),
  useShareCount: AnaAdd._intFromJson(json['useShareCount']),
  collectedCount: AnaAdd._intFromJson(json['collectedCount']),
  addInteractiveCount: AnaAdd._intFromJson(json['addInteractiveCount']),
  interactiveCount: AnaAdd._intFromJson(json['interactiveCount']),
)..addViewCount = AnaAdd._intFromJson(json['addViewCount']);

Map<String, dynamic> _$AnaAddToJson(AnaAdd instance) => <String, dynamic>{
  'addCollectCount': instance.addCollectCount,
  'addCommentCount': instance.addCommentCount,
  'addLikeCount': instance.addLikeCount,
  'addShareCount': instance.addShareCount,
  'useCollectCount': instance.useCollectCount,
  'useCommentCount': instance.useCommentCount,
  'addViewCount': instance.addViewCount,
  'useLikeCount': instance.useLikeCount,
  'useShareCount': instance.useShareCount,
  'collectedCount': instance.collectedCount,
  'addInteractiveCount': instance.addInteractiveCount,
  'interactiveCount': instance.interactiveCount,
};

Author _$AuthorFromJson(Map<String, dynamic> json) => Author(
  id: Author._idFromJson(json['id']),
  name: Author._stringFromJson(json['name']),
  fansCount: Author._intFromJson(json['fansCount']),
  avatar: Author._stringFromJson(json['avatar']),
);

Map<String, dynamic> _$AuthorToJson(Author instance) => <String, dynamic>{
  'id': Author._idToJson(instance.id),
  'name': instance.name,
  'fansCount': instance.fansCount,
  'avatar': instance.avatar,
};

Stats _$StatsFromJson(Map<String, dynamic> json) => Stats(
  (json['commentCount'] as num?)?.toInt(),
  (json['likeCount'] as num?)?.toInt(),
  (json['viewCount'] as num?)?.toInt(),
);

Map<String, dynamic> _$StatsToJson(Stats instance) => <String, dynamic>{
  'commentCount': instance.commentCount,
  'likeCount': instance.likeCount,
  'viewCount': instance.viewCount,
};

Meta _$MetaFromJson(Map<String, dynamic> json) => Meta(
  (json['itemCount'] as num).toInt(),
  (json['totalItems'] as num).toInt(),
  (json['itemsPerPage'] as num).toInt(),
  (json['totalPages'] as num).toInt(),
  (json['currentPage'] as num).toInt(),
);

Map<String, dynamic> _$MetaToJson(Meta instance) => <String, dynamic>{
  'itemCount': instance.itemCount,
  'totalItems': instance.totalItems,
  'itemsPerPage': instance.itemsPerPage,
  'totalPages': instance.totalPages,
  'currentPage': instance.currentPage,
};
