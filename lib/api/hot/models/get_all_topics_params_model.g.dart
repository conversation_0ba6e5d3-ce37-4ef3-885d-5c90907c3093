// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_all_topics_params_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetAllTopicsParamsModel _$GetAllTopicsParamsModelFromJson(
  Map<String, dynamic> json,
) => GetAllTopicsParamsModel(
  msgType: json['msgType'] as String?,
  timeType: json['timeType'] as String?,
  type: json['type'] as String?,
  platformId: json['platformId'] as String?,
  startTime: json['startTime'] as String?,
  endTime: json['endTime'] as String?,
  topic: json['topic'] as String?,
  page: (json['page'] as num?)?.toInt(),
  pageSize: (json['pageSize'] as num?)?.toInt(),
);

Map<String, dynamic> _$GetAllTopicsParamsModelToJson(
  GetAllTopicsParamsModel instance,
) => <String, dynamic>{
  'msgType': instance.msgType,
  'timeType': instance.timeType,
  'type': instance.type,
  'platformId': instance.platformId,
  'startTime': instance.startTime,
  'endTime': instance.endTime,
  'topic': instance.topic,
  'page': instance.page,
  'pageSize': instance.pageSize,
};
