// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hot_event_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HotEventModel _$HotEventModelFromJson(Map<String, dynamic> json) =>
    HotEventModel(
      HotPlatformModel.fromJson(json['platform'] as Map<String, dynamic>),
      (json['topics'] as List<dynamic>)
          .map((e) => Topics.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$HotEventModelToJson(HotEventModel instance) =>
    <String, dynamic>{'platform': instance.platform, 'topics': instance.topics};

Topics _$TopicsFromJson(Map<String, dynamic> json) => Topics(
  json['originalId'] as String?,
  json['title'] as String?,
  json['hotValue'] as String?,
  (json['rank'] as num?)?.toInt(),
  (json['rankChange'] as num?)?.toInt(),
  (json['hotValueHistory'] as List<dynamic>?)
      ?.map((e) => HotValueHistory.fromJson(e as Map<String, dynamic>))
      .toList(),
  json['rankHistory'] as List<dynamic>?,
  json['fetchTime'] as String?,
  json['createdAt'] as String?,
  json['updatedAt'] as String?,
  json['plat_name'] as String?,
  json['platformId'] as String,
  json['url'] as String,
  json['id'] as String,
);

Map<String, dynamic> _$TopicsToJson(Topics instance) => <String, dynamic>{
  'originalId': instance.originalId,
  'title': instance.title,
  'hotValue': instance.hotValue,
  'rank': instance.rank,
  'rankChange': instance.rankChange,
  'hotValueHistory': instance.hotValueHistory,
  'rankHistory': instance.rankHistory,
  'fetchTime': instance.fetchTime,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'plat_name': instance.platName,
  'platformId': instance.platformId,
  'url': instance.url,
  'id': instance.id,
};

HotValueHistory _$HotValueHistoryFromJson(Map<String, dynamic> json) =>
    HotValueHistory(
      (json['hotValue'] as num?)?.toDouble(),
      json['sentence'] as String,
      json['updateTime'] as String,
    );

Map<String, dynamic> _$HotValueHistoryToJson(HotValueHistory instance) =>
    <String, dynamic>{
      'hotValue': instance.hotValue,
      'sentence': instance.sentence,
      'updateTime': instance.updateTime,
    };
