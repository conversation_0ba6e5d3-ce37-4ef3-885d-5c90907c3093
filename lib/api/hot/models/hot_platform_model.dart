import 'package:json_annotation/json_annotation.dart';

part 'hot_platform_model.g.dart';

List<HotPlatformModel> getHotPlatformModelList(List<dynamic> list) {
  List<HotPlatformModel> result = [];
  for (var item in list) {
    result.add(HotPlatformModel.fromJson(item));
  }
  return result;
}

@JsonSerializable()
class HotPlatformModel extends Object {
  @J<PERSON><PERSON><PERSON>(name: 'name')
  String name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'type')
  String type;

  @Json<PERSON>ey(name: 'description')
  String? description;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'icon')
  String icon;

  @Json<PERSON>ey(name: 'status')
  int status;

  @<PERSON>son<PERSON>ey(name: 'sort')
  int sort;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'createTime')
  String createTime;

  @Json<PERSON>ey(name: 'updateTime')
  String updateTime;

  @<PERSON>son<PERSON><PERSON>(name: 'hotinfo')
  String? hotinfo;

  @Json<PERSON>ey(name: 'language')
  String? language;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'name_en')
  String? nameEn;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'description_en')
  String? descriptionEn;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'id')
  String id;

  HotPlatformModel(
    this.name,
    this.type,
    this.description,
    this.icon,
    this.status,
    this.sort,
    this.createTime,
    this.updateTime,
    this.hotinfo,
    this.language,
    this.nameEn,
    this.descriptionEn,
    this.id,
  );

  factory HotPlatformModel.fromJson(Map<String, dynamic> srcJson) =>
      _$HotPlatformModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotPlatformModelToJson(this);
}
