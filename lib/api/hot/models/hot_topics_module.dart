import 'package:aitoearn_app/api/hot/models/hot_ranking_contents_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'hot_topics_module.g.dart';

@JsonSerializable()
class HotTopicsModule extends Object {
  @Json<PERSON>ey(name: 'items')
  List<Items> items;

  @Json<PERSON>ey(name: 'meta')
  Meta meta;

  HotTopicsModule(this.items, this.meta);

  factory HotTopicsModule.fromJson(Map<String, dynamic> srcJson) =>
      _$HotTopicsModuleFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotTopicsModuleToJson(this);
}

@JsonSerializable()
class Items extends Object {
  @Json<PERSON><PERSON>(name: 'title')
  String? title;

  @Json<PERSON>ey(name: 'description')
  String? description;

  @Json<PERSON>ey(name: 'msgType')
  String? msgType;

  @JsonKey(name: 'category')
  String? category;

  @Json<PERSON>ey(name: 'subCategory')
  String? subCategory;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'originalId')
  String? originalId;

  @Json<PERSON><PERSON>(name: 'author')
  String? author;

  @JsonKey(name: 'avatar')
  String? avatar;

  @Json<PERSON>ey(name: 'cover')
  String? cover;

  @JsonKey(name: 'authorId')
  String? authorId;

  @JsonKey(name: 'fans')
  int? fans;

  @JsonKey(name: 'topics')
  List<String>? topics;

  @JsonKey(name: 'rank')
  double? rank;

  @JsonKey(name: 'shareCount')
  int? shareCount;

  @JsonKey(name: 'likeCount')
  int? likeCount;

  @JsonKey(name: 'readCount')
  int readCount;

  @JsonKey(name: 'collectCount')
  int collectCount;

  @JsonKey(name: 'commentCount')
  int commentCount;

  @JsonKey(name: 'publishTime')
  String publishTime;

  @JsonKey(name: 'platformId')
  String platformId;

  @JsonKey(name: 'update_time')
  String updateTime;

  @JsonKey(name: 'label')
  String? label;

  @JsonKey(name: 'secondLabel')
  String? secondLabel;

  @JsonKey(name: 'timeType')
  String? timeType;

  @JsonKey(name: 'platform_name')
  String? platformName;

  @JsonKey(name: 'url')
  String? url;

  @JsonKey(name: 'authorUrl')
  String? authorUrl;

  @JsonKey(name: 'id')
  String id;

  @JsonKey(name: 'rankingPosition')
  int rankingPosition;

  Items(
    this.title,
    this.description,
    this.msgType,
    this.category,
    this.subCategory,
    this.originalId,
    this.author,
    this.avatar,
    this.cover,
    this.authorId,
    this.fans,
    this.topics,
    this.rank,
    this.shareCount,
    this.likeCount,
    this.readCount,
    this.collectCount,
    this.commentCount,
    this.publishTime,
    this.platformId,
    this.updateTime,
    this.label,
    this.secondLabel,
    this.timeType,
    this.platformName,
    this.url,
    this.authorUrl,
    this.id,
    this.rankingPosition,
  );

  factory Items.fromJson(Map<String, dynamic> srcJson) =>
      _$ItemsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ItemsToJson(this);
}
