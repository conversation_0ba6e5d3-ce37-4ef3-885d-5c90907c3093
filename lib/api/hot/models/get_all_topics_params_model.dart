import 'package:json_annotation/json_annotation.dart';

part 'get_all_topics_params_model.g.dart';

/// 获取热门专题列表入参
@JsonSerializable(genericArgumentFactories: true)
class GetAllTopicsParamsModel {
  final String? msgType; // 项目类型
  final String? timeType; // 时间分类
  final String? type; // 类型
  final String? platformId; // 平台ID
  final String? startTime; // 发布时间开始
  final String? endTime; // 发布时间结束
  final String? topic; // 话题标签
  final int? page; // 页码
  final int? pageSize; // 每页大小

  GetAllTopicsParamsModel({
    this.msgType,
    this.timeType,
    this.type,
    this.platformId,
    this.startTime,
    this.endTime,
    this.topic,
    this.page,
    this.pageSize,
  });

  factory GetAllTopicsParamsModel.fromJson(Map<String, dynamic> json) =>
      _$GetAllTopicsParamsModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetAllTopicsParamsModelToJson(this);
}
