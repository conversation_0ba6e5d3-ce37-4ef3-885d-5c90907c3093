import 'package:json_annotation/json_annotation.dart';

part 'hot_ranking_contents_model.g.dart';

@JsonSerializable()
class HotRankingContentsModel extends Object {
  @JsonKey(name: 'items')
  List<Items> items;

  @Json<PERSON>ey(name: 'meta')
  Meta meta;

  HotRankingContentsModel(this.items, this.meta);

  factory HotRankingContentsModel.fromJson(Map<String, dynamic> srcJson) =>
      _$HotRankingContentsModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$HotRankingContentsModelToJson(this);
}

@JsonSerializable()
class Items extends Object {
  @JsonKey(name: 'rankingId')
  String? rankingId;

  @Json<PERSON>ey(name: 'col_name')
  String? colName;

  @JsonKey(name: 'status')
  double? status;

  @Json<PERSON>ey(name: 'videoDuration')
  int? videoDuration;

  @<PERSON>son<PERSON><PERSON>(name: 'anaAdd')
  AnaAdd? anaAdd;

  @J<PERSON><PERSON><PERSON>(name: 'url')
  String? url;

  @Json<PERSON><PERSON>(name: 'photoId')
  String? photoId;

  @JsonKey(name: 'category')
  String? category;

  @JsonKey(name: 'subCategory')
  String? subCategory;

  @JsonKey(name: 'type')
  String? type;

  @JsonKey(name: 'cover')
  String? cover;

  @JsonKey(name: 'title')
  String? title;

  @JsonKey(name: 'createTime')
  String? createTime;

  @JsonKey(name: 'updateTime')
  String? updateTime;

  @JsonKey(name: 'author')
  Author? author;

  @JsonKey(name: 'stats')
  Stats? stats;

  @JsonKey(name: 'publishTime')
  String? publishTime;

  @JsonKey(name: 'rankingPosition')
  int? rankingPosition;

  @JsonKey(name: 'shareCount')
  int? shareCount;

  @JsonKey(name: 'collectCount')
  int? collectCount;

  @JsonKey(name: 'spider_time')
  String? spiderTime;

  @JsonKey(name: 'platformId')
  String? platformId;

  @JsonKey(name: 'originalId')
  String? originalId;

  @JsonKey(name: 'label')
  String? label;

  @JsonKey(name: 'secondLabel')
  String? secondLabel;

  @JsonKey(name: 'id')
  String id;

  Items(
    this.rankingId,
    this.colName,
    this.status,
    this.videoDuration,
    this.anaAdd,
    this.url,
    this.photoId,
    this.category,
    this.subCategory,
    this.type,
    this.cover,
    this.title,
    this.createTime,
    this.updateTime,
    this.author,
    this.stats,
    this.publishTime,
    this.rankingPosition,
    this.spiderTime,
    this.platformId,
    this.originalId,
    this.label,
    this.secondLabel,
    this.id,
  );

  factory Items.fromJson(Map<String, dynamic> srcJson) =>
      _$ItemsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$ItemsToJson(this);
}

@JsonSerializable()
class AnaAdd extends Object {
  @JsonKey(name: 'addCollectCount', fromJson: _intFromJson)
  int? addCollectCount;

  @JsonKey(name: 'addCommentCount', fromJson: _intFromJson)
  int? addCommentCount;

  @JsonKey(name: 'addLikeCount', fromJson: _intFromJson)
  int? addLikeCount;

  @JsonKey(name: 'addShareCount', fromJson: _intFromJson)
  int? addShareCount;

  @JsonKey(name: 'useCollectCount', fromJson: _intFromJson)
  int? useCollectCount;

  @JsonKey(name: 'useCommentCount', fromJson: _intFromJson)
  int? useCommentCount;

  @JsonKey(name: 'addViewCount', fromJson: _intFromJson)
  int? addViewCount;

  @JsonKey(name: 'useLikeCount', fromJson: _intFromJson)
  int? useLikeCount;

  @JsonKey(name: 'useShareCount', fromJson: _intFromJson)
  int? useShareCount;

  @JsonKey(name: 'collectedCount', fromJson: _intFromJson)
  int? collectedCount;

  @JsonKey(name: 'addInteractiveCount', fromJson: _intFromJson)
  int? addInteractiveCount;

  @JsonKey(name: 'interactiveCount', fromJson: _intFromJson)
  int? interactiveCount;

  AnaAdd({
    this.addCollectCount,
    this.addCommentCount,
    this.addLikeCount,
    this.addShareCount,
    this.useCollectCount,
    this.useCommentCount,
    this.useLikeCount,
    this.useShareCount,
    this.collectedCount,
    this.addInteractiveCount,
    this.interactiveCount,
  });

  static int? _intFromJson(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  factory AnaAdd.fromJson(Map<String, dynamic> srcJson) =>
      _$AnaAddFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AnaAddToJson(this);
}

@JsonSerializable()
class Author extends Object {
  @JsonKey(name: 'id', fromJson: _idFromJson, toJson: _idToJson)
  String id;

  @JsonKey(name: 'name', fromJson: _stringFromJson)
  String? name;

  @JsonKey(name: 'fansCount', fromJson: _intFromJson)
  int? fansCount;

  @JsonKey(name: 'avatar', fromJson: _stringFromJson)
  String? avatar;

  Author({required this.id, this.name, this.fansCount, this.avatar});

  static String _idFromJson(dynamic value) {
    if (value == null) return '';
    if (value is String) return value;
    if (value is int) return value.toString();
    if (value is double) return value.toInt().toString();
    return value.toString();
  }

  static String? _stringFromJson(dynamic value) {
    if (value == null) return null;
    if (value is String) return value;
    if (value is int) return value.toString();
    if (value is double) return value.toString();
    return value.toString();
  }

  static int? _intFromJson(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  static dynamic _idToJson(String value) => value;

  factory Author.fromJson(Map<String, dynamic> srcJson) =>
      _$AuthorFromJson(srcJson);

  Map<String, dynamic> toJson() => _$AuthorToJson(this);
}

@JsonSerializable()
class Stats extends Object {
  int? commentCount;
  int? likeCount;
  int? viewCount;

  Stats(this.commentCount, this.likeCount, this.viewCount);

  factory Stats.fromJson(Map<String, dynamic> srcJson) =>
      _$StatsFromJson(srcJson);

  Map<String, dynamic> toJson() => _$StatsToJson(this);
}

@JsonSerializable()
class Meta extends Object {
  @JsonKey(name: 'itemCount')
  int itemCount;

  @JsonKey(name: 'totalItems')
  int totalItems;

  @JsonKey(name: 'itemsPerPage')
  int itemsPerPage;

  @JsonKey(name: 'totalPages')
  int totalPages;

  @JsonKey(name: 'currentPage')
  int currentPage;

  Meta(
    this.itemCount,
    this.totalItems,
    this.itemsPerPage,
    this.totalPages,
    this.currentPage,
  );

  factory Meta.fromJson(Map<String, dynamic> srcJson) =>
      _$MetaFromJson(srcJson);

  Map<String, dynamic> toJson() => _$MetaToJson(this);
}
