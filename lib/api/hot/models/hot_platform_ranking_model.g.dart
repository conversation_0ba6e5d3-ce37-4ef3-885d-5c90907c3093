// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hot_platform_ranking_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HotPlatformRankingModel _$HotPlatformRankingModelFromJson(
  Map<String, dynamic> json,
) => HotPlatformRankingModel(
  json['createTime'] as String,
  json['description'] as String?,
  json['description_en'] as String,
  json['icon'] as String,
  json['name'] as String,
  json['name_en'] as String,
  json['platformId'] as String,
  json['rules'] as String,
  (json['sort'] as num).toInt(),
  (json['status'] as num).toInt(),
  json['type'] as String,
  json['updateFrequency'] as String,
  json['updateFrequency_en'] as String,
  json['updateTime'] as String,
  json['labels_show'] as String,
  json['id'] as String,
  Platform.fromJson(json['platform'] as Map<String, dynamic>),
);

Map<String, dynamic> _$HotPlatformRankingModelToJson(
  HotPlatformRankingModel instance,
) => <String, dynamic>{
  'createTime': instance.createTime,
  'description': instance.description,
  'description_en': instance.descriptionEn,
  'icon': instance.icon,
  'name': instance.name,
  'name_en': instance.nameEn,
  'platformId': instance.platformId,
  'rules': instance.rules,
  'sort': instance.sort,
  'status': instance.status,
  'type': instance.type,
  'updateFrequency': instance.updateFrequency,
  'updateFrequency_en': instance.updateFrequencyEn,
  'updateTime': instance.updateTime,
  'labels_show': instance.labelsShow,
  'id': instance.id,
  'platform': instance.platform,
};

Platform _$PlatformFromJson(Map<String, dynamic> json) => Platform(
  json['name'] as String,
  json['type'] as String,
  json['description'] as String?,
  json['icon'] as String,
  (json['status'] as num).toInt(),
  (json['sort'] as num).toInt(),
  json['hotinfo'] as String,
  json['language'] as String,
  json['name_en'] as String,
  json['description_en'] as String,
  json['id'] as String,
);

Map<String, dynamic> _$PlatformToJson(Platform instance) => <String, dynamic>{
  'name': instance.name,
  'type': instance.type,
  'description': instance.description,
  'icon': instance.icon,
  'status': instance.status,
  'sort': instance.sort,
  'hotinfo': instance.hotinfo,
  'language': instance.language,
  'name_en': instance.nameEn,
  'description_en': instance.descriptionEn,
  'id': instance.id,
};
