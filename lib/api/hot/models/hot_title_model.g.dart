// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hot_title_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HotTitleModel _$HotTitleModelFromJson(Map<String, dynamic> json) =>
    HotTitleModel(
      (json['items'] as List<dynamic>)
          .map((e) => Items.fromJson(e as Map<String, dynamic>))
          .toList(),
      Meta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$HotTitleModelToJson(HotTitleModel instance) =>
    <String, dynamic>{'items': instance.items, 'meta': instance.meta};

Items _$ItemsFromJson(Map<String, dynamic> json) => Items(
  json['category'] as String,
  json['title'] as String,
  (json['rank'] as num).toInt(),
  (json['engagement'] as num).toInt(),
  json['originalId'] as String,
  json['update_time'] as String,
  json['timeType'] as String,
  json['url'] as String,
  json['platformId'] as String,
  json['id'] as String,
);

Map<String, dynamic> _$ItemsToJson(Items instance) => <String, dynamic>{
  'category': instance.category,
  'title': instance.title,
  'rank': instance.rank,
  'engagement': instance.engagement,
  'originalId': instance.originalId,
  'update_time': instance.updateTime,
  'timeType': instance.timeType,
  'url': instance.url,
  'platformId': instance.platformId,
  'id': instance.id,
};

HotTitleTopBModel _$HotTitleTopBModelFromJson(Map<String, dynamic> json) =>
    HotTitleTopBModel(
      json['category'] as String,
      (json['titles'] as List<dynamic>)
          .map((e) => Items.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$HotTitleTopBModelToJson(HotTitleTopBModel instance) =>
    <String, dynamic>{'category': instance.category, 'titles': instance.titles};
