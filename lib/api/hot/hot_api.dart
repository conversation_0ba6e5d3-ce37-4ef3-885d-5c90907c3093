
import 'package:aitoearn_app/api/hot/models/get_all_topics_params_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_event_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_platform_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_platform_ranking_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_ranking_contents_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_ranking_dates_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_title_model.dart';
import 'package:aitoearn_app/api/hot/models/hot_topics_module.dart';
import 'package:aitoearn_app/network/http.dart';
import 'package:aitoearn_app/utils/index.dart';

var transfer = '/hotdata';

/// ------------------- 热门内容 -------------------

/// 获取平台列表
Future<List<HotPlatformModel>?> getPlatformList() async {
  var res = await Http.get('$transfer/hotinfo/platform');
  if (res?['data'] != null) {
    return getHotPlatformModelList(res['data']);
  }
  return null;
}

/// 获取平台榜单列表
Future<List<HotPlatformRankingModel>?> getPlatformRanking(
  String platformId,
) async {
  var res = await Http.get('$transfer/ranking/platform?platformId=$platformId');
  if (res?['data'] != null) {
    return getHotPlatformRankingModelList(res['data']);
  }
  return null;
}

/// 获取榜单内容
Future<HotRankingContentsModel?> getRankingContents(
  String rankingId,
  int page,
  int pageSize, {
  String? category,
  String? date,
}) async {
  var res = await Http.get(
    '$transfer/ranking/$rankingId/contents',
    query: {
      'page': page,
      'pageSize': pageSize,
      'category': category,
      'date': date,
    },
  );
  if (res?['data'] != null) {
    return HotRankingContentsModel.fromJson(res['data']);
  }
  return null;
}

/// 获取榜单分类
Future<List<String>?> getRankingLabel(String rankingId) async {
  var res = await Http.get('$transfer/ranking/label/$rankingId');
  return dynamicToStringList(res['data']);
}

/// 获取榜单日期
Future<List<HotRankingDatesModel>?> getRankingDates(String rankingId) async {
  var res = await Http.get('$transfer/ranking/hotinfo/$rankingId/dates');
  if (res?['data'] != null) {
    return getHotRankingDatesModelList(res['data']);
  }
  return null;
}

/// ------------------- 热点事件 -------------------

/// 获取八大平台热点事件
Future<List<HotEventModel>?> getAllHotTopics() async {
  var res = await Http.get('$transfer/hot-topics/all');
  if (res?['data'] != null) {
    return getHotEventModelList(res['data']);
  }
  return null;
}

/// ------------------- 热门专题 -------------------

/// 获取所有专题类型
Future<List<String>?> getMsgType() async {
  var res = await Http.get('$transfer/topics/msgType');
  return dynamicToStringList(res['data']);
}

/// 获取专题的时间类型
Future<List<String>?> getTopicTimeTypes(String msgType) async {
  var res = await Http.get('$transfer/topics/timeType/$msgType');
  return dynamicToStringList(res['data']);
}

/// 获取热门专题列表
Future<HotTopicsModule?> getAllTopics(GetAllTopicsParamsModel params) async {
  var res = await Http.get('$transfer/topics', query: params.toJson());
  if (res?['data'] != null) {
    return HotTopicsModule.fromJson(res['data']);
  }
  return null;
}

/// ------------------- 爆款标题 -------------------

/// 获取有数据的平台列表
Future<List<HotPlatformModel>?> findPlatformsWithData() async {
  var res = await Http.get('$transfer/viral-titles/platforms');
  if (res?['data'] != null) {
    return getHotPlatformModelList(res['data']);
  }
  return null;
}

/// 获取指定平台的分类列表
Future<List<String>?> findCategoriesByPlatform(String platformId) async {
  var res = await Http.get(
    '$transfer/viral-titles/platforms/$platformId/categories',
  );
  return dynamicToStringList(res['data']);
}

/// 获取平台下所有分类的前五条数据
Future<List<HotTitleTopBModel>?> findTopByPlatformAndCategories(
  String platformId,
) async {
  var res = await Http.get(
    '$transfer/viral-titles/platforms/$platformId/top-by-categories',
  );
  if (res?['data'] != null) {
    return getHotTitleTopBModelList(res['data']);
  }
  return null;
}

/// 获取平台下指定分类的所有数据
Future<HotTitleModel?> findByPlatformAndCategory(
  String platformId,
  String category,
  int page,
  int pageSize,
) async {
  var res = await Http.get(
    '$transfer/viral-titles/platforms/$platformId',
    query: {'category': category, 'page': page, 'pageSize': pageSize},
  );
  if (res?['data'] != null) {
    return HotTitleModel.fromJson(res['data']);
  }
  return null;
}
