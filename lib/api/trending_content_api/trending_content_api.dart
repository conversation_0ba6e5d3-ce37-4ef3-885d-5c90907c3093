import 'dart:convert';

import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/trending_content_models/trending_models.dart';
import 'package:http/http.dart' as http;

class RankingAPI {
  final String baseUrl = 'https://att-contents.yikart.cn/api/ranking';

  // Fetch ranking details by ranking ID
  Future<RankingDetail> fetchRankingDetail(
    String rankingId,
    String language,
  ) async {
    final url = Uri.parse('$baseUrl/$rankingId/detail?language=$language');

    try {
      final response = await http.get(url);
      LoggerUtil.i(response.body);
      if (response.statusCode == 200) {
        return RankingDetail.fromJson(json.decode(response.body)['data']);
      } else {
        throw Exception('Failed to load ranking detail');
      }
    } catch (e) {
      throw Exception('Error fetching ranking detail: $e');
    }
  }

  // Fetch rankings by platform ID
  Future<List<RankingPlatform>> fetchRankingByPlatform(
    String platformId,
    String language,
  ) async {
    final url = Uri.parse(
      '$baseUrl/platform?platformId=$platformId&language=$language',
    );

    try {
      final response = await http.get(url);
      LoggerUtil.i(response.body);
      if (response.statusCode == 200) {
        var list = json.decode(response.body)['data'] as List;
        return list.map((item) => RankingPlatform.fromJson(item)).toList();
      } else {
        throw Exception('Failed to load rankings for platform');
      }
    } catch (e) {
      throw Exception('Error fetching rankings for platform: $e');
    }
  }

  // Fetch ranking labels by ranking ID
  Future<RankingLabels> fetchRankingLabels(
    String rankingId,
    String language,
  ) async {
    final url = Uri.parse('$baseUrl/label/$rankingId?language=$language');

    try {
      final response = await http.get(url);
      LoggerUtil.i(response.body);
      if (response.statusCode == 200) {
        return RankingLabels.fromJson(json.decode(response.body));
      } else {
        throw Exception('Failed to load ranking labels');
      }
    } catch (e) {
      throw Exception('Error fetching ranking labels: $e');
    }
  }

  // Fetch hot info for ranking by ID and date
  Future<List<RankingHotInfo>> fetchRankingHotInfoDates(
    String rankingId,
    String language,
  ) async {
    final url = Uri.parse(
      '$baseUrl/hotinfo/$rankingId/dates?language=$language',
    );

    try {
      final response = await http.get(url);
      LoggerUtil.i(response.body);
      if (response.statusCode == 200) {
        var list = json.decode(response.body)['data'] as List;
        LoggerUtil.i('list is:$list');
        return list.map((item) => RankingHotInfo.fromJson(item)).toList();
      } else {
        throw Exception('Failed to load ranking hot info dates');
      }
    } catch (e) {
      throw Exception('Error fetching ranking hot info dates: $e');
    }
  }
}
