import 'package:aitoearn_app/api/base_api_service.dart';
import 'package:aitoearn_app/api/models/base_response.dart';
import 'package:aitoearn_app/api/platform_apis/xhs_content_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/api/account/models/account_user_info_modle.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/network/douyin_content_service.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_home_feed_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_note_detail_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/xhs_service.dart';
import 'package:aitoearn_app/plat_core/services/account_service.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';

/// 平台服务
/// 提供跨平台的通用API调用
class PlatformService {
  // 单例实例
  static final PlatformService _instance = PlatformService._internal();

  // 工厂构造函数
  factory PlatformService() => _instance;

  // 内部构造函数
  PlatformService._internal();

  // 账号服务
  final _accountService = AccountService();

  // 基础API服务
  final _baseApiService = BaseApiService();

  // 抖音内容服务
  final _douyinContentService = DouyinContentService();

  /// 获取抖音作品详情
  ///
  /// [awemeId] 作品ID
  /// 返回作品详情响应
  Future<BaseResponse<Map<String, dynamic>?>> getDouyinAwemeDetail({
    required String awemeId,
  }) async {
    return await _douyinContentService.getAwemeDetail(awemeId: awemeId);
  }

  /// 获取抖音作品评论
  ///
  /// [awemeId] 作品ID
  /// [cursor] 分页游标，默认从0开始
  /// [count] 每页数量，默认10条
  /// [cookie] 用户Cookie，可选参数
  /// 返回评论列表响应
  Future<BaseResponse<Map<String, dynamic>?>> getDouyinVideoComments({
    required String awemeId,
    String cursor = '',
    int count = 10,
    String? cookie,
  }) async {
    return await _douyinContentService.getVideoComments(
      awemeId: awemeId,
      cursor: cursor,
      count: count,
      cookie: cookie,
    );
  }

  /// 获取抖音作品二级评论
  ///
  /// [awemeId] 作品ID
  /// [commentId] 一级评论ID
  /// [cursor] 分页游标，默认从0开始
  /// [count] 每页数量，默认10条
  /// [cookie] 用户Cookie，可选参数
  /// 返回二级评论列表响应
  Future<BaseResponse<Map<String, dynamic>?>> getDouyinVideoReplyComments({
    required String awemeId,
    required String commentId,
    String cursor = '',
    int count = 10,
    String? cookie,
  }) async {
    return await _douyinContentService.getVideoReplyComments(
      awemeId: awemeId,
      commentId: commentId,
      cursor: cursor,
      count: count,
      cookie: cookie,
    );
  }

  /// 发布抖音作品评论
  ///
  /// [awemeId] 作品ID
  /// [text] 评论内容
  /// [cookie] 用户Cookie，可选参数
  /// [replyId] 回复的评论ID，默认为"0"表示不是回复评论
  /// [replyToReplyId] 回复的二级评论ID，默认为"0"表示直接回复一级评论
  /// 返回评论发布结果响应
  Future<BaseResponse<Map<String, dynamic>?>> publishDouyinVideoComment({
    required String awemeId,
    required String text,
    String? cookie,
    String replyId = "0",
    String replyToReplyId = "0",
  }) async {
    return await _douyinContentService.publishVideoComment(
      awemeId: awemeId,
      text: text,
      cookie: cookie,
      replyId: replyId,
      replyToReplyId: replyToReplyId,
    );
  }

  /// 搜索小红书内容
  ///
  /// [accountId] 账号ID
  /// [keyword] 搜索关键词
  /// [page] 页码，从1开始
  /// [pageSize] 每页数量
  /// [sort] 排序方式，默认为general
  /// 返回搜索结果响应
  Future<BaseResponse<XhsSearchContentData?>> searchXhsContent({
    required String accountId,
    required String keyword,
    int page = 1,
    int pageSize = 20,
    String sort = 'general',
  }) async {
    return await XhsContentApi().searchContent(
      accountId: accountId,
      keyword: keyword,
      page: page,
      pageSize: pageSize,
      sort: sort,
    );
  }

  /// 获取小红书笔记详情
  ///
  /// [accountId] 账号ID
  /// [noteId] 笔记ID
  /// [xsecToken] 安全令牌
  /// 返回笔记详情响应
  Future<BaseResponse<NoteDetailItem?>> getXhsNoteDetail({
    required String accountId,
    required String noteId,
    required String xsecToken,
  }) async {
    try {
      return await XhsContentApi().getNoteDetail(
        accountId: accountId,
        noteId: noteId,
        xsecToken: xsecToken,
      );
    } catch (e) {
      return BaseResponse(msg: '获取笔记详情失败: $e', data: null);
    }
  }

  /// 获取小红书笔记评论
  Future<BaseResponse<XhsCommentData?>> getXhsNoteComments({
    required String accountId,
    required String noteId,
    required String xsecToken,
    String? cursor,
    String? topCommentId,
  }) async {
    try {
      final account = await _accountService.getAccountById(accountId);
      if (account == null) {
        return BaseResponse(msg: '账号不存在', data: null);
      }

      final cookie = account.cookie ?? '';
      if (cookie.isEmpty) {
        return BaseResponse(msg: '账号Cookie为空，请重新登录', data: null);
      }

      final response = await XhsService.getNoteComments(
        cookieStr: cookie,
        noteId: noteId,
        xsecToken: xsecToken,
        cursor: cursor,
        topCommentId: topCommentId,
      );

      if (response.success && response.data != null) {
        return BaseResponse(success: true, msg: '获取评论成功', data: response.data);
      } else {
        return BaseResponse(msg: response.msg, data: null);
      }
    } catch (e) {
      return BaseResponse(msg: '获取评论失败: $e', data: null);
    }
  }

  /// 点赞小红书笔记
  ///
  /// [accountId] 账号ID
  /// [noteId] 笔记ID
  /// 返回点赞结果响应
  Future<BaseResponse<Map<String, dynamic>?>> likeXhsNote({
    required String accountId,
    required String noteId,
  }) async {
    try {
      final account = await _accountService.getAccountById(accountId);
      if (account == null) {
        return BaseResponse(msg: '账号不存在', data: null);
      }

      final cookie = account.cookie ?? '';
      if (cookie.isEmpty) {
        return BaseResponse(msg: '账号Cookie为空，请重新登录', data: null);
      }

      final response = await XhsService.likeNote(
        cookieStr: cookie,
        noteId: noteId,
      );

      if (response['success'] == true) {
        return BaseResponse(
          success: true,
          msg: response['message'] ?? '点赞成功',
          data: response['data'],
        );
      } else {
        return BaseResponse(msg: response['message'] ?? '点赞失败', data: null);
      }
    } catch (e) {
      return BaseResponse(msg: '点赞失败: $e', data: null);
    }
  }

  /// 取消点赞小红书笔记
  ///
  /// [accountId] 账号ID
  /// [noteId] 笔记ID
  /// 返回取消点赞结果响应
  Future<BaseResponse<Map<String, dynamic>?>> dislikeXhsNote({
    required String accountId,
    required String noteId,
  }) async {
    try {
      final account = await _accountService.getAccountById(accountId);
      if (account == null) {
        return BaseResponse(msg: '账号不存在', data: null);
      }

      final cookie = account.cookie ?? '';
      if (cookie.isEmpty) {
        return BaseResponse(msg: '账号Cookie为空，请重新登录', data: null);
      }

      final response = await XhsService.dislikeNote(
        cookieStr: cookie,
        noteId: noteId,
      );

      if (response['success'] == true) {
        return BaseResponse(
          success: true,
          msg: response['message'] ?? '取消点赞成功',
          data: response['data'],
        );
      } else {
        return BaseResponse(msg: response['message'] ?? '取消点赞失败', data: null);
      }
    } catch (e) {
      return BaseResponse(msg: '取消点赞失败: $e', data: null);
    }
  }

  /// 点赞小红书评论
  ///
  /// [accountId] 账号ID
  /// [noteId] 笔记ID
  /// [commentId] 评论ID
  /// 返回点赞评论结果响应
  Future<BaseResponse<Map<String, dynamic>?>> likeXhsComment({
    required String accountId,
    required String noteId,
    required String commentId,
  }) async {
    try {
      final account = await _accountService.getAccountById(accountId);
      if (account == null) {
        return BaseResponse(msg: '账号不存在', data: null);
      }

      final cookie = account.cookie ?? '';
      if (cookie.isEmpty) {
        return BaseResponse(msg: '账号Cookie为空，请重新登录', data: null);
      }

      final response = await XhsService.likeComment(
        cookieStr: cookie,
        noteId: noteId,
        commentId: commentId,
      );

      if (response['success'] == true) {
        return BaseResponse(
          success: true,
          msg: response['message'] ?? '点赞评论成功',
          data: response['data'],
        );
      } else {
        return BaseResponse(msg: response['message'] ?? '点赞评论失败', data: null);
      }
    } catch (e) {
      return BaseResponse(msg: '点赞评论失败: $e', data: null);
    }
  }

  /// 取消点赞小红书评论
  ///
  /// [accountId] 账号ID
  /// [noteId] 笔记ID
  /// [commentId] 评论ID
  /// 返回取消点赞评论结果响应
  Future<BaseResponse<Map<String, dynamic>?>> dislikeXhsComment({
    required String accountId,
    required String noteId,
    required String commentId,
  }) async {
    try {
      final account = await _accountService.getAccountById(accountId);
      if (account == null) {
        return BaseResponse(msg: '账号不存在', data: null);
      }

      final cookie = account.cookie ?? '';
      if (cookie.isEmpty) {
        return BaseResponse(msg: '账号Cookie为空，请重新登录', data: null);
      }

      final response = await XhsService.dislikeComment(
        cookieStr: cookie,
        noteId: noteId,
        commentId: commentId,
      );

      if (response['success'] == true) {
        return BaseResponse(
          success: true,
          msg: response['message'] ?? '取消点赞评论成功',
          data: response['data'],
        );
      } else {
        return BaseResponse(msg: response['message'] ?? '取消点赞评论失败', data: null);
      }
    } catch (e) {
      return BaseResponse(msg: '取消点赞评论失败: $e', data: null);
    }
  }

  /// 获取小红书首页热门作品
  ///
  /// [accountId] 账号ID
  /// [cursorScore] 光标分数，用于分页，首次加载传空字符串
  /// [num] 每页数量，默认18
  /// [refreshType] 刷新类型，1为下拉刷新
  /// 返回首页热门作品响应
  Future<BaseResponse<XhsHomeFeedData?>> getXhsHomeFeed({
    required String accountId,
    String cursorScore = '',
    int num = 18,
    int refreshType = 1,
  }) async {
    try {
      return await XhsContentApi().getHomeFeed(
        accountId: accountId,
        cursorScore: cursorScore,
        num: num,
        refreshType: refreshType,
      );
    } catch (e) {
      return BaseResponse(msg: '获取首页热门作品失败: $e', data: null);
    }
  }

  /// 搜索抖音内容
  ///
  /// [keyword] 搜索关键词
  /// [cookie] 用户Cookie
  /// [offset] 分页偏移量，默认从0开始
  /// [count] 每页数量，默认10条
  Future<BaseResponse<Map<String, dynamic>?>> searchDouyin({
    required String keyword,
    required String cookie,
    int offset = 0,
    int count = 10,
  }) async {
    try {
      return await _douyinContentService.searchDouyin(
        keyword: keyword,
        cookie: cookie,
        offset: offset,
        count: count,
      );
    } catch (e) {
      return BaseResponse(code: -1, msg: '搜索抖音内容异常: $e', data: null);
    }
  }
  
  /// 发布抖音作品评论
  ///
  /// [awemeId] 作品ID
  /// [text] 评论内容
  /// [cookie] 用户Cookie，可选参数
  /// [replyId] 回复的评论ID，默认为"0"表示不是回复评论
  /// [replyToReplyId] 回复的二级评论ID，默认为"0"表示直接回复一级评论
  /// 返回评论发布结果响应
  Future<BaseResponse<Map<String, dynamic>?>> publishVideoComment({
    required String awemeId,
    required String text,
    String? cookie,
    String replyId = "0",
    String replyToReplyId = "0",
  }) async {
    return await _douyinContentService.publishVideoComment(
      awemeId: awemeId,
      text: text,
      cookie: cookie,
      replyId: replyId,
      replyToReplyId: replyToReplyId,
    );
  }

  /// 获取抖音账号列表
  ///
  /// 返回所有抖音类型的账号
  Future<List<AccountUserInfoModle>> getDouyinAccounts() async {
    try {
      // 使用AccountPersistentService获取账号列表
      final accounts = AccountPersistentService.to.accounts;
      
      // 过滤出抖音类型的账号
      final douyinAccounts = accounts.where((acc) => acc.type == PlatTypeEnum.douyin).toList();
      
      // 优先返回在线账号
      douyinAccounts.sort((a, b) {
        if (a.online && !b.online) return -1;
        if (!a.online && b.online) return 1;
        return 0;
      });
      
      return douyinAccounts;
    } catch (e) {
      LoggerUtil.e('获取抖音账号列表失败: $e');
      return [];
    }
  }

  /// 获取小红书账号Cookie
  ///
  /// [accountId] 账号ID
  /// 返回账号Cookie
  Future<BaseResponse<String>> getXhsAccountCookie({
    required String accountId,
  }) async {
    try {
      LoggerUtil.i('【PlatformService】开始获取小红书账号Cookie，账号ID: $accountId');
      
      final account = await _accountService.getAccountById(accountId);
      if (account == null) {
        LoggerUtil.e('【PlatformService】账号不存在，ID: $accountId');
        return BaseResponse(msg: '账号不存在', data: '');
      }

      final cookie = account.cookie ?? '';
      if (cookie.isEmpty) {
        LoggerUtil.e('【PlatformService】账号Cookie为空，请重新登录，账号ID: $accountId');
        return BaseResponse(msg: '账号Cookie为空，请重新登录', data: '');
      }
      
      LoggerUtil.i('【PlatformService】成功获取小红书账号Cookie，长度: ${cookie.length}');
      return BaseResponse(
        success: true,
        msg: '获取Cookie成功',
        data: cookie,
      );
    } catch (e) {
      LoggerUtil.e('【PlatformService】获取小红书账号Cookie异常: $e');
      return BaseResponse(msg: '获取Cookie失败: $e', data: '');
    }
  }
}
