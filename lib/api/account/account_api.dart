import 'dart:convert';

import 'package:aitoearn_app/api/account/account_enum.dart';
import 'package:aitoearn_app/api/account/models/account_group_model.dart'
    as api_model;
import 'package:aitoearn_app/api/account/models/account_user_info_modle.dart';
import 'package:aitoearn_app/api/base_api_service.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/utils/dio_util/dio_util.dart';
import 'package:dio/dio.dart';

/// 创建或更新账号
Future<Response?> createOrUpdateAccountInfoApi(
  AccountUserInfoModle data,
) async {
  var req = await DioUtils.defaultClient.request(
    '/user/login/code/phone',
    data: data.toJson(),
    options: Options(method: 'POST'),
  );
  return req;
}

/// 创建账号API接口
Future<AccountUserInfoModle?> createAccountApi(
  String cookie,
  PlatTypeEnum platType,
  String account,
  String nickname,
  String avatar,
  String uid, {
  String? groupId,
  String? accessToken,
  String? refreshToken,
  int fansCount = 0,
  int readCount = 0,
  int likeCount = 0,
  int collectCount = 0,
  int forwardCount = 0,
  int commentCount = 0,
  int workCount = 0,
  int income = 0,
}) async {
  try {
    LoggerUtil.i('开始创建账号: 平台=${platType.name}, 账号=$account, UID=$uid');

    // 如果未提供access_token和refresh_token，尝试从cookie中提取
    String? finalAccessToken = accessToken;
    String? finalRefreshToken = refreshToken;

    if ((finalAccessToken == null || finalAccessToken.isEmpty) &&
        (finalRefreshToken == null || finalRefreshToken.isEmpty) &&
        cookie.isNotEmpty) {
      // 尝试从cookie中提取access_token
      final accessTokenMatch = RegExp(
        r'access_token=([^;]+)',
      ).firstMatch(cookie);
      if (accessTokenMatch != null && accessTokenMatch.groupCount >= 1) {
        finalAccessToken = accessTokenMatch.group(1);
        LoggerUtil.i(
          '从cookie中提取到access_token: ${finalAccessToken!.substring(0, finalAccessToken.length > 10 ? 10 : finalAccessToken.length)}...',
        );
      }

      // 尝试从cookie中提取refresh_token
      final refreshTokenMatch = RegExp(
        r'refresh_token=([^;]+)',
      ).firstMatch(cookie);
      if (refreshTokenMatch != null && refreshTokenMatch.groupCount >= 1) {
        finalRefreshToken = refreshTokenMatch.group(1);
        LoggerUtil.i(
          '从cookie中提取到refresh_token: ${finalRefreshToken!.substring(0, finalRefreshToken.length > 10 ? 10 : finalRefreshToken.length)}...',
        );
      }
    }

    // 当前登录和统计时间
    final currentTime = DateTime.now().toIso8601String();
    var type = platType.val;
    if (platType.val == PlatTypeEnum.wxWph.val) {
      type = 'wxSph';
    }
    // 构建请求参数
    final Map<String, dynamic> data = {
      'type': type,
      'uid': uid,
      'account': account,
      'cookie': cookie,
      'nickname': nickname,
      'avatar': avatar.isEmpty ? '0' : avatar,
      'loginTime': currentTime,
      'lastStatsTime': currentTime,
      'fansCount': fansCount,
      'readCount': readCount,
      'likeCount': likeCount,
      'collectCount': collectCount,
      'forwardCount': forwardCount,
      'commentCount': commentCount,
      'workCount': workCount,
      'income': income,
      'groupId': groupId ?? '',
    };

    // 添加token到请求参数
    if (finalAccessToken != null && finalAccessToken.isNotEmpty) {
      data['access_token'] = finalAccessToken;
    } else if (cookie.isNotEmpty) {
      // 如果没有access_token，但有cookie，使用cookie内容作为access_token
      data['access_token'] = cookie;
      LoggerUtil.i('未找到access_token，使用cookie作为替代');
    } else {
      data['access_token'] = ''; // 提供空字符串而不是null
    }

    if (finalRefreshToken != null && finalRefreshToken.isNotEmpty) {
      data['refresh_token'] = finalRefreshToken;
    } else if (cookie.isNotEmpty) {
      // 如果没有refresh_token，但有cookie，使用cookie内容作为refresh_token
      data['refresh_token'] = cookie;
      LoggerUtil.i('未找到refresh_token，使用cookie作为替代');
    } else {
      data['refresh_token'] = ''; // 提供空字符串而不是null
    }

    LoggerUtil.i('准备发送API请求创建账号: ${data.toString()}');
    var datax = jsonEncode(data);
    try {
      var req = await DioUtils.defaultClient.request<Map<String, dynamic>>(
        '/account/login',
        data: data,
        options: Options(method: 'POST', contentType: 'application/json'),
      );

      LoggerUtil.i('创建账号API响应: status=${req?.statusCode}, data=${req?.data}');

      if (req?.data != null &&
          req?.data!['code'] == 0 &&
          req?.data!['data'] != null) {
        // 解析返回的账号数据
        final accountData = req!.data!['data'] as Map<String, dynamic>;
        LoggerUtil.i('成功创建账号，返回数据: $accountData');

        // 创建AccountUserInfoModle对象
        final accountInfo = AccountUserInfoModle(
          platType,
          cookie,
          currentTime,
          uid,
          account,
          avatar,
          nickname,
          fansCount,
          readCount,
          likeCount,
          collectCount,
          forwardCount,
          commentCount,
          DateTime.now().millisecondsSinceEpoch,
          // lastStatsTime
          workCount,
          income,
          online: true,
          groupId: accountData['groupId'] ?? '',
          accessToken: finalAccessToken,
          refreshToken: finalRefreshToken,
          id: accountData['_id'] ?? accountData['id'],
        );

        LoggerUtil.i(
          '创建的账号对象: ID=${accountInfo.id}, UID=${accountInfo.uid}, 平台=${accountInfo.type.name}',
        );
        return accountInfo;
      } else {
        LoggerUtil.e('API创建账号失败: ${req?.data?['message'] ?? '未知错误'}');
        return null;
      }
    } catch (apiError) {
      LoggerUtil.e('API请求异常: $apiError');
      return null;
    }
  } catch (e) {
    LoggerUtil.e('创建账号过程中发生异常: $e');
    return null;
  }
}

/// 更新账号状态
Future<Response?> updateAccountStatusApi(
  int id,
  AccountStatus accountStatus,
) async {
  var req = await DioUtils.defaultClient.request(
    '/user/login/code/phone',
    data: {'id': id, 'status': accountStatus},
    options: Options(method: 'POST'),
  );
  return req;
}

/// 获取账号信息
Future<Response?> getAccountInfoListApi() async {
  var req = await DioUtils.defaultClient.request('/user/login/code/phone');
  return req;
}

/// 获取所有账号列表API接口
Future<Response<Map<String, dynamic>>?> getAllAccountsApi() async {
  try {
    LoggerUtil.i('开始获取所有账号列表');

    var req = await DioUtils.defaultClient.request<Map<String, dynamic>>(
      '/account/list/all',
      options: Options(method: 'GET', contentType: 'application/json'),
    );

    if (req?.data != null) {
      // 添加更详细的日志，记录API响应的结构
      LoggerUtil.i('获取账号列表成功，状态码: ${req?.statusCode}');

      if (req?.data!['data'] != null) {
        final dataType = req?.data!['data'].runtimeType;
        LoggerUtil.i('API返回的data字段类型: $dataType');

        if (req?.data!['data'] is Map<String, dynamic>) {
          // 如果data是Map类型，检查是否包含list字段
          final mapData = req?.data!['data'] as Map<String, dynamic>;
          if (mapData.containsKey('list')) {
            LoggerUtil.i(
              'API返回的data是Map类型，包含list字段，list类型: ${mapData['list'].runtimeType}',
            );
          } else {
            LoggerUtil.i(
              'API返回的data是Map类型，但不包含list字段，keys: ${mapData.keys.toList()}',
            );
          }
        }
      }
    }

    return req;
  } catch (e) {
    LoggerUtil.e('获取账号列表失败: $e');
    return null;
  }
}

/// 删除账号API接口
Future<Response<Map<String, dynamic>>?> deleteAccountApi(
  String accountId,
) async {
  try {
    LoggerUtil.i('开始删除账号: $accountId');

    var req = await DioUtils.defaultClient.request<Map<String, dynamic>>(
      '/account/delete/$accountId',
      options: Options(method: 'POST', contentType: 'application/json'),
    );

    LoggerUtil.i('删除账号结果: ${req?.data}');
    return req;
  } catch (e) {
    LoggerUtil.e('删除账号失败: $e');
    return null;
  }
}

/// 更新账号API接口
Future<Response<Map<String, dynamic>>?> updateAccountApi(
  AccountUserInfoModle account,
) async {
  try {
    LoggerUtil.i('开始更新账号: ${account.id}');

    // 构建请求数据
    final Map<String, dynamic> data = {
      'refresh_token': account.refreshToken ?? '',
      'access_token': account.accessToken ?? '',
      'type': account.type.name,
      'cookie': account.cookie ?? '',
      'loginTime': account.loginTime,
      'uid': account.uid,
      'account': account.account,
      'avatar': account.avatar ?? '',
      'nickname': account.nickname ?? '',
      'fansCount': account.fansCount ?? 0,
      'readCount': account.readCount ?? 0,
      'likeCount': account.likeCount ?? 0,
      'collectCount': account.collectCount ?? 0,
      'forwardCount': account.forwardCount ?? 0,
      'commentCount': account.commentCount ?? 0,
      'lastStatsTime': DateTime.now().toIso8601String(),
      'workCount': account.workCount ?? 0,
      'income': account.income ?? 0,
      'groupId': account.groupId,
    };

    // 如果有ID，添加到请求数据
    if (account.id != null && account.id!.isNotEmpty) {
      data['id'] = account.id;
    }

    var req = await DioUtils.defaultClient.request<Map<String, dynamic>>(
      '/account/update',
      data: data,
      options: Options(method: 'POST', contentType: 'application/json'),
    );

    LoggerUtil.i('更新账号结果: ${req?.data}');
    return req;
  } catch (e) {
    LoggerUtil.e('更新账号失败: $e');
    return null;
  }
}

/// 获取平台账号详细信息
Future<Map<String, dynamic>?> getPlatformAccountInfoApi(
  PlatTypeEnum platType,
  String cookie,
  String uid,
) async {
  try {
    LoggerUtil.i('开始从API获取平台账号信息: 平台=${platType.name}, UID=$uid');

    // 构建请求数据
    final Map<String, dynamic> data = {
      'type': platType.name,
      'cookie': cookie,
      'uid': uid,
    };

    var req = await DioUtils.defaultClient.request<Map<String, dynamic>>(
      '/account/getPlatformInfo',
      data: data,
      options: Options(method: 'POST', contentType: 'application/json'),
    );

    LoggerUtil.i('获取平台账号信息响应: ${req?.data}');

    if (req?.data != null &&
        req?.data!['code'] == 0 &&
        req?.data!['data'] != null) {
      // 返回平台账号信息数据
      return req?.data!['data'] as Map<String, dynamic>;
    } else {
      LoggerUtil.w('获取平台账号信息失败: ${req?.data?['message'] ?? "未知错误"}');
      return null;
    }
  } catch (e) {
    LoggerUtil.e('获取平台账号信息异常: $e');
    return null;
  }
}

class AccountApi extends BaseApiService {
  AccountApi() : super.create();

  /// 获取账号空间列表
  Future<api_model.AccountGroupResponse?> getAccountGroups() async {
    try {
      LoggerUtil.i('开始获取账号空间列表');

      final response = await DioUtils.defaultClient
          .request<Map<String, dynamic>>(
            '/accountGroup/getList',
            options: Options(method: 'GET'),
          );

      if (response != null &&
          response.statusCode == 200 &&
          response.data != null) {
        LoggerUtil.i('获取账号空间列表成功，状态码: ${response.statusCode}');

        // 记录API响应的结构
        if (response.data!.containsKey('data')) {
          final dataType = response.data!['data'].runtimeType;
          LoggerUtil.i('API返回的data字段类型: $dataType');
        }

        try {
          // 首先尝试标准解析
          final result = api_model.AccountGroupResponse.fromJson(
            response.data!,
          );
          LoggerUtil.i('标准解析账号空间数据成功: ${result.data.length} 个空间');
          return result;
        } catch (parseError) {
          LoggerUtil.e('标准解析账号空间数据失败: $parseError，尝试手动解析');

          // 手动解析数据
          final List<api_model.AccountGroupModel> groups = [];

          // 处理data字段是List的情况
          if (response.data!.containsKey('data') &&
              response.data!['data'] is List) {
            final List<dynamic> groupList = response.data!['data'];
            LoggerUtil.i('data字段是List类型，包含 ${groupList.length} 项');

            for (var item in groupList) {
              if (item is Map<String, dynamic>) {
                try {
                  _addGroupFromMap(item, groups);
                } catch (e) {
                  LoggerUtil.e('解析空间项失败: $e');
                }
              }
            }
          }
          // 处理data字段是Map的情况
          else if (response.data!.containsKey('data') &&
              response.data!['data'] is Map) {
            final mapData = response.data!['data'] as Map<String, dynamic>;
            LoggerUtil.i('data字段是Map类型，keys: ${mapData.keys.toList()}');

            // 检查是否包含list字段
            if (mapData.containsKey('list') && mapData['list'] is List) {
              final List<dynamic> groupList = mapData['list'];
              LoggerUtil.i('data.list字段是List类型，包含 ${groupList.length} 项');

              for (var item in groupList) {
                if (item is Map<String, dynamic>) {
                  try {
                    _addGroupFromMap(item, groups);
                  } catch (e) {
                    LoggerUtil.e('解析data.list中的空间项失败: $e');
                  }
                }
              }
            }
            // 如果data本身就是一个空间对象
            else if (mapData.containsKey('name')) {
              try {
                _addGroupFromMap(mapData, groups);
              } catch (e) {
                LoggerUtil.e('解析单个空间对象失败: $e');
              }
            }
          }

          // 创建手动解析的响应
          return api_model.AccountGroupResponse(
            data: groups,
            code: response.data!['code'] is int ? response.data!['code'] : 0,
            message:
                response.data!['message']?.toString() ??
                response.data!['msg']?.toString() ??
                '',
            url: response.data!['url']?.toString() ?? '',
          );
        }
      }

      LoggerUtil.w('获取账号空间列表失败: ${response?.statusCode}, ${response?.data}');
      return null;
    } catch (e) {
      LoggerUtil.e('获取账号空间列表失败: $e');
      return null;
    }
  }

  // 从Map中提取空间信息并添加到列表
  void _addGroupFromMap(
    Map<String, dynamic> item,
    List<api_model.AccountGroupModel> groups,
  ) {
    final id = item['_id'] ?? item['id'] ?? '';
    final name = item['name'] ?? '未命名空间';
    final isDefault = item['isDefault'] ?? false;
    final userId = item['userId'] ?? '';
    final rank = item['rank'] ?? 0;

    groups.add(
      api_model.AccountGroupModel(
        id: id.toString(),
        name: name.toString(),
        isDefault: isDefault is bool ? isDefault : false,
        userId: userId.toString(),
        rank: rank is int ? rank : 0,
        browserConfig:
            item['browserConfig'] is Map<String, dynamic>
                ? item['browserConfig'] as Map<String, dynamic>
                : <String, dynamic>{},
      ),
    );

    LoggerUtil.i('解析空间: $name, ID: $id, 默认: $isDefault');
  }

  /// 创建账号空间
  Future<Map<String, dynamic>?> createAccountGroup(
    String name,
    int rank,
  ) async {
    try {
      final response = await DioUtils.defaultClient
          .request<Map<String, dynamic>>(
            '/accountGroup/create',
            data: {'name': name, 'rank': rank},
            options: Options(method: 'POST', contentType: 'application/json'),
          );

      if (response != null &&
          response.statusCode == 200 &&
          response.data != null) {
        return response.data;
      }
      return null;
    } catch (e) {
      print('创建账号空间失败: $e');
      return null;
    }
  }

  /// 更新账号空间
  Future<bool> updateAccountGroup(String id, String name, int rank) async {
    try {
      final response = await DioUtils.defaultClient
          .request<Map<String, dynamic>>(
            '/accountGroup/update',
            data: {'id': id, 'name': name, 'rank': rank},
            options: Options(method: 'POST', contentType: 'application/json'),
          );

      if (response != null &&
          response.statusCode == 200 &&
          response.data != null) {
        final data = response.data!;
        return data['code'] == 0 && data['data'] == true;
      }
      return false;
    } catch (e) {
      print('更新账号空间失败: $e');
      return false;
    }
  }

  /// 删除账号空间
  Future<bool> deleteAccountGroup(List<String> ids) async {
    try {
      final response = await DioUtils.defaultClient
          .request<Map<String, dynamic>>(
            '/accountGroup/deletes',
            data: {'ids': ids},
            options: Options(method: 'POST', contentType: 'application/json'),
          );

      if (response != null &&
          response.statusCode == 200 &&
          response.data != null) {
        final data = response.data!;
        return data['code'] == 0 && data['data'] == true;
      }
      return false;
    } catch (e) {
      LoggerUtil.e('删除账号空间失败: $e');
      return false;
    }
  }

  /// 批量删除账号空间
  Future<bool> deleteAccountGroups(List<String> ids) async {
    try {
      final response = await DioUtils.defaultClient
          .request<Map<String, dynamic>>(
            '/accountGroup/deletes',
            data: {'ids': ids},
            options: Options(method: 'POST', contentType: 'application/json'),
          );

      if (response != null &&
          response.statusCode == 200 &&
          response.data != null) {
        // 返回成功状态
        final data = response.data!;
        return data['code'] == 0 && data['data'] == true;
      }
      return false;
    } catch (e) {
      print('批量删除账号空间失败: $e');
      return false;
    }
  }
}
