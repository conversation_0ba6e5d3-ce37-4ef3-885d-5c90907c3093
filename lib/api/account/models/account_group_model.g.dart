// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_group_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AccountGroupModel _$AccountGroupModelFromJson(Map<String, dynamic> json) =>
    AccountGroupModel(
      id: json['_id'] as String,
      userId: json['userId'] as String,
      isDefault: json['isDefault'] as bool,
      name: json['name'] as String,
      rank: (json['rank'] as num).toInt(),
      browserConfig: json['browserConfig'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AccountGroupModelToJson(AccountGroupModel instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'userId': instance.userId,
      'isDefault': instance.isDefault,
      'name': instance.name,
      'rank': instance.rank,
      'browserConfig': instance.browserConfig,
    };

AccountGroupResponse _$AccountGroupResponseFromJson(
  Map<String, dynamic> json,
) => AccountGroupResponse(
  data:
      (json['data'] as List<dynamic>)
          .map((e) => AccountGroupModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  code: (json['code'] as num).toInt(),
  message: json['message'] as String,
  url: json['url'] as String,
);

Map<String, dynamic> _$AccountGroupResponseToJson(
  AccountGroupResponse instance,
) => <String, dynamic>{
  'data': instance.data,
  'code': instance.code,
  'message': instance.message,
  'url': instance.url,
};
