import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:json_annotation/json_annotation.dart';

part 'account_user_info_modle.g.dart';

@JsonSerializable()
class AccountUserInfoModle {
  /// 平台类型
  @Json<PERSON><PERSON>(name: 'type')
  PlatTypeEnum type;

  /// 登录Cookie
  @JsonKey(name: 'cookie')
  String? cookie;

  /// 登录时间
  @J<PERSON><PERSON><PERSON>(name: 'loginTime')
  String? loginTime;

  /// 平台用户ID
  @Json<PERSON>ey(name: 'uid')
  String uid;

  /// 账号
  @Json<PERSON><PERSON>(name: 'account')
  String account;

  /// 头像
  @Json<PERSON>ey(name: 'avatar')
  String? avatar;

  /// 昵称
  @Json<PERSON>ey(name: 'nickname')
  String? nickname;

  /// 粉丝数
  @Json<PERSON>ey(name: 'fansCount')
  int? fansCount;

  /// 阅读数
  @<PERSON><PERSON><PERSON><PERSON>(name: 'readCount')
  int? readCount;

  /// 点赞数
  @J<PERSON><PERSON><PERSON>(name: 'likeCount')
  int? likeCount;

  /// 收藏数
  @Json<PERSON><PERSON>(name: 'collectCount')
  int? collectCount;

  /// 转发数
  @JsonKey(name: 'forwardCount')
  int? forwardCount;

  /// 评论数
  @JsonKey(name: 'commentCount')
  int? commentCount;

  /// 最后统计时间
  @JsonKey(name: 'lastStatsTime')
  int? lastStatsTime;

  /// 作品数
  @JsonKey(name: 'workCount')
  int? workCount;

  /// 收入
  @JsonKey(name: 'income')
  int? income;

  /// 账号在线状态
  @JsonKey(name: 'online')
  bool online;

  @JsonKey(name: 'ip')
  String? ip;

  /// 空间ID
  @JsonKey(name: 'groupId')
  String groupId;

  @JsonKey(name: 'isSelected')
  bool isSelected;

  /// 访问令牌
  @JsonKey(name: 'accessToken')
  String? accessToken;

  /// 刷新令牌
  @JsonKey(name: 'refreshToken')
  String? refreshToken;

  @JsonKey(name: 'id')
  String? id;

  AccountUserInfoModle(
    this.type,
    this.cookie,
    this.loginTime,
    this.uid,
    this.account,
    this.avatar,
    this.nickname,
    this.fansCount,
    this.readCount,
    this.likeCount,
    this.collectCount,
    this.forwardCount,
    this.commentCount,
    this.lastStatsTime,
    this.workCount,
    this.income, {
    this.online = false,
    this.ip,
    this.groupId = '',
    this.isSelected = false,
    this.accessToken,
    this.refreshToken,
    this.id,
  });

  factory AccountUserInfoModle.fromJson(Map<String, dynamic> json) =>
      _$AccountUserInfoModleFromJson(json);

  Map<String, dynamic> toJson() => _$AccountUserInfoModleToJson(this);
}
