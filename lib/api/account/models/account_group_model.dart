import 'package:json_annotation/json_annotation.dart';

part 'account_group_model.g.dart';

@JsonSerializable()
class AccountGroupModel {
  @Json<PERSON>ey(name: '_id')
  final String id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'userId')
  final String userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'isDefault')
  final bool isDefault;

  @Json<PERSON><PERSON>(name: 'name')
  final String name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'rank')
  final int rank;

  @<PERSON>son<PERSON>ey(name: 'browserConfig')
  final Map<String, dynamic>? browserConfig;

  AccountGroupModel({
    required this.id,
    required this.userId,
    required this.isDefault,
    required this.name,
    required this.rank,
    required this.browserConfig,
  });

  factory AccountGroupModel.fromJson(Map<String, dynamic> json) =>
      _$AccountGroupModelFromJson(json);
  Map<String, dynamic> toJson() => _$AccountGroupModelToJson(this);
}

@JsonSerializable()
class AccountGroupResponse {
  final List<AccountGroupModel> data;
  final int code;
  final String message;
  final String url;

  AccountGroupResponse({
    required this.data,
    required this.code,
    required this.message,
    required this.url,
  });

  factory AccountGroupResponse.fromJson(Map<String, dynamic> json) =>
      _$AccountGroupResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AccountGroupResponseToJson(this);
}
