// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_user_info_modle.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AccountUserInfoModle _$AccountUserInfoModleFromJson(
  Map<String, dynamic> json,
) => AccountUserInfoModle(
  $enumDecode(_$PlatTypeEnumEnumMap, json['type']),
  json['cookie'] as String?,
  json['loginTime'] as String?,
  json['uid'] as String,
  json['account'] as String,
  json['avatar'] as String?,
  json['nickname'] as String?,
  (json['fansCount'] as num?)?.toInt(),
  (json['readCount'] as num?)?.toInt(),
  (json['likeCount'] as num?)?.toInt(),
  (json['collectCount'] as num?)?.toInt(),
  (json['forwardCount'] as num?)?.toInt(),
  (json['commentCount'] as num?)?.toInt(),
  (json['lastStatsTime'] as num?)?.toInt(),
  (json['workCount'] as num?)?.toInt(),
  (json['income'] as num?)?.toInt(),
  online: json['online'] as bool? ?? false,
  ip: json['ip'] as String?,
  groupId: json['groupId'] as String? ?? '',
  isSelected: json['isSelected'] as bool? ?? false,
  accessToken: json['accessToken'] as String?,
  refreshToken: json['refreshToken'] as String?,
  id: json['id'] as String?,
);

Map<String, dynamic> _$AccountUserInfoModleToJson(
  AccountUserInfoModle instance,
) => <String, dynamic>{
  'type': _$PlatTypeEnumEnumMap[instance.type]!,
  'cookie': instance.cookie,
  'loginTime': instance.loginTime,
  'uid': instance.uid,
  'account': instance.account,
  'avatar': instance.avatar,
  'nickname': instance.nickname,
  'fansCount': instance.fansCount,
  'readCount': instance.readCount,
  'likeCount': instance.likeCount,
  'collectCount': instance.collectCount,
  'forwardCount': instance.forwardCount,
  'commentCount': instance.commentCount,
  'lastStatsTime': instance.lastStatsTime,
  'workCount': instance.workCount,
  'income': instance.income,
  'online': instance.online,
  'ip': instance.ip,
  'groupId': instance.groupId,
  'isSelected': instance.isSelected,
  'accessToken': instance.accessToken,
  'refreshToken': instance.refreshToken,
  'id': instance.id,
};

const _$PlatTypeEnumEnumMap = {
  PlatTypeEnum.pinterest: 'pinterest',
  PlatTypeEnum.threads: 'threads',
  PlatTypeEnum.instagram: 'instagram',
  PlatTypeEnum.facebook: 'facebook',
  PlatTypeEnum.twitter: 'twitter',
  PlatTypeEnum.bilibili: 'bilibili',
  PlatTypeEnum.youTube: 'youTube',
  PlatTypeEnum.tiktok: 'tiktok',
  PlatTypeEnum.wxGzh: 'wxGzh',
  PlatTypeEnum.douyin: 'douyin',
  PlatTypeEnum.xhs: 'xhs',
  PlatTypeEnum.wxWph: 'wxWph',
  PlatTypeEnum.kwai: 'kwai',
};
