import 'package:aitoearn_app/network/http.dart';

/// 话题数据模型
class TopicItem {
  final String id;
  final String name;
  final int viewCount;

  TopicItem({required this.id, required this.name, required this.viewCount});

  factory TopicItem.fromJson(Map<String, dynamic> json) {
    return TopicItem(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      viewCount: json['view_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'view_count': viewCount};
  }
}

/// 话题响应模型
class TopicResponse {
  final int status;
  final List<TopicItem>? data;
  final String? message;

  TopicResponse({required this.status, this.data, this.message});

  factory TopicResponse.fromJson(Map<String, dynamic> json) {
    return TopicResponse(
      status: json['status'] ?? 0,
      data:
          json['data'] != null
              ? (json['data'] as List)
                  .map((item) => TopicItem.fromJson(item))
                  .toList()
              : null,
      message: json['message'],
    );
  }
}

/// 获取平台话题
///
/// [platform] 平台类型 (douyin, xhs, kwai, wx-sph)
/// [keyword] 搜索关键词
/// [accessToken] 账号访问令牌
/// 返回话题列表
Future<TopicResponse?> getTopicsApi({
  required String platform,
  required String keyword,
  required String accessToken,
}) async {
  try {
    final Map<String, dynamic> data = {
      'platform': platform,
      'keyword': keyword,
      'access_token': accessToken,
    };

    final response = await Http.post('/publish/topics', data: data);

    if (response != null) {
      return TopicResponse.fromJson(response);
    }

    return null;
  } catch (e) {
    print('获取话题失败: $e');
    return null;
  }
}

/// 获取用户列表（@好友功能）
///
/// [platform] 平台类型
/// [keyword] 搜索关键词
/// [accessToken] 账号访问令牌
/// [page] 页码
Future<Map<String, dynamic>?> getUsersApi({
  required String platform,
  required String keyword,
  required String accessToken,
  int page = 1,
}) async {
  try {
    final Map<String, dynamic> data = {
      'platform': platform,
      'keyword': keyword,
      'access_token': accessToken,
      'page': page,
    };

    return await Http.post('/publish/users', data: data);
  } catch (e) {
    print('获取用户列表失败: $e');
    return null;
  }
}

/// 获取活动列表
///
/// [platform] 平台类型
/// [accessToken] 账号访问令牌
Future<Map<String, dynamic>?> getActivitiesApi({
  required String platform,
  required String accessToken,
}) async {
  try {
    final Map<String, dynamic> data = {
      'platform': platform,
      'access_token': accessToken,
    };

    return await Http.post('/publish/activities', data: data);
  } catch (e) {
    print('获取活动列表失败: $e');
    return null;
  }
}

/// 获取热点列表
///
/// [platform] 平台类型
/// [keyword] 搜索关键词
/// [accessToken] 账号访问令牌
Future<Map<String, dynamic>?> getHotspotsApi({
  required String platform,
  required String keyword,
  required String accessToken,
}) async {
  try {
    final Map<String, dynamic> data = {
      'platform': platform,
      'keyword': keyword,
      'access_token': accessToken,
    };

    return await Http.post('/publish/hotspots', data: data);
  } catch (e) {
    print('获取热点列表失败: $e');
    return null;
  }
}

/// 获取合集列表
///
/// [platform] 平台类型
/// [accessToken] 账号访问令牌
Future<Map<String, dynamic>?> getMixListApi({
  required String platform,
  required String accessToken,
}) async {
  try {
    final Map<String, dynamic> data = {
      'platform': platform,
      'access_token': accessToken,
    };

    return await Http.post('/publish/mix-list', data: data);
  } catch (e) {
    print('获取合集列表失败: $e');
    return null;
  }
}
