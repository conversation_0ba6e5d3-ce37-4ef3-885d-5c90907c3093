import 'dart:convert';
import 'package:aitoearn_app/api/models/base_response.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/douyin_models/douyin_hot_feed_model.dart';
import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';

/// 抖音热门Feed API服务
class DouyinHotFeedApi {
  /// 单例实例
  static final DouyinHotFeedApi _instance = DouyinHotFeedApi._internal();
  
  /// 工厂构造函数
  factory DouyinHotFeedApi() => _instance;
  
  /// 内部构造函数
  DouyinHotFeedApi._internal();
  
  /// Dio客户端
  final Dio _dio = Dio();
  
  /// 获取抖音热门Feed
  /// 
  /// [cookie] 用户Cookie
  /// 返回热门Feed响应
  Future<BaseResponse<List<DouyinHotFeedItem>?>> getHotFeed({
    required String cookie,
  }) async {
    try {
      LoggerUtil.i('【DouyinHotFeedApi】开始获取抖音热门Feed');
      
      // 从cookie中提取webid
      final String webid = _extractCookieValue(cookie, 'webid') ?? '7524991660163335734';
      
      // 构建请求URL - 使用与curl示例完全一致的参数
      final url = 'https://www.douyin.com/aweme/v1/web/module/feed/'
          '?device_platform=webapp'
          '&aid=6383'
          '&channel=channel_pc_web'
          '&module_id=3003101'
          '&count=20'
          '&filterGids='
          '&presented_ids='
          '&refresh_index=1'
          '&refer_id='
          '&refer_type=10'
          '&awemePcRecRawData=%7B%22is_xigua_user%22%3A0%2C%22is_client%22%3Afalse%7D'
          '&Seo-Flag=0'
          '&install_time=1752048665'
          '&tag_id='
          '&use_lite_type=1'
          '&xigua_user=0'
          '&pc_client_type=1'
          '&pc_libra_divert=Windows'
          '&update_version_code=170400'
          '&support_h265=1'
          '&support_dash=1'
          '&version_code=170400'
          '&version_name=17.4.0'
          '&cookie_enabled=true'
          '&screen_width=1440'
          '&screen_height=900'
          '&browser_language=zh-CN'
          '&browser_platform=Win32'
          '&browser_name=Chrome'
          '&browser_version=*********'
          '&browser_online=true'
          '&engine_name=Blink'
          '&engine_version=*********'
          '&os_name=Windows'
          '&os_version=10'
          '&cpu_core_num=18'
          '&device_memory=8'
          '&platform=PC'
          '&downlink=10'
          '&effective_type=4g'
          '&round_trip_time=0'
          '&webid=7524991660163335734'
          '&uifid=e71d819f1cb72e7166823ce125547a3e5a83b631a52f7c0b3c34cd9714dd602dd7bc13634f0c23b0886bb423bb9c7e103de8326c71539e5226d3e435983d4476a3168c71d542c09c19ba9e9a09bff1be52b410f40f9a39346533a41556a64002c820c1440a2bfe2b28e187bae7a374a59c43ab0f4bed463a974ae4ff49adca2eaacd47b5712ab7560f3fb9d498a739915354dccc6716868a6c81d361dff89942'
          '&verifyFp=verify_mcvojfsn_p3TUfyDU_QVdi_4vsy_BvJA_2AZCRNzzG5Wx'
          '&fp=verify_mcvojfsn_p3TUfyDU_QVdi_4vsy_BvJA_2AZCRNzzG5Wx'
          '&msToken=nj1hb7TfAYQpbC0dRZEKBXU2itJ6wANwM6IqiI-TyvzabcLaWPcIL5WxdHehShJNp8OehlhlXCpfBkMcS7rOkh9D59QuI0wR9zyLt7TwxKjW__rVimmGn0FvR0EI8-YMz9GPdPjtKKsCEcR0PL2h53GIAtn0miMPRupR9MX5BGqegeg5yWrKo_4%3D'
          '&a_bogus=xf0RkeWEmo%2FfKdKGmKmjH3lUkH2%2FrTSys4T2bEpPCNYtG1ecLbPbwPbNbxFr06uFwRpiheP77jaMajdbKGUhZKnpomhvuBXbZ0dn9gmo8Z7haaihnZfLCvbEoJ-e0WGOQQdJE3EXXUlnIoO3pqKqAMKyC%2FeoQ%2FbMzHPyd5WUtxgQ6aiY99Q3Cagl';
      
      // 构建请求头 - 使用与curl示例完全一致的头部
      final headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-length': '0',
        'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'origin': 'https://www.douyin.com',
        'priority': 'u=1, i',
        'referer': 'https://www.douyin.com/jingxuan',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie, // 这里使用传入的cookie
      };
      
      LoggerUtil.i('【DouyinHotFeedApi】发送请求: $url');
      LoggerUtil.i('【DouyinHotFeedApi】请求头: $headers');
      
      // 发送请求
      final response = await _dio.post(
        url,
        options: Options(headers: headers),
      );
      
      // 检查响应状态
      if (response.statusCode == 200) {
        LoggerUtil.i('【DouyinHotFeedApi】请求成功，解析数据');
        LoggerUtil.i('【DouyinHotFeedApi】响应数据: ${response.data}');
        
        // 检查响应数据是否为空
        if (response.data == null) {
          LoggerUtil.e('【DouyinHotFeedApi】响应数据为空');
          return BaseResponse(
            code: -1,
            msg: '响应数据为空',
            data: null,
          );
        }
        
        try {
          // 解析响应数据
          final douyinResponse = DouyinHotFeedResponse.fromJson(response.data);
          
          // 检查业务状态码
          if (douyinResponse.statusCode == 0) {
            // 提取有效的视频项
            final List<DouyinHotFeedItem> items = [];
            for (var card in douyinResponse.cards) {
              if (card.type == 1 && card.aweme != null) {
                items.add(card.aweme!);
              }
            }
            
            LoggerUtil.i('【DouyinHotFeedApi】获取抖音热门Feed成功，数据条数: ${items.length}');
            
            return BaseResponse(
              code: 0,
              msg: '获取抖音热门Feed成功',
              data: items,
              success: true,
            );
          } else {
            LoggerUtil.e('【DouyinHotFeedApi】获取抖音热门Feed失败: ${douyinResponse.statusMsg}');
            
            return BaseResponse(
              code: douyinResponse.statusCode,
              msg: douyinResponse.statusMsg,
              data: null,
            );
          }
        } catch (e) {
          LoggerUtil.e('【DouyinHotFeedApi】解析响应数据失败: $e');
          return BaseResponse(
            code: -1,
            msg: '解析响应数据失败: $e',
            data: null,
          );
        }
      } else {
        LoggerUtil.e('【DouyinHotFeedApi】请求失败: ${response.statusCode}');
        
        return BaseResponse(
          code: response.statusCode ?? -1,
          msg: '请求失败: ${response.statusMessage}',
          data: null,
        );
      }
    } catch (e, stackTrace) {
      LoggerUtil.e('【DouyinHotFeedApi】获取抖音热门Feed异常: $e\n$stackTrace');
      
      return BaseResponse(
        code: -1,
        msg: '获取抖音热门Feed异常: $e',
        data: null,
      );
    }
  }
  
  /// 从cookie字符串中提取指定键的值
  String? _extractCookieValue(String cookie, String key) {
    final RegExp regex = RegExp('$key=([^;]+)');
    final Match? match = regex.firstMatch(cookie);
    return match?.group(1);
  }
  
  /// 生成随机字符串
  String _generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_';
    final random = const Uuid().v4();
    return random.replaceAll('-', '').substring(0, length);
  }
} 