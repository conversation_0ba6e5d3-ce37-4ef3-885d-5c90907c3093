import 'package:aitoearn_app/api/base_api_service.dart';
import 'package:aitoearn_app/api/models/base_response.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/plat_core/models/plat_form_model.dart';
import 'package:aitoearn_app/plat_core/plat_manager.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_search_content_model.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/plat_xhs.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_home_feed_model.dart';
import 'package:aitoearn_app/plat_core/utils/platform_checker.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/xhs_service.dart';
import 'package:dio/dio.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_note_detail_model.dart';
import 'package:collection/collection.dart'; // Added for firstWhereOrNull

/// 小红书内容API服务
class XhsContentApi extends BaseApiService {
  /// 单例实例
  static final XhsContentApi _instance = XhsContentApi._internal();
  
  /// 工厂构造函数
  factory XhsContentApi() => _instance;
  
  /// 内部构造函数
  XhsContentApi._internal() : super.create();
  
  /// 获取账号的Cookie
  /// 
  /// [accountId] 账号ID
  /// 返回Cookie字符串
  Future<BaseResponse<String?>> getAccountCookie(String accountId) async {
    try {
      print('【XhsContentApi】开始获取账号Cookie，账号ID: $accountId');
      
      // 从账号持久化服务获取账号信息
      final accounts = await AccountPersistentService.to.accounts;
      print('【XhsContentApi】获取到的账号列表长度: ${accounts.length}');
      
      // 打印所有账号的ID和类型，便于调试
      for (var acc in accounts) {
        print('【XhsContentApi】账号: ${acc.nickname}, ID: ${acc.id}, UID: ${acc.uid}, 平台类型: ${acc.type}');
      }
      
      // 查找指定ID的账号
      final account = accounts.firstWhereOrNull(
        (account) => account.id == accountId || account.uid == accountId,
        // orElse: () => throw Exception('账号不存在'),
      );
      
      if (account == null) {
        print('【XhsContentApi】找不到账号，ID: $accountId');
        return BaseResponse(
          msg: '账号不存在，ID: $accountId',
          code: -1,
        );
      }
      
      print('【XhsContentApi】找到账号: ${account.nickname}, ID: ${account.id}, UID: ${account.uid}, 类型: ${account.type}');
      
      // 检查账号是否是小红书账号
      if (account.type != PlatTypeEnum.xhs) {
        print('【XhsContentApi】账号类型错误: ${account.type}，期望类型: ${PlatTypeEnum.xhs}');
        return BaseResponse(
          msg: '账号类型错误，请使用小红书账号',
          code: -1,
        );
      }
      
      // 获取Cookie
      final cookie = account.accessToken;
      if (cookie == null || cookie.isEmpty) {
        print('【XhsContentApi】账号Cookie为空');
        return BaseResponse(
          msg: '账号Cookie为空，请重新登录',
          code: -1,
        );
      }
      
      print('【XhsContentApi】成功获取到Cookie，长度: ${cookie.length}');
      if (cookie.length > 50) {
        print('【XhsContentApi】Cookie前50个字符: ${cookie.substring(0, 50)}...');
      } else {
        print('【XhsContentApi】Cookie: $cookie');
      }
      
      // 返回Cookie
      return BaseResponse(
        msg: '成功',
        code: 0,
        data: cookie,
        success: true,
      );
    } catch (e) {
      print('【XhsContentApi】获取账号Cookie异常: $e');
      return BaseResponse(
        msg: '获取账号Cookie失败: $e',
        code: -1,
      );
    }
  }
  
  /// 搜索小红书内容
  /// 
  /// [accountId] 账号ID
  /// [keyword] 搜索关键词
  /// [page] 页码，从1开始
  /// [pageSize] 每页数量，默认20
  /// [sort] 排序方式，默认为general
  /// 返回搜索结果响应
  Future<BaseResponse<XhsSearchContentData?>> searchContent({
    required String accountId,
    required String keyword,
    int page = 1,
    int pageSize = 20,
    String sort = 'general',
  }) async {
    try {
      // 获取账号的Cookie
      final cookieResult = await getAccountCookie(accountId);
      if (!cookieResult.isSuccess || cookieResult.data == null) {
        return BaseResponse(
          msg: cookieResult.msg ?? '获取账号Cookie失败',
          code: cookieResult.code,
        );
      }
      
      // 构建请求参数
      final request = XhsSearchContentRequest(
        keyword: keyword,
        page: page,
        pageSize: pageSize,
      );
      
      // 调用小红书服务
      final result = await XhsService.searchContent(
        cookieStr: cookieResult.data!,
        request: request,
      );
      
      // 转换为BaseResponse格式
      if (result.success && result.code == 0) {
        return BaseResponse(
          msg: '成功',
          code: 0,
          data: result.data,
          success: true,
        );
      } else {
        return BaseResponse(
          msg: result.msg,
          code: result.code,
        );
      }
    } catch (e, stackTrace) {
      print('搜索小红书内容异常: $e\n$stackTrace');
      return BaseResponse(
        msg: '搜索失败: $e',
        code: -1,
      );
    }
  }

  /// 获取小红书笔记详情
  ///
  /// [accountId] 账号ID
  /// [noteId] 笔记ID
  /// [xsecToken] 安全令牌
  /// 返回笔记详情响应
  Future<BaseResponse<NoteDetailItem?>> getNoteDetail({
    required String accountId,
    required String noteId,
    required String xsecToken,
  }) async {
    try {
      print('【XhsContentApi】开始获取笔记详情，账号ID: $accountId, 笔记ID: $noteId');
      
      // 获取账号的Cookie
      final cookieResult = await getAccountCookie(accountId);
      if (!cookieResult.isSuccess || cookieResult.data == null) {
        print('【XhsContentApi】获取账号Cookie失败: ${cookieResult.msg}');
        return BaseResponse(
          msg: cookieResult.msg ?? '获取账号Cookie失败',
          code: cookieResult.code,
        );
      }
      
      print('【XhsContentApi】成功获取Cookie，长度: ${cookieResult.data!.length}');
      
      // 获取平台模型
      final platFormModel = PlatformModel(
        id: accountId,
        platType: 'xhs',
        cookieList: PlatformModel.cookieStringToCookies(cookieResult.data!),
        cookie: cookieResult.data!,
      );
      
      // 获取小红书平台实例
      final platXhs = PlatManager.getPlatInstanceByName(PlatformChecker.XHS, platFormModel) as PlatXhs;
      
      // 调用平台实例的笔记详情方法
      print('【XhsContentApi】调用平台实例获取笔记详情');
      final result = await platXhs.getNoteDetail(
        noteId: noteId,
        xsecToken: xsecToken,
      );

      if (result.success && result.data.items.isNotEmpty) {
        print('【XhsContentApi】获取笔记详情成功');
        return BaseResponse(
          msg: '成功',
          code: 0,
          data: result.data.items.first,
          success: true,
        );
      } else {
        print('【XhsContentApi】获取笔记详情失败: ${result.msg}');
        return BaseResponse(
          msg: result.msg,
          code: result.code,
        );
      }
    } catch (e, stackTrace) {
      print('【XhsContentApi】获取小红书笔记详情异常: $e\n$stackTrace');
      return BaseResponse(
        msg: '获取笔记详情失败: $e',
        code: -1,
      );
    }
  }

  /// 获取小红书首页热门作品
  /// 
  /// [accountId] 账号ID
  /// [cursorScore] 光标分数，用于分页，首次加载传空字符串
  /// [num] 每页数量，默认18
  /// [refreshType] 刷新类型，1为下拉刷新
  /// [needFilterImage] 是否需要过滤图片，默认false
  /// 返回首页热门作品响应
  Future<BaseResponse<XhsHomeFeedData?>> getHomeFeed({
    required String accountId,
    String cursorScore = '',
    int num = 18,
    int refreshType = 1,
    bool needFilterImage = false,
  }) async {
    try {
      // 获取账号的Cookie
      final cookieResult = await getAccountCookie(accountId);
      if (!cookieResult.isSuccess || cookieResult.data == null) {
        return BaseResponse(
          msg: cookieResult.msg ?? '获取账号Cookie失败',
          code: cookieResult.code,
        );
      }
      
      // 构建请求参数
      final request = XhsHomeFeedRequest(
        cursorScore: cursorScore,
        num: num,
        refreshType: refreshType,
        noteIndex: cursorScore.isEmpty ? 0 : 31,
        needFilterImage: needFilterImage,
      );
      
      // 调用小红书服务
      final result = await XhsService.getHomeFeed(
        cookieStr: cookieResult.data!,
        request: request,
      );
      
      // 转换为BaseResponse格式
      if (result.success && result.code == 0) {
        return BaseResponse(
          msg: '成功',
          code: 0,
          data: result.data,
          success: true,
        );
      } else {
        return BaseResponse(
          msg: result.msg,
          code: result.code,
        );
      }
    } catch (e, stackTrace) {
      print('获取小红书首页热门作品异常: $e\n$stackTrace');
      return BaseResponse(
        msg: '获取失败: $e',
        code: -1,
      );
    }
  }
} 