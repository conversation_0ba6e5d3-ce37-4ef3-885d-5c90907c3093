import 'package:aitoearn_app/network/http.dart';

/// 创建快手授权
/// type = "h5" | "pc"
Future<Map<String, dynamic>?> createKwaiAuth(String type) async {
  var res = await Http.get('/plat/kwai/auth/url/$type');
  if (res?['data'] != null) {
    return res['data'];
  }
  return null;
}

// 获取账号授权状态
Future<Map<String, dynamic>?> getKwaiAuthStatus(String taskId) async {
  final res = await Http.post('/plat/kwai/auth/create-account/$taskId');
  if (res?['data'] != null) {
    return res['data'];
  }
  return null;
}
