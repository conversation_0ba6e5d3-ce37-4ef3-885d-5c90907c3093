import 'package:aitoearn_app/network/http.dart';

/// 获取B站登录地址
/// type = "h5" | "pc"
Future<Map<String, dynamic>?> apiGetBilibiliLoginUrl(String type) async {
  var res = await Http.get('/plat/bilibili/auth/url/$type');
  if (res?['data'] != null) {
    return res['data'] as Map<String, dynamic>;
  }
  return null;
}

// 检查B站授权状态
Future<bool?> apiCheckAccountAuthStatus(String accountId) async {
  final res = await Http.get<Map<String, dynamic>>(
    '/plat/bilibili/auth/status/$accountId',
  );
  return res?['data'] as bool?;
}

// 获取B站分区列表
Future<List<dynamic>?> apiGetBilibiliPartitions(String accountId) async {
  return await Http.get<List<dynamic>>(
    '/plat/bilibili/archive/type/list/$accountId',
  );
}
