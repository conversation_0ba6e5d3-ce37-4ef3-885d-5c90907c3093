import 'dart:io';

import 'package:aitoearn_app/i18n/app_translations.dart';
import 'package:aitoearn_app/i18n/language_service.dart';
import 'package:aitoearn_app/pages/ai_tools/global_media_generation_controller.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/plat_core/models/publish_factory.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:aitoearn_app/store/draft/draft_store_service.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:animations/animations.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:media_kit/media_kit.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:window_manager/window_manager.dart';

// app初始化
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await LanguageService.initLocale();
  MediaKit.ensureInitialized();
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    // 必须加上这一行。
    await windowManager.ensureInitialized();
    WindowOptions windowOptions = const WindowOptions(
      size: Size(500, 900),
      minimumSize: Size(400, 600),
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      title: '哎呦赚',
      titleBarStyle: TitleBarStyle.normal,
    );
    windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
  }
  await GetStorage.init();
  var themeJson = await rootBundle.loadString('assets/theme.json');
  Get.put<UserStoreService>(UserStoreService());
  // 初始化账号持久化服务
  Get.put<AccountPersistentService>(
    AccountPersistentService(),
    permanent: true,
  );
  // 初始化草稿箱服务
  Get.put<DraftStoreService>(DraftStoreService(), permanent: true);
  // 初始化发布服务，用于后台发布
  Get.put<PublishLogic>(PublishLogic(), permanent: true, tag: 'global');
  // 初始化发布工厂
  Get.put<PublishFactory>(PublishFactory(), permanent: true);
  // 预加载互动控制器
  // Get.put<InteractionController>(
  //   InteractionController(),
  //   permanent: true,
  // );

  Get.put(GlobalMediaGenerationController(), permanent: true);

  // Get测试模式
  // TODO 发行版要关闭，后面可以改成通过dotenv读配置文件来控制
  Get.testMode = true;

  runApp(Application(themeJson: themeJson));
  // 配置 EasyLoading 样式
  configToastAndLoading();
}

class Application extends StatelessWidget {
  final String themeJson;

  const Application({required this.themeJson, super.key});

  @override
  Widget build(BuildContext context) {
    // 初始化主题
    TDTheme.needMultiTheme();
    final themeData = TDThemeData.fromJson('theme', themeJson);

    // 设置系统UI样式
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle.light.copyWith(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    return GetMaterialApp(
      title: 'AiToEarn',
      initialRoute: AppRouter.welcomePath,
      getPages: AppRouter.pages,
      navigatorKey: Get.key,
      navigatorObservers: [BotToastNavigatorObserver()],
      debugShowCheckedModeBanner: false,
      builder: BotToastInit(),
      theme: ThemeData(
        extensions: [themeData!],
        scaffoldBackgroundColor: TDTheme.of(context).whiteColor1,
        canvasColor: TDTheme.of(context).whiteColor1,
        pageTransitionsTheme: const PageTransitionsTheme(
          builders: {
            TargetPlatform.android: SharedAxisPageTransitionsBuilder(
              transitionType: SharedAxisTransitionType.horizontal,
            ),
            TargetPlatform.iOS: SharedAxisPageTransitionsBuilder(
              transitionType: SharedAxisTransitionType.horizontal,
            ),
          },
        ),
      ),
      translations: AppTranslations(),
      locale: Get.locale, // 不需要 Obx
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
      fallbackLocale: const Locale('zh', 'CN'),
      supportedLocales: LanguageService.supportedLocales,
    );
  }
}
