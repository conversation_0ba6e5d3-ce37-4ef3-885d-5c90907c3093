/// 发布类型枚举
enum PubTypeEnum {
  /// 文章发布
  article('文章'),

  /// 视频发布
  video('视频'),

  /// 图文发布
  imageText('图文');

  final String val;
  const PubTypeEnum(this.val);
}

/// 账户类型枚举
enum PlatTypeEnum {
  pinterest('pinterest'),
  threads('threads'),
  instagram('instagram'),
  facebook('facebook'),
  twitter('twitter'),
  bilibili('bilibili'),
  youTube('YouTube'),
  tiktok('tiktok'),

  wxGzh('wxGzh'),
  douyin('douyin'),
  xhs('xhs'),
  wxWph('wx-sph'),
  kwai('ks');

  final String val;
  const PlatTypeEnum(this.val);
}

extension PlatTypeEnumExt on PlatTypeEnum {
  static PlatTypeEnum fromString(String type) {
    final t = type.toLowerCase();
    switch (t) {
      case 'pinterest':
        return PlatTypeEnum.pinterest;
      case 'threads':
        return PlatTypeEnum.threads;
      case 'instagram':
        return PlatTypeEnum.instagram;
      case 'facebook':
        return PlatTypeEnum.facebook;
      case 'twitter':
        return PlatTypeEnum.twitter;
      case 'bilibili':
        return PlatTypeEnum.bilibili;
      case 'youtube':
        return PlatTypeEnum.youTube;
      case 'tiktok':
        return PlatTypeEnum.tiktok;
      case 'wxgzh':
        return PlatTypeEnum.wxGzh;
      case 'douyin':
        return PlatTypeEnum.douyin;
      case 'xhs':
        return PlatTypeEnum.xhs;
      case 'wx-sph':
      case 'wxwph':
      case 'wxsph':
        return PlatTypeEnum.wxWph;
      case 'kwai':
      case 'ks':
        return PlatTypeEnum.kwai;
      default:
        return PlatTypeEnum.douyin; // 默认值
    }
  }
}
