import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';

/// 平台的通用发布参数配置
class CommonPubParamsConfig {
  /// 标题限制
  final int? titleMax;

  /// 定时发布参数
  final TimingMax? timingMax;

  /// 话题数量限制
  final int topicMax;

  /// 图文发布限制
  final ImgTextConfig? imgTextConfig;

  const CommonPubParamsConfig({
    required this.topicMax,
    this.titleMax,
    this.timingMax,
    this.imgTextConfig,
  });
}

/// 定时发布的限制参数
class TimingMax {
  /// 最大时间
  final int maxDate;

  /// 时间偏移
  final int timeOffset;
  const TimingMax({required this.maxDate, required this.timeOffset});
}

/// 图文发布的限制参数
class ImgTextConfig {
  /// 图片最大数量
  final int imagesMax;
  const ImgTextConfig({required this.imagesMax});
}

/// 平台信息类
class AccountPlatInfo {
  /// 图标路径
  final String icon;

  /// 平台名称
  final String name;

  /// 支持的发布类型
  final Set<PubTypeEnum> pubTypes;

  /// 通用发布参数配置
  final CommonPubParamsConfig commonPubParamsConfig;

  // 平台类型
  final PlatTypeEnum platType;

  const AccountPlatInfo({
    required this.icon,
    required this.name,
    required this.pubTypes,
    required this.commonPubParamsConfig,
    required this.platType,
  });
}
