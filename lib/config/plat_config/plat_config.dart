import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_models.dart';
import 'package:get/get.dart';

/// 平台配置列表
final List<AccountPlatInfo> platConfigList = [
  AccountPlatInfo(
    platType: PlatTypeEnum.kwai,
    name: 'account.platform.kuaishou'.tr,
    icon: 'assets/images/account/plat_icon/ks.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: const CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 13, timeOffset: 60),
      topicMax: 3,
    ),
  ),
  AccountPlatInfo(
    platType: PlatTypeEnum.xhs,
    name: 'account.platform.xhs'.tr,
    icon: 'assets/images/account/plat_icon/xhs.png',
    pubTypes: {PubTypeEnum.video, PubTypeEnum.article, PubTypeEnum.imageText},
    commonPubParamsConfig: const CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 14, timeOffset: 60),
      topicMax: 20,
      titleMax: 20,
      imgTextConfig: ImgTextConfig(imagesMax: 18),
    ),
  ),
  AccountPlatInfo(
    platType: PlatTypeEnum.douyin,
    name: 'account.platform.douyin'.tr,
    icon: 'assets/images/account/plat_icon/douyin.png',
    pubTypes: {PubTypeEnum.video, PubTypeEnum.article, PubTypeEnum.imageText},
    commonPubParamsConfig: const CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 14, timeOffset: 120),
      titleMax: 30,
      topicMax: 5,
      imgTextConfig: ImgTextConfig(imagesMax: 35),
    ),
  ),
  AccountPlatInfo(
    platType: PlatTypeEnum.wxWph,
    name: 'account.platform.weixin'.tr,
    icon: 'assets/images/account/plat_icon/wx-sph.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: const CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  AccountPlatInfo(
    platType: PlatTypeEnum.wxGzh,
    name: 'account.platform.wxGzh'.tr,
    icon: 'assets/images/account/plat_icon/wx-gzh.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: const CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  const AccountPlatInfo(
    platType: PlatTypeEnum.facebook,
    name: 'facebook',
    icon: 'assets/images/account/plat_icon/facebook.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  const AccountPlatInfo(
    platType: PlatTypeEnum.pinterest,
    name: 'pinterest',
    icon: 'assets/images/account/plat_icon/pinterest.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  const AccountPlatInfo(
    platType: PlatTypeEnum.threads,
    name: 'threads',
    icon: 'assets/images/account/plat_icon/threads.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  const AccountPlatInfo(
    platType: PlatTypeEnum.instagram,
    name: 'instagram',
    icon: 'assets/images/account/plat_icon/instagram.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  const AccountPlatInfo(
    platType: PlatTypeEnum.twitter,
    name: 'twitter',
    icon: 'assets/images/account/plat_icon/twtter.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  const AccountPlatInfo(
    platType: PlatTypeEnum.bilibili,
    name: 'bilibili',
    icon: 'assets/images/account/plat_icon/bilibili.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  const AccountPlatInfo(
    platType: PlatTypeEnum.youTube,
    name: 'YouTube',
    icon: 'assets/images/account/plat_icon/youtube.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
  const AccountPlatInfo(
    platType: PlatTypeEnum.tiktok,
    name: 'TikTok',
    icon: 'assets/images/account/plat_icon/tiktok.png',
    pubTypes: {PubTypeEnum.video},
    commonPubParamsConfig: CommonPubParamsConfig(
      timingMax: TimingMax(maxDate: 30, timeOffset: 60),
      titleMax: 16,
      topicMax: 10,
    ),
  ),
];

/// 根据列表生成 Map
final Map<PlatTypeEnum, AccountPlatInfo> platConfigMap = {
  for (var config in platConfigList) config.platType: config,
};

/// 本地登录的平台 set
final Set<PlatTypeEnum> localLoginPlatSet = {
  PlatTypeEnum.xhs,
  PlatTypeEnum.douyin,
  PlatTypeEnum.wxWph,
};

AccountPlatInfo? getPlatformInfoByString(String platform) {
  final platType = PlatTypeEnumExt.fromString(platform);
  return platConfigMap[platType];
  return null;
}
