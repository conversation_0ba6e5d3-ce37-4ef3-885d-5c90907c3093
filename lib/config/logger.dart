import 'package:logger/logger.dart';

abstract class LoggerUtil {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2, // 显示多少方法调用
      errorMethodCount: 8, // 错误时显示多少方法调用
      lineLength: 120, // 每行长度
      colors: true, // 彩色输出
      printEmojis: true, // 打印表情
    ),
    level: Level.debug, // 设置日志级别
  );

  // 普通日志
  static void log(dynamic message) {
    _logger.d('====d：$message');
  }

  // 信息日志
  static void i(dynamic message) {
    _logger.i('====i：$message');
  }

  // 警告日志
  static void w(dynamic message) {
    _logger.w('====w：$message');
  }

  // 错误日志
  static void e(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e('====e：$message', error: error, stackTrace: stackTrace);
  }

  // 仅在开发环境显示的日志
  static void d(dynamic message) {
    // 可以根据环境变量判断是否为开发环境
    const bool isDevelopment = true; // 这里可以替换为实际的环境判断
    if (isDevelopment) {
      _logger.d('====[DEV] $message');
    }
  }

  // 网络请求日志
  static void network(String message) {
    _logger.i('====[NETWORK] $message');
  }
}
