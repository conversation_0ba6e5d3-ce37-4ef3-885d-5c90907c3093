import 'package:flutter/material.dart';
import 'package:aitoearn_app/pages/location/xhs_location_select_page.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/models/xhs_poi_model.dart';

/// 打开小红书位置选择页面
///
/// [context] 上下文
/// [cookies] 小红书Cookie
/// [initialPoiData] 初始POI数据（可选）
/// [onLocationSelected] 位置选择回调（可选）
/// 
/// 返回选中的位置信息，如果用户取消选择则返回null
Future<XhsPoiItem?> navigateToXhsLocationSelect({
  required BuildContext context,
  required String cookies,
  Map<String, dynamic>? initialPoiData,
  Function(XhsPoiItem)? onLocationSelected,
}) async {
  // 跳转到小红书位置选择页面
  final result = await Navigator.push<XhsPoiItem>(
    context,
    MaterialPageRoute(
      builder: (context) => XhsLocationSelectPage(
        cookies: cookies,
        initialPoiData: initialPoiData,
        onLocationSelected: onLocationSelected,
      ),
    ),
  );
  
  return result;
}