import 'package:flutter/material.dart';
import 'package:aitoearn_app/pages/location/douyin_location_select_page.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_poi_model.dart';

/// 打开抖音位置选择页面
///
/// [context] 上下文
/// [cookies] 抖音Cookie
/// [proxy] 代理地址（可选）
/// [onLocationSelected] 位置选择回调（可选）
/// 
/// 返回选中的位置信息，如果用户取消选择则返回null
Future<DouyinPoiItem?> navigateToDouyinLocationSelect({
  required BuildContext context,
  required String cookies,
  String? proxy,
  Function(DouyinPoiItem)? onLocationSelected,
}) async {
  // 跳转到抖音位置选择页面
  final result = await Navigator.push<DouyinPoiItem>(
    context,
    MaterialPageRoute(
      builder: (context) => DouyinLocationSelectPage(
        cookies: cookies,
        proxy: proxy,
        onLocationSelected: onLocationSelected,
      ),
    ),
  );
  
  return result;
}