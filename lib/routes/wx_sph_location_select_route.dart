import 'package:flutter/material.dart';
import 'package:aitoearn_app/pages/location/wx_sph_location_select_page.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/models/wx_sph_poi_model.dart';

/// 打开微信视频号位置选择页面
///
/// [context] 上下文
/// [cookies] 微信视频号Cookie
/// [initialPoiData] 初始POI数据（可选）
/// [onLocationSelected] 位置选择回调（可选）
/// 
/// 返回选中的位置信息，如果用户取消选择则返回null
Future<WxSphPoiItem?> navigateToWxSphLocationSelect({
  required BuildContext context,
  required String cookies,
  Map<String, dynamic>? initialPoiData,
  Function(WxSphPoiItem)? onLocationSelected,
}) async {
  // 跳转到微信视频号位置选择页面
  final result = await Navigator.push<WxSphPoiItem>(
    context,
    MaterialPageRoute(
      builder: (context) => WxSphLocationSelectPage(
        cookies: cookies,
        initialPoiData: initialPoiData,
        onLocationSelected: onLocationSelected,
      ),
    ),
  );
  
  return result;
} 