class PlatformM {
  final String id;
  final String name;
  final String type;
  final String description;
  final String icon;
  final int status;
  final int sort;
  final String createTime;
  final String updateTime;
  final String hotinfo;

  PlatformM({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.icon,
    required this.status,
    required this.sort,
    required this.createTime,
    required this.updateTime,
    required this.hotinfo,
  });

  factory PlatformM.fromJson(Map<String, dynamic> json) {
    return PlatformM(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      description: json['description'] ?? '',
      icon: json['icon'] ?? '',
      status:
          (json['status'] is int)
              ? json['status']
              : (json['status'] is double)
              ? json['status'].toInt()
              : 0,
      sort:
          (json['sort'] is int)
              ? json['sort']
              : (json['sort'] is double)
              ? json['sort'].toInt()
              : 0,
      createTime: json['createTime'] ?? '',
      updateTime: json['updateTime'] ?? '',
      hotinfo: json['hotinfo'] ?? '',
    );
  }
}

// models/category_model.dart
class Category {
  final String name;

  Category({required this.name});

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(name: json as String);
  }
}

// models/title_model.dart
class Title {
  final String id;
  final String category;
  final String title;
  final int rank;
  final int engagement;
  final String? publishTime;
  final String originalId;
  final String updateTime;
  final String timeType;
  final String url;
  final String platformId;

  Title({
    required this.id,
    required this.category,
    required this.title,
    required this.rank,
    required this.engagement,
    required this.originalId, required this.updateTime, required this.timeType, required this.url, required this.platformId, this.publishTime,
  });

  factory Title.fromJson(Map<String, dynamic> json) {
    return Title(
      id: json['_id'] ?? json['id'] ?? '',
      category: json['category'] ?? '',
      title: json['title'] ?? '',
      rank: json['rank'] ?? 0,
      engagement: json['engagement'] ?? 0,
      publishTime: json['publishTime'],
      originalId: json['originalId'] ?? '',
      updateTime: json['update_time'] ?? json['updateTime'] ?? '',
      timeType: json['timeType'] ?? '',
      url: json['url'] ?? '',
      platformId: json['platformId'] ?? '',
    );
  }
}

class CategoryTitles {
  final String category;
  final List<Title> titles;

  CategoryTitles({required this.category, required this.titles});

  factory CategoryTitles.fromJson(Map<String, dynamic> json) {
    return CategoryTitles(
      category: json['category'] ?? '',
      titles:
          (json['titles'] as List<dynamic>?)
              ?.map((title) => Title.fromJson(title))
              .toList() ??
          [],
    );
  }
}

class TitlesResponse {
  final List<CategoryTitles> data;

  TitlesResponse({required this.data});

  factory TitlesResponse.fromJson(Map<String, dynamic> json) {
    return TitlesResponse(
      data:
          (json['data'] as List<dynamic>?)
              ?.map((item) => CategoryTitles.fromJson(item))
              .toList() ??
          [],
    );
  }
}

class TitlesListResponse {
  final List<Title> items;
  final Meta meta;

  TitlesListResponse({required this.items, required this.meta});

  factory TitlesListResponse.fromJson(Map<String, dynamic> json) {
    return TitlesListResponse(
      items:
          (json['data']['items'] as List<dynamic>?)
              ?.map((item) => Title.fromJson(item))
              .toList() ??
          [],
      meta: Meta.fromJson(json['data']['meta'] ?? {}),
    );
  }
}

class Meta {
  final int itemCount;
  final int totalItems;
  final int itemsPerPage;
  final int totalPages;
  final int currentPage;

  Meta({
    required this.itemCount,
    required this.totalItems,
    required this.itemsPerPage,
    required this.totalPages,
    required this.currentPage,
  });

  factory Meta.fromJson(Map<String, dynamic> json) {
    return Meta(
      itemCount: json['itemCount'] ?? 0,
      totalItems: json['totalItems'] ?? 0,
      itemsPerPage: json['itemsPerPage'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      currentPage: json['currentPage'] ?? 0,
    );
  }
}
