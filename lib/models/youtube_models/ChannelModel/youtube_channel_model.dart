class ChannelModel {
  final String id;
  final Snippet snippet;
  final Statistics statistics;

  ChannelModel({
    required this.id,
    required this.snippet,
    required this.statistics,
  });

  factory ChannelModel.fromJson(Map<String, dynamic> json) {
    return ChannelModel(
      id: json['id'],
      snippet: Snippet.fromJson(json['snippet']),
      statistics: Statistics.fromJson(json['statistics']),
    );
  }
}

class Snippet {
  final String title;
  final String description;
  final Thumbnails thumbnails;
  final String publishedAt;

  Snippet({
    required this.title,
    required this.description,
    required this.thumbnails,
    required this.publishedAt,
  });

  factory Snippet.fromJson(Map<String, dynamic> json) {
    return Snippet(
      title: json['title'],
      description: json['description'],
      thumbnails: Thumbnails.from<PERSON>son(json['thumbnails']),
      publishedAt: json['publishedAt'],
    );
  }
}

class Thumbnails {
  final Thumbnail defaultThumbnail;
  final Thumbnail medium;
  final Thumbnail high;

  Thumbnails({
    required this.defaultThumbnail,
    required this.medium,
    required this.high,
  });

  factory Thumbnails.fromJson(Map<String, dynamic> json) {
    return Thumbnails(
      defaultThumbnail: Thumbnail.fromJson(json['default']),
      medium: Thumbnail.fromJson(json['medium']),
      high: Thumbnail.fromJson(json['high']),
    );
  }
}

class Thumbnail {
  final String url;
  final int width;
  final int height;

  Thumbnail({
    required this.url,
    required this.width,
    required this.height,
  });

  factory Thumbnail.fromJson(Map<String, dynamic> json) {
    return Thumbnail(
      url: json['url'],
      width: json['width'],
      height: json['height'],
    );
  }
}

class Statistics {
  final String viewCount;
  final String subscriberCount;
  final bool hiddenSubscriberCount;
  final String videoCount;

  Statistics({
    required this.viewCount,
    required this.subscriberCount,
    required this.hiddenSubscriberCount,
    required this.videoCount,
  });

  factory Statistics.fromJson(Map<String, dynamic> json) {
    return Statistics(
      viewCount: json['viewCount'],
      subscriberCount: json['subscriberCount'],
      hiddenSubscriberCount: json['hiddenSubscriberCount'],
      videoCount: json['videoCount'],
    );
  }
}