class YoutubeAuthUrlModel {
  final String data;
  final int code;
  final String msg;
  final String url;

  YoutubeAuthUrlModel({
    required this.data,
    required this.code,
    required this.msg,
    required this.url,
  });

  factory YoutubeAuthUrlModel.fromJson(Map<String, dynamic> json) {
    return YoutubeAuthUrlModel(
      data: json['data'],
      code: json['code'],
      msg: json['msg'],
      url: json['url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'code': code,
      'msg': msg,
      'url': url,
    };
  }
}