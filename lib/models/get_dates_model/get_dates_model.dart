class RankingData {
  String id;
  String spiderTime;
  String rankingId;
  String? startTime;
  String? endTime;
  String showDate;
  String queryDate;
  String value;

  RankingData({
    required this.id,
    required this.spiderTime,
    required this.rankingId,
    required this.showDate, required this.queryDate, required this.value, this.startTime,
    this.endTime,
  });

  factory RankingData.fromJson(Map<String, dynamic> json) {
    return RankingData(
      id: json['_id'] ?? '',
      spiderTime: json['spiderTime'] ?? '',
      rankingId: json['rankingId'] ?? '',
      startTime: json['startTime'],
      endTime: json['endTime'],
      showDate: json['showDate'] ?? '',
      queryDate: json['queryDate'] ?? '',
      value: json['value'] ?? '',
    );
  }
}

class DateResponse {
  int code;
  String msg;
  List<RankingData> data;

  DateResponse({
    required this.code,
    required this.msg,
    required this.data,
  });

  factory DateResponse.fromJson(Map<String, dynamic> json) {
    var list = json['data'] as List;
    List<RankingData> dataList = list.map((i) => RankingData.fromJson(i)).toList();

    return DateResponse(
      code: json['code'] ?? 0,
      msg: json['msg'] ?? '',
      data: dataList,
    );
  }
}
