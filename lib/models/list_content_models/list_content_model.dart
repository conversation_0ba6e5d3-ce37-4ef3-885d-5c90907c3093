class RankingContentResponse {
  final int code;
  final String message;
  final RankingContentData data;

  RankingContentResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory RankingContentResponse.fromJson(Map<String, dynamic> json) {
    return RankingContentResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: RankingContentData.fromJson(json['data']),
    );
  }
}

class RankingContentData {
  final List<RankingItem> items;
  final Meta meta;

  RankingContentData({
    required this.items,
    required this.meta,
  });

  factory RankingContentData.fromJson(Map<String, dynamic> json) {
    var itemsList = (json['items'] as List<dynamic>)
        .map((e) => RankingItem.fromJson(e as Map<String, dynamic>))
        .toList();
    
    return RankingContentData(
      items: itemsList,
      meta: Meta.fromJson(json['meta']),
    );
  }
}

class RankingItem {
  final String rankingId;
  final String colName;
  final int status;
  final String url;
  final String category;
  final String subCategory;
  final String type;
  final int shareCount;
  final int collectCount;
  final String cover;
  final String title;
  final String createTime;
  final String updateTime;
  final Author author;
  final Stats stats;
  final AnaAdd anaAdd;
  final String publishTime;
  final int rankingPosition;
  final String platformId;
  final String originalId;
  final String label;
  final String id;

  RankingItem({
    required this.rankingId,
    required this.colName,
    required this.status,
    required this.url,
    required this.category,
    required this.subCategory,
    required this.type,
    required this.shareCount,
    required this.collectCount,
    required this.cover,
    required this.title,
    required this.createTime,
    required this.updateTime,
    required this.author,
    required this.stats,
    required this.anaAdd,
    required this.publishTime,
    required this.rankingPosition,
    required this.platformId,
    required this.originalId,
    required this.label,
    required this.id,
  });

  factory RankingItem.fromJson(Map<String, dynamic> json) {
    return RankingItem(
      rankingId: json['rankingId'] ?? '',
      colName: json['col_name'] ?? '',
      status: json['status'] ?? 0,
      url: json['url'] ?? '',
      category: json['category'] ?? '',
      subCategory: json['subCategory'] ?? '',
      type: json['type'] ?? '',
      shareCount: json['shareCount'] ?? 0,
      collectCount: json['collectCount'] ?? 0,
      cover: json['cover'] ?? '',
      title: json['title'] ?? '',
      createTime: json['createTime'] ?? '',
      updateTime: json['updateTime'] ?? '',
      author: Author.fromJson(json['author']),
      stats: Stats.fromJson(json['stats']),
      anaAdd: AnaAdd.fromJson(json['anaAdd']),
      publishTime: json['publishTime'] ?? '',
      rankingPosition: json['rankingPosition'] ?? 0,
      platformId: json['platformId'] ?? '',
      originalId: json['originalId'] ?? '',
      label: json['label'] ?? '',
      id: json['id'] ?? '',
    );
  }
}
class AnaAdd {
  final int addCommentCount;
  final int addLikeCount;
  final int addShareCount;
  final dynamic addViewCount;  // 'null' is allowed in the original data.
  final int collectedCount;
  final int interactiveCount;
  final int predReadnum;
  final int useCommentCount;
  final int useLikeCount;
  final int useShareCount;
  final dynamic useViewCount;  // 'null' is allowed in the original data.
  final int addCollectCount;
  final int addInteractiveCount;
  final int useCollectCount;

  AnaAdd({
    required this.addCommentCount,
    required this.addLikeCount,
    required this.addShareCount,
    required this.addViewCount,
    required this.collectedCount,
    required this.interactiveCount,
    required this.predReadnum,
    required this.useCommentCount,
    required this.useLikeCount,
    required this.useShareCount,
    required this.useViewCount,
    required this.addCollectCount,
    required this.addInteractiveCount,
    required this.useCollectCount,
  });

  factory AnaAdd.fromJson(Map<String, dynamic> json) {
    return AnaAdd(
      addCommentCount: json['addCommentCount'] ?? 0,
      addLikeCount: json['addLikeCount'] ?? 0,
      addShareCount: json['addShareCount'] ?? 0,
      addViewCount: json['addViewCount'], // null is allowed here
      collectedCount: json['collectedCount'] ?? 0,
      interactiveCount: json['interactiveCount'] ?? 0,
      predReadnum: json['pred_readnum'] ?? 0,
      useCommentCount: json['useCommentCount'] ?? 0,
      useLikeCount: json['useLikeCount'] ?? 0,
      useShareCount: json['useShareCount'] ?? 0,
      useViewCount: json['useViewCount'], // null is allowed here
      addCollectCount: json['addCollectCount'] ?? 0,
      addInteractiveCount: json['addInteractiveCount'] ?? 0,
      useCollectCount: json['useCollectCount'] ?? 0,
    );
  }
}

class Author {
  final String id;
  final String name;
  final int fansCount;
  final String avatar;

  Author({
    required this.id,
    required this.name,
    required this.fansCount,
    required this.avatar,
  });

  factory Author.fromJson(Map<String, dynamic> json) {
    return Author(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      fansCount: json['fansCount'] ?? 0,
      avatar: json['avatar'] ?? '',
    );
  }
}

class Stats {
  final int viewCount;
  final int likeCount;
  final int commentCount;

  Stats({
    required this.viewCount,
    required this.likeCount,
    required this.commentCount,
  });

  factory Stats.fromJson(Map<String, dynamic> json) {
    return Stats(
      viewCount: json['viewCount'] ?? 0,
      likeCount: json['likeCount'] ?? 0,
      commentCount: json['commentCount'] ?? 0,
    );
  }
}

class Meta {
  final int itemCount;
  final int totalItems;
  final int itemsPerPage;
  final int totalPages;
  final int currentPage;

  Meta({
    required this.itemCount,
    required this.totalItems,
    required this.itemsPerPage,
    required this.totalPages,
    required this.currentPage,
  });

  factory Meta.fromJson(Map<String, dynamic> json) {
    return Meta(
      itemCount: json['itemCount'] ?? 0,
      totalItems: json['totalItems'] ?? 0,
      itemsPerPage: json['itemsPerPage'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      currentPage: json['currentPage'] ?? 0,
    );
  }
}
