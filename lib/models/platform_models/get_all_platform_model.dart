class PlatformModel {
  String id;
  String name;
  String type;
  String description;
  String icon;
  double status;
  double sort;
  String createTime;
  String updateTime;
  String hotinfo;
  String nameEn;
  String descriptionEn;

  PlatformModel({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.icon,
    required this.status,
    required this.sort,
    required this.createTime,
    required this.updateTime,
    required this.hotinfo,
    required this.nameEn,
    required this.descriptionEn,
  });

  factory PlatformModel.fromJson(Map<String, dynamic> json) {
    return PlatformModel(
      id: json['id'] ?? '',  // If null, use empty string
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      description: json['description'] ?? '',  // Handle null for description
      icon: json['icon'] ?? '',  // Handle null for icon
      status: json['status']?.toDouble() ?? 0.0,  // Ensure it's a double, default 0.0 if null
      sort: json['sort']?.toDouble() ?? 0.0,  // Default 0.0 if null
      createTime: json['createTime'] ?? '',
      updateTime: json['updateTime'] ?? '',
      hotinfo: json['hotinfo'] ?? '',
      nameEn: json['name_en'] ?? '',
      descriptionEn: json['description_en'] ?? '',
    );
  }
}

class GetPlatformModel {
  int code;
  String msg;
   dynamic data; 

  GetPlatformModel({
    required this.code,
    required this.msg,
    required this.data,
  });

  // For fetching a single platform
  factory GetPlatformModel.fromJsonSingle(Map<String, dynamic> json) {
    return GetPlatformModel(
      code: json['code'],
      msg: json['msg'],
            data: PlatformModel.fromJson(json['data']),  // Single platform object
    );
  }

  // For fetching multiple platforms
  factory GetPlatformModel.fromJsonList(Map<String, dynamic> json) {
    var list = json['data'] as List;
    List<PlatformModel> platformList = list.map((i) => PlatformModel.fromJson(i)).toList();

    return GetPlatformModel(
      code: json['code'],
      msg: json['msg'],
      data: platformList,  // List of platform objects
    );
  }
}
