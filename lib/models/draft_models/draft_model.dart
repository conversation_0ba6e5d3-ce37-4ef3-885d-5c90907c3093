import 'dart:io';

/// 草稿项模型，表示单个草稿内容
class DraftItem {
  /// 草稿ID
  String id;
  
  /// 创建时间
  DateTime createTime;
  
  /// 更新时间
  DateTime updateTime;
  
  /// 标题
  String title;
  
  /// 描述
  String description;
  
  /// 标签列表
  List<String> tags;
  
  /// 位置信息
  Map<String, dynamic>? locationInfo;
  
  /// 是否定时发布
  bool isScheduled;
  
  /// 定时发布时间
  DateTime? scheduledTime;
  
  /// 视频路径（本地存储）
  String? videoPath;
  
  /// 图片路径列表（本地存储）
  List<String>? imagePaths;
  
  /// 视频缩略图路径（本地存储）
  String? thumbnailPath;
  
  /// 已选择的账号列表
  List<Map<String, dynamic>>? selectedAccounts;
  
  /// 草稿状态：0-未完成，1-已完成
  int status;

  DraftItem({
    required this.id,
    required this.createTime,
    required this.updateTime,
    required this.title,
    required this.description,
    required this.tags,
    this.locationInfo,
    this.isScheduled = false,
    this.scheduledTime,
    this.videoPath,
    this.imagePaths,
    this.thumbnailPath,
    this.selectedAccounts,
    this.status = 0,
  });
  
  /// 从JSON创建对象
  factory DraftItem.fromJson(Map<String, dynamic> json) {
    return DraftItem(
      id: json['id'] as String,
      createTime: DateTime.parse(json['createTime'] as String),
      updateTime: DateTime.parse(json['updateTime'] as String),
      title: json['title'] as String,
      description: json['description'] as String,
      tags: List<String>.from(json['tags'] as List),
      locationInfo: json['locationInfo'] != null 
          ? Map<String, dynamic>.from(json['locationInfo'] as Map)
          : null,
      isScheduled: json['isScheduled'] as bool,
      scheduledTime: json['scheduledTime'] != null 
          ? DateTime.parse(json['scheduledTime'] as String)
          : null,
      videoPath: json['videoPath'] as String?,
      imagePaths: json['imagePaths'] != null 
          ? List<String>.from(json['imagePaths'] as List)
          : null,
      thumbnailPath: json['thumbnailPath'] as String?,
      selectedAccounts: json['selectedAccounts'] != null 
          ? List<Map<String, dynamic>>.from(
              (json['selectedAccounts'] as List).map(
                (e) => Map<String, dynamic>.from(e as Map),
              ),
            )
          : null,
      status: json['status'] as int,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createTime': createTime.toIso8601String(),
      'updateTime': updateTime.toIso8601String(),
      'title': title,
      'description': description,
      'tags': tags,
      'locationInfo': locationInfo,
      'isScheduled': isScheduled,
      'scheduledTime': scheduledTime?.toIso8601String(),
      'videoPath': videoPath,
      'imagePaths': imagePaths,
      'thumbnailPath': thumbnailPath,
      'selectedAccounts': selectedAccounts,
      'status': status,
    };
  }
  
  /// 创建副本
  DraftItem copyWith({
    String? id,
    DateTime? createTime,
    DateTime? updateTime,
    String? title,
    String? description,
    List<String>? tags,
    Map<String, dynamic>? locationInfo,
    bool? isScheduled,
    DateTime? scheduledTime,
    String? videoPath,
    List<String>? imagePaths,
    String? thumbnailPath,
    List<Map<String, dynamic>>? selectedAccounts,
    int? status,
  }) {
    return DraftItem(
      id: id ?? this.id,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      title: title ?? this.title,
      description: description ?? this.description,
      tags: tags ?? List<String>.from(this.tags),
      locationInfo: locationInfo ?? this.locationInfo,
      isScheduled: isScheduled ?? this.isScheduled,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      videoPath: videoPath ?? this.videoPath,
      imagePaths: imagePaths ?? this.imagePaths,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      selectedAccounts: selectedAccounts ?? this.selectedAccounts,
      status: status ?? this.status,
    );
  }
}

/// 草稿箱模型，表示一个草稿箱，包含多个草稿项
class DraftBox {
  /// 草稿箱ID
  String id;
  
  /// 草稿箱名称
  String name;
  
  /// 创建时间
  DateTime createTime;
  
  /// 更新时间
  DateTime updateTime;
  
  /// 草稿箱描述
  String? description;
  
  /// 草稿箱图标
  String? icon;
  
  /// 草稿箱颜色
  String? color;
  
  /// 草稿列表
  List<DraftItem> drafts;
  
  /// 是否为默认草稿箱
  bool isDefault;

  DraftBox({
    required this.id,
    required this.name,
    required this.createTime,
    required this.updateTime,
    this.description,
    this.icon,
    this.color,
    required this.drafts,
    this.isDefault = false,
  });
  
  /// 从JSON创建对象
  factory DraftBox.fromJson(Map<String, dynamic> json) {
    return DraftBox(
      id: json['id'] as String,
      name: json['name'] as String,
      createTime: DateTime.parse(json['createTime'] as String),
      updateTime: DateTime.parse(json['updateTime'] as String),
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      color: json['color'] as String?,
      drafts: (json['drafts'] as List)
          .map((e) => DraftItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      isDefault: json['isDefault'] as bool,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'createTime': createTime.toIso8601String(),
      'updateTime': updateTime.toIso8601String(),
      'description': description,
      'icon': icon,
      'color': color,
      'drafts': drafts.map((e) => e.toJson()).toList(),
      'isDefault': isDefault,
    };
  }
  
  /// 创建副本
  DraftBox copyWith({
    String? id,
    String? name,
    DateTime? createTime,
    DateTime? updateTime,
    String? description,
    String? icon,
    String? color,
    List<DraftItem>? drafts,
    bool? isDefault,
  }) {
    return DraftBox(
      id: id ?? this.id,
      name: name ?? this.name,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      drafts: drafts ?? List<DraftItem>.from(this.drafts),
      isDefault: isDefault ?? this.isDefault,
    );
  }
} 