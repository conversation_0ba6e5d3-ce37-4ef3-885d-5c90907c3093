import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'douyin_hot_feed_model.g.dart';

/// 抖音热门Feed响应
@JsonSerializable()
class DouyinHotFeedResponse {
  /// 状态码
  @J<PERSON><PERSON><PERSON>(name: 'status_code')
  final int statusCode;
  
  /// 状态消息
  @Json<PERSON>ey(name: 'status_msg')
  final String statusMsg;
  
  /// 卡片列表
  @JsonKey(name: 'cards')
  final List<DouyinHotFeedCard> cards;
  
  /// 构造函数
  DouyinHotFeedResponse({
    required this.statusCode,
    required this.statusMsg,
    required this.cards,
  });
  
  /// 从JSON创建
  factory DouyinHotFeedResponse.fromJson(Map<String, dynamic> json) => 
      _$DouyinHotFeedResponseFromJson(json);
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinHotFeedResponseToJson(this);
}

/// 抖音热门Feed卡片
@JsonSerializable()
class DouyinHotFeedCard {
  /// 卡片类型
  @Json<PERSON>ey(name: 'type')
  final int type;
  
  /// 视频内容
  @JsonKey(name: 'aweme')
  final DouyinHotFeedItem? aweme;
  
  /// 构造函数
  DouyinHotFeedCard({
    required this.type,
    this.aweme,
  });
  
  /// 从JSON创建
  factory DouyinHotFeedCard.fromJson(Map<String, dynamic> json) {
    dynamic awemeData = json['aweme'];
    DouyinHotFeedItem? awemeItem;

    if (awemeData is String && awemeData.isNotEmpty) {
      try {
        final decodedData = jsonDecode(awemeData);
        if (decodedData is Map<String, dynamic>) {
          awemeItem = DouyinHotFeedItem.fromJson(decodedData);
        }
      } catch (e) {
        print('Error decoding aweme JSON string: $e');
      }
    } else if (awemeData is Map<String, dynamic>) {
      awemeItem = DouyinHotFeedItem.fromJson(awemeData);
    }
    
    return DouyinHotFeedCard(
      type: json['type'] as int,
      aweme: awemeItem,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinHotFeedCardToJson(this);
}

/// 抖音热门Feed项
@JsonSerializable()
class DouyinHotFeedItem {
  /// 视频ID
  @JsonKey(name: 'aweme_id')
  final String awemeId;
  
  /// 描述
  @JsonKey(name: 'desc')
  final String desc;
  
  /// 作者信息
  @JsonKey(name: 'author')
  final DouyinAuthor? author;
  
  /// 统计信息
  @JsonKey(name: 'statistics')
  final DouyinStatistics? statistics;
  
  /// 视频信息
  @JsonKey(name: 'video')
  final DouyinVideo? video;
  
  /// 封面图片
  @JsonKey(name: 'cover_data')
  final DouyinCoverData? coverData;
  
  /// 构造函数
  DouyinHotFeedItem({
    required this.awemeId,
    required this.desc,
    this.author,
    this.statistics,
    this.video,
    this.coverData,
  });
  
  /// 从JSON创建
  factory DouyinHotFeedItem.fromJson(Map<String, dynamic> json) => 
      _$DouyinHotFeedItemFromJson(json);
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinHotFeedItemToJson(this);
}

/// 抖音作者信息
@JsonSerializable()
class DouyinAuthor {
  /// 用户ID
  @JsonKey(name: 'uid')
  final String? uid;
  
  /// 昵称
  @JsonKey(name: 'nickname')
  final String? nickname;
  
  /// 头像
  @JsonKey(name: 'avatar_thumb')
  final DouyinAvatar? avatarThumb;
  
  /// 构造函数
  DouyinAuthor({
    this.uid,
    this.nickname,
    this.avatarThumb,
  });
  
  /// 从JSON创建
  factory DouyinAuthor.fromJson(Map<String, dynamic> json) => 
      _$DouyinAuthorFromJson(json);
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinAuthorToJson(this);
}

/// 抖音头像
@JsonSerializable()
class DouyinAvatar {
  /// 头像URL列表
  @JsonKey(name: 'url_list')
  final List<String>? urlList;
  
  /// 构造函数
  DouyinAvatar({
    this.urlList,
  });
  
  /// 从JSON创建
  factory DouyinAvatar.fromJson(Map<String, dynamic> json) => 
      _$DouyinAvatarFromJson(json);
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinAvatarToJson(this);
}

/// 抖音统计信息
@JsonSerializable()
class DouyinStatistics {
  /// 点赞数
  @JsonKey(name: 'digg_count')
  final int? diggCount;
  
  /// 评论数
  @JsonKey(name: 'comment_count')
  final int? commentCount;
  
  /// 分享数
  @JsonKey(name: 'share_count')
  final int? shareCount;
  
  /// 播放数
  @JsonKey(name: 'play_count')
  final int? playCount;
  
  /// 构造函数
  DouyinStatistics({
    this.diggCount,
    this.commentCount,
    this.shareCount,
    this.playCount,
  });
  
  /// 从JSON创建
  factory DouyinStatistics.fromJson(Map<String, dynamic> json) => 
      _$DouyinStatisticsFromJson(json);
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinStatisticsToJson(this);
}

/// 抖音视频信息
@JsonSerializable()
class DouyinVideo {
  /// 播放地址
  @JsonKey(name: 'play_addr')
  final DouyinVideoPlayAddr? playAddr;
  
  /// 封面
  @JsonKey(name: 'cover')
  final DouyinVideoCover? cover;
  
  /// 视频比例
  @JsonKey(name: 'ratio')
  final String? ratio;
  
  /// 视频宽度
  @JsonKey(name: 'width')
  final int? width;
  
  /// 视频高度
  @JsonKey(name: 'height')
  final int? height;
  
  /// 构造函数
  DouyinVideo({
    this.playAddr,
    this.cover,
    this.ratio,
    this.width,
    this.height,
  });
  
  /// 从JSON创建
  factory DouyinVideo.fromJson(Map<String, dynamic> json) => 
      _$DouyinVideoFromJson(json);
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinVideoToJson(this);
}

/// 抖音视频播放地址
@JsonSerializable()
class DouyinVideoPlayAddr {
  /// 视频URL列表
  @JsonKey(name: 'url_list')
  final List<String>? urlList;
  
  /// 构造函数
  DouyinVideoPlayAddr({
    this.urlList,
  });
  
  /// 从JSON创建
  factory DouyinVideoPlayAddr.fromJson(Map<String, dynamic> json) => 
      _$DouyinVideoPlayAddrFromJson(json);
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinVideoPlayAddrToJson(this);
}

/// 抖音视频封面
@JsonSerializable()
class DouyinVideoCover {
  /// 封面URL列表
  @JsonKey(name: 'url_list')
  final List<String>? urlList;
  
  /// 构造函数
  DouyinVideoCover({
    this.urlList,
  });
  
  /// 从JSON创建
  factory DouyinVideoCover.fromJson(Map<String, dynamic> json) => 
      _$DouyinVideoCoverFromJson(json);
  
  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinVideoCoverToJson(this);
}

/// 抖音封面数据
@JsonSerializable()
class DouyinCoverData {
  /// 封面
  @JsonKey(name: 'cover')
  final DouyinVideoCover? cover;
  
  /// 动态封面
  @JsonKey(name: 'dynamic_cover')
  final DouyinVideoCover? dynamicCover;

  /// 构造函数
  DouyinCoverData({
    this.cover,
    this.dynamicCover,
  });

  /// 从JSON创建
  factory DouyinCoverData.fromJson(Map<String, dynamic> json) =>
      _$DouyinCoverDataFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DouyinCoverDataToJson(this);
} 