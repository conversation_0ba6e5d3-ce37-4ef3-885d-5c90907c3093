import 'package:json_annotation/json_annotation.dart';

part 'douyin_aweme_detail_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class DouyinAwemeDetailResponse {
  @JsonKey(defaultValue: 0)
  final int code;
  @JsonKey(defaultValue: '')
  final String path;
  @JsonKey(defaultValue: '')
  final String time;
  @JsonKey(defaultValue: '')
  final String msg;
  final DouyinAwemeDetailData data;

  DouyinAwemeDetailResponse({
    required this.code,
    required this.path,
    required this.time,
    required this.msg,
    required this.data,
  });

  factory DouyinAwemeDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$DouyinAwemeDetailResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DouyinAwemeDetailResponseToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class DouyinAwemeDetailData {
  @JsonKey(defaultValue: 0, name: 'status_code')
  final int statusCode;
  @JsonKey(name: 'aweme_detail')
  final AwemeDetail? awemeDetail;

  DouyinAwemeDetailData({
    required this.statusCode,
    this.awemeDetail,
  });

  factory DouyinAwemeDetailData.fromJson(Map<String, dynamic> json) =>
      _$DouyinAwemeDetailDataFromJson(json);

  Map<String, dynamic> toJson() => _$DouyinAwemeDetailDataToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class AwemeDetail {
  final Statistics statistics;
  @JsonKey(name: 'aweme_id', defaultValue: '')
  final String awemeId;
  @JsonKey(defaultValue: '')
  final String desc;
  final Author author;
  final Music music;
  final Video video;
  @JsonKey(name: 'create_time', defaultValue: 0)
  final int createTime;

  AwemeDetail({
    required this.statistics,
    required this.awemeId,
    required this.desc,
    required this.author,
    required this.music,
    required this.video,
    required this.createTime,
  });

  factory AwemeDetail.fromJson(Map<String, dynamic> json) =>
      _$AwemeDetailFromJson(json);

  Map<String, dynamic> toJson() => _$AwemeDetailToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class Statistics {
  @JsonKey(name: 'comment_count', defaultValue: '0')
  final String commentCount;
  @JsonKey(name: 'digg_count', defaultValue: '0')
  final String diggCount;
  @JsonKey(name: 'collect_count', defaultValue: '0')
  final String collectCount;
  @JsonKey(name: 'share_count', defaultValue: '0')
  final String shareCount;
  @JsonKey(name: 'play_count', defaultValue: '0')
  final String playCount;

  Statistics({
    required this.commentCount,
    required this.diggCount,
    required this.collectCount,
    required this.shareCount,
    required this.playCount,
  });

  factory Statistics.fromJson(Map<String, dynamic> json) =>
      _$StatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$StatisticsToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class Author {
  @JsonKey(defaultValue: '')
  final String nickname;
  @JsonKey(name: 'sec_uid', defaultValue: '')
  final String secUid;
  @JsonKey(name: 'avatar_thumb', defaultValue: '')
  final String avatarThumb;
  @JsonKey(defaultValue: '')
  final String signature;
  @JsonKey(name: 'unique_id', defaultValue: '')
  final String uniqueId;
  @JsonKey(defaultValue: '')
  final String uid;

  Author({
    required this.nickname,
    required this.secUid,
    required this.avatarThumb,
    required this.signature,
    required this.uniqueId,
    required this.uid,
  });

  factory Author.fromJson(Map<String, dynamic> json) => _$AuthorFromJson(json);

  Map<String, dynamic> toJson() => _$AuthorToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class Music {
  @JsonKey(defaultValue: '')
  final String id;
  @JsonKey(defaultValue: '')
  final String title;
  @JsonKey(name: 'play_url', defaultValue: '')
  final String playUrl;
  @JsonKey(defaultValue: '')
  final String author;
  @JsonKey(defaultValue: '')
  final String album;
  @JsonKey(defaultValue: 0)
  final int duration;

  Music({
    required this.id,
    required this.title,
    required this.playUrl,
    required this.author,
    required this.album,
    required this.duration,
  });

  factory Music.fromJson(Map<String, dynamic> json) => _$MusicFromJson(json);

  Map<String, dynamic> toJson() => _$MusicToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class Video {
  final VideoCover cover;
  @JsonKey(name: 'dynamic_cover')
  final VideoCover dynamicCover;
  @JsonKey(name: 'play_addr')
  final VideoPlayAddr playAddr;
  @JsonKey(defaultValue: 0)
  final int duration;
  @JsonKey(defaultValue: 0)
  final int width;
  @JsonKey(defaultValue: 0)
  final int height;
  @JsonKey(defaultValue: '')
  final String ratio;

  Video({
    required this.cover,
    required this.dynamicCover,
    required this.playAddr,
    required this.duration,
    required this.width,
    required this.height,
    required this.ratio,
  });

  factory Video.fromJson(Map<String, dynamic> json) => _$VideoFromJson(json);

  Map<String, dynamic> toJson() => _$VideoToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class VideoCover {
  @JsonKey(defaultValue: '')
  final String uri;
  @JsonKey(name: 'url_list', defaultValue: <String>[])
  final List<String> urlList;
  @JsonKey(defaultValue: 0)
  final int width;
  @JsonKey(defaultValue: 0)
  final int height;

  VideoCover({
    required this.uri,
    required this.urlList,
    required this.width,
    required this.height,
  });

  factory VideoCover.fromJson(Map<String, dynamic> json) =>
      _$VideoCoverFromJson(json);

  Map<String, dynamic> toJson() => _$VideoCoverToJson(this);
}

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class VideoPlayAddr {
  @JsonKey(defaultValue: '')
  final String uri;
  @JsonKey(name: 'url_list', defaultValue: <String>[])
  final List<String> urlList;
  @JsonKey(defaultValue: 0)
  final int width;
  @JsonKey(defaultValue: 0)
  final int height;
  @JsonKey(name: 'url_key', defaultValue: '')
  final String urlKey;
  @JsonKey(name: 'data_size', defaultValue: 0)
  final int dataSize;
  @JsonKey(name: 'file_hash', defaultValue: '')
  final String fileHash;

  VideoPlayAddr({
    required this.uri,
    required this.urlList,
    required this.width,
    required this.height,
    required this.urlKey,
    required this.dataSize,
    required this.fileHash,
  });

  factory VideoPlayAddr.fromJson(Map<String, dynamic> json) =>
      _$VideoPlayAddrFromJson(json);

  Map<String, dynamic> toJson() => _$VideoPlayAddrToJson(this);
} 