// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'douyin_hot_feed_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DouyinHotFeedResponse _$DouyinHotFeedResponseFromJson(
  Map<String, dynamic> json,
) => DouyinHotFeedResponse(
  statusCode: (json['status_code'] as num).toInt(),
  statusMsg: json['status_msg'] as String,
  cards:
      (json['cards'] as List<dynamic>)
          .map((e) => DouyinHotFeedCard.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$DouyinHotFeedResponseToJson(
  DouyinHotFeedResponse instance,
) => <String, dynamic>{
  'status_code': instance.statusCode,
  'status_msg': instance.statusMsg,
  'cards': instance.cards,
};

DouyinHotFeedCard _$DouyinHotFeedCardFromJson(Map<String, dynamic> json) =>
    DouyinHotFeedCard(
      type: (json['type'] as num).toInt(),
      aweme:
          json['aweme'] == null
              ? null
              : DouyinHotFeedItem.fromJson(
                json['aweme'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$DouyinHotFeedCardToJson(DouyinHotFeedCard instance) =>
    <String, dynamic>{'type': instance.type, 'aweme': instance.aweme};

DouyinHotFeedItem _$DouyinHotFeedItemFromJson(Map<String, dynamic> json) =>
    DouyinHotFeedItem(
      awemeId: json['aweme_id'] as String,
      desc: json['desc'] as String,
      author:
          json['author'] == null
              ? null
              : DouyinAuthor.fromJson(json['author'] as Map<String, dynamic>),
      statistics:
          json['statistics'] == null
              ? null
              : DouyinStatistics.fromJson(
                json['statistics'] as Map<String, dynamic>,
              ),
      video:
          json['video'] == null
              ? null
              : DouyinVideo.fromJson(json['video'] as Map<String, dynamic>),
      coverData:
          json['cover_data'] == null
              ? null
              : DouyinCoverData.fromJson(
                json['cover_data'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$DouyinHotFeedItemToJson(DouyinHotFeedItem instance) =>
    <String, dynamic>{
      'aweme_id': instance.awemeId,
      'desc': instance.desc,
      'author': instance.author,
      'statistics': instance.statistics,
      'video': instance.video,
      'cover_data': instance.coverData,
    };

DouyinAuthor _$DouyinAuthorFromJson(Map<String, dynamic> json) => DouyinAuthor(
  uid: json['uid'] as String?,
  nickname: json['nickname'] as String?,
  avatarThumb:
      json['avatar_thumb'] == null
          ? null
          : DouyinAvatar.fromJson(json['avatar_thumb'] as Map<String, dynamic>),
);

Map<String, dynamic> _$DouyinAuthorToJson(DouyinAuthor instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'nickname': instance.nickname,
      'avatar_thumb': instance.avatarThumb,
    };

DouyinAvatar _$DouyinAvatarFromJson(Map<String, dynamic> json) => DouyinAvatar(
  urlList:
      (json['url_list'] as List<dynamic>?)?.map((e) => e as String).toList(),
);

Map<String, dynamic> _$DouyinAvatarToJson(DouyinAvatar instance) =>
    <String, dynamic>{'url_list': instance.urlList};

DouyinStatistics _$DouyinStatisticsFromJson(Map<String, dynamic> json) =>
    DouyinStatistics(
      diggCount: (json['digg_count'] as num?)?.toInt(),
      commentCount: (json['comment_count'] as num?)?.toInt(),
      shareCount: (json['share_count'] as num?)?.toInt(),
      playCount: (json['play_count'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DouyinStatisticsToJson(DouyinStatistics instance) =>
    <String, dynamic>{
      'digg_count': instance.diggCount,
      'comment_count': instance.commentCount,
      'share_count': instance.shareCount,
      'play_count': instance.playCount,
    };

DouyinVideo _$DouyinVideoFromJson(Map<String, dynamic> json) => DouyinVideo(
  playAddr:
      json['play_addr'] == null
          ? null
          : DouyinVideoPlayAddr.fromJson(
            json['play_addr'] as Map<String, dynamic>,
          ),
  cover:
      json['cover'] == null
          ? null
          : DouyinVideoCover.fromJson(json['cover'] as Map<String, dynamic>),
  ratio: json['ratio'] as String?,
  width: (json['width'] as num?)?.toInt(),
  height: (json['height'] as num?)?.toInt(),
);

Map<String, dynamic> _$DouyinVideoToJson(DouyinVideo instance) =>
    <String, dynamic>{
      'play_addr': instance.playAddr,
      'cover': instance.cover,
      'ratio': instance.ratio,
      'width': instance.width,
      'height': instance.height,
    };

DouyinVideoPlayAddr _$DouyinVideoPlayAddrFromJson(Map<String, dynamic> json) =>
    DouyinVideoPlayAddr(
      urlList:
          (json['url_list'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
    );

Map<String, dynamic> _$DouyinVideoPlayAddrToJson(
  DouyinVideoPlayAddr instance,
) => <String, dynamic>{'url_list': instance.urlList};

DouyinVideoCover _$DouyinVideoCoverFromJson(Map<String, dynamic> json) =>
    DouyinVideoCover(
      urlList:
          (json['url_list'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
    );

Map<String, dynamic> _$DouyinVideoCoverToJson(DouyinVideoCover instance) =>
    <String, dynamic>{'url_list': instance.urlList};

DouyinCoverData _$DouyinCoverDataFromJson(Map<String, dynamic> json) =>
    DouyinCoverData(
      cover:
          json['cover'] == null
              ? null
              : DouyinVideoCover.fromJson(
                json['cover'] as Map<String, dynamic>,
              ),
      dynamicCover:
          json['dynamic_cover'] == null
              ? null
              : DouyinVideoCover.fromJson(
                json['dynamic_cover'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$DouyinCoverDataToJson(DouyinCoverData instance) =>
    <String, dynamic>{
      'cover': instance.cover,
      'dynamic_cover': instance.dynamicCover,
    };
