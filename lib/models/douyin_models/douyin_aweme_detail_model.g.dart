// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'douyin_aweme_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DouyinAwemeDetailResponse _$DouyinAwemeDetailResponseFromJson(
  Map<String, dynamic> json,
) => DouyinAwemeDetailResponse(
  code: (json['code'] as num?)?.toInt() ?? 0,
  path: json['path'] as String? ?? '',
  time: json['time'] as String? ?? '',
  msg: json['msg'] as String? ?? '',
  data: DouyinAwemeDetailData.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$DouyinAwemeDetailResponseToJson(
  DouyinAwemeDetailResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'path': instance.path,
  'time': instance.time,
  'msg': instance.msg,
  'data': instance.data.toJson(),
};

DouyinAwemeDetailData _$DouyinAwemeDetailDataFromJson(
  Map<String, dynamic> json,
) => DouyinAwemeDetailData(
  statusCode: (json['status_code'] as num?)?.toInt() ?? 0,
  awemeDetail:
      json['aweme_detail'] == null
          ? null
          : AwemeDetail.fromJson(json['aweme_detail'] as Map<String, dynamic>),
);

Map<String, dynamic> _$DouyinAwemeDetailDataToJson(
  DouyinAwemeDetailData instance,
) => <String, dynamic>{
  'status_code': instance.statusCode,
  if (instance.awemeDetail?.toJson() case final value?) 'aweme_detail': value,
};

AwemeDetail _$AwemeDetailFromJson(Map<String, dynamic> json) => AwemeDetail(
  statistics: Statistics.fromJson(json['statistics'] as Map<String, dynamic>),
  awemeId: json['aweme_id'] as String? ?? '',
  desc: json['desc'] as String? ?? '',
  author: Author.fromJson(json['author'] as Map<String, dynamic>),
  music: Music.fromJson(json['music'] as Map<String, dynamic>),
  video: Video.fromJson(json['video'] as Map<String, dynamic>),
  createTime: (json['create_time'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$AwemeDetailToJson(AwemeDetail instance) =>
    <String, dynamic>{
      'statistics': instance.statistics.toJson(),
      'aweme_id': instance.awemeId,
      'desc': instance.desc,
      'author': instance.author.toJson(),
      'music': instance.music.toJson(),
      'video': instance.video.toJson(),
      'create_time': instance.createTime,
    };

Statistics _$StatisticsFromJson(Map<String, dynamic> json) => Statistics(
  commentCount: json['comment_count'] as String? ?? '0',
  diggCount: json['digg_count'] as String? ?? '0',
  collectCount: json['collect_count'] as String? ?? '0',
  shareCount: json['share_count'] as String? ?? '0',
  playCount: json['play_count'] as String? ?? '0',
);

Map<String, dynamic> _$StatisticsToJson(Statistics instance) =>
    <String, dynamic>{
      'comment_count': instance.commentCount,
      'digg_count': instance.diggCount,
      'collect_count': instance.collectCount,
      'share_count': instance.shareCount,
      'play_count': instance.playCount,
    };

Author _$AuthorFromJson(Map<String, dynamic> json) => Author(
  nickname: json['nickname'] as String? ?? '',
  secUid: json['sec_uid'] as String? ?? '',
  avatarThumb: json['avatar_thumb'] as String? ?? '',
  signature: json['signature'] as String? ?? '',
  uniqueId: json['unique_id'] as String? ?? '',
  uid: json['uid'] as String? ?? '',
);

Map<String, dynamic> _$AuthorToJson(Author instance) => <String, dynamic>{
  'nickname': instance.nickname,
  'sec_uid': instance.secUid,
  'avatar_thumb': instance.avatarThumb,
  'signature': instance.signature,
  'unique_id': instance.uniqueId,
  'uid': instance.uid,
};

Music _$MusicFromJson(Map<String, dynamic> json) => Music(
  id: json['id'] as String? ?? '',
  title: json['title'] as String? ?? '',
  playUrl: json['play_url'] as String? ?? '',
  author: json['author'] as String? ?? '',
  album: json['album'] as String? ?? '',
  duration: (json['duration'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$MusicToJson(Music instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'play_url': instance.playUrl,
  'author': instance.author,
  'album': instance.album,
  'duration': instance.duration,
};

Video _$VideoFromJson(Map<String, dynamic> json) => Video(
  cover: VideoCover.fromJson(json['cover'] as Map<String, dynamic>),
  dynamicCover: VideoCover.fromJson(
    json['dynamic_cover'] as Map<String, dynamic>,
  ),
  playAddr: VideoPlayAddr.fromJson(json['play_addr'] as Map<String, dynamic>),
  duration: (json['duration'] as num?)?.toInt() ?? 0,
  width: (json['width'] as num?)?.toInt() ?? 0,
  height: (json['height'] as num?)?.toInt() ?? 0,
  ratio: json['ratio'] as String? ?? '',
);

Map<String, dynamic> _$VideoToJson(Video instance) => <String, dynamic>{
  'cover': instance.cover.toJson(),
  'dynamic_cover': instance.dynamicCover.toJson(),
  'play_addr': instance.playAddr.toJson(),
  'duration': instance.duration,
  'width': instance.width,
  'height': instance.height,
  'ratio': instance.ratio,
};

VideoCover _$VideoCoverFromJson(Map<String, dynamic> json) => VideoCover(
  uri: json['uri'] as String? ?? '',
  urlList:
      (json['url_list'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      [],
  width: (json['width'] as num?)?.toInt() ?? 0,
  height: (json['height'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$VideoCoverToJson(VideoCover instance) =>
    <String, dynamic>{
      'uri': instance.uri,
      'url_list': instance.urlList,
      'width': instance.width,
      'height': instance.height,
    };

VideoPlayAddr _$VideoPlayAddrFromJson(Map<String, dynamic> json) =>
    VideoPlayAddr(
      uri: json['uri'] as String? ?? '',
      urlList:
          (json['url_list'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      width: (json['width'] as num?)?.toInt() ?? 0,
      height: (json['height'] as num?)?.toInt() ?? 0,
      urlKey: json['url_key'] as String? ?? '',
      dataSize: (json['data_size'] as num?)?.toInt() ?? 0,
      fileHash: json['file_hash'] as String? ?? '',
    );

Map<String, dynamic> _$VideoPlayAddrToJson(VideoPlayAddr instance) =>
    <String, dynamic>{
      'uri': instance.uri,
      'url_list': instance.urlList,
      'width': instance.width,
      'height': instance.height,
      'url_key': instance.urlKey,
      'data_size': instance.dataSize,
      'file_hash': instance.fileHash,
    };
