class HotTopicResponse {
  final int code;
  final String msg;
  final List<HotTopicData> data;

  HotTopicResponse({
    required this.code,
    required this.msg,
    required this.data,
  });

  factory HotTopicResponse.fromJson(Map<String, dynamic> json) {
    return HotTopicResponse(
      code: json['code'] as int? ?? 0,
      msg: json['msg'] as String? ?? '',
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => HotTopicData.fromJson(e as Map<String, dynamic>))
              .toList() ?? // Safely handle null list
          [],
    );
  }
}

class HotTopicData {
  final CustomPlatform platform;
  final List<Topic> topics;

  HotTopicData({
    required this.platform,
    required this.topics,
  });

  factory HotTopicData.fromJson(Map<String, dynamic> json) {
    return HotTopicData(
      platform: CustomPlatform.fromJson(json['platform'] as Map<String, dynamic>),
      topics: (json['topics'] as List<dynamic>?)?.map((e) => Topic.fromJson(e as Map<String, dynamic>)).toList() ?? [], // Safely handle null list
    );
  }
}

class CustomPlatform {
  final String id;
  final String name;
  final String type;
  final String description;
  final String icon;
  final int status;
  final int sort;
  final String createTime;
  final String updateTime;
  final String hotinfo;
  final String language;
  final String nameEn;
  final String descriptionEn;

  CustomPlatform({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.icon,
    required this.status,
    required this.sort,
    required this.createTime,
    required this.updateTime,
    required this.hotinfo,
    required this.language,
    required this.nameEn,
    required this.descriptionEn,
  });

  factory CustomPlatform.fromJson(Map<String, dynamic> json) {
    return CustomPlatform(
      id: json['_id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      type: json['type'] as String? ?? '',
      description: json['description'] as String? ?? '',
      icon: json['icon'] as String? ?? '',
      status: json['status'] as int? ?? 0,
      sort: json['sort'] as int? ?? 0,
      createTime: json['createTime'] as String? ?? '',
      updateTime: json['updateTime'] as String? ?? '',
      hotinfo: json['hotinfo'] as String? ?? '',
      language: json['language'] as String? ?? '',
      nameEn: json['name_en'] as String? ?? '',
      descriptionEn: json['description_en'] as String? ?? '',
    );
  }
}

class Topic {
  final String id;
  final String originalId;
  final String title;
  final String hotValue;
  final int rank;
  final int rankChange;
  final List<HotValueHistory> hotValueHistory;
  final List<dynamic> rankHistory; // Empty array in example
  final String fetchTime;
  final String createdAt;
  final String updatedAt;
  final String platName;
  final String platformId;
  final String url;

  Topic({
    required this.id,
    required this.originalId,
    required this.title,
    required this.hotValue,
    required this.rank,
    required this.rankChange,
    required this.hotValueHistory,
    required this.rankHistory,
    required this.fetchTime,
    required this.createdAt,
    required this.updatedAt,
    required this.platName,
    required this.platformId,
    required this.url,
  });

  factory Topic.fromJson(Map<String, dynamic> json) {
    return Topic(
      id: json['_id'] as String? ?? '',
      originalId: json['originalId'] as String? ?? '',
      title: json['title'] as String? ?? '',
      hotValue: json['hotValue'] as String? ?? '',
      rank: json['rank'] as int? ?? 0,
      rankChange: json['rankChange'] as int? ?? 0,
      hotValueHistory: (json['hotValueHistory'] as List<dynamic>?)
          ?.map((e) => HotValueHistory.fromJson(e as Map<String, dynamic>))
          .toList() ?? [], // Safely handle null list
      rankHistory: json['rankHistory'] as List<dynamic>? ?? [], // Safely handle null list
      fetchTime: json['fetchTime'] as String? ?? '',
      createdAt: json['createdAt'] as String? ?? '',
      updatedAt: json['updatedAt'] as String? ?? '',
      platName: json['plat_name'] as String? ?? '',
      platformId: json['platformId'] as String? ?? '',
      url: json['url'] as String? ?? '',
    );
  }
}

class HotValueHistory {
  final double hotValue;
  final String sentence;
  final String updateTime;

  HotValueHistory({
    required this.hotValue,
    required this.sentence,
    required this.updateTime,
  });

  factory HotValueHistory.fromJson(Map<String, dynamic> json) {
    return HotValueHistory(
      hotValue: (json['hotValue'] is int)
          ? (json['hotValue'] as int).toDouble()
          : json['hotValue'] as double? ?? 0.0,
      sentence: json['sentence'] as String? ?? '',
      updateTime: json['updateTime'] as String? ?? '',
    );
  }
}
