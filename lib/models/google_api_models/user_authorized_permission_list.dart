class TokenInfo {
  final String issuedTo;
  final String audience;
  final String userId;
  final String scope;
  final int expiresIn;
  final String email;
  final bool verifiedEmail;
  final String accessType;

  TokenInfo({
    required this.issuedTo,
    required this.audience,
    required this.userId,
    required this.scope,
    required this.expiresIn,
    required this.email,
    required this.verifiedEmail,
    required this.accessType,
  });

  factory TokenInfo.fromJson(Map<String, dynamic> json) {
    return TokenInfo(
      issuedTo: json['issued_to'],
      audience: json['audience'],
      userId: json['user_id'],
      scope: json['scope'],
      expiresIn: json['expires_in'],
      email: json['email'],
      verifiedEmail: json['verified_email'],
      accessType: json['access_type'],
    );
  }
}

class UserInfo {
  final String id;
  final String email;
  final bool verifiedEmail;
  final String picture;

  UserInfo({
    required this.id,
    required this.email,
    required this.verifiedEmail,
    required this.picture,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'],
      email: json['email'],
      verifiedEmail: json['verified_email'],
      picture: json['picture'],
    );
  }
}

class UserAuthorizedPermissionListModel {
  final TokenInfo tokenInfo;
  final UserInfo userInfo;
  final int code;
  final String msg;
  final String url;

  UserAuthorizedPermissionListModel({
    required this.tokenInfo,
    required this.userInfo,
    required this.code,
    required this.msg,
    required this.url,
  });

  factory UserAuthorizedPermissionListModel.fromJson(Map<String, dynamic> json) {
    return UserAuthorizedPermissionListModel(
      tokenInfo: TokenInfo.fromJson(json['data']['tokenInfo']),
      userInfo: UserInfo.fromJson(json['data']['userInfo']),
      code: json['code'],
      msg: json['msg'],
      url: json['url'],
    );
  }
}
