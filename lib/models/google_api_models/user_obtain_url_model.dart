class UserObtainUrlModel {
  final String data;
  final int code;
  final String msg;
  final String url;

  UserObtainUrlModel({
    required this.data,
    required this.code,
    required this.msg,
    required this.url,
  });

  factory UserObtainUrlModel.fromJson(Map<String, dynamic> json) {
    return UserObtainUrlModel(
      data: json['data'] ?? '',
      code: json['code'] ?? 0,
      msg: json['msg'] ?? '',
      url: json['url'] ?? '',
    );
  }
}
