class UserInfoResponseModel {
  final UserData data;
  final int code;
  final String msg;
  final String url;

  UserInfoResponseModel({
    required this.data,
    required this.code,
    required this.msg,
    required this.url,
  });

  factory UserInfoResponseModel.fromJson(Map<String, dynamic> json) {
    return UserInfoResponseModel(
      data: UserData.fromJson(json['data']),
      code: json['code'] ?? 0,
      msg: json['msg'] ?? '',
      url: json['url'] ?? '',
    );
  }
}

class UserData {
  final String sub;
  final String picture;
  final String email;
  final bool emailVerified;

  UserData({
    required this.sub,
    required this.picture,
    required this.email,
    required this.emailVerified,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      sub: json['sub'] ?? '',
      picture: json['picture'] ?? '',
      email: json['email'] ?? '',
      emailVerified: json['email_verified'] ?? false,
    );
  }
}
