class TopicLabel {
  final String name;

  TopicLabel({required this.name});

  factory TopicLabel.fromJson(Map<String, dynamic> json) {
    return TopicLabel(name: json as String);
  }
}

class TopicTimeType {
  final String name;

  TopicTimeType({required this.name});

  factory TopicTimeType.fromJson(Map<String, dynamic> json) {
    return TopicTimeType(name: json as String);
  }
}

class Topic {
  final String id;
  final String title;
  final String? description;
  final String msgType;
  final String category;
  final String? subCategory;
  final String originalId;
  final String author;
  final String avatar;
  final String cover;
  final String authorId;
  final int? fans;
  final List<String> topics;
  final int rank;
  final int shareCount;
  final int likeCount;
  final int? watchingCount;
  final int readCount;
  final String publishTime;
  final String platformId;
  final String updateTime;
  final String label;
  final String secondLabel;
  final String timeType;
  final String platformName;
  final String? url;
  final String? type;
  final String? authorUrl;

  Topic({
    required this.id,
    required this.title,
    required this.msgType, required this.category, required this.originalId, required this.author, required this.avatar, required this.cover, required this.authorId, required this.topics, required this.rank, required this.shareCount, required this.likeCount, required this.readCount, required this.publishTime, required this.platformId, required this.updateTime, required this.label, required this.secondLabel, required this.timeType, required this.platformName, this.description,
    this.subCategory,
    this.fans,
    this.watchingCount,
    this.url,
    this.type,
    this.authorUrl,
  });

  factory Topic.fromJson(Map<String, dynamic> json) {
    return Topic(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      msgType: json['msgType'] ?? '',
      category: json['category'] ?? '',
      subCategory: json['subCategory'],
      originalId: json['originalId'] ?? '',
      author: json['author'] ?? '',
      avatar: json['avatar'] ?? '',
      cover: json['cover'] ?? '',
      authorId: json['authorId'] ?? '',
      fans: json['fans'],
      topics:
          (json['topics'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      rank: json['rank'] ?? 0,
      shareCount: json['shareCount'] ?? 0,
      likeCount: json['likeCount'] ?? 0,
      watchingCount: json['watchingCount'],
      readCount: json['readCount'] ?? 0,
      publishTime: json['publishTime'] ?? '',
      platformId: json['platformId'] ?? '',
      updateTime: json['update_time'] ?? json['updateTime'] ?? '',
      label: json['label'] ?? '',
      secondLabel: json['secondLabel'] ?? '',
      timeType: json['timeType'] ?? '',
      platformName: json['platform_name'] ?? '',
      url: json['url'],
      type: json['type'],
      authorUrl: json['authorUrl'],
    );
  }
}

class TopicListResponse {
  final List<Topic> items;
  final MetaM meta;

  TopicListResponse({required this.items, required this.meta});

  factory TopicListResponse.fromJson(Map<String, dynamic> json) {
    return TopicListResponse(
      items:
          (json['data']['items'] as List<dynamic>?)
              ?.map((item) => Topic.fromJson(item))
              .toList() ??
          [],
      meta: MetaM.fromJson(json['data']['meta'] ?? {}),
    );
  }
}

class TopicMsgType {
  final String name;

  TopicMsgType({required this.name});

  factory TopicMsgType.fromJson(Map<String, dynamic> json) {
    return TopicMsgType(name: json as String);
  }
}

class MetaM {
  final int itemCount;
  final int totalItems;
  final int itemsPerPage;
  final int totalPages;
  final int currentPage;

  MetaM({
    required this.itemCount,
    required this.totalItems,
    required this.itemsPerPage,
    required this.totalPages,
    required this.currentPage,
  });

  factory MetaM.fromJson(Map<String, dynamic> json) {
    return MetaM(
      itemCount: json['itemCount'] ?? 0,
      totalItems: json['totalItems'] ?? 0,
      itemsPerPage: json['itemsPerPage'] ?? 0,
      totalPages: json['totalPages'] ?? 0,
      currentPage: json['currentPage'] ?? 0,
    );
  }
}
