// Model for single ranking detail API
class RankingDetail {
  String id;
  String name;
  String description;
  String icon;
  String platformId;
  String rules;
  String updateFrequency;
  String createTime;
  String updateTime;
  Platforms platform;

  RankingDetail({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.platformId,
    required this.rules,
    required this.updateFrequency,
    required this.createTime,
    required this.updateTime,
    required this.platform,
  });

  factory RankingDetail.fromJson(Map<String, dynamic> json) {
    return RankingDetail(
      id: json['_id'] ?? '',
      name: json['name'] ?? 'No name',
      description: json['description'] ?? 'No description',
      icon: json['icon'] ?? '',
      platformId: json['platformId'] ?? '',
      rules: json['rules'] ?? '',
      updateFrequency: json['updateFrequency'] ?? 'Unknown',
      createTime: json['createTime'] ?? '',
      updateTime: json['updateTime'] ?? '',
      platform: Platforms.fromJson(json['platform'] ?? {}),
    );
  }
}

// Model for platform details
class Platforms {
  String id;
  String name;
  String description;
  String icon;
  int status;
  int sort;
  String type;

  Platforms({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.status,
    required this.sort,
    required this.type,
  });

  factory Platforms.fromJson(Map<String, dynamic> json) {
    return Platforms(
      id: json['_id'],
      name: json['name'],
      description: json['description'],
      icon: json['icon'],
      status: json['status'],
      sort: json['sort'],
      type: json['type'],
    );
  }
}

// Model for ranking platform API (multiple rankings)
class RankingPlatform {
  String id;
  String name;
  String description;
  String icon;
  String platformId;
  String rules;
  String updateFrequency;
  String createTime;
  String updateTime;
  Platforms platform;

  RankingPlatform({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.platformId,
    required this.rules,
    required this.updateFrequency,
    required this.createTime,
    required this.updateTime,
    required this.platform,
  });

  factory RankingPlatform.fromJson(Map<String, dynamic> json) {
    return RankingPlatform(
      id: json['_id'],
      name: json['name'],
      description: json['description'],
      icon: json['icon'],
      platformId: json['platformId'],
      rules: json['rules'],
      updateFrequency: json['updateFrequency'],
      createTime: json['createTime'],
      updateTime: json['updateTime'],
      platform: Platforms.fromJson(json['platform']),
    );
  }
}

// Model for ranking labels API (list of categories)
class RankingLabels {
  List<String> categories;

  RankingLabels({required this.categories});

  factory RankingLabels.fromJson(Map<String, dynamic> json) {
    var categoriesList = List<String>.from(json['data']);
    return RankingLabels(categories: categoriesList);
  }
}

// Model for ranking hot info dates API
class RankingHotInfo {
  String showDate;
  String queryDate;
  String value;
  String spiderTime;
  String rankingId;

  RankingHotInfo({
    required this.showDate,
    required this.queryDate,
    required this.value,
    required this.spiderTime,
    required this.rankingId,
  });

  factory RankingHotInfo.fromJson(Map<String, dynamic> json) {
    return RankingHotInfo(
      showDate: json['showDate'],
      queryDate: json['queryDate'],
      value: json['value'],
      spiderTime: json['spiderTime'],
      rankingId: json['rankingId'],
    );
  }
}
