import 'dart:convert';

/// JSON工具类
class JsonUtil {
  /// 格式化JSON字符串
  static String formatJson(String json) {
    try {
      final object = jsonDecode(json);
      return const JsonEncoder.withIndent('  ').convert(object);
    } catch (e) {
      return json;
    }
  }
  
  /// 将JSON字符串转换为Map
  static Map<String, dynamic>? jsonToMap(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) {
      return null;
    }
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }
  
  /// 将Map转换为JSON字符串
  static String? mapToJson(Map<String, dynamic>? map) {
    if (map == null || map.isEmpty) {
      return null;
    }
    
    try {
      return jsonEncode(map);
    } catch (e) {
      return null;
    }
  }
}

/// 将Map转换为查询字符串
String jsonToQueryString(Map<String, dynamic> json) {
  final queryList = <String>[];
  
  void addToQueryList(String key, dynamic value) {
    if (value != null) {
      final encodedKey = Uri.encodeComponent(key);
      final encodedValue = Uri.encodeComponent(value.toString());
      queryList.add('$encodedKey=$encodedValue');
    }
  }
  
  json.forEach((key, value) {
    if (value != null) {
      if (value is List) {
        for (final item in value) {
          addToQueryList(key, item);
        }
      } else {
        addToQueryList(key, value);
      }
    }
  });
  
  return queryList.join('&');
}

/// 从查询字符串解析参数
Map<String, dynamic> queryStringToJson(String queryString) {
  final result = <String, dynamic>{};
  
  if (queryString.isEmpty) {
    return result;
  }
  
  // 移除可能的开头的?
  final normalizedQueryString = queryString.startsWith('?') 
      ? queryString.substring(1) 
      : queryString;
  
  final pairs = normalizedQueryString.split('&');
  
  for (final pair in pairs) {
    final parts = pair.split('=');
    if (parts.length >= 2) {
      final key = Uri.decodeComponent(parts[0]);
      final value = Uri.decodeComponent(parts[1]);
      
      // 处理数组参数
      if (key.endsWith('[]')) {
        final arrayKey = key.substring(0, key.length - 2);
        if (result.containsKey(arrayKey)) {
          if (result[arrayKey] is List) {
            (result[arrayKey] as List).add(value);
          } else {
            result[arrayKey] = [result[arrayKey], value];
          }
        } else {
          result[arrayKey] = [value];
        }
      } else {
        result[key] = value;
      }
    }
  }
  
  return result;
} 