import 'package:aitoearn_app/res/app_colors.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';

/// 全屏图片预览组件（支持手势缩放 + hero 动画 + 滑动关闭）
class ImagePreviewViewer extends StatefulWidget {
  final List<String> imageUrls;
  final int initialIndex;
  final List<String>? heroTags;
  final void Function(int index)? onLongPress;
  final void Function(int index)? onTap;
  final Widget Function(BuildContext context, int index)? topOverlayBuilder;

  const ImagePreviewViewer({
    required this.imageUrls, super.key,
    this.initialIndex = 0,
    this.heroTags,
    this.onLongPress,
    this.onTap,
    this.topOverlayBuilder,
  });

  static void show({
    required BuildContext context,
    required List<String> imageUrls,
    int initialIndex = 0,
    List<String>? heroTags,
    void Function(int index)? onLongPress,
    void Function(int index)? onTap,
    Widget Function(BuildContext, int)? topOverlayBuilder,
  }) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        transitionDuration: const Duration(milliseconds: 250),
        reverseTransitionDuration: const Duration(milliseconds: 200),
        pageBuilder: (_, __, ___) => ImagePreviewViewer(
          imageUrls: imageUrls,
          initialIndex: initialIndex,
          heroTags: heroTags,
          onLongPress: onLongPress,
          onTap: onTap,
          topOverlayBuilder: topOverlayBuilder,
        ),
        transitionsBuilder: (_, animation, __, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }

  @override
  State<ImagePreviewViewer> createState() => _ImagePreviewViewerState();
}

class _ImagePreviewViewerState extends State<ImagePreviewViewer> {
  late final ExtendedPageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    _currentIndex = widget.initialIndex;
    _pageController = ExtendedPageController(initialPage: _currentIndex);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ExtendedImageSlidePage(
      slideType: SlideType.onlyImage,
      slidePageBackgroundHandler: (offset, pageSize) {
        final opacity = 1.0 - (offset.dy.abs() / pageSize.height).clamp(0.0, 1.0);
        return AppColors.mediaBgFrame.withOpacity(opacity);
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            ExtendedImageGesturePageView.builder(
              controller: _pageController,
              itemCount: widget.imageUrls.length,
              onPageChanged: (index) => setState(() => _currentIndex = index),
              itemBuilder: (ctx, index) {
                final url = widget.imageUrls[index];
                final tag = widget.heroTags != null && widget.heroTags!.length > index
                    ? widget.heroTags![index]
                    : url;

                final image = ExtendedImage.network(
                  url,
                  mode: ExtendedImageMode.gesture,
                  cache: true,
                  enableLoadState: true,
                  initGestureConfigHandler: (_) => GestureConfig(
                    minScale: 1.0,
                    maxScale: 3.0,
                    initialScale: 1.0,
                    inPageView: true,
                    initialAlignment: InitialAlignment.center,
                  ),
                  loadStateChanged: (state) {
                    if (state.extendedImageLoadState == LoadState.loading) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (state.extendedImageLoadState == LoadState.failed) {
                      return const Center(child: Icon(Icons.broken_image, color: Colors.white));
                    }
                    return GestureDetector(
                      onTap: () {
                        if (widget.onTap != null) {
                          widget.onTap!(index);
                        } else {
                          Navigator.of(context).pop();
                        }
                      },
                      onLongPress: () => widget.onLongPress?.call(index),
                      child: state.completedWidget,
                    );
                  },
                );

                return Hero(
                  tag: tag,
                  child: image,
                );
              },
            ),

            // 顶部操作栏
            if (widget.topOverlayBuilder != null)
              Positioned(
                top: MediaQuery.of(context).padding.top + 8,
                left: 0,
                right: 0,
                child: widget.topOverlayBuilder!(context, _currentIndex),
              )
            else
              Positioned(
                top: MediaQuery.of(context).padding.top + 6,
                left: 6,
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),

            // 底部指示条（紧贴底部，居中对齐）
            Positioned(
              bottom: MediaQuery.of(context).padding.bottom + 12,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(widget.imageUrls.length, (index) {
                  final isActive = index == _currentIndex;
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 250),
                    margin: const EdgeInsets.symmetric(horizontal: 3),
                    height: 4,
                    width: isActive ? 16 : 4,
                    decoration: BoxDecoration(
                      color: isActive ? Colors.white : Colors.white54,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  );
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
