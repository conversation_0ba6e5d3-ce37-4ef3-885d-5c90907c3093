import 'package:aitoearn_app/config/app_config.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/store/user/user_store_service.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart' as getx;

class DioUtil {
  late final Dio dio;

  DioUtil(String baseUrl) {
    dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 15),
        receiveTimeout: const Duration(seconds: 15),
      ),
    );

    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          LoggerUtil.i('请求：uri=${options.uri}，data=${options.data}');
          // true=需要token，false=不需要token，默认为需要
          if (options.extra['isToken'] != false) {
            // 添加token
            var token = getx.Get.find<UserStoreService>().token;
            // var testToken =
            //     'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJtYWlsIjoiNDk2OTM3NjY4QHFxLmNvbSIsImlkIjoiNjg1YTEzNzA5ZGI1MDRjMDNlMWI3Y2FlIiwibmFtZSI6IueUqOaIt19nWmJESmJqRCIsImlhdCI6MTc1MDczMzY4MH0.slKvQ4_0ZWUEkQ9XQdOyfMRKFh7o4ArCkJcqYoJHy8o';
            options.headers['Authorization'] = 'Bearer $token';
          }
          return handler.next(options);
        },
        onResponse: (response, handler) {
          LoggerUtil.i('请求响应：${response = response}');
          return handler.next(response);
        },
        onError: (DioException e, handler) {
          return handler.next(e);
        },
      ),
    );
  }

  /// 发起 http 请求
  Future<Response<T>?> request<T>(
    String url, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    Options? options,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      options ??= Options(method: 'GET');

      var req = await dio.request<T>(
        url,
        data: data,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        options: options,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      req.statusCode = req.statusCode == 201 ? 200 : req.statusCode;
      return req;
    } on DioException catch (e) {
      LoggerUtil.e(e.toString());
      LoggerUtil.e(e.response.toString());
      if (e.response == null) {
        return null;
      }
      return e.response as Response<T>;
    }
  }
}

class DioUtils {
  static DioUtil defaultClient = DioUtil(AppConfig.appHostApi);

  /// 无 base URL
  static DioUtil client = DioUtil('');
}
