import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

/// 图片处理工具类
class ImageUtils {
  /// 缩放图片，最大边不超过1024
  static Future<File> scaleImage(String imagePath, {int maxDimension = 200}) async {
    final bytes = await File(imagePath).readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('无法解码图片');
    }

    // 计算缩放比例
    double scale = 1.0;
    if (image.width > image.height && image.width > maxDimension) {
      scale = maxDimension / image.width;
    } else if (image.height > maxDimension) {
      scale = maxDimension / image.height;
    }

    // 如果不需要缩放，直接返回原图
    if (scale == 1.0) {
      return File(imagePath);
    }

    // 缩放图片
    final scaledImage = img.copyResize(
      image,
      width: (image.width * scale).round(),
      height: (image.height * scale).round(),
    );

    // 保存到临时文件
    final tempDir = await getTemporaryDirectory();
    final tempFile = File('${tempDir.path}/scaled_${DateTime.now().millisecondsSinceEpoch}.jpg');
    await tempFile.writeAsBytes(img.encodeJpg(scaledImage, quality: 90));

    return tempFile;
  }

  /// 将图片文件转换为base64字符串
  static Future<String> imageToBase64(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    return base64Encode(bytes);
  }

  /// 处理编辑图片：缩放并转换为base64
  static Future<String?> processImageForEdit(String imagePath) async {
    try {
      final scaledImage = await scaleImage(imagePath);
      return await imageToBase64(scaledImage);
    } catch (e) {
      print('图片处理失败: $e');
      return null;
    }
  }
}