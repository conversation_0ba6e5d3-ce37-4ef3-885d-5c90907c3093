import 'dart:convert';
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/utils/dialog/models/form_item_model.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:aitoearn_app/widgets/input_box.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 表单对话框辅助工具类
abstract class FormDialogHelper {
  /// 显示表单对话框
  /// [formItems] - 表单元素列表
  /// 返回值: Map<String, dynamic> - 表单提交的结果
  static Future<Map<String, dynamic>?> showFormDialog({
    required String title,
    required List<FormItem> formItems,
    String? confirmText,
    String? cancelText,
    bool barrierDismissible = true,
  }) async {
    // 设置默认值
    confirmText ??= 'form_dialog.confirm'.tr;
    cancelText ??= 'form_dialog.cancel'.tr;
    
    // 创建表单控制器
    final Map<String, TextEditingController> textControllers = {};
    final Map<String, String?> dropdownValues = {};

    // 必填字段key
    final List<String> requiredKeys = [];

    // 初始化控制器
    for (final item in formItems) {
      if (item is InputFormItem) {
        if (item.required) {
          requiredKeys.add(item.name);
        }
        textControllers[item.name] = TextEditingController(text: item.value);
      } else if (item is DropdownFormItem) {
        dropdownValues[item.name] = item.options.isNotEmpty ? item.options.first : null;
      }
    }

    // 显示对话框
    final bool? confirmed = await Get.dialog<bool>(
      PopScope(
        canPop: barrierDismissible,
        child: AlertDialog(
            insetPadding: const EdgeInsets.symmetric(horizontal: 16.0),
          shape: RoundedRectangleBorder(
            side: const BorderSide(color: AppColors.dividerColor, width: 1),
            borderRadius: BorderRadius.circular(30),
          ),
          backgroundColor: AppColors.cardColor,
          titlePadding: const EdgeInsets.only(top: 20, bottom: 10, left: 20, right: 20),
          title: Center(
            child: Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
            ),
          ),
          content: ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 350, minWidth: 320),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: _buildFormItems(
                  formItems,
                  textControllers,
                  dropdownValues,
                      (String name, String? value) => dropdownValues[name] = value,
                ),
              ),
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceAround,
          actions: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back(result: false);
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                      foregroundColor: AppColors.textColor,
                      backgroundColor: Colors.white,
                      side: const BorderSide(color: AppColors.textColor, width: 1),
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    child: Text(
                      cancelText,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      var closeIndex = true;
                      textControllers.forEach((key, controller) {
                        if (requiredKeys.contains(key) && controller.text.isEmpty) {
                          showError('form_dialog.field_required'.trParams({'field': key}));
                          closeIndex = false;
                          return;
                        }
                      });
                      if (closeIndex) {
                        Get.back(result: true);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
                      foregroundColor: Colors.white,
                      backgroundColor: AppColors.primaryColor,
                      side: const BorderSide(color: AppColors.primaryColor, width: 1),
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    child: Text(
                      confirmText,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      barrierDismissible: barrierDismissible,
    );

    // 如果用户确认，则收集并返回表单数据
    if (confirmed == true) {
      final Map<String, dynamic> result = {};
      // 收集输入框数据
      textControllers.forEach((key, controller) {
        result[key] = controller.text;
      });
      // 收集下拉框数据
      dropdownValues.forEach((key, value) {
        result[key] = value;
      });
      return result;
    }
    return null;
  }

  /// 构建表单元素Widget
  static List<Widget> _buildFormItems(
    List<FormItem> formItems,
    Map<String, TextEditingController> textControllers,
    Map<String, String?> dropdownValues,
    Function(String, String?) onDropdownChanged,
  ) {
    final List<Widget> widgets = [];

    for (final item in formItems) {
      widgets.add(Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  item.name,
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                if (item is InputFormItem && item.required)
                  const Text(
                    ' *',
                    style: TextStyle(color: Colors.red, fontSize: 14),
                  )
              ],
            ),
            const SizedBox(height: 8),
            if (item is InputFormItem)
              CustomInputBox(
                controller: textControllers[item.name]!,
                hintText: item.hint,
              )
            else if (item is DropdownFormItem)
              StatefulBuilder(
                builder: (context, setState) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: AppColors.cardColor,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: AppColors.dividerColor, width: 1),
                    ),
                    child: DropdownButton<String>(
                      value: dropdownValues[item.name],
                      isExpanded: true,
                      underline: Container(), // 移除下划线
                      items: item.options
                          .map((option) => DropdownMenuItem(
                                value: option,
                                child: Text(option),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          dropdownValues[item.name] = value;
                        });
                      },
                    ),
                  );
                },
              ),
          ],
        ),
      ));
    }

    return widgets;
  }
}