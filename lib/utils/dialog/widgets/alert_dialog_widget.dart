import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';

class AlertDialogWidget extends StatelessWidget {
  final String title;
  final String? content;
  final Widget? contentWidget;
  final String confirmText;
  final VoidCallback? onConfirm;
  final String cancelText;
  final VoidCallback? onCancel;
  final bool barrierDismissible;

  const AlertDialogWidget({
    required this.title,
    super.key,
    this.content,
    this.contentWidget,
    this.confirmText = '确定',
    this.onConfirm,
    this.cancelText = '取消',
    this.onCancel,
    this.barrierDismissible = false,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: barrierDismissible,
      child: AlertDialog(
        shape: RoundedRectangleBorder(
          side: const BorderSide(color: AppColors.dividerColor, width: 1),
          borderRadius: BorderRadius.circular(30),
        ),
        backgroundColor: AppColors.cardColor,
        titlePadding: EdgeInsets.only(
          top: (content == null && contentWidget == null) ? 40 : 30,
          bottom: (content == null && contentWidget == null) ? 30 : 0,
          left: 20,
          right: 20,
        ),
        title: Center(
          child: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
          ),
        ),
        content: _buildContent(),
        actionsAlignment: MainAxisAlignment.spaceAround,
        actions: [
          Row(
            children: [
              if (onCancel != null)
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      onCancel?.call();
                    },
                    style: ElevatedButton.styleFrom(
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(
                        vertical: 5,
                        horizontal: 8,
                      ),
                      foregroundColor: AppColors.textColor,
                      backgroundColor: Colors.white,
                      side: const BorderSide(
                        color: AppColors.textColor,
                        width: 1,
                      ),
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    child: Text(
                      cancelText,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              if (onCancel != null) const SizedBox(width: 10),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    onConfirm?.call();
                  },
                  style: ElevatedButton.styleFrom(
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(
                      vertical: 5,
                      horizontal: 8,
                    ),
                    foregroundColor: Colors.white,
                    backgroundColor: AppColors.primaryColor,
                    side: const BorderSide(
                      color: AppColors.primaryColor,
                      width: 1,
                    ),
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: Text(
                    confirmText,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget? _buildContent() {
    if (contentWidget != null) {
      return ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 350),
        child: SingleChildScrollView(child: contentWidget),
      );
    } else if (content != null) {
      return ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 350),
        child: SingleChildScrollView(
          child: Center(
            child: content!.toNormalText(
              fontSize: Dimens.font_sp14,
              color: AppColors.textColor,
            ),
          ),
        ),
      );
    } else {
      return null;
    }
  }
}
