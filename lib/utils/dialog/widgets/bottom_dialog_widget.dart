// 自定义底部弹窗的选项模型
import 'package:aitoearn_app/res/app_colors.dart';
import 'package:flutter/material.dart';

class BottomSheetContent extends StatelessWidget {
  final List<MenuItem> items;
  final VoidCallback? cancelCallBack;

  const BottomSheetContent({
    required this.items, super.key,
    this.cancelCallBack,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      padding: const EdgeInsets.only(top: 5, bottom: 5),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 选项列表
          ListView.separated(
            shrinkWrap: true,
            // 根据内容调整高度
            physics: const NeverScrollableScrollPhysics(),
            // 禁止滚动
            itemCount: items.length,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(
                  items[index].text,
                  style: const TextStyle(
                      color: AppColors.textColor,
                      fontWeight: FontWeight.w500),
                  textAlign: TextAlign.center,
                ),
                onTap: () {
                  Navigator.pop(context); // 关闭弹窗
                  items[index].onTap?.call(); // 执行点击回调
                },
              );
            },
            separatorBuilder: (context, index) {
              return const Divider(
                  height: 1, color: AppColors.dividerColor); // 细线
            },
          ),
          // 取消按钮
          Container(height: 10, color: AppColors.dividerColor),
          ListTile(
            title: const Text(
              '取消',
              style: TextStyle(color: AppColors.errorColor),
              textAlign: TextAlign.center,
            ),
            onTap: () {
              Navigator.pop(context); // 关闭弹窗
              cancelCallBack?.call();
            },
          ),
          Container(
            color: Colors.white,
            height: MediaQuery.of(context).padding.bottom/2,
            width: double.infinity,
          )
        ],
      ),
    );
  }
}

class MenuItem {
  final String text; // 选项文本
  final VoidCallback? onTap; // 点击回调

  MenuItem({
    required this.text,
    this.onTap,
  });
}
