import 'dart:async';

import 'package:aitoearn_app/utils/dialog/widgets/alert_dialog_widget.dart';
import 'package:aitoearn_app/utils/dialog/widgets/bottom_dialog_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

// 弹窗辅助工具类
abstract class DialogHelper {
  // 显示基础AlertDialog
  static Future showAlertDialog({
    required String title,
    String? content,
    Widget? contentWidget,
    String confirmText = '确定',
    VoidCallback? onConfirm,
    String cancelText = '取消',
    VoidCallback? onCancel,
    bool barrierDismissible = true,
  }) {
    return Get.dialog(
      AlertDialogWidget(
        title: title,
        content: content,
        contentWidget: contentWidget,
        confirmText: confirmText,
        onConfirm: onConfirm,
        cancelText: cancelText,
        onCancel: onCancel,
        barrierDismissible: barrierDismissible,
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  // 显示简单对话框，返回用户的选择（true/false）
  static Future<bool?> showSimpleDialog({
    String? content,
    Widget? contentWidget,
    String? title,
    String? confirmText,
    String? cancelText,
    bool barrierDismissible = true,
  }) async {
    title ??= 'notice'.tr;
    confirmText ??= 'confirm'.tr;
    cancelText ??= 'cancel'.tr;
    return await Get.dialog<bool>(
      AlertDialogWidget(
        title: title,
        content: content,
        contentWidget: contentWidget,
        confirmText: confirmText,
        cancelText: cancelText,
        onConfirm: () {
          Get.back(result: true);
        },
        onCancel: () {
          Get.back(result: false);
        },
        barrierDismissible: barrierDismissible,
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  // 显示底部弹出多个选项的Dialog
  static void showBottomSheetDialog({
    required List<MenuItem> items,
    VoidCallback? cancelCallBack,
  }) {
    showModalBottomSheet(
      context: Get.context!,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return BottomSheetContent(items: items, cancelCallBack: cancelCallBack);
      },
    );
  }
}
