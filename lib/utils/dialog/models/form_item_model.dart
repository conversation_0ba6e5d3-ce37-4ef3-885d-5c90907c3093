/// 表单元素类型枚举
enum FormItemType {
  input,
  dropdown,
}

/// 表单元素模型基类
abstract class FormItem {
  final String name;
  final FormItemType type;

  FormItem({
    required this.name,
    required this.type,
  });
}

/// 输入框表单元素
class InputFormItem extends FormItem {
  final String hint;
  final String value;
  final int maxLength;
  final bool required;

  InputFormItem({
    required super.name,
    required this.hint,
    this.value = '',
    this.maxLength = -1,
    this.required = false,
  }) : super(type: FormItemType.input);
}

/// 下拉框表单元素
class DropdownFormItem extends FormItem {
  final List<String> options;

  DropdownFormItem({
    required super.name,
    required this.options,
  }) : super(type: FormItemType.dropdown);
}