import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';

class UniversalBottomSheet {
  /// 显示通用底部弹出框
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    // 弹出框标题
    String? title,
    // 是否可滚动控制，设为true时可超过屏幕高度
    bool isScrollControlled = true,
    // 是否可拖拽关闭
    bool enableDrag = true,
    // 是否可点击外部关闭
    bool isDismissible = true,
    // 最大高度，默认为屏幕高度的1/3
    double? maxHeight,
    // 内容区域内边距
    EdgeInsetsGeometry? padding,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            width: double.infinity,
            height: maxHeight ?? MediaQuery.of(context).size.height / 3,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 拖拽指示器
                if (enableDrag)
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    width: 36,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                // 标题栏
                if (title != null)
                  Container(
                    padding: const EdgeInsets.all(10),
                    child: Center(
                      child: title.toNormalText(
                        fontSize: Dimens.font_sp16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                // 内容区域
                Expanded(
                  child: Container(
                    padding: padding ?? const EdgeInsets.all(15),
                    child: child,
                  ),
                ),
              ],
            ),
          ),
    );
  }
}
