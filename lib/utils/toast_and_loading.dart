import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

void configToastAndLoading() {
  // 配置 BotToast 默认样式
  BotToast.defaultOption.customLoading =
      BotToast.defaultOption.customLoading
        ..clickClose = true
        ..allowClick = false
        ..backButtonBehavior = BackButtonBehavior.close
        ..ignoreContentClick = false
        ..animationDuration = const Duration(milliseconds: 250)
        ..animationReverseDuration = const Duration(milliseconds: 250)
        ..backgroundColor = Colors.transparent
        ..align = Alignment.center;

  // 配置 BotToast 文本样式
  BotToast.defaultOption.customText =
      BotToast.defaultOption.customText
        ..align = const Alignment(0, -0.95); // 设置 Toast 默认靠近顶部展示 顶部偏下一点的位置
}

void showToast(String text, {Duration? duration}) {
  showInfo(text, duration: duration);
}

void showSuccess(String text, {Duration? duration}) {
  BotToast.showCustomText(
    toastBuilder:
        (cancelFunc) => _buildToastWidget(
          text,
          Icons.check_circle_outline,
          backgroundColor: AppColors.successBgColor,
          iconColor: AppColors.successColor,
        ),
    duration: duration ?? const Duration(milliseconds: 2500),
  );
}

void showError(String text, {Duration? duration}) {
  BotToast.showCustomText(
    toastBuilder:
        (cancelFunc) => _buildToastWidget(
          text,
          Icons.cancel_outlined,
          backgroundColor: AppColors.errorBgColor,
          iconColor: AppColors.errorColor,
        ),
    duration: duration ?? const Duration(milliseconds: 2500),
  );
}

void showWarning(String text, {Duration? duration}) {
  BotToast.showCustomText(
    toastBuilder:
        (cancelFunc) => _buildToastWidget(
          text,
          Icons.warning_amber_outlined,
          backgroundColor: AppColors.warningBgColor,
          iconColor: AppColors.warningColor,
        ),
    duration: duration ?? const Duration(milliseconds: 2500),
  );
}

void showInfo(String text, {Duration? duration}) {
  BotToast.showCustomText(
    toastBuilder: (cancelFunc) => _buildToastWidget(text, null),
    duration: duration ?? const Duration(milliseconds: 2500),
  );
}

CancelFunc showProgress({
  String? text,
  bool cancelable = true,
  String? cancelButton,
}) {
  return BotToast.showCustomLoading(
    backgroundColor: const Color(0x33666666),
    animationDuration: const Duration(milliseconds: 250),
    animationReverseDuration: const Duration(milliseconds: 150),
    toastBuilder: (cancel) {
      return _CustomLoadWidget(
        text: text ?? 'loading'.tr,
        cancel: cancel,
        cancelButton: cancelButton,
      );
    },
    allowClick: cancelable,
    clickClose: cancelable,
    backButtonBehavior:
        cancelable ? BackButtonBehavior.close : BackButtonBehavior.ignore,
  );
}

dismissAllProgress() {
  BotToast.closeAllLoading();
}

Widget _buildToastWidget(
  String text,
  IconData? icon, {
  Color? backgroundColor,
  Color? iconColor,
}) {
  return Container(
    constraints: const BoxConstraints(minWidth: 100),
    decoration:
        backgroundColor != null
            ? BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: iconColor ?? const Color(0xFF1A1A1A),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF090300).withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 0),
                ),
              ],
            )
            : BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFFF3DDFD),
                  Color(0xFFEAD8FB),
                  Color(0xFFD8E0FB),
                ],
              ),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: iconColor ?? const Color(0xFF1A1A1A),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF090300).withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (icon != null) ...[
          Icon(icon, color: iconColor ?? const Color(0xFF1A1A1A), size: 20),
          const SizedBox(width: 4, height: 4),
        ],
        ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 280),
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1A1A1A),
            ),
          ),
        ),
        if (icon != null) ...[const SizedBox(width: 4, height: 4)],
      ],
    ),
  );
}

class _CustomLoadWidget extends StatelessWidget {
  final String text;
  final CancelFunc cancel;
  final String? cancelButton;

  const _CustomLoadWidget({
    required this.text,
    required this.cancel,
    this.cancelButton,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: cancelButton != null ? 120 : 100,
      height: cancelButton != null ? 120 : 100,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: AppColors.primaryColor, width: 0.5),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withOpacity(0.2),
            blurRadius: 5,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            const SizedBox(width: 0, height: 10),
            const SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(),
            ),
            Gaps.vGap10,
            Text(
              text,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textHintColor,
              ),
            ),
            if (cancelButton != null) ...[
              Gaps.vGap10,
              GestureDetector(
                onTap: () {
                  cancel();
                },
                child: Text(
                  cancelButton!,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.primaryHighLightColor,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
