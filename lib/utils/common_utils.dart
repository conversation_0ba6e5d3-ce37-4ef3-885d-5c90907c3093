import 'package:aitoearn_app/config/logger.dart';

class CommonUtils {
  /// 重试
  /// [callback] 每次循环的回调，返回 `bool`，为 `true` 则会结束循环。
  /// [max] 重试上限。
  /// [interval] 重试间隔时间（单位：毫秒），默认为 1000 毫秒。
  /// 返回 `true` 表示成功，`false` 表示失败。
  static Future<bool> retryWhile(
    Future<bool?> Function(int count) callback,
    int max, {
    int interval = 1000,
  }) async {
    int count = 0;
    bool flag = true;

    while (true) {
      bool? isEnd = await callback(count);
      if (isEnd == true) {
        break;
      }
      if (count > max) {
        flag = false;
        break;
      }
      count++;
      await Future.delayed(Duration(milliseconds: interval));
      LoggerUtil.i('开始第 $count 次重试');
    }
    return flag;
  }
}
