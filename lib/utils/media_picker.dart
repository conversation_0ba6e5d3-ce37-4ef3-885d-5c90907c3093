import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:media_kit/media_kit.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

/// 视频数据封装：包含视频文件和缩略图
class VideoInfo {
  final File file;
  final File? thumbnail;
  final Duration? duration;

  VideoInfo({required this.file, this.thumbnail, this.duration});
}

/// 多媒体选择器
class MediaPicker {
  static final _imagePicker = ImagePicker();

  /// 选择多张图片（支持 maxCount）
  static Future<List<File>> pickImages({int? maxCount}) async {
    List<File> images = [];

    if (Platform.isAndroid || Platform.isIOS) {
      final files = await _imagePicker.pickMultiImage();
      if (files.isNotEmpty) {
        images = files.map((x) => File(x.path)).toList();
      }
    } else {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
      );
      if (result != null && result.files.isNotEmpty) {
        images =
            result.files
                .where((f) => f.path != null)
                .map((f) => File(f.path!))
                .toList();
      }
    }

    if (maxCount != null && images.length > maxCount) {
      images = images.sublist(0, maxCount);
    }

    return images;
  }

  /// 选择视频（支持多选，返回 [VideoInfo] 列表）
  static Future<List<VideoInfo>> pickVideos({int? maxCount}) async {
    List<VideoInfo> videos = [];

    if (Platform.isAndroid || Platform.isIOS) {
      // image_picker 只支持单个视频选择
      final video = await _imagePicker.pickVideo(source: ImageSource.gallery);
      if (video != null) {
        final file = File(video.path);
        final thumb = await _generateThumbnail(file.path);
        videos.add(VideoInfo(file: file, thumbnail: thumb));
      }
    } else {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: true,
      );
      if (result != null && result.files.isNotEmpty) {
        final files =
            result.files
                .where((f) => f.path != null)
                .map((f) => File(f.path!))
                .toList();

        for (var i = 0; i < files.length; i++) {
          final file = files[i];
          final thumb = await _generateThumbnail(file.path);
          final duration = await _getVideoDuration(file.path);
          videos.add(
            VideoInfo(file: file, thumbnail: thumb, duration: duration),
          );
          if (maxCount != null && videos.length >= maxCount) {
            break;
          }
        }
      }
    }
    return videos;
  }

  /// 生成视频缩略图
  static Future<File?> _generateThumbnail(String videoPath) async {
    try {
      final thumbPath = await VideoThumbnail.thumbnailFile(
        video: videoPath,
        imageFormat: ImageFormat.PNG,
        maxHeight: 200,
        quality: 75,
      );
      if (thumbPath != null) {
        return File(thumbPath);
      }
    } catch (e) {
      print('生成缩略图失败: $e');
    }
    return null;
  }

  /// 获取视频时长（使用 media_kit）
  static Future<Duration?> _getVideoDuration(String videoPath) async {
    try {
      final player = Player(); // 创建 player
      await player.open(Media(videoPath)); // 加载视频文件
      await player.stream.completed.first; // 等待加载完成
      final duration = player.state.duration;
      await player.dispose(); // 清理资源
      return duration;
    } catch (e) {
      print('获取视频时长失败: $e');
      return null;
    }
  }
}
