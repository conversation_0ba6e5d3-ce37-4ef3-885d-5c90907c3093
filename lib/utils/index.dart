/// 数值转换
String formatNumber(int number) {
  if (number >= 10000) {
    double result = number / 10000.0;
    if (result == result.toInt()) {
      return '${result.toInt()}w';
    }
    return '${result.toStringAsFixed(1)}w';
  } else if (number >= 1000) {
    double result = number / 1000.0;
    if (result == result.toInt()) {
      return '${result.toInt()}k';
    }
    return '${result.toStringAsFixed(1)}k';
  }
  return number.toString();
}

/// dynamic 转 List<String>
List<String>? dynamicToStringList(dynamic data) {
  if (data != null && data is List) {
    return (data).map((e) => e.toString()).toList();
  }
  return null;
}
