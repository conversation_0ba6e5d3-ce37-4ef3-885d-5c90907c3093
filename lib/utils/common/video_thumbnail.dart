import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

class VideoThumbnailUtil {
  /// 获取视频缩略图
  static Future<File?> getVideoThumbnail(String videoPath) async {
    try {
      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final thumbnailPath = '${tempDir.path}/thumbnail_${DateTime.now().millisecondsSinceEpoch}.jpg';
      
      // 生成缩略图
      final thumbnailBytes = await VideoThumbnail.thumbnailData(
        video: videoPath,
        imageFormat: ImageFormat.JPEG,
        maxWidth: 720,
        quality: 75,
      );
      
      if (thumbnailBytes != null) {
        // 将缩略图保存为文件
        final thumbnailFile = File(thumbnailPath);
        await thumbnailFile.writeAsBytes(thumbnailBytes);
        LoggerUtil.i('生成视频缩略图: $thumbnailPath');
        return thumbnailFile;
      }
      
      LoggerUtil.e('生成视频缩略图失败: 未能获取缩略图数据');
      return null;
    } catch (e) {
      LoggerUtil.e('生成视频缩略图出错: $e');
      return null;
    }
  }
} 