import 'package:aitoearn_app/res/app_colors.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';

class VideoPreviewViewer extends StatefulWidget {
  final String videoUrl;
  final String? heroTag;

  const VideoPreviewViewer({required this.videoUrl, this.heroTag, super.key});

  static void show({
    required BuildContext context,
    required String videoUrl,
    String? thumbUrl,
    String? heroTag,
  }) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        transitionDuration: const Duration(milliseconds: 250),
        reverseTransitionDuration: const Duration(milliseconds: 200),
        pageBuilder:
            (_, __, ___) =>
                VideoPreviewViewer(videoUrl: videoUrl, heroTag: heroTag),
        transitionsBuilder: (_, animation, __, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    );
  }

  @override
  State<VideoPreviewViewer> createState() => _VideoPreviewViewerState();
}

class _VideoPreviewViewerState extends State<VideoPreviewViewer> {
  late final Player _player;
  late final VideoController _videoController;
  bool _isPlaying = false;
  bool _isExiting = false;

  @override
  void initState() {
    super.initState();

    // 初始化播放器
    _player = Player();
    _videoController = VideoController(
      _player,
      configuration: const VideoControllerConfiguration(),
    );

    // 设置视频源
    _player.open(Media(widget.videoUrl));

    // 监听播放状态
    _player.stream.playing.listen((playing) {
      if (mounted) {
        setState(() => _isPlaying = playing);
      }
    });

    // 准备播放
    _player.stream.completed.listen((completed) {
      if (completed && mounted) {
        setState(() => _isPlaying = false);
      }
    });
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _player.pause();
    } else {
      _player.play();
    }
  }

  void _exit() {
    if (_isExiting) {
      return;
    }
    _isExiting = true;

    // 停止播放
    _player.pause();

    // 关闭页面
    Navigator.of(context).pop();
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final heroTag = widget.heroTag ?? widget.videoUrl;

    return ExtendedImageSlidePage(
      slideType: SlideType.onlyImage,
      slidePageBackgroundHandler: (offset, pageSize) {
        final opacity =
            1.0 - (offset.dy.abs() / pageSize.height).clamp(0.0, 1.0);
        return AppColors.mediaBgFrame.withOpacity(opacity);
      },
      resetPageDuration: const Duration(milliseconds: 300),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            // Hero 动画容器
            Positioned.fill(
              child: // 视频播放器核心
                  Hero(
                tag: heroTag,
                child:
                    Video(
                      controller: _videoController,
                      fill: AppColors.mediaBgFrame,
                    ).safeArea(),
              ),
            ),
            // 顶部关闭按钮
            Positioned(
              top: MediaQuery.of(context).padding.top + 6,
              left: 6,
              child: IconButton(
                icon: const Icon(Icons.close, color: Colors.white, size: 28),
                onPressed: _exit,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
