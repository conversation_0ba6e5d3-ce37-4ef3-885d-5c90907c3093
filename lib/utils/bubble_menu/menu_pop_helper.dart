import 'package:flutter/material.dart';
import 'menu_option.dart';

// 菜单工具类
class MenuPopHelper {

  static OverlayEntry? _currentMenu;

  static void showMenuByKey({
    required BuildContext context,
    required GlobalKey key,
    required List<MenuOption> options,
  }) {
    final RenderBox renderBox =
        key.currentContext?.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    final Offset target = Offset(offset.dx, offset.dy + size.height);
    final Size anchorSize = size;

    showMenuByOffset(
      context: context,
      offset: target,
      anchorSize: anchorSize,
      options: options,
    );
  }

  static void showMenuByOffset({
    required BuildContext context,
    required Offset offset,
    required Size anchorSize,
    required List<MenuOption> options,
  }) {
    _removeCurrentMenu();

    final overlayState = Overlay.of(context);
    final screenSize = MediaQuery.of(context).size;
    const double menuWidth = 200;
    const double menuHeight = 44 * 3.0;

    // 判断是否贴近右边/底部
    final bool showAbove = offset.dy + menuHeight > screenSize.height;
    final bool alignRight = offset.dx + menuWidth > screenSize.width;

    late final OverlayEntry overlayEntry;
    overlayEntry = OverlayEntry(
      builder: (_) {
        return Stack(
          children: [
            // 背景遮罩，点击关闭
            Positioned.fill(
              child: GestureDetector(
                onTap: _removeCurrentMenu,
                behavior: HitTestBehavior.translucent,
              ),
            ),
            // 菜单定位
            Positioned(
              left: alignRight ? null : offset.dx,
              right:
                  alignRight
                      ? (screenSize.width - offset.dx - anchorSize.width)
                      : null,
              top: showAbove ? null : offset.dy,
              bottom:
                  showAbove
                      ? (screenSize.height - offset.dy + anchorSize.height)
                      : null,
              child: _BubbleMenu(
                options: options,
                onDismiss: _removeCurrentMenu,
                isAbove: showAbove,
              ),
            ),
          ],
        );
      },
    );

    _currentMenu = overlayEntry;
    overlayState.insert(overlayEntry);
  }

  static void _removeCurrentMenu() {
    _currentMenu?.remove();
    _currentMenu = null;
  }
}

class _BubbleMenu extends StatefulWidget {
  final List<MenuOption> options;
  final VoidCallback onDismiss;
  final bool isAbove;

  const _BubbleMenu({
    required this.options,
    required this.onDismiss,
    required this.isAbove,
  });

  @override
  State<_BubbleMenu> createState() => _BubbleMenuState();
}

class _BubbleMenuState extends State<_BubbleMenu>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _opacity;
  late final Animation<double> _size;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
      reverseDuration: const Duration(milliseconds: 150),
    );

    _opacity = CurvedAnimation(parent: _controller, curve: Curves.easeOut);
    _size = CurvedAnimation(parent: _controller, curve: Curves.easeOutBack);

    // 正向播放
    _controller.forward();
  }

  Future<void> _dismiss() async {
    // 播放反向动画后再移除
    await _controller.reverse();
    widget.onDismiss();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _opacity,
      child: SizeTransition(
        sizeFactor: _size,
        axis: Axis.vertical,
        axisAlignment: widget.isAbove ? 1.0 : -1.0, // 上方弹出：从下撑；下方弹出：从上撑
        child: Material(
          color: Colors.transparent,
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children:
                  widget.options.map((option) {
                    return InkWell(
                      onTap: () {
                        option.onTap();
                        _dismiss();
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 24,
                          top: 12,
                          bottom: 12,
                        ),
                        child: Row(
                          children: [
                            if (option.icon != null) ...[
                              Icon(
                                option.icon,
                                size: 20,
                                color: Colors.grey[700],
                              ),
                              const SizedBox(width: 8),
                            ],
                            SizedBox(width: 60, child: Text(
                              option.name,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.black87,
                              ),
                            ),),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}
