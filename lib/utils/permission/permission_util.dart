import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionUtil {
  /// 检查并请求单个权限

  /// [permission] 需要请求的权限
  /// [onGranted] 权限被授予时的回调
  /// [onDenied] 权限被拒绝时的回调 (用户明确拒绝，但仍可再次请求)
  /// [onPermanentlyDenied] 权限被永久拒绝时的回调 (用户拒绝且选择了不再询问，需要引导用户去设置)
  /// [onRestricted] 权限受限时的回调 (例如家长控制)
  static Future<void> customRequest(
    Permission permission, {
    VoidCallback? onGranted,
    VoidCallback? onDenied,
    VoidCallback? onPermanentlyDenied,
    VoidCallback? onRestricted,
  }) async {
    final status = await permission.request();
    switch (status) {
      case PermissionStatus.granted:
      case PermissionStatus.limited: // 对于照片等权限，limited也视为已获取部分权限
        onGranted?.call();
        break;
      case PermissionStatus.denied:
        onDenied?.call();
        break;
      case PermissionStatus.permanentlyDenied:
        onPermanentlyDenied?.call();
        _showOpenSettingsDialog();
        break;
      case PermissionStatus.restricted:
        onRestricted?.call();
        break;
      default:
        onDenied?.call();
        break;
    }
  }

  /// 检查并请求多个权限
  /// 检查并请求多个权限 (基础方法，不带UI提示)
  static Future<Map<Permission, PermissionStatus>> requestMultiple(
    List<Permission> permissions,
  ) async {
    final Map<Permission, PermissionStatus> statuses =
        await permissions.request();
    return statuses;
  }

  /// 检查并请求多个权限，并在未通过时弹窗提示
  static Future<bool> requestMultipleWithPrompt(List<Permission> permissions) async {
    final Map<Permission, PermissionStatus> statuses = await requestMultiple(permissions);

    List<String> deniedPermissions = [];
    bool allGranted = true;

    statuses.forEach((permission, status) {
      if (status != PermissionStatus.granted && status != PermissionStatus.limited) {
        allGranted = false;
        deniedPermissions.add(_getPermissionName(permission));
      }
    });

    if (!allGranted) {
      await Get.dialog(
        AlertDialog(
          title: Text('permissionDeniedTitle'.tr),
          content: Text(
            '${'permissionDeniedMessage'.tr}\n${deniedPermissions.join("\n")}',
          ),
          actions: <Widget>[
            TextButton(
              child: Text('permissionAcknowledge'.tr),
              onPressed: () => Get.back(),
            ),
            TextButton(
              child: Text('permissionGoSettings'.tr),
              onPressed: () {
                Get.back();
                openAppSettings();
              },
            ),
          ],
        ),
      );
    }

    return allGranted;
  }

  /// 辅助方法：将 Permission 枚举转换为用户可读的名称（已优化并支持国际化）
  static String _getPermissionName(Permission permission) {
    final Map<Permission, String> permissionKeys = {
      Permission.camera: 'permissionCamera'.tr,
      Permission.storage: 'permissionStorage'.tr,
      Permission.manageExternalStorage: 'permissionStorage'.tr,
      Permission.photos: 'permissionPhotos'.tr,
      Permission.location: 'permissionLocation'.tr,
      Permission.locationAlways: 'permissionLocationAlways'.tr,
      Permission.locationWhenInUse: 'permissionLocationWhenInUse'.tr,
      Permission.microphone: 'permissionMicrophone'.tr,
      Permission.contacts: 'permissionContacts'.tr,
      Permission.calendarFullAccess: 'permissionCalendar'.tr,
      Permission.calendarWriteOnly: 'permissionCalendar'.tr,
      Permission.notification: 'permissionNotification'.tr,
      Permission.sensors: 'permissionSensors'.tr,
      Permission.sms: 'permissionSms'.tr,
      Permission.speech: 'permissionSpeech'.tr,
      Permission.ignoreBatteryOptimizations: 'permissionIgnoreBatteryOptimizations'.tr,
      Permission.bluetooth: 'permissionBluetooth'.tr,
      Permission.bluetoothScan: 'permissionBluetoothScan'.tr,
      Permission.bluetoothAdvertise: 'permissionBluetoothAdvertise'.tr,
      Permission.bluetoothConnect: 'permissionBluetoothConnect'.tr,
      Permission.appTrackingTransparency: 'permissionAppTrackingTransparency'.tr,
      Permission.criticalAlerts: 'permissionCriticalAlerts'.tr,
      Permission.accessMediaLocation: 'permissionAccessMediaLocation'.tr,
      Permission.activityRecognition: 'permissionActivityRecognition'.tr,
    };

    return permissionKeys[permission] ?? permission.toString().split('.').last;
  }

  /// 检查单个权限的状态
  static Future<PermissionStatus> status(Permission permission) async {
    return await permission.status;
  }

  /// 检查多个权限的状态
  static Future<Map<Permission, PermissionStatus>> statusMultiple(
    List<Permission> permissions,
  ) async {
    Map<Permission, PermissionStatus> statuses = {};
    for (var permission in permissions) {
      statuses[permission] = await permission.status;
    }
    return statuses;
  }

  /// 打开应用设置页面
  static Future<bool> openAppSettings() async {
    final data = await _showOpenSettingsDialog();
    return data;
  }

  /// 显示引导用户去设置中开启权限的对话框
  static Future<bool> _showOpenSettingsDialog() async {
    await Get.dialog(
      AlertDialog(
        title: Text('permissionRequestTitle'.tr),
        content: Text('permissionPermanentlyDeniedMessage'.tr),
        actions: <Widget>[
          TextButton(
            child: Text('cancel'.tr),
            onPressed: () => Get.back(result: false),
          ),
          TextButton(
            child: Text('permissionGoSettings'.tr),
            onPressed: () {
              Get.back(result: true);
              openAppSettings();
            },
          ),
        ],
      ),
    );
    return false;
  }

  // --- 特定权限的便捷方法 ---

  /// 请求相机权限
  static Future<void> requestCamera({
    VoidCallback? onGranted,
    VoidCallback? onDenied,
    VoidCallback? onPermanentlyDenied,
    VoidCallback? onRestricted,
  }) async {
    await customRequest(
      Permission.camera,
      onGranted: onGranted,
      onDenied: onDenied,
      onPermanentlyDenied: onPermanentlyDenied,
      onRestricted: onRestricted,
    );
  }

  /// 请求存储权限 (Android)
  /// 注意：Android 10 (API 29) 及以上版本对存储权限有较大变更，
  /// 通常使用 `Permission.photos` (访问照片/视频) 或 `Permission.manageExternalStorage` (需要特殊声明)
  /// `Permission.storage` 主要用于 Android 9 及以下。
  static Future<void> requestStorage({
    VoidCallback? onGranted,
    VoidCallback? onDenied,
    VoidCallback? onPermanentlyDenied,
    VoidCallback? onRestricted,
  }) async {
    // 根据 Android 版本选择合适的权限
    // 简化处理，具体项目需要根据 Android 版本细化
    await customRequest(
      Permission.storage,
      onGranted: onGranted,
      onDenied: onDenied,
      onPermanentlyDenied: onPermanentlyDenied,
      onRestricted: onRestricted,
    );
  }

  /// 请求照片权限 (iOS 和 Android 13+)
  static Future<void> requestPhotos({
    VoidCallback? onGranted,
    VoidCallback? onDenied,
    VoidCallback? onPermanentlyDenied,
    VoidCallback? onRestricted,
  }) async {
    await customRequest(
      Permission.photos,
      onGranted: onGranted,
      onDenied: onDenied,
      onPermanentlyDenied: onPermanentlyDenied,
      onRestricted: onRestricted,
    );
  }

  /// 请求位置权限
  static Future<void> requestLocation({
    VoidCallback? onGranted,
    VoidCallback? onDenied,
    VoidCallback? onPermanentlyDenied,
    VoidCallback? onRestricted,
  }) async {
    await customRequest(
      Permission.location,
      onGranted: onGranted,
      onDenied: onDenied,
      onPermanentlyDenied: onPermanentlyDenied,
      onRestricted: onRestricted,
    );
  }
}
