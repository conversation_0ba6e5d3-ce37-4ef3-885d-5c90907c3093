// Represents a topic suggestion from Douyin API
class TopicSug {
  final String chaName;
  final int viewCount;
  final String cid;
  final String groupId;
  final int tag;

  TopicSug({
    required this.chaName,
    required this.viewCount,
    required this.cid,
    required this.groupId,
    required this.tag,
  });

  factory TopicSug.fromJson(Map<String, dynamic> json) {
    return TopicSug(
      chaName: json['cha_name'] as String,
      viewCount: json['view_count'] as int,
      cid: json['cid'] as String,
      groupId: json['group_id'] as String,
      tag: json['tag'] as int,
    );
  }

  Map<String, dynamic> toJson() => {
    'cha_name': chaName,
    'view_count': viewCount,
    'cid': cid,
    'group_id': groupId,
    'tag': tag,
  };
}

class WordsQueryRecord {
  final String info;
  final String wordsSource;
  final String queryId;

  WordsQueryRecord({
    required this.info,
    required this.wordsSource,
    required this.queryId,
  });

  factory WordsQueryRecord.fromJson(Map<String, dynamic> json) {
    return WordsQueryRecord(
      info: json['info'] as String,
      wordsSource: json['words_source'] as String,
      queryId: json['query_id'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
    'info': info,
    'words_source': wordsSource,
    'query_id': queryId,
  };
}

class Extra {
  final int now;
  final String logid;
  final List<dynamic> fatalItemIds;
  final String searchRequestId;

  Extra({
    required this.now,
    required this.logid,
    required this.fatalItemIds,
    required this.searchRequestId,
  });

  factory Extra.fromJson(Map<String, dynamic> json) {
    return Extra(
      now: json['now'] as int,
      logid: json['logid'] as String,
      fatalItemIds: json['fatal_item_ids'] as List<dynamic>,
      searchRequestId: json['search_request_id'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
    'now': now,
    'logid': logid,
    'fatal_item_ids': fatalItemIds,
    'search_request_id': searchRequestId,
  };
}

class LogPb {
  final String imprId;

  LogPb({required this.imprId});

  factory LogPb.fromJson(Map<String, dynamic> json) {
    return LogPb(imprId: json['impr_id'] as String);
  }

  Map<String, dynamic> toJson() => {'impr_id': imprId};
}

class AddressInfo {
  final String adCodeV2;
  final String addrWithExtraInfo;
  final String address;
  final String city;
  final String cityCode;
  final String district;
  final String imAddr;
  final String province;
  final String simpleAddr;

  AddressInfo({
    required this.adCodeV2,
    required this.addrWithExtraInfo,
    required this.address,
    required this.city,
    required this.cityCode,
    required this.district,
    required this.imAddr,
    required this.province,
    required this.simpleAddr,
  });

  factory AddressInfo.fromJson(Map<String, dynamic> json) {
    return AddressInfo(
      adCodeV2: json['ad_code_v2'] as String,
      addrWithExtraInfo: json['addr_with_extra_info'] as String,
      address: json['address'] as String,
      city: json['city'] as String,
      cityCode: json['city_code'] as String,
      district: json['district'] as String,
      imAddr: json['im_addr'] as String,
      province: json['province'] as String,
      simpleAddr: json['simple_addr'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
    'ad_code_v2': adCodeV2,
    'addr_with_extra_info': addrWithExtraInfo,
    'address': address,
    'city': city,
    'city_code': cityCode,
    'district': district,
    'im_addr': imAddr,
    'province': province,
    'simple_addr': simpleAddr,
  };
}

class Cover {
  final String uri;
  final List<String> urlList;

  Cover({required this.uri, required this.urlList});

  factory Cover.fromJson(Map<String, dynamic> json) {
    return Cover(
      uri: json['uri'] as String,
      urlList: (json['url_list'] as List).cast<String>(),
    );
  }

  Map<String, dynamic> toJson() => {
    'uri': uri,
    'url_list': urlList,
  };
}

class Icon {
  final String uri;
  final List<String> urlList;

  Icon({required this.uri, required this.urlList});

  factory Icon.fromJson(Map<String, dynamic> json) {
    return Icon(
      uri: json['uri'] as String,
      urlList: (json['url_list'] as List).cast<String>(),
    );
  }

  Map<String, dynamic> toJson() => {
    'uri': uri,
    'url_list': urlList,
  };
}

class LifeCommerceInfo {
  final String simpleAddrStr;

  LifeCommerceInfo({required this.simpleAddrStr});

  factory LifeCommerceInfo.fromJson(Map<String, dynamic> json) {
    return LifeCommerceInfo(
      simpleAddrStr: json['simple_addr_str'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
    'simple_addr_str': simpleAddrStr,
  };
}

class PoiBackendType {
  final String code;
  final String content;
  final String l1Name;
  final String l2Name;
  final String l3Name;
  final String name;

  PoiBackendType({
    required this.code,
    required this.content,
    required this.l1Name,
    required this.l2Name,
    required this.l3Name,
    required this.name,
  });

  factory PoiBackendType.fromJson(Map<String, dynamic> json) {
    return PoiBackendType(
      code: json['code'] as String,
      content: json['content'] as String,
      l1Name: json['l1_name'] as String,
      l2Name: json['l2_name'] as String,
      l3Name: json['l3_name'] as String,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
    'code': code,
    'content': content,
    'l1_name': l1Name,
    'l2_name': l2Name,
    'l3_name': l3Name,
    'name': name,
  };
}

class PoiFrontendType {
  final String code;
  final String name;

  PoiFrontendType({required this.code, required this.name});

  factory PoiFrontendType.fromJson(Map<String, dynamic> json) {
    return PoiFrontendType(
      code: json['code'] as String,
      name: json['name'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
    'code': code,
    'name': name,
  };
}

class ShareInfo {
  final int boolPersist;
  final String shareTitle;
  final String shareUrl;
  final String shareWeiboDesc;

  ShareInfo({
    required this.boolPersist,
    required this.shareTitle,
    required this.shareUrl,
    required this.shareWeiboDesc,
  });

  factory ShareInfo.fromJson(Map<String, dynamic> json) {
    return ShareInfo(
      boolPersist: json['bool_persist'] as int,
      shareTitle: json['share_title'] as String,
      shareUrl: json['share_url'] as String,
      shareWeiboDesc: json['share_weibo_desc'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
    'bool_persist': boolPersist,
    'share_title': shareTitle,
    'share_url': shareUrl,
    'share_weibo_desc': shareWeiboDesc,
  };
}

class Poi {
  final Map<String, dynamic>? staturatedTags;
  final AddressInfo addressInfo;
  final Map<String, bool> anchorPostExt;
  final String businessAreaName;
  final dynamic channelExclusive;
  final int collectStat;
  final String collectedCount;
  final double cost;
  final Cover coverHd;
  final Cover coverItem;
  final Cover coverLarge;
  final Cover coverMedium;
  final Cover coverThumb;
  final dynamic cpsCommissionRateRange;
  final dynamic cpsCommissionValueRange;
  final String distance;
  final List<String> effectIds;
  final int expandType;
  final dynamic fulfillTaskList;
  final Icon iconOnEntry;
  final Icon iconOnInfo;
  final Icon iconOnMap;
  final List<dynamic> iconServiceTypeList;
  final int iconType;
  final bool isAdminArea;
  final bool isCommerceIntention;
  final int itemCount;
  final LifeCommerceInfo lifeCommerceInfo;
  final PoiBackendType poiBackendType;
  final List<dynamic> poiDetailTags;
  final List<PoiFrontendType> poiFrontendType;
  final String poiId;
  final double poiLatitude;
  final double poiLatitudeGcj02;
  final double poiLongitude;
  final double poiLongitudeGcj02;
  final String poiName;
  final dynamic poiRanks;
  final dynamic poiSearchTags;
  final dynamic poiSearchTagsV2;
  final int poiType;
  final String poiVoucher;
  final dynamic serviceTypeList;
  final ShareInfo shareInfo;
  final int showType;
  final String simpleAddressStr;
  final String typeCode;
  final int userCount;
  final String viewCount;
  final List<dynamic> voucherReleaseAreas;
  final int withRecommendTag;

  Poi({
    this.staturatedTags,
    required this.addressInfo,
    required this.anchorPostExt,
    required this.businessAreaName,
    this.channelExclusive,
    required this.collectStat,
    required this.collectedCount,
    required this.cost,
    required this.coverHd,
    required this.coverItem,
    required this.coverLarge,
    required this.coverMedium,
    required this.coverThumb,
    this.cpsCommissionRateRange,
    this.cpsCommissionValueRange,
    required this.distance,
    required this.effectIds,
    required this.expandType,
    this.fulfillTaskList,
    required this.iconOnEntry,
    required this.iconOnInfo,
    required this.iconOnMap,
    required this.iconServiceTypeList,
    required this.iconType,
    required this.isAdminArea,
    required this.isCommerceIntention,
    required this.itemCount,
    required this.lifeCommerceInfo,
    required this.poiBackendType,
    required this.poiDetailTags,
    required this.poiFrontendType,
    required this.poiId,
    required this.poiLatitude,
    required this.poiLatitudeGcj02,
    required this.poiLongitude,
    required this.poiLongitudeGcj02,
    required this.poiName,
    this.poiRanks,
    this.poiSearchTags,
    this.poiSearchTagsV2,
    required this.poiType,
    required this.poiVoucher,
    this.serviceTypeList,
    required this.shareInfo,
    required this.showType,
    required this.simpleAddressStr,
    required this.typeCode,
    required this.userCount,
    required this.viewCount,
    required this.voucherReleaseAreas,
    required this.withRecommendTag,
  });

  factory Poi.fromJson(Map<String, dynamic> json) {
    return Poi(
      staturatedTags: json['Staturated_tags'],
      addressInfo: AddressInfo.fromJson(json['address_info']),
      anchorPostExt: Map<String, bool>.from(json['anchor_post_ext']),
      businessAreaName: json['business_area_name'],
      channelExclusive: json['channel_exclusive'],
      collectStat: json['collect_stat'],
      collectedCount: json['collected_count'],
      cost: json['cost'].toDouble(),
      coverHd: Cover.fromJson(json['cover_hd']),
      coverItem: Cover.fromJson(json['cover_item']),
      coverLarge: Cover.fromJson(json['cover_large']),
      coverMedium: Cover.fromJson(json['cover_medium']),
      coverThumb: Cover.fromJson(json['cover_thumb']),
      cpsCommissionRateRange: json['cps_commission_rate_range'],
      cpsCommissionValueRange: json['cps_commission_value_range'],
      distance: json['distance'],
      effectIds: List<String>.from(json['effect_ids']),
      expandType: json['expand_type'],
      fulfillTaskList: json['fulfill_task_list'],
      iconOnEntry: Icon.fromJson(json['icon_on_entry']),
      iconOnInfo: Icon.fromJson(json['icon_on_info']),
      iconOnMap: Icon.fromJson(json['icon_on_map']),
      iconServiceTypeList: json['icon_service_type_list'],
      iconType: json['icon_type'],
      isAdminArea: json['is_admin_area'],
      isCommerceIntention: json['is_commerce_intention'],
      itemCount: json['item_count'],
      lifeCommerceInfo: LifeCommerceInfo.fromJson(json['life_commerce_info']),
      poiBackendType: PoiBackendType.fromJson(json['poi_backend_type']),
      poiDetailTags: json['poi_detail_tags'],
      poiFrontendType: (json['poi_frontend_type'] as List)
          .map((e) => PoiFrontendType.fromJson(e))
          .toList(),
      poiId: json['poi_id'],
      poiLatitude: json['poi_latitude'].toDouble(),
      poiLatitudeGcj02: json['poi_latitude_gcj02'].toDouble(),
      poiLongitude: json['poi_longitude'].toDouble(),
      poiLongitudeGcj02: json['poi_longitude_gcj02'].toDouble(),
      poiName: json['poi_name'],
      poiRanks: json['poi_ranks'],
      poiSearchTags: json['poi_search_tags'],
      poiSearchTagsV2: json['poi_search_tags_v2'],
      poiType: json['poi_type'],
      poiVoucher: json['poi_voucher'],
      serviceTypeList: json['service_type_list'],
      shareInfo: ShareInfo.fromJson(json['share_info']),
      showType: json['show_type'],
      simpleAddressStr: json['simple_address_str'],
      typeCode: json['type_code'],
      userCount: json['user_count'],
      viewCount: json['view_count'],
      voucherReleaseAreas: json['voucher_release_areas'],
      withRecommendTag: json['with_recommend_tag'],
    );
  }

  Map<String, dynamic> toJson() => {
    'Staturated_tags': staturatedTags,
    'address_info': addressInfo.toJson(),
    'anchor_post_ext': anchorPostExt,
    'business_area_name': businessAreaName,
    'channel_exclusive': channelExclusive,
    'collect_stat': collectStat,
    'collected_count': collectedCount,
    'cost': cost,
    'cover_hd': coverHd.toJson(),
    'cover_item': coverItem.toJson(),
    'cover_large': coverLarge.toJson(),
    'cover_medium': coverMedium.toJson(),
    'cover_thumb': coverThumb.toJson(),
    'cps_commission_rate_range': cpsCommissionRateRange,
    'cps_commission_value_range': cpsCommissionValueRange,
    'distance': distance,
    'effect_ids': effectIds,
    'expand_type': expandType,
    'fulfill_task_list': fulfillTaskList,
    'icon_on_entry': iconOnEntry.toJson(),
    'icon_on_info': iconOnInfo.toJson(),
    'icon_on_map': iconOnMap.toJson(),
    'icon_service_type_list': iconServiceTypeList,
    'icon_type': iconType,
    'is_admin_area': isAdminArea,
    'is_commerce_intention': isCommerceIntention,
    'item_count': itemCount,
    'life_commerce_info': lifeCommerceInfo.toJson(),
    'poi_backend_type': poiBackendType.toJson(),
    'poi_detail_tags': poiDetailTags,
    'poi_frontend_type': poiFrontendType.map((e) => e.toJson()).toList(),
    'poi_id': poiId,
    'poi_latitude': poiLatitude,
    'poi_latitude_gcj02': poiLatitudeGcj02,
    'poi_longitude': poiLongitude,
    'poi_longitude_gcj02': poiLongitudeGcj02,
    'poi_name': poiName,
    'poi_ranks': poiRanks,
    'poi_search_tags': poiSearchTags,
    'poi_search_tags_v2': poiSearchTagsV2,
    'poi_type': poiType,
    'poi_voucher': poiVoucher,
    'service_type_list': serviceTypeList,
    'share_info': shareInfo.toJson(),
    'show_type': showType,
    'simple_address_str': simpleAddressStr,
    'type_code': typeCode,
    'user_count': userCount,
    'view_count': viewCount,
    'voucher_release_areas': voucherReleaseAreas,
    'with_recommend_tag': withRecommendTag,
  };
}

class DriftInfo {
  final double score;
  final int timestamp;

  DriftInfo({required this.score, required this.timestamp});

  factory DriftInfo.fromJson(Map<String, dynamic> json) {
    return DriftInfo(
      score: json['score'].toDouble(),
      timestamp: json['timestamp'],
    );
  }

  Map<String, dynamic> toJson() => {
    'score': score,
    'timestamp': timestamp,
  };
}

class DouyinActivity {
  final String activityId;
  final int activityLevel;
  final String activityName;
  final int activityStatus;
  final int activityType;
  final List<String> challenge;
  final List<int> challengeIds;
  final int collectId;
  final bool collectStatus;
  final String coverImage;
  final String gameId;
  final double hotScore;
  final bool ifWellChosen;
  final String jumpLink;
  final int jumpType;
  final int queryTag;
  final int rewardType;
  final String showEndTime;
  final String showStartTime;

  DouyinActivity({
    required this.activityId,
    required this.activityLevel,
    required this.activityName,
    required this.activityStatus,
    required this.activityType,
    required this.challenge,
    required this.challengeIds,
    required this.collectId,
    required this.collectStatus,
    required this.coverImage,
    required this.gameId,
    required this.hotScore,
    required this.ifWellChosen,
    required this.jumpLink,
    required this.jumpType,
    required this.queryTag,
    required this.rewardType,
    required this.showEndTime,
    required this.showStartTime,
  });

  factory DouyinActivity.fromJson(Map<String, dynamic> json) {
    return DouyinActivity(
      activityId: json['activity_id'],
      activityLevel: json['activity_level'],
      activityName: json['activity_name'],
      activityStatus: json['activity_status'],
      activityType: json['activity_type'],
      challenge: List<String>.from(json['challenge']),
      challengeIds: List<int>.from(json['challenge_ids']),
      collectId: json['collect_id'],
      collectStatus: json['collect_status'],
      coverImage: json['cover_image'],
      gameId: json['game_id'],
      hotScore: json['hot_score'].toDouble(),
      ifWellChosen: json['if_well_chosen'],
      jumpLink: json['jump_link'],
      jumpType: json['jump_type'],
      queryTag: json['query_tag'],
      rewardType: json['reward_type'],
      showEndTime: json['show_end_time'],
      showStartTime: json['show_start_time'],
    );
  }

  Map<String, dynamic> toJson() => {
    'activity_id': activityId,
    'activity_level': activityLevel,
    'activity_name': activityName,
    'activity_status': activityStatus,
    'activity_type': activityType,
    'challenge': challenge,
    'challenge_ids': challengeIds,
    'collect_id': collectId,
    'collect_status': collectStatus,
    'cover_image': coverImage,
    'game_id': gameId,
    'hot_score': hotScore,
    'if_well_chosen': ifWellChosen,
    'jump_link': jumpLink,
    'jump_type': jumpType,
    'query_tag': queryTag,
    'reward_type': rewardType,
    'show_end_time': showEndTime,
    'show_start_time': showStartTime,
  };
}

class Avatar {
  final String uri;
  final List<String> urlList;

  Avatar({required this.uri, required this.urlList});

  factory Avatar.fromJson(Map<String, dynamic> json) {
    return Avatar(
      uri: json['uri'],
      urlList: List<String>.from(json['url_list']),
    );
  }

  Map<String, dynamic> toJson() => {
    'uri': uri,
    'url_list': urlList,
  };
}

class OriginalMusician {
  final int musicCount;
  final int musicUsedCount;

  OriginalMusician({required this.musicCount, required this.musicUsedCount});

  factory OriginalMusician.fromJson(Map<String, dynamic> json) {
    return OriginalMusician(
      musicCount: json['music_count'],
      musicUsedCount: json['music_used_count'],
    );
  }

  Map<String, dynamic> toJson() => {
    'music_count': musicCount,
    'music_used_count': musicUsedCount,
  };
}

class VideoIcon {
  final String uri;
  final List<String> urlList;

  VideoIcon({required this.uri, required this.urlList});

  factory VideoIcon.fromJson(Map<String, dynamic> json) {
    return VideoIcon(
      uri: json['uri'],
      urlList: List<String>.from(json['url_list']),
    );
  }

  Map<String, dynamic> toJson() => {
    'uri': uri,
    'url_list': urlList,
  };
}

class DouyinUser {
  final Avatar avatarLarger;
  final Avatar avatarMedium;
  final Avatar avatarThumb;
  final int awemeCount;
  final dynamic cardEntries;
  final String customVerify;
  final String enterpriseVerifyReason;
  final int favoritingCount;
  final int followStatus;
  final int followerCount;
  final int followerStatus;
  final dynamic followersDetail;
  final int followingCount;
  final dynamic geofencing;
  final bool hasOrders;
  final bool isAdFake;
  final bool isGovMediaVip;
  final dynamic mixInfo;
  final String nickname;
  final OriginalMusician originalMusician;
  final dynamic platformSyncInfo;
  final dynamic policyVersion;
  final double rate;
  final String region;
  final String secUid;
  final int secret;
  final String shortId;
  final String signature;
  final int status;
  final bool storyOpen;
  final String totalFavorited;
  final List<dynamic> typeLabel;
  final String uid;
  final String uniqueId;
  final bool userCanceled;
  final int verificationType;
  final VideoIcon videoIcon;
  final bool withCommerceEntry;
  final bool withFusionShopEntry;
  final bool withShopEntry;

  DouyinUser({
    required this.avatarLarger,
    required this.avatarMedium,
    required this.avatarThumb,
    required this.awemeCount,
    this.cardEntries,
    required this.customVerify,
    required this.enterpriseVerifyReason,
    required this.favoritingCount,
    required this.followStatus,
    required this.followerCount,
    required this.followerStatus,
    this.followersDetail,
    required this.followingCount,
    this.geofencing,
    required this.hasOrders,
    required this.isAdFake,
    required this.isGovMediaVip,
    this.mixInfo,
    required this.nickname,
    required this.originalMusician,
    this.platformSyncInfo,
    this.policyVersion,
    required this.rate,
    required this.region,
    required this.secUid,
    required this.secret,
    required this.shortId,
    required this.signature,
    required this.status,
    required this.storyOpen,
    required this.totalFavorited,
    required this.typeLabel,
    required this.uid,
    required this.uniqueId,
    required this.userCanceled,
    required this.verificationType,
    required this.videoIcon,
    required this.withCommerceEntry,
    required this.withFusionShopEntry,
    required this.withShopEntry,
  });

  factory DouyinUser.fromJson(Map<String, dynamic> json) {
    return DouyinUser(
      avatarLarger: Avatar.fromJson(json['avatar_larger']),
      avatarMedium: Avatar.fromJson(json['avatar_medium']),
      avatarThumb: Avatar.fromJson(json['avatar_thumb']),
      awemeCount: json['aweme_count'],
      cardEntries: json['card_entries'],
      customVerify: json['custom_verify'],
      enterpriseVerifyReason: json['enterprise_verify_reason'],
      favoritingCount: json['favoriting_count'],
      followStatus: json['follow_status'],
      followerCount: json['follower_count'],
      followerStatus: json['follower_status'],
      followersDetail: json['followers_detail'],
      followingCount: json['following_count'],
      geofencing: json['geofencing'],
      hasOrders: json['has_orders'],
      isAdFake: json['is_ad_fake'],
      isGovMediaVip: json['is_gov_media_vip'],
      mixInfo: json['mix_info'],
      nickname: json['nickname'],
      originalMusician: OriginalMusician.fromJson(json['original_musician']),
      platformSyncInfo: json['platform_sync_info'],
      policyVersion: json['policy_version'],
      rate: json['rate'].toDouble(),
      region: json['region'],
      secUid: json['sec_uid'],
      secret: json['secret'],
      shortId: json['short_id'],
      signature: json['signature'],
      status: json['status'],
      storyOpen: json['story_open'],
      totalFavorited: json['total_favorited'],
      typeLabel: json['type_label'],
      uid: json['uid'],
      uniqueId: json['unique_id'],
      userCanceled: json['user_canceled'],
      verificationType: json['verification_type'],
      videoIcon: VideoIcon.fromJson(json['video_icon']),
      withCommerceEntry: json['with_commerce_entry'],
      withFusionShopEntry: json['with_fusion_shop_entry'],
      withShopEntry: json['with_shop_entry'],
    );
  }

  Map<String, dynamic> toJson() => {
    'avatar_larger': avatarLarger.toJson(),
    'avatar_medium': avatarMedium.toJson(),
    'avatar_thumb': avatarThumb.toJson(),
    'aweme_count': awemeCount,
    'card_entries': cardEntries,
    'custom_verify': customVerify,
    'enterprise_verify_reason': enterpriseVerifyReason,
    'favoriting_count': favoritingCount,
    'follow_status': followStatus,
    'follower_count': followerCount,
    'follower_status': followerStatus,
    'followers_detail': followersDetail,
    'following_count': followingCount,
    'geofencing': geofencing,
    'has_orders': hasOrders,
    'is_ad_fake': isAdFake,
    'is_gov_media_vip': isGovMediaVip,
    'mix_info': mixInfo,
    'nickname': nickname,
    'original_musician': originalMusician.toJson(),
    'platform_sync_info': platformSyncInfo,
    'policy_version': policyVersion,
    'rate': rate,
    'region': region,
    'sec_uid': secUid,
    'secret': secret,
    'short_id': shortId,
    'signature': signature,
    'status': status,
    'story_open': storyOpen,
    'total_favorited': totalFavorited,
    'type_label': typeLabel,
    'uid': uid,
    'unique_id': uniqueId,
    'user_canceled': userCanceled,
    'verification_type': verificationType,
    'video_icon': videoIcon.toJson(),
    'with_commerce_entry': withCommerceEntry,
    'with_fusion_shop_entry': withFusionShopEntry,
    'with_shop_entry': withShopEntry,
  };
}

class Video {
  final dynamic bigThumbs;
  final dynamic bitRate;
  final Cover cover;
  final int duration;

  Video({
    this.bigThumbs,
    this.bitRate,
    required this.cover,
    required this.duration,
  });

  factory Video.fromJson(Map<String, dynamic> json) {
    return Video(
      bigThumbs: json['big_thumbs'],
      bitRate: json['bit_rate'],
      cover: Cover.fromJson(json['cover']),
      duration: json['duration'],
    );
  }

  Map<String, dynamic> toJson() => {
    'big_thumbs': bigThumbs,
    'bit_rate': bitRate,
    'cover': cover.toJson(),
    'duration': duration,
  };
}

class DemoVideo {
  final String awemeId;
  final dynamic chaList;
  final dynamic chapterBarColor;
  final dynamic chapterList;
  final dynamic commentList;
  final dynamic geofencing;
  final dynamic imageInfos;
  final dynamic images;
  final dynamic imgBitrate;
  final dynamic interactionStickers;
  final dynamic labelTopText;
  final dynamic longVideo;
  final dynamic promotions;
  final dynamic textExtra;
  final Video video;
  final dynamic videoLabels;
  final dynamic videoText;

  DemoVideo({
    required this.awemeId,
    this.chaList,
    this.chapterBarColor,
    this.chapterList,
    this.commentList,
    this.geofencing,
    this.imageInfos,
    this.images,
    this.imgBitrate,
    this.interactionStickers,
    this.labelTopText,
    this.longVideo,
    this.promotions,
    this.textExtra,
    required this.video,
    this.videoLabels,
    this.videoText,
  });

  factory DemoVideo.fromJson(Map<String, dynamic> json) {
    return DemoVideo(
      awemeId: json['aweme_id'],
      chaList: json['cha_list'],
      chapterBarColor: json['chapter_bar_color'],
      chapterList: json['chapter_list'],
      commentList: json['comment_list'],
      geofencing: json['geofencing'],
      imageInfos: json['image_infos'],
      images: json['images'],
      imgBitrate: json['img_bitrate'],
      interactionStickers: json['interaction_stickers'],
      labelTopText: json['label_top_text'],
      longVideo: json['long_video'],
      promotions: json['promotions'],
      textExtra: json['text_extra'],
      video: Video.fromJson(json['video']),
      videoLabels: json['video_labels'],
      videoText: json['video_text'],
    );
  }

  Map<String, dynamic> toJson() => {
    'aweme_id': awemeId,
    'cha_list': chaList,
    'chapter_bar_color': chapterBarColor,
    'chapter_list': chapterList,
    'comment_list': commentList,
    'geofencing': geofencing,
    'image_infos': imageInfos,
    'images': images,
    'img_bitrate': imgBitrate,
    'interaction_stickers': interactionStickers,
    'label_top_text': labelTopText,
    'long_video': longVideo,
    'promotions': promotions,
    'text_extra': textExtra,
    'video': video.toJson(),
    'video_labels': videoLabels,
    'video_text': videoText,
  };
}

class DouyinActivityDetailResponse {
  final String activityDescription;
  final ActivityInfo activityInfo;
  final List<DemoVideo> demoVideos;
  final Extra extra;
  final dynamic myPublishVideos;
  final dynamic otherPublishVideos;
  final String publishEndTime;
  final String publishStartTime;
  final String rewardRules;
  final int statusCode;
  final List<String> topics;
  final List<String> topicsIds;

  DouyinActivityDetailResponse({
    required this.activityDescription,
    required this.activityInfo,
    required this.demoVideos,
    required this.extra,
    this.myPublishVideos,
    this.otherPublishVideos,
    required this.publishEndTime,
    required this.publishStartTime,
    required this.rewardRules,
    required this.statusCode,
    required this.topics,
    required this.topicsIds,
  });

  factory DouyinActivityDetailResponse.fromJson(Map<String, dynamic> json) {
    return DouyinActivityDetailResponse(
      activityDescription: json['activity_description'],
      activityInfo: ActivityInfo.fromJson(json['activity_info']),
      demoVideos: (json['demo_videos'] as List)
          .map((e) => DemoVideo.fromJson(e))
          .toList(),
      extra: Extra.fromJson(json['extra']),
      myPublishVideos: json['my_publish_videos'],
      otherPublishVideos: json['other_publish_videos'],
      publishEndTime: json['publish_end_time'],
      publishStartTime: json['publish_start_time'],
      rewardRules: json['reward_rules'],
      statusCode: json['status_code'],
      topics: List<String>.from(json['topics']),
      topicsIds: List<String>.from(json['topics_ids']),
    );
  }

  Map<String, dynamic> toJson() => {
    'activity_description': activityDescription,
    'activity_info': activityInfo.toJson(),
    'demo_videos': demoVideos.map((e) => e.toJson()).toList(),
    'extra': extra.toJson(),
    'my_publish_videos': myPublishVideos,
    'other_publish_videos': otherPublishVideos,
    'publish_end_time': publishEndTime,
    'publish_start_time': publishStartTime,
    'reward_rules': rewardRules,
    'status_code': statusCode,
    'topics': topics,
    'topics_ids': topicsIds,
  };
}

class DouyinQueryTags {
  final int id;
  final String name;

  DouyinQueryTags({required this.id, required this.name});

  factory DouyinQueryTags.fromJson(Map<String, dynamic> json) {
    return DouyinQueryTags(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
  };
}

class DouyinActivityTagsResponse {
  final int statusCode;
  final Extra extra;
  final List<DouyinQueryTags> queryTags;

  DouyinActivityTagsResponse({
    required this.statusCode,
    required this.extra,
    required this.queryTags,
  });

  factory DouyinActivityTagsResponse.fromJson(Map<String, dynamic> json) {
    return DouyinActivityTagsResponse(
      statusCode: json['status_code'],
      extra: Extra.fromJson(json['extra']),
      queryTags: (json['query_tags'] as List)
          .map((e) => DouyinQueryTags.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
    'status_code': statusCode,
    'extra': extra.toJson(),
    'query_tags': queryTags.map((e) => e.toJson()).toList(),
  };
}

class DouyinActivityListResponse {
  final List<DouyinActivity> activityList;
  final Extra extra;
  final int statusCode;

  DouyinActivityListResponse({
    required this.activityList,
    required this.extra,
    required this.statusCode,
  });

  factory DouyinActivityListResponse.fromJson(Map<String, dynamic> json) {
    return DouyinActivityListResponse(
      activityList: (json['activity_list'] as List)
          .map((e) => DouyinActivity.fromJson(e))
          .toList(),
      extra: Extra.fromJson(json['extra']),
      statusCode: json['status_code'],
    );
  }

  Map<String, dynamic> toJson() => {
    'activity_list': activityList.map((e) => e.toJson()).toList(),
    'extra': extra.toJson(),
    'status_code': statusCode,
  };
}

class DouyinHotDataResponse {
  final Extra extra;
  final LogPb logPb;
  final List<DouyinHotSentence> sentences;
  final int statusCode;

  DouyinHotDataResponse({
    required this.extra,
    required this.logPb,
    required this.sentences,
    required this.statusCode,
  });

  factory DouyinHotDataResponse.fromJson(Map<String, dynamic> json) {
    return DouyinHotDataResponse(
      extra: Extra.fromJson(json['extra']),
      logPb: LogPb.fromJson(json['log_pb']),
      sentences: (json['sentences'] as List)
          .map((e) => DouyinHotSentence.fromJson(e))
          .toList(),
      statusCode: json['status_code'],
    );
  }

  Map<String, dynamic> toJson() => {
    'extra': extra.toJson(),
    'log_pb': logPb.toJson(),
    'sentences': sentences.map((e) => e.toJson()).toList(),
    'status_code': statusCode,
  };
}

class DouyinAllHotDataResponse {
  final Extra extra;
  final LogPb logPb;
  final List<DouyinHotSentence> allSentences;
  final int statusCode;

  DouyinAllHotDataResponse({
    required this.extra,
    required this.logPb,
    required this.allSentences,
    required this.statusCode,
  });

  factory DouyinAllHotDataResponse.fromJson(Map<String, dynamic> json) {
    return DouyinAllHotDataResponse(
      extra: Extra.fromJson(json['extra']),
      logPb: LogPb.fromJson(json['log_pb']),
      allSentences: (json['all_sentences'] as List)
          .map((e) => DouyinHotSentence.fromJson(e))
          .toList(),
      statusCode: json['status_code'],
    );
  }

  Map<String, dynamic> toJson() => {
    'extra': extra.toJson(),
    'log_pb': logPb.toJson(),
    'all_sentences': allSentences.map((e) => e.toJson()).toList(),
    'status_code': statusCode,
  };
}

class DouyinTopicsSugResponse {
  final List<TopicSug> sugList;
  final int statusCode;
  final String statusMsg;
  final String rid;
  final WordsQueryRecord wordsQueryRecord;
  final Extra extra;
  final LogPb logPb;

  DouyinTopicsSugResponse({
    required this.sugList,
    required this.statusCode,
    required this.statusMsg,
    required this.rid,
    required this.wordsQueryRecord,
    required this.extra,
    required this.logPb,
  });

  factory DouyinTopicsSugResponse.fromJson(Map<String, dynamic> json) {
    return DouyinTopicsSugResponse(
      sugList: (json['sug_list'] as List)
          .map((e) => TopicSug.fromJson(e))
          .toList(),
      statusCode: json['status_code'],
      statusMsg: json['status_msg'],
      rid: json['rid'],
      wordsQueryRecord: WordsQueryRecord.fromJson(json['words_query_record']),
      extra: Extra.fromJson(json['extra']),
      logPb: LogPb.fromJson(json['log_pb']),
    );
  }

  Map<String, dynamic> toJson() => {
    'sug_list': sugList.map((e) => e.toJson()).toList(),
    'status_code': statusCode,
    'status_msg': statusMsg,
    'rid': rid,
    'words_query_record': wordsQueryRecord.toJson(),
    'extra': extra.toJson(),
    'log_pb': logPb.toJson(),
  };
}

class DouyinLocationDataResponse {
  final String abParamExtra;
  final ContentTab contentTab;
  final List<dynamic> currentLocs;
  final Extra extra;
  final GuideInfo guideInfo;
  final bool hasKeyuserComboEntrance;
  final int hasMore;
  final bool hasPromoteAuth;
  final LogPb logPb;
  final int page;
  final dynamic poiActivity;
  final List<Poi> poiList;
  final List<int> poiSearchRange;
  final int postRole;
  final bool showFilterOption;
  final bool showGuide;
  final bool showNewMerchantGuide;
  final int statusCode;
  final String statusMsg;
  final dynamic userInterestCityCodeList;

  DouyinLocationDataResponse({
    required this.abParamExtra,
    required this.contentTab,
    required this.currentLocs,
    required this.extra,
    required this.guideInfo,
    required this.hasKeyuserComboEntrance,
    required this.hasMore,
    required this.hasPromoteAuth,
    required this.logPb,
    required this.page,
    this.poiActivity,
    required this.poiList,
    required this.poiSearchRange,
    required this.postRole,
    required this.showFilterOption,
    required this.showGuide,
    required this.showNewMerchantGuide,
    required this.statusCode,
    required this.statusMsg,
    this.userInterestCityCodeList,
  });

  factory DouyinLocationDataResponse.fromJson(Map<String, dynamic> json) {
    return DouyinLocationDataResponse(
      abParamExtra: json['ab_param_extra'],
      contentTab: ContentTab.fromJson(json['content_tab']),
      currentLocs: json['current_locs'],
      extra: Extra.fromJson(json['extra']),
      guideInfo: GuideInfo.fromJson(json['guide_info']),
      hasKeyuserComboEntrance: json['has_keyuser_combo_entrance'],
      hasMore: json['has_more'],
      hasPromoteAuth: json['has_promote_auth'],
      logPb: LogPb.fromJson(json['log_pb']),
      page: json['page'],
      poiActivity: json['poi_activity'],
      poiList: (json['poi_list'] as List).map((e) => Poi.fromJson(e)).toList(),
      poiSearchRange: List<int>.from(json['poi_search_range']),
      postRole: json['post_role'],
      showFilterOption: json['show_filter_option'],
      showGuide: json['show_guide'],
      showNewMerchantGuide: json['show_new_merchant_guide'],
      statusCode: json['status_code'],
      statusMsg: json['status_msg'],
      userInterestCityCodeList: json['user_interest_city_code_list'],
    );
  }

  Map<String, dynamic> toJson() => {
    'ab_param_extra': abParamExtra,
    'content_tab': contentTab.toJson(),
    'current_locs': currentLocs,
    'extra': extra.toJson(),
    'guide_info': guideInfo.toJson(),
    'has_keyuser_combo_entrance': hasKeyuserComboEntrance,
    'has_more': hasMore,
    'has_promote_auth': hasPromoteAuth,
    'log_pb': logPb.toJson(),
    'page': page,
    'poi_activity': poiActivity,
    'poi_list': poiList.map((e) => e.toJson()).toList(),
    'poi_search_range': poiSearchRange,
    'post_role': postRole,
    'show_filter_option': showFilterOption,
    'show_guide': showGuide,
    'show_new_merchant_guide': showNewMerchantGuide,
    'status_code': statusCode,
    'status_msg': statusMsg,
    'user_interest_city_code_list': userInterestCityCodeList,
  };
}

class DouyinUserListResponse {
  final List<dynamic> challengeList;
  final int cursor;
  final Extra extra;
  final bool hasMore;
  final int scene;
  final int statusCode;
  final List<DouyinUser> userList;

  DouyinUserListResponse({
    required this.challengeList,
    required this.cursor,
    required this.extra,
    required this.hasMore,
    required this.scene,
    required this.statusCode,
    required this.userList,
  });

  factory DouyinUserListResponse.fromJson(Map<String, dynamic> json) {
    return DouyinUserListResponse(
      challengeList: json['challenge_list'],
      cursor: json['cursor'],
      extra: Extra.fromJson(json['extra']),
      hasMore: json['has_more'],
      scene: json['scene'],
      statusCode: json['status_code'],
      userList: (json['user_list'] as List)
          .map((e) => DouyinUser.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
    'challenge_list': challengeList,
    'cursor': cursor,
    'extra': extra.toJson(),
    'has_more': hasMore,
    'scene': scene,
    'status_code': statusCode,
    'user_list': userList.map((e) => e.toJson()).toList(),
  };
}

class ContentTab {
  final int defaultTab;
  final List<int> tabList;

  ContentTab({
    required this.defaultTab,
    required this.tabList,
  });

  factory ContentTab.fromJson(Map<String, dynamic> json) {
    return ContentTab(
      defaultTab: json['default_tab'],
      tabList: List<int>.from(json['tab_list']),
    );
  }

  Map<String, dynamic> toJson() => {
    'default_tab': defaultTab,
    'tab_list': tabList,
  };
}

class GuideInfo {
  final String guideTextAfter;
  final String guideTextBefore;
  final String guideUrl;

  GuideInfo({
    required this.guideTextAfter,
    required this.guideTextBefore,
    required this.guideUrl,
  });

  factory GuideInfo.fromJson(Map<String, dynamic> json) {
    return GuideInfo(
      guideTextAfter: json['guide_text_after'],
      guideTextBefore: json['guide_text_before'],
      guideUrl: json['guide_url'],
    );
  }

  Map<String, dynamic> toJson() => {
    'guide_text_after': guideTextAfter,
    'guide_text_before': guideTextBefore,
    'guide_url': guideUrl,
  };
}