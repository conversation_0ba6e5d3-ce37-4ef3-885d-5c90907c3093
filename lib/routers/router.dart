import 'package:aitoearn_app/examples/douyin_location_example.dart';
import 'package:aitoearn_app/examples/douyin_poi_test_page.dart';
import 'package:aitoearn_app/examples/douyin_search_example.dart';
import 'package:aitoearn_app/pages/account/pages/account_get_cookie/index.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/binding.dart';
import 'package:aitoearn_app/pages/account/pages/account_official_auth/view.dart';
import 'package:aitoearn_app/pages/account/pages/account_test_login/test_login_page.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_controller.dart';
import 'package:aitoearn_app/pages/account_manager/account_list_page.dart';
import 'package:aitoearn_app/pages/account_manager/subPages/fingerprint_details/binding.dart';
import 'package:aitoearn_app/pages/account_manager/subPages/fingerprint_details/view.dart';
import 'package:aitoearn_app/pages/ai_tools/ai_to_image/ai_to_image_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/ai_to_image/ai_to_image_page.dart';
import 'package:aitoearn_app/pages/ai_tools/ai_to_video/ai_to_video_controller.dart';
import 'package:aitoearn_app/pages/ai_tools/ai_to_video/ai_to_video_page.dart';
import 'package:aitoearn_app/pages/balance/balance_controller.dart';
import 'package:aitoearn_app/pages/balance/balance_page.dart';
import 'package:aitoearn_app/pages/content_detail/content_detail_binding.dart';
import 'package:aitoearn_app/pages/content_detail/content_detail_page.dart';
import 'package:aitoearn_app/pages/content_detail/douyin_content_detail_binding.dart';
import 'package:aitoearn_app/pages/content_detail/douyin_content_detail_page.dart';
import 'package:aitoearn_app/pages/draft_and_media/bindings.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_boxes_page.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_bucket/draft_bucket_controller.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_bucket/draft_bucket_page.dart';
import 'package:aitoearn_app/pages/draft_and_media/draft_create_page.dart';
import 'package:aitoearn_app/pages/draft_and_media/media_bucket/media_bucket_controller.dart';
import 'package:aitoearn_app/pages/draft_and_media/media_bucket/media_bucket_page.dart';
import 'package:aitoearn_app/pages/home_page/home_page_bindings.dart';
import 'package:aitoearn_app/pages/home_page/home_page_view.dart';
import 'package:aitoearn_app/pages/interaction/bindings.dart';
import 'package:aitoearn_app/pages/interaction/index.dart';
import 'package:aitoearn_app/pages/interaction/interaction_select_page.dart';
import 'package:aitoearn_app/pages/login/login_email/login_email_controller.dart';
import 'package:aitoearn_app/pages/login/login_email/login_email_page.dart';
import 'package:aitoearn_app/pages/login/register_email/register_email_controller.dart';
import 'package:aitoearn_app/pages/login/register_email/register_email_page.dart';
import 'package:aitoearn_app/pages/login/reset_password/reset_password_controller.dart';
import 'package:aitoearn_app/pages/login/reset_password/reset_password_page.dart';
import 'package:aitoearn_app/pages/points/points_controller.dart';
import 'package:aitoearn_app/pages/points/points_page.dart';
import 'package:aitoearn_app/pages/publish/hotspot/pages/hot_title_more/binding.dart';
import 'package:aitoearn_app/pages/publish/hotspot/pages/hot_title_more/view.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:aitoearn_app/pages/publish/publish_home/binding.dart';
import 'package:aitoearn_app/pages/publish/publish_home/view.dart';
import 'package:aitoearn_app/pages/publish/video_publish_page.dart';
import 'package:aitoearn_app/pages/setting/setting_page_controller.dart';
import 'package:aitoearn_app/pages/setting/setting_page_view.dart';
import 'package:aitoearn_app/pages/subscription/subscription_controller.dart';
import 'package:aitoearn_app/pages/subscription/subscription_page.dart';
import 'package:aitoearn_app/pages/webview/web_controller.dart';
import 'package:aitoearn_app/pages/webview/web_page.dart';
import 'package:aitoearn_app/pages/welcome/welcome_controller.dart';
import 'package:aitoearn_app/pages/welcome/welcome_page.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_demo_entry.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_test_entry.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_test_page.dart';
import 'package:get/get.dart';

abstract class AppRouter {
  /// 欢迎页
  static const String welcomePath = '/welcome';

  /// 登录页【邮箱】
  static const String loginEmailPath = '/loginEmailPath';

  /// 注册页【邮箱】
  static const String registerEmailPath = '/registerEmailPath';

  /// 忘记密码
  static const String resetPasswordPath = '/resetPassword';

  // 首页
  static const String homePath = '/home';

  // 底部导航栏页面 --------------------------------
  /// 发布页 - 首页
  static const String publishHomePath = '/publishHome';

  /// 热点 - 爆款标题 - 查看更多
  static const String hotTitleMorePath = '/hotTitleMore';

  /// 发布
  static const String publishPath = '/publish';

  /// 我的
  static const String userPath = '/userPath';

  /// 设置
  static const String settingPath = '/settingPath';

  /// 订阅
  static const String subscriptionPath = '/subscriptionPath';

  /// 草稿箱
  static const String draftBoxesPath = '/draftBoxes';

  /// 创建草稿
  static const String draftCreatePath = '/draft_create';

  /// 互动页面
  static const String interactionPath = '/interaction';

  /// 互动选择页面
  static const String interactionSelectPath = '/interaction_select';

  /// 内容详情页
  static const String contentDetailPath = '/content/detail';

  // 抖音内容详情页面路径
  static const String douyinContentDetailPath = '/content/detail/douyin';

  // 账户 ---------------------------------
  /// 官方授权账户
  static const String accountGetCookiePath = '/account/get-cookie';

  /// 账户cookie获取
  static const String accountOfficialAuthPath =
      '/account/accountOfficialAuthPath';

  /// 查看浏览器指纹页面
  static const String fingerprintDetailsPath = '/fingerprintDetailsPath';

  ///抖音测试
  static const String douyinTestPage = '/douyinTestPage';
  static const String douyinTestPage1 = '/douyinTestPage1';
  static const String douyinContentTestPage = '/douyinContentTestPage';

  /// 抖音POI测试页面
  static const String douyinPoiTestPage = '/douyinPoiTestPage';

  /// 抖音位置示例页面
  static const String douyinLocationExamplePage = '/douyinLocationExamplePage';

  /// 抖音搜索示例页面
  static const String douyinSearchExamplePage = '/douyinSearchExamplePage';

  /// 视频发布页面
  static const String videoPublishPath = '/video_publish';

  static const String kwaiExamplePath = '/kwaiExamplePath';

  /// 测试登录页面路径
  static const String testLoginPath = '/account/test-login';

  /// Media Bucket页面路径
  static const String mediaBucketPath = '/media/bucket';

  /// 草稿箱详情页面路径
  static const String draftBucketPath = '/draft/bucket';

  /// AI图片生成页面
  static const String aiToImagePath = '/ai_tools/ai_to_image';

  /// 账号管理页面路径
  static const String accountManagerPath = '/account/manager';

  static const String pointsPath = '/mine/points';

  static const String balancePath = '/mine/balance';

  static const String webPath = '/webPage';

  static const String aiToVideo = '/aiToVideo';

  static final pages = [
    GetPage(
      name: welcomePath,
      page: () => const WelcomePage(),
      binding: BindingsBuilder(() {
        Get.put(WelcomeController());
      }),
    ),
    GetPage(
      name: loginEmailPath,
      page: () => const LoginEmailPage(),
      binding: BindingsBuilder(() {
        Get.put(LoginEmailController());
      }),
    ),
    GetPage(
      name: publishHomePath,
      page: () => const PublishHomePage(),
      binding: PublishHomeBinding(),
    ),
    GetPage(
      name: hotTitleMorePath,
      page: () => const HotTitleMorePage(),
      binding: HotTitleMoreBinding(),
    ),
    GetPage(
      name: registerEmailPath,
      page: () => const RegisterEmailPage(),
      binding: BindingsBuilder(() {
        Get.put(RegisterEmailController());
      }),
    ),
    GetPage(
      name: resetPasswordPath,
      page: () => const ResetPasswordPage(),
      binding: BindingsBuilder(() {
        Get.put(ResetPasswordController());
      }),
    ),
    GetPage(
      name: homePath,
      page: () => const HomePage(),
      binding: HomePageBinding(),
    ),
    GetPage(
      name: settingPath,
      page: () => const SettingPage(),
      binding: BindingsBuilder(() {
        Get.put(SettingController());
      }),
    ),
    GetPage(
      name: subscriptionPath,
      page: () => const SubscriptionPage(),
      binding: BindingsBuilder(() {
        Get.put(SubscriptionController());
      }),
    ),
    GetPage(
      name: accountOfficialAuthPath,
      page: () => const AccountOfficialAuthPage(),
      binding: AccountOfficialAuthBinding(),
    ),
    GetPage(
      name: fingerprintDetailsPath,
      page: () => const FingerprintDetailsPage(),
      binding: FingerprintDetailsBinding(),
    ),
    GetPage(
      name: accountGetCookiePath,
      page: () => const AccountGetCookiePage(),
      binding: BindingsBuilder(() {
        Get.put(AccountGetCookieLogic());
      }),
    ),
    GetPage(
      name: interactionPath,
      page: () => const InteractionPage(),
      binding: InteractionBinding(),
    ),
    GetPage(
      name: interactionSelectPath,
      page: () => const InteractionSelectPage(),
      binding: InteractionBinding(),
    ),
    GetPage(
      name: contentDetailPath,
      page: () => const ContentDetailPage(),
      binding: ContentDetailBinding(),
    ),
    GetPage(
      name: douyinContentDetailPath,
      page: () => const DouyinContentDetailPage(),
      binding: DouyinContentDetailBinding(),
    ),
    GetPage(
      name: douyinTestPage,
      page: () {
        return const DouyinTestPage();
      },
    ),
    GetPage(
      name: douyinTestPage1,
      page: () {
        return const DouyinDemoEntry();
      },
    ),
    GetPage(
      name: douyinContentTestPage,
      page: () {
        return DouyinTestEntry.getDouyinContentTestPage();
      },
    ),
    GetPage(name: douyinPoiTestPage, page: () => const DouyinPoiTestPage()),
    GetPage(
      name: douyinLocationExamplePage,
      page: () => const DouyinLocationExamplePage(),
    ),
    GetPage(
      name: douyinSearchExamplePage,
      page: () => const DouyinSearchExamplePage(),
    ),
    // 测试登录页面
    GetPage(
      name: mediaBucketPath,
      page: () => const MediaBucketPage(),
      binding: BindingsBuilder(() {
        Get.put(MediaBucketController());
      }),
    ),
    GetPage(name: testLoginPath, page: () => const TestLoginPage()),
    // 发布页面
    GetPage(
      name: publishPath,
      page: () => const VideoPublishPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => PublishLogic());
      }),
    ),
    // 草稿箱页面
    GetPage(name: draftBoxesPath, page: () => const DraftBoxesPage()),
    // 创建草稿页面
    GetPage(
      name: draftCreatePath,
      page: () => const DraftCreatePage(),
      binding: DraftCreateBinding(),
    ),
    // 视频发布页面（从草稿箱进入）
    GetPage(
      name: videoPublishPath,
      page: () => const VideoPublishPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut(() => PublishLogic());
      }),
    ),
    GetPage(
      name: draftBucketPath,
      page: () => const DraftBucketPage(),
      binding: BindingsBuilder(() {
        Get.put(DraftBucketController());
      }),
    ),
    GetPage(
      name: aiToImagePath,
      page: () => const AiToImagePage(),
      binding: BindingsBuilder(() {
        Get.put(AiToImageController());
      }),
    ),
    // 添加账号管理页面路由
    GetPage(
      name: accountManagerPath,
      page: () => const AccountListPage(),
      binding: BindingsBuilder(() {
        Get.put(AccountListController());
      }),
    ),
    GetPage(
      name: pointsPath,
      page: () => const PointsPage(),
      binding: BindingsBuilder(() {
        Get.put(PointsController());
      }),
    ),
    GetPage(
      name: balancePath,
      page: () => const BalancePage(),
      binding: BindingsBuilder(() {
        Get.put(BalanceController());
      }),
    ),
    GetPage(
      name: webPath,
      page: () => const WebPage(),
      binding: BindingsBuilder(() {
        Get.put(WebController());
      }),
    ),
    GetPage(
      name: aiToVideo,
      page: () => const AiToVideoPage(),
      binding: BindingsBuilder(() {
        Get.put(AiToVideoController());
      }),
    ),
  ];
}
