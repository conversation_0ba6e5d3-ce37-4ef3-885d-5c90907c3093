
// Google 配置
import 'dart:io';

class GoogleSignInConfig {
  // clientId：前端用来发起授权
  static const androidClientId = '288959646156-bsuoo4oeqkuu16u7ir6md6vda6dfi10r.apps.googleusercontent.com';
  static const iosClientId = '288959646156-0poi0j5t8fme6h0t1atg5r7vitgmvee7.apps.googleusercontent.com';
  // 桌面端
  static const desktopClientId = '288959646156-1n4017uliafpd9rfkq83mcj2mhctpm0u.apps.googleusercontent.com';
  static const desktopSecret = '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';
  // webServerClientId：后端用来处理授权
  static const webServerClientId = '288959646156-4t9gm2t8uloqc4fkimipper2ihska0um.apps.googleusercontent.com';


  /// 获取平台对应的 clientId 和 serverClientId
  static (String, String) getGoogleSignInParams() {
    if (Platform.isAndroid) {
      return (
      GoogleSignInConfig.androidClientId,
      GoogleSignInConfig.webServerClientId,
      );
    }
    if (Platform.isIOS) {
      return (
      GoogleSignInConfig.iosClientId,
      GoogleSignInConfig.webServerClientId,
      );
    }
    if (Platform.isMacOS || Platform.isWindows || Platform.isLinux) {
      return (
      GoogleSignInConfig.desktopClientId,
      GoogleSignInConfig.desktopSecret,
      );
    }
    return ('', '');
  }
}