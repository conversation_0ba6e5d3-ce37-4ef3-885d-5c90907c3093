import 'package:flutter/material.dart';

class AppColors {
  static const Color white = Color(0xFFFFFFFF); // 成功提示颜色
  // 渐变
  static const Gradient blueAndPurple = LinearGradient(
    colors: [Colors.blue, Colors.purple],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );

  static const Color mediaBgFrame = Color(0xFF222A2F);

  static const Color primaryColor = Color(0xFF7468E4);
  static const Color primaryHighLightColor = Color(0xFF9C27B0);
  static const Color backgroundColor = Color(0xFFF0F5FF);
  static const Color cardColor = Color(0xFFFFFFFF);
  static const Color textColor = Color(0xFF333333);
  static const Color textSecondColor = Color(0xFF666666);
  static const Color textHintColor = Color(0xFF999999);
  static const Color dividerColor = Color(0xFFEEEEEE);
  static const Color deepDividerColor = Color(0xFFCCCCCC);
  static const Color halfTabColor = Color(0x33FFFFFF);

  // 错误色
  static const Color errorColor = Color(0xFFA72519); // 错误提示颜色
  static const Color errorBgColor = Color(0xFFFFC2C2); // 错误提示颜色

  // 成功色
  static const Color successColor = Color(0xFF4CD964); // 成功提示颜色
  static const Color successBgColor = Color(0xFFE4FFE9); // 成功提示颜色

  // 警告色
  static const Color warningColor = Color(0xFFFFA500); // 警告提示颜色
  static const Color warningBgColor = Color(0xFFFFF3E0); // 警告提示颜色

  static const Color noVipColor = Color(0xFF4A667B); // 不是VIP的卡片主题色
  static const Color vipColor = Color(0xFFD3B58A); // 不是VIP的卡片主题色
  static const Color vipTextColor = Color(0xFF6C5533); // 不是VIP的卡片主题色

  static const Color transparentColor = Color(0x00000000);
}
