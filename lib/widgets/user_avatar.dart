import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/network_image_widget.dart';
import 'package:flutter/material.dart';

/// 图片预览
/// ImagePreviewViewer.show
///
class UserAvatar extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final Color backgroundColor;
  final Widget? errorWidget;
  final String? heroTag;
  final VoidCallback? onTap;

  const UserAvatar({
    required this.imageUrl,
    super.key,
    this.size = 60,
    this.backgroundColor = AppColors.textHintColor,
    this.errorWidget,
    this.heroTag,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final hasValidImage = imageUrl != null && imageUrl!.isNotEmpty;

    final error =
        errorWidget ?? Icon(Icons.person, color: Colors.white, size: size / 2);

    final Widget avatarImage =
        hasValidImage
            ? NetworkImageWidget(
              imageUrl!,
              fit: BoxFit.cover,
              placeholder: _buildLoadingPlaceholder(),
              errorWidget: error,
              isCircle: true,
              heroTag: heroTag,
              onTap: onTap,
            )
            : GestureDetector(onTap: onTap, child: error);

    return ClipOval(
      child: Container(
        width: size,
        height: size,
        color: backgroundColor,
        child: avatarImage,
      ),
    );
  }

  Widget _buildLoadingPlaceholder() {
    return Center(
      child: SizedBox(
        width: size / 2.5,
        height: size / 2.5,
        child: const CircularProgressIndicator(strokeWidth: 2),
      ),
    );
  }
}
