import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:flutter/material.dart';

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final Color color;
  final Color textColor;
  final Widget? icon;
  final Gradient? gradient;
  final double? width;
  final double? height;
  final double? fontSize;
  final double? borderRadius;
  final double? borderWidth;
  final Color? borderColor;
  final bool enable;
  final bool showLoading;

  const CustomButton({
    required this.onPressed,
    super.key,
    this.text = '【todo】',
    this.color = Colors.white,
    this.textColor = Colors.black,
    this.icon,
    this.gradient,
    this.width,
    this.height,
    this.fontSize,
    this.borderRadius,
    this.borderWidth,
    this.borderColor,
    this.enable = true,
    this.showLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
          onPressed: enable ? onPressed : null,
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size(width ?? 0, height ?? 0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 6),
            ),
          ),
          child:
              showLoading
                  ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      text.toNormalText(
                        fontSize: fontSize ?? 16,
                        color: textColor,
                        fontWeight: FontWeight.bold,
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: textColor,
                          backgroundColor: textColor.withAlpha(66),
                        ),
                      ),
                    ],
                  )
                  : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (icon != null) ...[icon!, Gaps.hGap5],
                      text.toNormalText(
                        fontSize: fontSize ?? 16,
                        color: textColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ],
                  ),
        )
        .height(height ?? 45)
        .decorated(
          color: color.withOpacity(enable ? 1.0 : 0.6),
          gradient: gradient?.withOpacity(enable ? 1.0 : 0.6),
          border: Border.all(
            color: borderColor ?? Colors.transparent,
            width: borderWidth ?? 0,
          ),
          borderRadius: BorderRadius.circular(borderRadius ?? 6),
        )
        .width(double.infinity);
  }
}
