import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 加载更多指示器组件
class LoadMoreIndicator extends StatelessWidget {
  /// 是否正在加载
  final bool isLoading;

  /// 是否还有更多数据
  final bool hasMore;

  /// 是否显示组件（当数据为null时不显示）
  final bool visible;

  /// 容器高度
  final double height;

  /// 文本样式
  final TextStyle? textStyle;

  /// 指示器颜色
  final Color? indicatorColor;

  /// 指示器大小
  final double indicatorSize;

  /// 指示器线宽
  final double strokeWidth;

  /// 加载中图标与文本间距
  final double spacing;

  const LoadMoreIndicator({
    required this.isLoading,
    required this.hasMore,
    super.key,
    this.visible = true,
    this.height = 50,
    this.textStyle,
    this.indicatorColor,
    this.indicatorSize = 16,
    this.strokeWidth = 2,
    this.spacing = 8,
  });

  @override
  Widget build(BuildContext context) {
    if (!visible) {
      return const SizedBox.shrink();
    }

    return Container(
      height: height,
      alignment: Alignment.center,
      child: _buildContent(context),
    );
  }

  Widget _buildContent(BuildContext context) {
    final defaultTextStyle = TextStyle(color: Colors.grey[600], fontSize: 14);

    if (isLoading) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: indicatorSize,
            height: indicatorSize,
            child: CircularProgressIndicator(
              strokeWidth: strokeWidth,
              valueColor:
                  indicatorColor != null
                      ? AlwaysStoppedAnimation<Color>(indicatorColor!)
                      : null,
            ),
          ),
          SizedBox(width: spacing),
          Text('${'loading'.tr}...', style: textStyle ?? defaultTextStyle),
        ],
      );
    }

    if (!hasMore) {
      return Text('noMoreData'.tr, style: textStyle ?? defaultTextStyle);
    }

    // 当有更多数据但未加载时，显示空白，等待滚动触发
    return const SizedBox.shrink();
  }
}
