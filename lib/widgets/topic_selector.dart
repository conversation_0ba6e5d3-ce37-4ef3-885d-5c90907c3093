import 'dart:async';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_types.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 话题数据模型
class TopicItem {
  final String id;
  final String name;
  final int viewCount;

  TopicItem({
    required this.id,
    required this.name,
    required this.viewCount,
  });

  factory TopicItem.fromJson(Map<String, dynamic> json) {
    return TopicItem(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      viewCount: json['view_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'view_count': viewCount,
    };
  }
}

/// 话题选择器组件
class TopicSelector extends StatefulWidget {
  /// 平台类型
  final String platform;
  
  /// 账号访问令牌
  final String accessToken;
  
  /// 最大选择数量
  final int maxCount;
  
  /// 已选择的话题
  final List<String> selectedTopics;
  
  /// 选择变化回调
  final Function(List<String> topics) onChanged;
  
  /// 提示文本
  final String? tips;

  const TopicSelector({
    Key? key,
    required this.platform,
    required this.accessToken,
    required this.maxCount,
    required this.selectedTopics,
    required this.onChanged,
    this.tips,
  }) : super(key: key);

  @override
  State<TopicSelector> createState() => _TopicSelectorState();
}

class _TopicSelectorState extends State<TopicSelector> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  
  List<TopicItem> _searchResults = [];
  bool _isLoading = false;
  Timer? _debounceTimer;
  
  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }
  
  void _onSearchChanged() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      final keyword = _searchController.text.trim();
      if (keyword.isNotEmpty) {
        _searchTopics(keyword);
      } else {
        setState(() {
          _searchResults.clear();
        });
      }
    });
  }
  
  Future<void> _searchTopics(String keyword) async {
    if (widget.accessToken.isEmpty) {
      Get.snackbar('提示', '账号访问令牌无效');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      List<TopicItem> topics = [];

      // 根据平台调用不同的话题搜索服务
      switch (widget.platform) {
        case 'douyin':
          final douyinService = DouyinService();
          final response = await douyinService.getTopics(widget.accessToken, keyword);

          if (response.statusCode == 0 && response.sugList != null) {
            topics = response.sugList!.map((sug) => TopicItem(
              id: sug.cid,
              name: sug.chaName,
              viewCount: sug.viewCount,
            )).toList();
          }
          break;

        case 'xhs':
          // TODO: 实现小红书话题搜索
          LoggerUtil.w('小红书话题搜索功能待实现');
          break;

        case 'kwai':
        case 'ks':
          // TODO: 实现快手话题搜索
          LoggerUtil.w('快手话题搜索功能待实现');
          break;

        case 'wx-sph':
          // TODO: 实现微信视频号话题搜索
          LoggerUtil.w('微信视频号话题搜索功能待实现');
          break;

        default:
          LoggerUtil.w('不支持的平台: ${widget.platform}');
          break;
      }

      setState(() {
        _searchResults = topics;
      });

    } catch (e) {
      LoggerUtil.e('搜索话题失败: $e');
      setState(() {
        _searchResults.clear();
      });
      Get.snackbar('搜索失败', '网络错误，请稍后重试');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  void _addTopic(String topicName) {
    if (widget.selectedTopics.contains(topicName)) {
      Get.snackbar('提示', '话题已存在');
      return;
    }
    
    if (widget.selectedTopics.length >= widget.maxCount) {
      Get.snackbar('提示', '最多只能选择${widget.maxCount}个话题');
      return;
    }
    
    final newTopics = List<String>.from(widget.selectedTopics);
    newTopics.add(topicName);
    widget.onChanged(newTopics);
    
    // 清空搜索
    _searchController.clear();
    _searchFocusNode.unfocus();
  }
  
  void _removeTopic(String topicName) {
    final newTopics = List<String>.from(widget.selectedTopics);
    newTopics.remove(topicName);
    widget.onChanged(newTopics);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白区域时让输入框失去焦点
        _searchFocusNode.unfocus();
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        // 标题和提示
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              const Text(
                '话题',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF262626),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFFF0F0F0),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  widget.tips ?? '您可以添加${widget.maxCount}个话题',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF8C8C8C),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // 搜索框
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFFAFAFA),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: const Color(0xFFD9D9D9)),
          ),
          child: Column(
            children: [
              // 输入框
              TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                style: const TextStyle(fontSize: 14),
                decoration: const InputDecoration(
                  hintText: '输入关键字搜索话题',
                  hintStyle: TextStyle(color: Color(0xFFBFBFBF)),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  suffixIcon: Icon(Icons.search, color: Color(0xFF8C8C8C)),
                ),
              ),
              
              // 搜索结果
              if (_isLoading) ...[
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                ),
              ] else if (_searchResults.isNotEmpty) ...[
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _searchResults.length,
                    itemBuilder: (context, index) {
                      final topic = _searchResults[index];
                      final isSelected = widget.selectedTopics.contains(topic.name);
                      
                      return InkWell(
                        onTap: isSelected ? null : () => _addTopic(topic.name),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color: isSelected ? const Color(0xFFF0F0F0) : null,
                            border: index > 0 ? const Border(
                              top: BorderSide(color: Color(0xFFF0F0F0)),
                            ) : null,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      topic.name,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: isSelected 
                                            ? const Color(0xFF8C8C8C) 
                                            : const Color(0xFF262626),
                                      ),
                                    ),
                                    if (topic.viewCount > 0) ...[
                                      const SizedBox(height: 2),
                                      Text(
                                        '${_formatViewCount(topic.viewCount)}次浏览',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF8C8C8C),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                              if (isSelected) ...[
                                const Icon(
                                  Icons.check,
                                  color: Color(0xFF52C41A),
                                  size: 16,
                                ),
                              ],
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
        
        // 已选择的话题
        if (widget.selectedTopics.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: widget.selectedTopics.map((topic) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF1890FF),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '#$topic',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () => _removeTopic(topic),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 14,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
        ],
      ),
    );
  }
  
  String _formatViewCount(int count) {
    if (count >= 100000000) {
      return '${(count / 100000000).toStringAsFixed(1)}亿';
    } else if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    } else {
      return count.toString();
    }
  }
}
