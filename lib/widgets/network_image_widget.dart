import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// 图片预览
/// ImagePreviewViewer.show
///
class NetworkImageWidget extends StatelessWidget {
  final String? imageUrl;
  final ImageProvider? imageProvider;
  final double? width;
  final double? height;
  final BoxFit fit;
  final bool needLoading;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;
  final bool isCircle;
  final Map<String, String>? headers;
  final String? heroTag;
  final VoidCallback? onTap;

  const NetworkImageWidget(
      this.imageUrl, {
        super.key,
        this.imageProvider,
        this.width,
        this.height,
        this.fit = BoxFit.cover,
        this.needLoading = true,
        this.placeholder,
        this.errorWidget,
        this.borderRadius,
        this.isCircle = false,
        this.headers,
        this.heroTag,
        this.onTap,
      });

  Map<String, String>? _getHeaders() {
    if (headers != null) {
      return headers;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final placeHolderWidget = placeholder ??
        (needLoading
            ? const Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        )
            : const SizedBox());

    final error = errorWidget ??
        Container(
          color: Colors.grey[300],
          alignment: Alignment.center,
          child: const Icon(Icons.broken_image, color: Colors.white),
        );

    if (imageProvider != null) {
      Widget imgWidget = Image(
        image: imageProvider!,
        width: width,
        height: height,
        fit: fit,
      );

      if (isCircle) {
        imgWidget = ClipOval(child: imgWidget);
      } else {
        imgWidget = ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.zero,
          child: imgWidget,
        );
      }

      return _wrapGesture(child: _wrapHero(child: imgWidget));
    }

    if (imageUrl == null || imageUrl!.isEmpty || imageUrl!.toLowerCase().endsWith('.mp4')) {
      Widget errorWidget = error;
      if (isCircle) {
        errorWidget = ClipOval(child: error);
      } else {
        errorWidget = ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.zero,
          child: error,
        );
      }
      return _wrapGesture(child: _wrapHero(child: errorWidget));
    }

    if (imageUrl!.toLowerCase().endsWith('.svg')) {
      Widget svgWidget = SvgPicture.network(
        imageUrl!,
        width: width,
        height: height,
        fit: fit,
        placeholderBuilder: (_) => placeHolderWidget,
        errorBuilder: (_, __, ___) => error,
        headers: _getHeaders(),
      );

      if (isCircle) {
        svgWidget = ClipOval(child: svgWidget);
      } else {
        svgWidget = ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.zero,
          child: svgWidget,
        );
      }

      return _wrapGesture(child: _wrapHero(child: svgWidget));
    }

    final image = ExtendedImage.network(
      imageUrl!,
      width: width,
      height: height,
      fit: fit,
      cache: true,
      cacheMaxAge: const Duration(days: 7),
      headers: _getHeaders(),
      enableLoadState: true,
      borderRadius: isCircle ? null : borderRadius,
      shape: isCircle ? BoxShape.circle : BoxShape.rectangle,
      loadStateChanged: (ExtendedImageState state) {
        switch (state.extendedImageLoadState) {
          case LoadState.loading:
            return placeHolderWidget;
          case LoadState.completed:
            return state.completedWidget;
          case LoadState.failed:
            return error;
        }
      },
    );

    return _wrapGesture(child: _wrapHero(child: image));
  }

  Widget _wrapGesture({required Widget child}) {
    return GestureDetector(
      onTap: onTap,
      child: child,
    );
  }

  Widget _wrapHero({required Widget child}) {
    if (heroTag == null || heroTag!.isEmpty) {
      return child;
    }
    return Hero(tag: heroTag!, child: child);
  }
}
