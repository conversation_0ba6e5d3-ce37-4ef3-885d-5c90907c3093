import 'package:aitoearn_app/res/app_colors.dart';
import 'package:flutter/material.dart';

extension NormalTextWidget on String {
  Text toNormalText({
    TextStyle? style,
    Color color = AppColors.textColor,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.w400,
    TextAlign textAlign = TextAlign.start,
    TextDecoration decoration = TextDecoration.none,
    int? maxLines,
    TextOverflow overflow = TextOverflow.clip,
  }) {
    return Text(
      this,
      style:
          style ??
          TextStyle(
            color: color,
            fontSize: fontSize,
            fontWeight: fontWeight,
            decoration: decoration,
          ),
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
    );
  }
}
