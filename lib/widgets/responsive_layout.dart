import 'package:aitoearn_app/config/logger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 判断桌面的最大宽度
const double kDesktopMinWidth = 650.0;

/// 响应式布局
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? desktop;

  const ResponsiveLayout({required this.mobile, this.desktop, super.key});

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    if (desktop != null && width >= kDesktopMinWidth) {
      return desktop!;
    }
    return mobile;
  }
}

/// 是否是移动端
isMobileMode() {
  double width = MediaQuery.of(Get.context!).size.width;
  return width < kDesktopMinWidth;
}

/// 是否是桌面端
isDesktopMode() {
  double width = Get.width;
  return width >= kDesktopMinWidth;
}