import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomInputBox extends StatefulWidget {
  final String? title;
  final TextStyle? titleStyle;
  final String hintText;
  final ValueChanged<String>? onChanged;
  final FocusNode? focusNode;
  final TextEditingController controller;
  final bool obscureText;
  final TextInputType? textInputType;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onSubmitted;

  const CustomInputBox({
    required this.controller,
    super.key,
    this.title,
    this.titleStyle,
    this.hintText = '请输入内容',
    this.onChanged,
    this.focusNode,
    this.obscureText = false,
    this.textInputType, this.textInputAction, this.onSubmitted,
  });

  @override
  State<CustomInputBox> createState() => _CustomInputBoxState();
}

class _CustomInputBoxState extends State<CustomInputBox> {
  late bool _obscureText;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;
  }

  void toggleObscureText() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  Widget _buildTextField() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: AppColors.cardColor,
        borderRadius: BorderRadius.circular(6.0),
        border: Border.all(color: AppColors.dividerColor, width: 1),
      ),
      child: TextField(
        controller: widget.controller,
        focusNode: widget.focusNode,
        keyboardType: widget.textInputType,
        obscureText: _obscureText,
        style: const TextStyle(fontSize: 16.0, color: AppColors.textColor),
        onChanged: widget.onChanged,
        textInputAction: widget.textInputAction,
        onSubmitted: widget.onSubmitted,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: const TextStyle(color: AppColors.textHintColor),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 16.0),
          suffixIcon:
              widget.obscureText
                  ? IconButton(
                    icon: Icon(
                      _obscureText ? Icons.visibility_off : Icons.visibility,
                      color: AppColors.textHintColor,
                    ),
                    onPressed: toggleObscureText,
                  )
                  : null,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.title != null && widget.title!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.title!
              .toNormalText(style: widget.titleStyle)
              .marginOnly(bottom: 8),
          _buildTextField(),
        ],
      );
    }

    return _buildTextField();
  }
}
