import 'package:aitoearn_app/widgets/calendar/state.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:table_calendar/table_calendar.dart';

class CalendarLogic extends GetxController {
  final CalendarState state = CalendarState();

  // 根据当前格式切换到上一个时间段
  void goToPrevious() {
    if (state.calendarFormat.value == CalendarFormat.week) {
      // 周格式：切换到上一周
      final previousWeek = state.focusedDay.value.subtract(
        const Duration(days: 7),
      );
      state.focusedDay.value = previousWeek;
    } else {
      // 月格式：切换到上一个月
      final previousMonth = DateTime(
        state.focusedDay.value.year,
        state.focusedDay.value.month - 1,
        state.focusedDay.value.day,
      );
      state.focusedDay.value = previousMonth;
    }
  }

  // 根据当前格式切换到下一个时间段
  void goToNext() {
    if (state.calendarFormat.value == CalendarFormat.week) {
      // 周格式：切换到下一周
      final nextWeek = state.focusedDay.value.add(const Duration(days: 7));
      state.focusedDay.value = nextWeek;
    } else {
      // 月格式：切换到下一个月
      final nextMonth = DateTime(
        state.focusedDay.value.year,
        state.focusedDay.value.month + 1,
        state.focusedDay.value.day,
      );
      state.focusedDay.value = nextMonth;
    }
  }

  /// 设置时间
  void setFocusedDay(DateTime date) {
    state.focusedDay.value = date;
    state.selectedDay.value = date;
  }

  /// 处理水平滑动（左右切换月份）
  void horizontalDragEndHandle(DragEndDetails details) {
    const double sensitivity = 50;
    if (details.primaryVelocity != null) {
      if (details.primaryVelocity! > sensitivity) {
        // 向右滑动 - 上一个月
        goToPrevious();
      } else if (details.primaryVelocity! < -sensitivity) {
        // 向左滑动 - 下一个月
        goToNext();
      }
    }
  }

  /// 处理垂直滑动（上下切换格式）
  verticalDragEndHandle(DragEndDetails details) {
    const double sensitivity = 50; // 滑动敏感度
    if (details.primaryVelocity != null &&
        details.primaryVelocity!.abs() > sensitivity) {
      normalChange(); // 执行格式切换
    }
  }

  /// 日历 周/月格式切换
  normalChange() {
    if (state.allowSwipeToChangeFormat.value == false) {
      return;
    }

    if (state.calendarFormat.value == CalendarFormat.week) {
      state.calendarFormat.value = CalendarFormat.month;
      return;
    } else if (state.calendarFormat.value == CalendarFormat.month) {
      state.calendarFormat.value = CalendarFormat.week;
      return;
    } else {
      state.calendarFormat.value = CalendarFormat.week;
      return;
    }
  }
}
