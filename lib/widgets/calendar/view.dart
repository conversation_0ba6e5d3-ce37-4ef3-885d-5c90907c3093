import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/res/gaps.dart';
import 'package:aitoearn_app/widgets/calendar/logic.dart';
import 'package:aitoearn_app/widgets/calendar/state.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:ducafe_ui_core/ducafe_ui_core.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart' as tdesign;

typedef ChangeCalendarFormat = void Function();

class CalendarComponent extends StatefulWidget {
  const CalendarComponent({
    required this.onDaySelected,
    required this.calendarBuilders,
    required this.bottomWidget,
    required this.topRightWidget,
    super.key,
    this.allowSwipeToChangeFormat = true,
    this.defaultCalendarFormat = CalendarFormat.month,
    this.calendarBottomExtensionWidget,
    this.calendarBottomPadding = 5,
    this.hideCalendar = false,
    this.calendarBottomLineColor,
    this.yearMonthColor,
    this.calendarBackgroundImage,
    this.availableDays,
    this.daysOfWeekColor = AppColors.textColor,
  });

  /// 切换选择的天数事件
  final OnDaySelected onDaySelected;

  /// 是否允许滑动切换格式
  final bool allowSwipeToChangeFormat;

  /// 默认的日历格式
  final CalendarFormat defaultCalendarFormat;

  /// 自定义单元格
  final CalendarBuilders calendarBuilders;

  /// 底部内容
  final Widget bottomWidget;

  /// 顶部右侧显示内容
  final Widget topRightWidget;

  /// 日历顶部扩展
  final Widget? calendarBottomExtensionWidget;

  /// 日历下边距
  final double calendarBottomPadding;

  /// 日历下方线的颜色
  final Color? calendarBottomLineColor;

  /// 日历左上角 年/月 的颜色
  final Color? yearMonthColor;

  /// 日历背景图片路径
  final String? calendarBackgroundImage;

  /// 可用的时间列表，为空时所有时间可用
  final List<DateTime>? availableDays;

  /// 是否隐藏日历
  final bool hideCalendar;

  /// 周颜色
  final Color daysOfWeekColor;

  @override
  State<CalendarComponent> createState() => _CalendarComponentState();
}

class _CalendarComponentState extends State<CalendarComponent> {
  late final CalendarLogic logic;
  late final CalendarState state;

  @override
  void initState() {
    super.initState();
    final tag = widget.key.toString();
    logic = Get.put(CalendarLogic(), tag: tag);
    state = Get.find<CalendarLogic>(tag: tag).state;

    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          state.calendarFormat.value = widget.defaultCalendarFormat;
          state.allowSwipeToChangeFormat.value =
              widget.allowSwipeToChangeFormat;
        }
      });
    }
  }

  Widget _arrowLeft(Function? onArrowTap) {
    if (onArrowTap != null) {
      return Icon(
        Icons.arrow_drop_down_sharp,
        color: widget.yearMonthColor ?? Colors.black,
      ).marginOnly(right: 10).gestures(onTap: () => onArrowTap());
    }
    return const SizedBox();
  }

  Widget _yearMonth(DateTime data) {
    final text = DateFormat('yyyy/MM', Get.locale?.languageCode).format(data);
    return text.toNormalText(
      fontWeight: FontWeight.bold,
      fontSize: 20,
      color: widget.yearMonthColor ?? Colors.black,
    );
  }

  /// 日历操作扩展
  calendarOptionsExpand(Widget? child) {
    return GestureDetector(
      // 处理水平滑动（左右切换月份）
      onHorizontalDragEnd: logic.horizontalDragEndHandle,
      // 处理垂直滑动（上下切换格式）
      onVerticalDragEnd: logic.verticalDragEndHandle,
      child: child ?? const SizedBox(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 顶部日历
        Stack(
          children: [
            // 背景图覆盖整个屏幕，包括状态栏
            widget.calendarBackgroundImage != null
                ? Positioned.fill(
                  child: ExtendedImage.asset(
                    widget.calendarBackgroundImage!,
                    fit: BoxFit.cover,
                    filterQuality: FilterQuality.low,
                  ),
                )
                : const SizedBox(),

            SafeArea(
              child: Column(
                children: [
                  Gaps.vGap10,

                  Row(
                    children: [
                      widget.hideCalendar
                          ? const SizedBox()
                          : Obx(() => _yearMonth(state.focusedDay.value)),
                      widget.hideCalendar
                          ? const SizedBox()
                          : _arrowLeft(
                            () => {
                              tdesign.TDPicker.showDatePicker(
                                context,
                                title: '选择时间',
                                onConfirm: (selected) {
                                  if (selected.length >= 2) {
                                    final newDate = DateTime(
                                      selected['year'] as int,
                                      selected['month'] as int,
                                    );
                                    state.focusedDay.value = newDate;
                                    Navigator.of(context).pop();
                                  }
                                },
                                dateStart: [2000, 01],
                                // 当前年份往后推5年
                                dateEnd: [DateTime.now().year + 5, 12],
                                initialDate: [
                                  state.focusedDay.value.year,
                                  state.focusedDay.value.month + 1,
                                ],
                                useDay: false,
                              ),
                            },
                          ),
                      const Spacer(),
                      widget.topRightWidget,
                    ],
                  ).marginSymmetric(horizontal: 10),
                  Gaps.vGap15,

                  widget.hideCalendar
                      ? const SizedBox(height: 20)
                      : Obx(
                        () => TableCalendar(
                          locale: Get.locale?.languageCode,
                          headerVisible: false,
                          calendarFormat: state.calendarFormat.value,
                          formatAnimationDuration: const Duration(
                            milliseconds: 300,
                          ),
                          formatAnimationCurve: Curves.easeInOut,
                          focusedDay: state.focusedDay.value,
                          daysOfWeekStyle: DaysOfWeekStyle(
                            weekdayStyle: TextStyle(
                              fontSize: Dimens.font_sp12,
                              color: widget.daysOfWeekColor,
                            ),
                            weekendStyle: TextStyle(
                              fontSize: Dimens.font_sp12,
                              color: widget.daysOfWeekColor,
                            ),
                          ),
                          onPageChanged: (focusedDay) {
                            state.focusedDay.value = focusedDay;
                          },
                          selectedDayPredicate: (day) {
                            // 添加选中日期的判断
                            return isSameDay(state.selectedDay.value, day);
                          },
                          // 控制哪些日期可以选择
                          enabledDayPredicate: (day) {
                            // 如果 availableDays 为空，所有日期都可用
                            if (widget.availableDays == null ||
                                widget.availableDays!.isEmpty) {
                              return true;
                            }

                            // 检查当前日期是否在可用日期列表中
                            return widget.availableDays!.any(
                              (availableDay) => isSameDay(availableDay, day),
                            );
                          },
                          firstDay: DateTime.utc(2020, 1, 1),
                          lastDay: DateTime.utc(2030, 1, 1),
                          rowHeight: 40,
                          onFormatChanged: (_) => logic.normalChange(),
                          onDaySelected: (selectedDay, focusedDay) {
                            state.focusedDay.value = focusedDay;
                            state.selectedDay.value = selectedDay;
                            widget.onDaySelected(selectedDay, focusedDay);
                          },
                          calendarBuilders: CalendarBuilders(
                            todayBuilder: widget.calendarBuilders.todayBuilder,
                            selectedBuilder:
                                widget.calendarBuilders.selectedBuilder,
                            // dowBuilder: _buildWeekday,
                            defaultBuilder:
                                widget.calendarBuilders.defaultBuilder,
                            outsideBuilder:
                                widget.calendarBuilders.outsideBuilder,
                            disabledBuilder:
                                widget.calendarBuilders.disabledBuilder,
                            holidayBuilder:
                                widget.calendarBuilders.holidayBuilder,
                            withinRangeBuilder:
                                widget.calendarBuilders.withinRangeBuilder,
                            rangeStartBuilder:
                                widget.calendarBuilders.rangeStartBuilder,
                            rangeEndBuilder:
                                widget.calendarBuilders.rangeEndBuilder,
                          ),
                        ),
                      ),

                  widget.hideCalendar
                      ? const SizedBox()
                      : SizedBox(
                            height: 10,
                            width: double.infinity,
                            child:
                                const SizedBox(height: 4, width: 40)
                                    .decorated(
                                      color:
                                          widget.calendarBottomLineColor ??
                                          Colors.grey.withValues(alpha: 0.3),
                                      borderRadius: BorderRadius.circular(20),
                                    )
                                    .unconstrained(),
                          )
                          .decorated(color: Colors.transparent)
                          .gestures(onTap: () => logic.normalChange()),

                  SizedBox(height: widget.calendarBottomPadding),
                ],
              ),
            ),

            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: calendarOptionsExpand(
                widget.calendarBottomExtensionWidget,
              ),
            ),
          ],
        ),

        // 底部内容
        Expanded(
          child: SizedBox(
            width: double.infinity,
            child: calendarOptionsExpand(widget.bottomWidget),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    Get.delete<CalendarLogic>(tag: widget.key.toString());
    super.dispose();
  }
}
