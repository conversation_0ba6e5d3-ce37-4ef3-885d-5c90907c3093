import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 定时发布时间选择器组件
class ScheduleTimePicker extends StatefulWidget {
  /// 初始选择的日期时间
  final DateTime? initialDateTime;
  
  /// 确认选择时间的回调
  final Function(DateTime) onTimeSelected;
  
  /// 取消选择的回调
  final VoidCallback? onCancel;
  
  const ScheduleTimePicker({
    super.key,
    this.initialDateTime,
    required this.onTimeSelected,
    this.onCancel,
  });
  
  /// 显示定时发布时间选择器底部弹窗
  static Future<void> show({
    required BuildContext context,
    DateTime? initialDateTime,
    required Function(DateTime) onTimeSelected,
    VoidCallback? onCancel,
  }) async {
    // 默认选择当前时间30分钟之后
    final DateTime now = DateTime.now();
    final DateTime defaultDateTime = now.add(const Duration(minutes: 30));
    
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SafeArea(
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: ScheduleTimePicker(
            initialDateTime: initialDateTime ?? defaultDateTime,
            onTimeSelected: onTimeSelected,
            onCancel: onCancel,
          ),
        ),
      ),
    );
  }
  
  @override
  State<ScheduleTimePicker> createState() => _ScheduleTimePickerState();
}

class _ScheduleTimePickerState extends State<ScheduleTimePicker> {
  /// 选择的日期
  late DateTime _selectedDate;
  
  /// 选择的小时
  late int _selectedHour;
  
  /// 选择的分钟
  late int _selectedMinute;
  
  /// 日期选择器控制器
  late FixedExtentScrollController _dateController;
  
  /// 小时选择器控制器
  late FixedExtentScrollController _hourController;
  
  /// 分钟选择器控制器
  late FixedExtentScrollController _minuteController;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化选择的日期和时间
    final initialDateTime = widget.initialDateTime ?? DateTime.now().add(const Duration(minutes: 30));
    _selectedDate = DateTime(initialDateTime.year, initialDateTime.month, initialDateTime.day);
    _selectedHour = initialDateTime.hour;
    _selectedMinute = initialDateTime.minute;
    
    // 初始化控制器
    _dateController = FixedExtentScrollController(initialItem: _getInitialDateIndex());
    _hourController = FixedExtentScrollController(initialItem: _selectedHour);
    _minuteController = FixedExtentScrollController(initialItem: _selectedMinute);
  }
  
  @override
  void dispose() {
    // 释放控制器
    _dateController.dispose();
    _hourController.dispose();
    _minuteController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 标题栏
        _buildHeader(),
        
        // 分割线
        const Divider(height: 0.5, thickness: 0.5, color: Color(0xFFEEEEEE)),
        
        // 时间选择器
        _buildTimePicker(),
        
        // 底部提示文本
        Container(
          width: double.infinity,
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Text(
            'video_publish.schedule.note'.tr,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
  
  // 构建标题栏
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: 56,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            onPressed: () {
              // 取消选择
              Navigator.pop(context);
              if (widget.onCancel != null) {
                widget.onCancel!();
              }
            },
            child: Text(
              'video_publish.schedule.cancel'.tr,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
          Text(
            'video_publish.schedule.title'.tr,
            style: const TextStyle(
              fontSize: 17,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          TextButton(
            onPressed: () {
              // 确认选择时间
              final selectedTime = DateTime(
                _selectedDate.year,
                _selectedDate.month,
                _selectedDate.day,
                _selectedHour,
                _selectedMinute,
              );
              widget.onTimeSelected(selectedTime);
              Navigator.pop(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: Text(
              'video_publish.schedule.confirm'.tr,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // 构建时间选择器
  Widget _buildTimePicker() {
    // 获取当前选择的日期索引
    final int selectedDateIndex = _getInitialDateIndex();
    
    return Container(
      height: 260,
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        children: [
          // 日期选择列表
          Expanded(
            flex: 1,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 中间高亮区域
                Positioned.fill(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        height: 52,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          border: Border(
                            top: BorderSide(color: Colors.grey.shade200, width: 1),
                            bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 日期选择滚轮
                ListWheelScrollView.useDelegate(
                  controller: _dateController,
                  itemExtent: 52,
                  physics: const FixedExtentScrollPhysics(),
                  perspective: 0.008,
                  diameterRatio: 2.0,
                  onSelectedItemChanged: (index) {
                    final date = DateTime.now().add(Duration(days: index));
                    setState(() {
                      _selectedDate = date;
                    });
                  },
                  childDelegate: ListWheelChildBuilderDelegate(
                    builder: (context, index) {
                      if (index < 0 || index >= 30) return null;
                      final date = DateTime.now().add(Duration(days: index));
                      
                      // 日期格式化
                      String dayText;
                      if (index == 0) {
                        dayText = 'video_publish.schedule.today'.tr;
                      } else if (index == 1) {
                        dayText = 'video_publish.schedule.tomorrow'.tr;
                      } else if (index == 2) {
                        dayText = 'video_publish.schedule.day_after'.tr;
                      } else {
                        dayText = 'video_publish.schedule.month_day'.trParams({
                          'month': date.month.toString(),
                          'day': date.day.toString()
                        });
                      }
                      
                      final isSelected = index == selectedDateIndex;
                      
                      return Center(
                        child: Text(
                          dayText,
                          style: TextStyle(
                            fontSize: isSelected ? 18 : 16,
                            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                            color: isSelected ? Colors.black : Colors.grey.shade600,
                          ),
                        ),
                      );
                    },
                    childCount: 30,
                  ),
                ),
              ],
            ),
          ),
          
          // 右侧时间选择区域
          Expanded(
            flex: 1,
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(
                  left: BorderSide(color: Color(0xFFEEEEEE), width: 1),
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 时间选择器
                  Row(
                    children: [
                      // 小时选择器
                      Expanded(
                        child: _buildTimePickerWheel(
                          controller: _hourController,
                          initialItem: _selectedHour, 
                          itemCount: 24,
                          onChanged: (index) {
                            setState(() {
                              _selectedHour = index;
                            });
                          },
                        ),
                      ),
                      
                      // 分钟选择器
                      Expanded(
                        child: _buildTimePickerWheel(
                          controller: _minuteController,
                          initialItem: _selectedMinute, 
                          itemCount: 60,
                          onChanged: (index) {
                            setState(() {
                              _selectedMinute = index;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  // 中间的冒号
                  Center(
                    child: Text(
                      ":",
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // 获取初始日期在列表中的索引
  int _getInitialDateIndex() {
    final now = DateTime.now();
    return _selectedDate.difference(DateTime(now.year, now.month, now.day)).inDays;
  }
  
  // 构建时间选择轮组件
  Widget _buildTimePickerWheel({
    required FixedExtentScrollController controller,
    required int initialItem,
    required int itemCount,
    required Function(int) onChanged,
  }) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 中间高亮区域
        Positioned.fill(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 52,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  border: Border(
                    top: BorderSide(color: Colors.grey.shade200, width: 1),
                    bottom: BorderSide(color: Colors.grey.shade200, width: 1),
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // 时间轮
        ListWheelScrollView.useDelegate(
          controller: controller,
          itemExtent: 52,
          physics: const FixedExtentScrollPhysics(),
          perspective: 0.008,
          diameterRatio: 2.0,
          onSelectedItemChanged: onChanged,
          childDelegate: ListWheelChildBuilderDelegate(
            builder: (context, index) {
              if (index < 0 || index >= itemCount) return null;
              final isSelected = index == initialItem;
              
              return Center(
                child: Text(
                  index.toString().padLeft(2, '0'),
                  style: TextStyle(
                    fontSize: isSelected ? 22 : 18,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    color: isSelected ? Colors.black : Colors.grey.shade400,
                  ),
                ),
              );
            },
            childCount: itemCount,
          ),
        ),
      ],
    );
  }
} 