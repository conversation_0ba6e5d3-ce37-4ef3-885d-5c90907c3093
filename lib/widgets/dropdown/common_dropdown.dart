import 'package:aitoearn_app/res/app_colors.dart';
import 'package:aitoearn_app/res/dimens.dart';
import 'package:aitoearn_app/widgets/normal_text_widget.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CommonDropdown extends StatelessWidget {
  final List<String>? items;
  final String? value;
  final ValueChanged<String?>? onChanged;
  final bool isLoading;
  final double height;
  final EdgeInsets padding;
  final Alignment selectedItemAlignment;
  final String hintText;

  const CommonDropdown({
    required this.items,
    required this.value,
    required this.onChanged,
    super.key,
    this.hintText = '请选择',
    this.isLoading = false,
    this.height = 40,
    this.padding = const EdgeInsets.symmetric(horizontal: 10),
    this.selectedItemAlignment = Alignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: isLoading,
      child: DropdownButtonHideUnderline(
        child: DropdownButton2<String>(
          items:
              items
                  ?.map(
                    (String item) => DropdownMenuItem<String>(
                      value: item,
                      child: Container(
                        child: item.toNormalText(
                          color:
                              value == item
                                  ? AppColors.primaryColor
                                  : Colors.black,
                        ),
                      ),
                    ),
                  )
                  .toList(),
          value: value,
          onChanged: onChanged,
          menuItemStyleData: MenuItemStyleData(
            height: height,
            padding: padding,
          ),
          hint: Text(
            hintText,
            style: TextStyle(
              fontSize: Dimens.font_sp14,
              color: Theme.of(context).hintColor,
            ),
          ),
          selectedItemBuilder: (BuildContext context) {
            return items?.map<Widget>((String item) {
                  return Container(
                    alignment: selectedItemAlignment,
                    child: item.toNormalText(
                      color: Colors.black,
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList() ??
                [];
          },
        ),
      ),
    );
  }
}
