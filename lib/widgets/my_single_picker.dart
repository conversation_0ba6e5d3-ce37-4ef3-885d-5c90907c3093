import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

/// 数据选择器
class MySinglePicker extends StatefulWidget {
  const MySinglePicker({
    required this.title,
    required this.data,
    required this.pickerTitle,
    super.key,
    this.defaultValue,
    this.onConfirm,
    this.divider = const TDDivider(margin: EdgeInsets.only(left: 16)),
    this.cellStyle,
  });

  final String pickerTitle;
  final String title;
  final List<List<String>> data;
  final String? defaultValue;
  final void Function(String)? onConfirm;
  final Widget divider;
  final TDCellStyle? cellStyle;
  @override
  State<MySinglePicker> createState() => _MySinglePickerState();
}

class _MySinglePickerState extends State<MySinglePicker> {
  String selectedValue = '';

  @override
  void initState() {
    super.initState();
    selectedValue = widget.defaultValue ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TDCell(
          arrow: true,
          title: widget.title,
          note: selectedValue,
          style: widget.cellStyle,
          onClick: (call) {
            TDPicker.showMultiPicker(
              context,
              title: widget.pickerTitle,
              onConfirm: (selected) {
                setState(() {
                  selectedValue = widget.data[0][selected[0]];
                });
                Navigator.of(context).pop();
                if (widget.onConfirm != null) {
                  widget.onConfirm!(selectedValue);
                }
              },
              data: widget.data,
            );
          },
        ),
        widget.divider,
      ],
    );
  }
}
