import 'dart:io';

import 'package:aitoearn_app/api/ai/ai_api.dart';
import 'package:aitoearn_app/api/media_material/media_material_api.dart';
import 'package:aitoearn_app/config/app_config.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/enums/ai_create_type.dart';
import 'package:aitoearn_app/pages/publish/logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// AI生成按钮组件
class AiCreateButton extends StatefulWidget {
  /// AI生成类型
  final AiCreateType type;

  /// 提示信息
  final String? tips;

  /// 视频文件路径
  final String? videoFilePath;

  /// 已上传的视频URL（可选）
  final String? uploadedVideoUrl;

  /// 生成完成回调
  final Function(String text) onAiCreateFinish;

  /// 字数限制
  final int maxLength;

  const AiCreateButton({
    Key? key,
    required this.type,
    this.tips,
    this.videoFilePath,
    this.uploadedVideoUrl,
    required this.onAiCreateFinish,
    required this.maxLength,
  }) : super(key: key);

  @override
  State<AiCreateButton> createState() => _AiCreateButtonState();
}

class _AiCreateButtonState extends State<AiCreateButton> {
  bool _isLoading = false;

  // 视频缓存，防止重复上传
  static final Map<String, String> _videoCache = {};

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _isLoading ? null : _handleAiGenerate,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: _isLoading ? Colors.grey[300] : const Color(0xFF1890FF),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_isLoading) ...[
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
                ),
              ),
            ] else ...[
              const Icon(Icons.auto_awesome, color: Colors.white, size: 16),
            ],
            const SizedBox(width: 4),
            Text(
              widget.type.displayText,
              style: TextStyle(
                color: _isLoading ? Colors.grey[600] : Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (widget.tips != null) ...[
              const SizedBox(width: 4),
              Tooltip(
                message: widget.tips!,
                child: Icon(
                  Icons.help_outline,
                  color: _isLoading ? Colors.grey[600] : Colors.white,
                  size: 14,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 处理AI生成
  Future<void> _handleAiGenerate() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      String? videoUrl;

      // 优先使用已上传的视频URL
      if (widget.uploadedVideoUrl != null && widget.uploadedVideoUrl!.isNotEmpty) {
        videoUrl = widget.uploadedVideoUrl;
        LoggerUtil.i('使用已上传的视频URL: $videoUrl');
      } else {
        // 检查视频文件
        if (widget.videoFilePath == null || widget.videoFilePath!.isEmpty) {
          Get.snackbar('提示', '您必须上传一个视频！');
          return;
        }

        // 检查文件是否存在
        final videoFile = File(widget.videoFilePath!);
        if (!await videoFile.exists()) {
          Get.snackbar('提示', '视频文件不存在！');
          return;
        }

        // 生成视频缓存key
        final videoStat = await videoFile.stat();
        final videoKey = '${videoStat.size}_${videoFile.path.split('/').last}';

        videoUrl = _videoCache[videoKey];

        // 如果缓存中没有，则上传视频
        if (videoUrl == null) {
          LoggerUtil.i('上传视频文件: ${widget.videoFilePath}');
          final uploadResult = await _uploadFile(widget.videoFilePath!);

          if (uploadResult != null && uploadResult['code'] == 0) {
            final fileName = uploadResult['data']['key'] as String?;
            if (fileName != null) {
              // 构建完整的文件URL
              videoUrl = '${AppConfig.ossHost}/$fileName';
              _videoCache[videoKey] = videoUrl;

              // 保存上传后的URL到PublishLogic
              try {
                final publishLogic = Get.find<PublishLogic>();
                publishLogic.setUploadedVideoUrl(videoUrl);
              } catch (e) {
                LoggerUtil.w('未找到PublishLogic实例: $e');
              }
            } else {
              throw Exception('上传失败：未获取到文件名');
            }
          } else {
            throw Exception('上传失败：${uploadResult?['message'] ?? '网络繁忙，请稍后重试'}');
          }
        }
      }

      // 调用AI生成接口
      LoggerUtil.i(
        '调用AI生成接口: type=${widget.type.value}, max=${widget.maxLength}',
      );
      final result = await generateVideoAiTitleApi(
        url: videoUrl??'',
        type: widget.type.value,
        max: widget.maxLength,
      );

      if (result != null && result.isNotEmpty) {
        widget.onAiCreateFinish(result);
        Get.snackbar(
          '成功',
          '${widget.type.displayText}生成完成',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green[100],
          colorText: Colors.green[800],
        );
      } else {
        throw Exception('生成失败：返回内容为空');
      }
    } catch (e) {
      LoggerUtil.e('AI生成失败: $e');
      Get.snackbar(
        '生成失败',
        e.toString().contains('Exception:')
            ? e.toString().replaceFirst('Exception: ', '')
            : '网络繁忙，请稍后重试',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red[100],
        colorText: Colors.red[800],
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 上传文件
  Future<Map?> _uploadFile(String filePath) async {
    try {
      final file = File(filePath);
      final fileName = file.path.split('/').last;

      final response = await updateOSSFile(file: file, fileName: fileName);

      return response;
    } catch (e) {
      LoggerUtil.e('文件上传失败: $e');
      return null;
    }
  }

  /// 清除视频缓存
  static void clearVideoCache() {
    _videoCache.clear();
  }
}
