import 'package:flutter/material.dart';

/// 通用加载组件
class CommonLoading extends StatelessWidget {
  /// 加载文本
  final String? loadingText;

  /// 加载图标大小
  final double size;

  /// 加载图标颜色
  final Color? color;

  /// 加载文本样式
  final TextStyle? textStyle;

  /// 构造函数
  const CommonLoading({
    super.key,
    this.loadingText,
    this.size = 24.0,
    this.color,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? Theme.of(context).primaryColor,
            ),
          ),
        ),
        if (loadingText != null) ...[
          const SizedBox(height: 12),
          Text(
            loadingText!,
            style:
                textStyle ?? TextStyle(fontSize: 14, color: Colors.grey[700]),
          ),
        ],
      ],
    );
  }
}
