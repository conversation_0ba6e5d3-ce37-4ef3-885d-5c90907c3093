import 'dart:convert';

import 'package:aitoearn_app/api/platform_service.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/store/account_persistent_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 抖音搜索示例页面
class DouyinSearchExamplePage extends StatefulWidget {
  const DouyinSearchExamplePage({Key? key}) : super(key: key);

  @override
  State<DouyinSearchExamplePage> createState() => _DouyinSearchExamplePageState();
}

class _DouyinSearchExamplePageState extends State<DouyinSearchExamplePage> {
  final TextEditingController _keywordController = TextEditingController();
  final PlatformService _platformService = PlatformService();
  
  bool _isLoading = false;
  Map<String, dynamic>? _searchResult;
  String? _errorMessage;
  
  @override
  void initState() {
    super.initState();
    _keywordController.text = '啊';
  }
  
  @override
  void dispose() {
    _keywordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('抖音搜索示例'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _keywordController,
                    decoration: const InputDecoration(
                      hintText: '输入搜索关键词',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _searchDouyin,
                  child: const Text('搜索'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(8),
                color: Colors.red[100],
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              )
            else if (_searchResult != null)
              Expanded(
                child: SingleChildScrollView(
                  child: _buildSearchResultInfo(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResultInfo() {
    if (_searchResult == null) {
      return const Text('无搜索结果');
    }

    try {
      final data = _searchResult!;
      
      // 检查状态码
      final statusCode = data['status_code'] ?? 0;
      final statusMsg = data['status_msg'] ?? '';
      
      // 尝试获取搜索结果
      List<dynamic> resultList = [];
      
      // 检查data字段
      if (data['data'] != null && data['data'] is List<dynamic>) {
        resultList = data['data'] as List<dynamic>;
      }
      
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('状态码: $statusCode'),
          Text('状态消息: $statusMsg'),
          Text('结果数量: ${resultList.length}'),
          const SizedBox(height: 8),
          if (resultList.isNotEmpty) ...[
            const Text('搜索结果列表:'),
            const SizedBox(height: 4),
            ...resultList.take(5).map((item) {
              // 尝试提取每个结果的基本信息
              final type = item['type'] ?? 'unknown';
              String title = 'N/A';
              String desc = 'N/A';
              String author = 'N/A';
              String imageUrl = '';

              if (type == 1) {
                // 视频类型
                final aweme = item['aweme_info'];
                if (aweme != null) {
                  title = aweme['desc'] ?? 'N/A';
                  
                  // 获取作者信息
                  if (aweme['author'] != null) {
                    author = aweme['author']['nickname'] ?? 'N/A';
                  }
                  
                  // 获取统计信息
                  if (aweme['statistics'] != null) {
                    final diggCount = aweme['statistics']['digg_count'] ?? 0;
                    final commentCount = aweme['statistics']['comment_count'] ?? 0;
                    desc = '点赞: $diggCount, 评论: $commentCount';
                  }
                  
                  // 获取封面图
                  if (aweme['video'] != null && aweme['video']['cover'] != null) {
                    final coverInfo = aweme['video']['cover'];
                    final urlList = coverInfo['url_list'] as List?;
                    if (urlList != null && urlList.isNotEmpty) {
                      imageUrl = urlList[0] as String? ?? '';
                    }
                  }
                }
              } else if (type == 'user') {
                final user = item['user_info'];
                if (user != null) {
                  title = user['nickname'] ?? 'N/A';
                  desc = user['signature'] ?? 'N/A';
                }
              }

              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('类型: ${type == 1 ? "视频" : type}'),
                      Text('标题: $title'),
                      Text('作者: $author'),
                      Text('描述: $desc'),
                      if (imageUrl.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: Image.network(
                            imageUrl,
                            height: 120,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                height: 120,
                                width: double.infinity,
                                color: Colors.grey[300],
                                child: const Center(
                                  child: Text('图片加载失败'),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            }).toList(),
            if (resultList.length > 5)
              Text('... 还有 ${resultList.length - 5} 个结果未显示'),
          ],
        ],
      );
    } catch (e) {
      return Text('解析搜索结果出错: $e');
    }
  }

  Future<void> _searchDouyin() async {
    if (_keywordController.text.isEmpty) {
      setState(() {
        _errorMessage = '请输入搜索关键词';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 获取当前选中的抖音账号
      final accounts = await _platformService.getDouyinAccounts();
      if (accounts.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = '未找到抖音账号，请先登录';
        });
        return;
      }

      // 使用第一个账号的refreshToken
      final account = accounts.first;
      if (account.refreshToken == null || account.refreshToken!.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = '账号Token为空，请重新登录抖音账号';
        });
        return;
      }

      LoggerUtil.d('[DEV] 使用账号refreshToken，长度: ${account.refreshToken!.length}');

      // 调用搜索方法
      await _searchDouyinWithToken(_keywordController.text, account.refreshToken!);
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '搜索出错: $e';
      });
    }
  }

  // 使用指定的refreshToken执行搜索
  Future<void> _searchDouyinWithToken(String keyword, String token) async {
    setState(() {
      _isLoading = true;
      _searchResult = null;
      _errorMessage = null;
    });

    try {
      LoggerUtil.d('[DEV] 使用传入的refreshToken，长度: ${token.length}');

      final response = await _platformService.searchDouyin(
        keyword: keyword,
        cookie: token,
        offset: 0,
        count: 10,
      );

      setState(() {
        _isLoading = false;
        if (response.success && response.data != null) {
          _searchResult = response.data;
          _errorMessage = null;
        } else {
          _searchResult = null;
          _errorMessage = '搜索失败: ${response.msg}';
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _searchResult = null;
        _errorMessage = '搜索异常: $e';
      });
    }
  }
} 