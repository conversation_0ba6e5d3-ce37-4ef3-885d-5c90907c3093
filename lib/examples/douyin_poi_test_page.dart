import 'package:flutter/material.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_poi_model.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

/// 抖音POI接口测试页面
class DouyinPoiTestPage extends StatefulWidget {
  const DouyinPoiTestPage({Key? key}) : super(key: key);

  @override
  _DouyinPoiTestPageState createState() => _DouyinPoiTestPageState();
}

class _DouyinPoiTestPageState extends State<DouyinPoiTestPage> {
  // 控制器
  final TextEditingController _cookieController = TextEditingController(text: '''
UIFID_TEMP=4be83ecefa579a300714166db9e569bafd8689fc248d1e190e384db8df203b81571f7705667f37a95c1e20ff95dec1181200c212cac84fd18cfb5dba2f6bcf76fd6091b5874a9013def348d9c674ffdca9a47475da2d5a5168a62e9a28e486ee2c551f9a3963b295157b1784875c3a98; hevc_supported=true; bd_ticket_guard_client_web_domain=2; n_mh=WXDFgyRZo-A83IROJ4huF0IAZ2Wigc0QXVPAyoakz14; is_staff_user=false; _bd_ticket_crypt_doamin=2; __security_mc_1_s_sdk_cert_key=2d2c7ea3-4142-a930; __security_server_data_status=1; passport_mfa_token=Cjd7JCdgwefbtSTuz4ueWqpRbneUNzIHvqLzbMcL6WFpyewRHJA1gVwOu4jflqYOVhJxGZ0MbfTXGkoKPAAAAAAAAAAAAABO6P%2FiLYdMzUiNC0c59jUQc2mBP7zuGTfApNiR0qvvEiUYpE4VuvoHwR3CKY3Oai8VfBDAte8NGPax0WwgAiIBAzgunxw%3D; d_ticket=073cafc7d590b600ee6e536985cd600df47e6; is_dash_user=1; SelfTabRedDotControl=%5B%5D; enter_pc_once=1; x-web-secsdk-uid=33ea7df6-2be5-42ed-9053-6fd31a9b7eb3; _tea_utm_cache_2906=undefined; csrf_session_id=d16336dd4becb43d9afc2005029c4bf2; my_rd=2; download_guide=%223%2F20250612%2F0%22; FORCE_LOGIN=%7B%22videoConsumedRemainSeconds%22%3A180%7D; __security_mc_1_s_sdk_sign_data_key_sso=5de4ca5f-400a-91c7; WallpaperGuide=%7B%22showTime%22%3A1749787073861%2C%22closeTime%22%3A0%2C%22showCount%22%3A1%2C%22cursor1%22%3A10%2C%22cursor2%22%3A2%7D; uuid_for_login=1749798178306_iyxov22u7ui; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Afalse%2C%22volume%22%3A0.985%7D; SEARCH_RESULT_LIST_TYPE=%22single%22; publish_badge_show_info=%220%2C0%2C0%2C1749891389289%22; home_can_add_dy_2_desktop=%220%22; IsDouyinActive=false; FOLLOW_NUMBER_YELLOW_POINT_INFO=%22MS4wLjABAAAAEJNd-1MzK7UGuS8o7FKQDnyDkRwWt5rFtr4f9mwYdVNeZPTuOOVwTOXciEWDchkX%2F1749916800000%2F0%2F0%2F1749913184620%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A1440%2C%5C%22screen_height%5C%22%3A900%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A18%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A10%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A50%7D%22; __security_mc_1_s_sdk_crypt_sdk=63992fe0-4d76-b8b2; gfkadpd=2906,33638; passport_csrf_token=1c69cd34d7604b0c58a575a107db99e5; passport_csrf_token_default=1c69cd34d7604b0c58a575a107db99e5; cc_request_t=4ae41da7-3ebc-462d-a9d4-37869fa5488a; passport_assist_user=CkE_q8G61DLj9BzozyO4vlIsiDi9um6XyWsKbhADdGWTlAhzP0ikStvn_D9PKmGU4XRYA2ST1Bzqr35ZL30OtP0lyhpKCjwAAAAAAAAAAAAATx-Dqlfqf4-1quKkYRD6DfmCCI_AXZbfouP8loXh4sd34d6HpF6OMaaGpP07Lc3cCPAQvKL0DRiJr9ZUIAEiAQPMgJob; sid_guard=103bcae02999d67c4ce3edb883110c3f%7C1750083119%7C5184000%7CFri%2C+15-Aug-2025+14%3A11%3A59+GMT; uid_tt=81aa1c1b773ecafcbd1c82daa81943c9; uid_tt_ss=81aa1c1b773ecafcbd1c82daa81943c9; sid_tt=103bcae02999d67c4ce3edb883110c3f; sessionid=103bcae02999d67c4ce3edb883110c3f; sessionid_ss=103bcae02999d67c4ce3edb883110c3f; sid_ucp_v1=1.0.0-KDI2M2RlYmUyODRkYThiMzU1NzM1ZDlhMWFjOWZmMzc1NGNkYmQ5NTgKIQjj5tDarM3eAhCvzMDCBhjaFiAMMKDssq0GOAdA9AdIBBoCbGYiIDEwM2JjYWUwMjk5OWQ2N2M0Y2UzZWRiODgzMTEwYzNm; ssid_ucp_v1=1.0.0-KDI2M2RlYmUyODRkYThiMzU1NzM1ZDlhMWFjOWZmMzc1NGNkYmQ5NTgKIQjj5tDarM3eAhCvzMDCBhjaFiAMMKDssq0GOAdA9AdIBBoCbGYiIDEwM2JjYWUwMjk5OWQ2N2M0Y2UzZWRiODgzMTEwYzNm; _bd_ticket_crypt_cookie=6c75f04fdde50f659983c74c3b4adb77; __security_mc_1_s_sdk_sign_data_key_web_protect=63348633-49e3-8211; odin_tt=3f8dadf58b6e826aee6a4afe3a48eedd13845c7647331629e1b2933b7b83e0a1ba1a9565598c72ca30a8568668b9dc2d3815125b049ef4c99b035eef9ca78d05; passport_fe_beating_status=true; strategyABtestKey=%************.63%22; ttwid=1%7C__HBNypI3MOKrlvymHZFWFmIF4N9X9B-vK-Dvcv_YdY%7C1750127622%7Cc6dc025d23abec86fc5efef30d389877ce260b86b4a4a5e790a7c277a6518dca; biz_trace_id=4b493c57; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCTVYxRnFKY0EzYzZXSEFrbTlpMjFqcUxtTjdmaEFZM2VlbzRDUVREalBqcUczdWhZOEx2UTRUREdtd2wzMmJFR0d5QXFYRnY3QnBBZWR3WjJyQVZqSHM9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D''');
  final TextEditingController _proxyController = TextEditingController();
  final TextEditingController _keywordController = TextEditingController();
  
  // 抖音服务
  final DouyinService _douyinService = DouyinService();
  
  // 状态
  bool _isLoading = false;
  String _statusText = '请先设置Cookie并获取位置';
  List<DouyinPoiItem> _poiList = [];
  
  // 位置信息
  double _latitude = 0.0;
  double _longitude = 0.0;

  @override
  void dispose() {
    _cookieController.dispose();
    _proxyController.dispose();
    _keywordController.dispose();
    super.dispose();
  }

  // 获取位置权限
  Future<void> _requestLocationPermission() async {
    setState(() {
      _isLoading = true;
      _statusText = '正在请求位置权限...';
    });
    
    try {
      final permissionStatus = await Permission.location.request();
      
      if (permissionStatus.isGranted) {
        await _getCurrentLocation();
      } else {
        setState(() {
          _isLoading = false;
          _statusText = '无法获取位置权限，请在系统设置中授予';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusText = '请求位置权限失败: $e';
      });
      LoggerUtil.e('请求位置权限失败: $e');
    }
  }

  // 获取当前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
      _statusText = '正在获取当前位置...';
    });
    
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      
      setState(() {
        _latitude = position.latitude;
        _longitude = position.longitude;
        _isLoading = false;
        _statusText = '位置已获取: 纬度=${_latitude.toStringAsFixed(6)}, 经度=${_longitude.toStringAsFixed(6)}';
      });
      
      LoggerUtil.i('【位置测试】获取当前位置成功: 纬度=${_latitude.toStringAsFixed(6)}, 经度=${_longitude.toStringAsFixed(6)}');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusText = '获取位置失败: $e';
      });
      LoggerUtil.e('获取位置失败: $e');
    }
  }

  // 搜索POI位置
  Future<void> _searchPoi() async {
    // 检查Cookie
    if (_cookieController.text.isEmpty) {
      setState(() {
        _statusText = '错误: 请输入Cookie';
      });
      return;
    }
    
    // 检查位置
    if (_latitude == 0 || _longitude == 0) {
      setState(() {
        _statusText = '错误: 请先获取位置信息';
      });
      return;
    }
    
    setState(() {
      _isLoading = true;
      _statusText = '正在搜索POI位置...';
    });
    
    try {
      // 调用抖音POI搜索
      LoggerUtil.i('【位置测试】准备搜索POI - Cookie长度: ${_cookieController.text.length}, 关键词: ${_keywordController.text}');
      
      final poiResponse = await _douyinService.searchPoi(
        cookies: _cookieController.text,
        latitude: _latitude,
        longitude: _longitude,
        keywords: _keywordController.text.isNotEmpty ? _keywordController.text : null,
        proxy: _proxyController.text.isNotEmpty ? _proxyController.text : null,
      );
      
      setState(() {
        _isLoading = false;
        _poiList = poiResponse.poiList;
        _statusText = '搜索成功，共找到 ${_poiList.length} 个位置';
      });
      
      LoggerUtil.i('【位置测试】搜索POI成功 - 共 ${_poiList.length} 个结果');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusText = '搜索失败: $e';
      });
      LoggerUtil.e('【位置测试】搜索POI失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('抖音POI测试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Cookie输入
            const Text('抖音Cookie:', style: TextStyle(fontWeight: FontWeight.bold)),
            TextField(
              controller: _cookieController,
              decoration: const InputDecoration(
                hintText: '粘贴抖音Cookie',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 8),
            
            // 代理设置（可选）
            const Text('代理地址(可选):', style: TextStyle(fontWeight: FontWeight.bold)),
            TextField(
              controller: _proxyController,
              decoration: const InputDecoration(
                hintText: '例如: 127.0.0.1:8888',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            
            // 搜索关键词
            const Text('搜索关键词(可选):', style: TextStyle(fontWeight: FontWeight.bold)),
            TextField(
              controller: _keywordController,
              decoration: const InputDecoration(
                hintText: '例如: 咖啡厅',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            
            // 位置信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('当前位置:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 4),
                  Text(_latitude != 0 && _longitude != 0
                      ? '纬度: ${_latitude.toStringAsFixed(6)}, 经度: ${_longitude.toStringAsFixed(6)}'
                      : '未获取位置信息'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // 功能按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _requestLocationPermission,
                    icon: const Icon(Icons.my_location),
                    label: const Text('获取位置'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _searchPoi,
                    icon: const Icon(Icons.search),
                    label: const Text('搜索POI'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 状态信息
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('状态:', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 4),
                  Text(_statusText),
                  if (_isLoading)
                    const Padding(
                      padding: EdgeInsets.only(top: 8),
                      child: LinearProgressIndicator(),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // 搜索结果
            const Text('搜索结果:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            const SizedBox(height: 8),
            
            // 结果列表
            ..._poiList.map((poi) => _buildPoiItem(poi)).toList(),
            
            if (_poiList.isEmpty)
              Center(
                child: Text(
                  _isLoading ? '正在搜索...' : '暂无数据',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  // POI列表项
  Widget _buildPoiItem(DouyinPoiItem poi) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              poi.poiName,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text('地址: ${poi.address}'),
            Text('类型: ${poi.typeName}'),
            Text('经纬度: (${poi.longitude}, ${poi.latitude})'),
            Text('距离: ${(poi.distance / 1000).toStringAsFixed(2)}km'),
            Text('城市: ${poi.cityName}, 区: ${poi.districtName}'),
          ],
        ),
      ),
    );
  }
}