import 'package:aitoearn_app/config/logger.dart';
import 'package:flutter/material.dart';
import 'package:aitoearn_app/routes/douyin_location_select_route.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/models/douyin_poi_model.dart';

class DouyinLocationExamplePage extends StatefulWidget {
  const DouyinLocationExamplePage({Key? key}) : super(key: key);

  @override
  State<DouyinLocationExamplePage> createState() => _DouyinLocationExamplePageState();
}

class _DouyinLocationExamplePageState extends State<DouyinLocationExamplePage> {
  // 抖音Cookie - 实际使用时应当从用户配置或登录状态中获取
  final String _cookies = 'your_douyin_cookies_here';
  
  // 选中的位置
  DouyinPoiItem? _selectedLocation;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('抖音位置选择示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 显示已选位置信息
            if (_selectedLocation != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      '已选择位置:',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('名称: ${_selectedLocation!.poiName}'),
                    Text('地址: ${_selectedLocation!.address}'),
                    Text('经纬度: ${_selectedLocation!.longitude}, ${_selectedLocation!.latitude}'),
                    Text('距离: ${(_selectedLocation!.distance / 1000).toStringAsFixed(1)}km'),
                  ],
                ),
              ),
              
            // 选择位置按钮
            ElevatedButton(
              onPressed: _selectLocation,
              child: const Text('选择位置'),
            ),
          ],
        ),
      ),
    );
  }

  // 选择位置方法
  Future<void> _selectLocation() async {
    final selectedLocation = await navigateToDouyinLocationSelect(
      context: context,
      cookies: _cookies,
      // proxy: '可选的代理服务器',
      onLocationSelected: (location) {
        // 可以在这里处理位置选择事件
        LoggerUtil.i('位置已选择: ${location.poiName}');
      },
    );
    
    if (selectedLocation != null) {
      setState(() {
        _selectedLocation = selectedLocation;
      });
    }
  }
}