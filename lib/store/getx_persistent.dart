import 'package:aitoearn_app/config/logger.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

/// GetX持久化服务基类
abstract class GetxPersistentService extends GetxService {
  final String container;
  late GetStorage storage;

  GetxPersistentService(this.container);

  @override
  onInit() async {
    super.onInit();
    storage = GetStorage();
  }

  /// 读取数据
  T? read<T>(String key) {
    try {
      return storage.read<T>('${container}_$key');
    } catch (e) {
      LoggerUtil.e('读取数据失败: $e');
      return null;
    }
  }

  /// 写入数据
  Future<bool> write<T>(String key, T value) async {
    try {
      await storage.write('${container}_$key', value);
      return true;
    } catch (e) {
      LoggerUtil.e('写入数据失败: $e');
      return false;
    }
  }

  /// 删除数据
  Future<bool> remove(String key) async {
    try {
      storage.remove('${container}_$key');
      return true;
    } catch (e) {
      LoggerUtil.e('删除数据失败: $e');
      return false;
    }
  }
}
