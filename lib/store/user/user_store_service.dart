import 'dart:async';

import 'package:aitoearn_app/api/login/login_api.dart';
import 'package:aitoearn_app/api/models/base_response.dart';
import 'package:aitoearn_app/api/user/userInfo_api.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/routers/router.dart';
import 'package:aitoearn_app/store/getx_persistent.dart';
import 'package:aitoearn_app/store/user/model/user_info.dart';
import 'package:aitoearn_app/utils/toast_and_loading.dart';
import 'package:get/get.dart';

class UserStoreService extends GetxPersistentService {
  static const String TOKEN_KEY = 'token';

  final token = ''.obs;
  final userInfo = Rx<UserInfo?>(null);

  UserStoreService() : super('userStore');

  static const int maxRetry = 3;
  static const int statusUnauthorized = 401;
  static const int statusSuccess = 200;

  @override
  void onInit() {
    super.onInit();
    Future.delayed(const Duration(milliseconds: 20)).then((_) {
      final localToken = read<String>(TOKEN_KEY) ?? '';
      LoggerUtil.i('=== 读取token: $localToken');
      token.value = localToken;
    });
  }

  Future<void> saveToken(String saveToken) async {
    token.value = saveToken;
    await write(TOKEN_KEY, saveToken);
  }

  Future<bool> getUserInfo() async {
    try {
      final response = await _retryGetUserInfo();
      if (response != null && response.statusCode == statusSuccess) {
        final res = BaseResponse<UserInfo>.fromJson(
          response.data,
          (data) => UserInfo.fromJson(data),
        );
        userInfo.value = res.data;
        return true;
      }
      return false;
    } catch (e) {
      LoggerUtil.e('getUserInfo 异常: $e');
      return false;
    }
  }

  Future<dynamic> _retryGetUserInfo() async {
    for (int attempt = 1; attempt <= maxRetry; attempt++) {
      final res = await getUserInfoApi();
      LoggerUtil.i('第 $attempt 次获取用户信息: $res');

      if (res == null || res.statusCode != statusSuccess) {
        if (res?.statusCode == statusUnauthorized) {
          LoggerUtil.w('登录状态过期: $res');
          showToast('登录状态已过期');
          _clearStatus();
          break;
        }
        if (attempt < maxRetry) {
          await Future.delayed(const Duration(seconds: 1));
        } else {
          LoggerUtil.w('已重试 $maxRetry 次，仍然失败');
          showToast('获取用户信息失败');
          _clearStatus();
        }
      } else {
        return res;
      }
    }
    return null;
  }

  void _clearStatus() {
    token.value = '';
    userInfo.value = null;
    write(TOKEN_KEY, '');

    // 避免重复注入或多实例污染
    if (Get.isRegistered<UserStoreService>()) {
      Get.delete<UserStoreService>();
    }

    // 跳转欢迎页
    Get.offAllNamed(AppRouter.welcomePath);
  }

  void logout() {
    _clearStatus();
  }
}
