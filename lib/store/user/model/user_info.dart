// 用户信息
import 'package:aitoearn_app/config/logger.dart';
import 'package:logger/logger.dart';

class UserInfo {
  String? id;
  String? name;
  String? mail;
  String? phone;
  String? gender;
  String? avatar;
  String? desc;
  int? score;
  double? balance;
  int? status;
  String? inviteCode;
  VipInfo? vipInfo;

  UserInfo({
    this.id,
    this.name,
    this.mail,
    this.phone,
    this.gender,
    this.avatar,
    this.desc,
    this.score,
    this.balance,
    this.status,
    this.inviteCode,
    this.vipInfo,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] as String?,
      name: json['name'] as String?,
      mail: json['mail'] as String?,
      phone: json['phone'] as String?,
      gender: json['gender'] as String?,
      avatar: json['avatar'] as String?,
      desc: json['desc'] as String?,
      score: json['score'] as int?,
      balance: json['balance'] as double?,
      status: json['status'] as int?,
      inviteCode: json['inviteCode'] as String?,
      vipInfo: json['vipInfo'] != null
          ? VipInfo.fromJson(json['vipInfo'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mail': mail,
      'phone': phone,
      'gender': gender,
      'avatar': avatar,
      'desc': desc,
      'score': score,
      'balance': balance,
      'status': status,
      'inviteCode': inviteCode,
      'vipInfo': vipInfo?.toJson(),
    };
  }
}

class VipInfo {
  String? id;
  int? cycleType;
  String? expireTime;

  VipInfo({
    this.id,
    this.cycleType,
    this.expireTime,
  });

  factory VipInfo.fromJson(Map<String, dynamic> json) {
    String? time = json['expireTime'] as String?;
    try {
      if (time != null) {
        DateTime expireTime = DateTime.parse(time);
        /// yyyy-MM-dd HH:mm
        time = expireTime.toString().substring(0, 16);
      }
    } catch (e) {
      LoggerUtil.e(e.toString());
    }
    return VipInfo(
      id: json['id'] as String?,
      cycleType: json['cycleType'] as int?,
      expireTime: time,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cycleType': cycleType,
      'expireTime': expireTime,
    };
  }
}
