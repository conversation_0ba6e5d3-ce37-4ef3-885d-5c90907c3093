import 'dart:convert';
import 'dart:io';

import 'package:aitoearn_app/api/account/account_api.dart';
import 'package:aitoearn_app/api/account/models/account_user_info_modle.dart';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/config/plat_config/plat_config_enum.dart';
import 'package:aitoearn_app/plat_core/plats/plat_douyin/douyin_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_kwai/kwai_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_wx_sph/wx_sph_service.dart';
import 'package:aitoearn_app/plat_core/plats/plat_xhs/xhs_service.dart';
import 'package:aitoearn_app/store/getx_persistent.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

/// 账号持久化服务
class AccountPersistentService extends GetxPersistentService {
  static AccountPersistentService get to => Get.find();

  // 存储键
  static const String ACCOUNTS_KEY = 'accounts';
  static const String CURRENT_ACCOUNT_KEY = 'current_account';
  static const String GROUPS_KEY = 'account_groups';

  // 账号列表
  final accounts = <AccountUserInfoModle>[].obs;

  // 当前账号
  final currentAccount = Rx<AccountUserInfoModle?>(null);

  // 账号空间列表
  final groups = <LocalAccountGroupModel>[].obs;

  // 抖音服务
  final DouyinService _douyinService = DouyinService();

  AccountPersistentService() : super('account');

  @override
  void onInit() {
    super.onInit();
    LoggerUtil.i('【AccountPersistentService】初始化开始');

    // try {
    //   // 先加载账号列表
    //   _loadAccounts();
    //   LoggerUtil.i('【AccountPersistentService】账号列表加载完成，共 ${accounts.length} 个账号');
    //
    //   // 再加载空间
    //   _loadGroups();
    //   LoggerUtil.i('【AccountPersistentService】账号空间加载完成，共 ${groups.length} 个空间');
    //
    //   // 最后加载当前账号
    //   _loadCurrentAccount();
    //
    //   // 如果当前账号为空但有账号列表，设置第一个账号为当前账号
    //   if (currentAccount.value == null && accounts.isNotEmpty) {
    //     LoggerUtil.i('【AccountPersistentService】当前账号为空，设置第一个账号为当前账号');
    //     setCurrentAccount(accounts.first);
    //   }
    //
    //   // 记录当前账号状态
    //   if (currentAccount.value != null) {
    //     LoggerUtil.i('【AccountPersistentService】当前选中的账号: ${currentAccount.value!.nickname}, 平台: ${currentAccount.value!.type.name}');
    //   } else {
    //     LoggerUtil.w('【AccountPersistentService】当前未选中任何账号');
    //   }
    // } catch (e) {
    //   LoggerUtil.e('【AccountPersistentService】初始化异常: $e');
    // }
    //
    // LoggerUtil.i('【AccountPersistentService】初始化完成');
  }

  // 加载所有账号
  // void _loadAccounts() {
  //   final accountsJson = read<String>(ACCOUNTS_KEY);
  //   if (accountsJson != null) {
  //     try {
  //       final List<dynamic> accountsList = jsonDecode(accountsJson);
  //       accounts.value = accountsList
  //           .map((item) => AccountUserInfoModle.fromJson(item))
  //           .toList();
  //
  //       // 加载账号后，更新账号信息
  //       updateAccountsUserInfo();
  //     } catch (e) {
  //       accounts.value = [];
  //     }
  //   }
  // }
  //
  // // 加载所有空间
  // void _loadGroups() {
  //   final groupsJson = read<String>(GROUPS_KEY);
  //   if (groupsJson != null) {
  //     try {
  //       final List<dynamic> groupsList = jsonDecode(groupsJson);
  //       groups.value = groupsList
  //           .map((item) => LocalAccountGroupModel.fromJson(item))
  //           .toList();
  //
  //       LoggerUtil.i('已加载 ${groups.length} 个账号空间');
  //     } catch (e) {
  //       LoggerUtil.e('加载账号空间失败: $e');
  //       groups.value = [];
  //     }
  //   }
  //
  //   // 如果没有空间，创建默认空间
  //   if (groups.isEmpty) {
  //     groups.add(LocalAccountGroupModel(
  //       const Uuid().v4(),
  //       '默认空间',
  //       [],
  //     ));
  //     saveGroups();
  //   }
  // }

  // 更新所有账号的用户信息
  Future<void> updateAccountsUserInfo() async {
    for (var account in accounts) {
      await _updateAccountUserInfo(account);
      // AccountUserInfoModle? date = await getPlatformAccountInfo(
      //   account.type,
      //   account.cookie ?? '',
      //   account.uid,
      // );
      //更新状态
      LoggerUtil.d('更新账号状态: ${account.uid} - ${account.online}');
    }
  }

  // 更新单个账号的用户信息
  Future<void> _updateAccountUserInfo(AccountUserInfoModle account) async {
    try {
      switch (account.type) {
        case PlatTypeEnum.douyin:
          await _updateDouyinAccountInfo(account);
          break;
        case PlatTypeEnum.kwai:
          await _updateKwaiAccountInfo(account);
          break;
        case PlatTypeEnum.xhs:
          await _updateXhsAccountInfo(account);
          break;
        case PlatTypeEnum.wxWph:
          await _updateWxSphAccountInfo(account);
          break;
        default:
          // 其他平台暂不处理
          break;
      }
    } catch (e) {
      LoggerUtil.e('更新账号信息失败: ${account.type} - ${account.uid}: $e');
    }
  }

  // 更新抖音账号信息
  Future<void> _updateDouyinAccountInfo(AccountUserInfoModle account) async {
    try {
      // 调用抖音服务获取用户信息
      final userInfo = await _douyinService.getUserInfo(account.cookie ?? '');

      // 更新账号信息
      account.nickname = userInfo.nickname;
      account.avatar = userInfo.avatar;
      account.fansCount = userInfo.fansCount;
      account.uid = userInfo.uid; // 确保UID是最新的
      account.online = true; // 能获取到用户信息，设置为在线
      LoggerUtil.i('已更新抖音账号信息: ${account.nickname}');
    } catch (e) {
      account.online = false; // 发生异常，设置为离线
      LoggerUtil.e('获取抖音用户信息失败: $e');
    }
  }

  // 更新快手账号信息
  Future<void> _updateKwaiAccountInfo(AccountUserInfoModle account) async {
    try {
      // 从cookie中提取api_ph参数
      final apiPh = _extractApiPhFromCookie(account.cookie ?? '');
      if (apiPh == null) {
        LoggerUtil.e('从快手cookie中提取api_ph失败');
        account.online = false; // 无法提取api_ph，设置为离线
        return;
      }

      // 调用快手服务获取用户信息
      final userInfoResponse = await KwaiService.getUserInfo(
        apiPh,
        account.cookie ?? '',
      );

      if (userInfoResponse != null && userInfoResponse['result'] == 1) {
        final userInfo = userInfoResponse['data'];
        if (userInfo != null) {
          account.nickname = userInfo['userName'] ?? account.nickname;
          account.avatar = userInfo['headurl'] ?? account.avatar;
          account.fansCount = userInfo['fansCnt'] ?? account.fansCount;
          // 快手的UID可能需要特殊处理
          if (userInfo['userId'] != null) {
            account.uid = userInfo['userId'].toString();
          }
          account.online = true; // 能获取到用户信息，设置为在线
          LoggerUtil.i('已更新快手账号信息: ${account.nickname}');
        } else {
          account.online = false; // 响应中没有用户信息，设置为离线
        }
      } else {
        account.online = false; // 响应错误，设置为离线
        LoggerUtil.w('快手API返回错误，账号可能已掉线: ${account.account}');
      }
    } catch (e) {
      account.online = false; // 发生异常，设置为离线
      LoggerUtil.e('获取快手用户信息失败: $e');
    }
  }

  // 从cookie字符串中提取api_ph参数(快手专用)
  String? _extractApiPhFromCookie(String cookieStr) {
    try {
      final cookies = cookieStr.split(';');
      for (var cookie in cookies) {
        cookie = cookie.trim();
        if (cookie.startsWith('kuaishou.web.cp.api_ph=')) {
          return cookie.substring('kuaishou.web.cp.api_ph='.length);
        }
      }
      return null;
    } catch (e) {
      LoggerUtil.e('提取api_ph失败: $e');
      return null;
    }
  }

  // 将cookie字符串解析为Cookie对象列表
  static List<Cookie> parseCookieString(String cookieStr) {
    final List<Cookie> cookies = [];

    if (cookieStr.isEmpty) {
      return cookies;
    }

    final cookieParts = cookieStr.split(';');
    for (var part in cookieParts) {
      part = part.trim();
      if (part.isEmpty) continue;

      final index = part.indexOf('=');
      if (index > 0) {
        final name = part.substring(0, index).trim();
        final value = part.substring(index + 1).trim();
        cookies.add(Cookie(name, value));
      }
    }

    return cookies;
  }

  // 将Cookie对象列表转换为cookie字符串
  static String cookieListToString(List<Cookie> cookies) {
    return cookies.map((cookie) => '${cookie.name}=${cookie.value}').join('; ');
  }

  // 加载当前账号
  void _loadCurrentAccount() {
    try {
      LoggerUtil.i('【AccountPersistentService】开始加载当前账号');

      final currentAccountJson = read<String>(CURRENT_ACCOUNT_KEY);
      if (currentAccountJson != null && currentAccountJson.isNotEmpty) {
        try {
          LoggerUtil.i(
            '【AccountPersistentService】找到保存的当前账号数据，长度: ${currentAccountJson.length}',
          );

          final accountData = jsonDecode(currentAccountJson);
          currentAccount.value = AccountUserInfoModle.fromJson(accountData);

          LoggerUtil.i(
            '【AccountPersistentService】成功加载当前账号: ${currentAccount.value!.nickname}, 平台: ${currentAccount.value!.type.name}',
          );

          // 确保账号在账号列表中
          final existingIndex = accounts.indexWhere(
            (a) =>
                a.uid == currentAccount.value!.uid &&
                a.type == currentAccount.value!.type,
          );

          if (existingIndex == -1) {
            LoggerUtil.w('【AccountPersistentService】当前账号不在账号列表中，将添加到列表');
            accounts.add(currentAccount.value!);
            saveAccounts();
          }
        } catch (e) {
          LoggerUtil.e('【AccountPersistentService】解析当前账号数据异常: $e');
          currentAccount.value = null;
        }
      } else {
        LoggerUtil.w('【AccountPersistentService】未找到保存的当前账号数据');
        currentAccount.value = null;

        // 如果有账号列表，尝试设置第一个账号为当前账号
        if (accounts.isNotEmpty) {
          LoggerUtil.i('【AccountPersistentService】尝试将第一个账号设为当前账号');
          currentAccount.value = accounts.first;
          _saveCurrentAccount();
        }
      }
    } catch (e) {
      LoggerUtil.e('【AccountPersistentService】加载当前账号异常: $e');
      currentAccount.value = null;
    }
  }

  // 保存所有账号
  void saveAccounts() {
    // write(ACCOUNTS_KEY, jsonEncode(accounts.map((e) => e.toJson()).toList()));
  }

  // 保存所有空间
  void saveGroups() {
    // write(GROUPS_KEY, jsonEncode(groups.map((e) => e.toJson()).toList()));
    // LoggerUtil.i('已保存 ${groups.length} 个账号空间');
  }

  // 保存当前账号
  void _saveCurrentAccount() {
    try {
      if (currentAccount.value != null) {
        LoggerUtil.i(
          '【AccountPersistentService】开始保存当前账号: ${currentAccount.value!.nickname}',
        );

        // 转换为JSON并保存
        final json = jsonEncode(currentAccount.value!.toJson());
        write(CURRENT_ACCOUNT_KEY, json);

        // 验证保存是否成功
        final savedJson = read<String>(CURRENT_ACCOUNT_KEY);
        if (savedJson == null || savedJson.isEmpty) {
          LoggerUtil.e('【AccountPersistentService】当前账号保存失败，无法读取保存的数据');
        } else {
          LoggerUtil.i(
            '【AccountPersistentService】当前账号保存成功，数据长度: ${savedJson.length}',
          );
        }
      } else {
        LoggerUtil.i('【AccountPersistentService】当前账号为空，移除保存的数据');
        remove(CURRENT_ACCOUNT_KEY);
      }
    } catch (e) {
      LoggerUtil.e('【AccountPersistentService】保存当前账号异常: $e');
    }
  }

  /// 添加或更新账号
  Future<bool> addOrUpdateAccount(AccountUserInfoModle account) async {
    try {
      // 检查账号是否已存在
      final index = accounts.indexWhere(
        (a) => a.uid == account.uid && a.type == account.type,
      );

      // 如果账号不存在，则添加到API
      if (index < 0) {
        LoggerUtil.i(
          '准备创建新账号: ${account.account}, 平台: ${account.type.name}, UID: ${account.uid}',
        );

        // 调用API创建账号
        final createdAccount = await createAccountApi(
          account.cookie ?? '',
          account.type,
          account.account,
          account.nickname ?? '',
          account.avatar ?? '',
          account.uid,
          groupId: account.groupId,
          accessToken: account.accessToken,
          refreshToken: account.refreshToken,
          fansCount: account.fansCount ?? 0,
          readCount: account.readCount ?? 0,
          likeCount: account.likeCount ?? 0,
          collectCount: account.collectCount ?? 0,
          forwardCount: account.forwardCount ?? 0,
          commentCount: account.commentCount ?? 0,
          workCount: account.workCount ?? 0,
          income: account.income ?? 0,
        );

        if (createdAccount != null) {
          LoggerUtil.i(
            'API创建账号成功: ${createdAccount.account}, ID: ${createdAccount.id}',
          );

          // 使用API返回的账号数据
          accounts.add(createdAccount);

          // 如果账号有空间ID，确保添加到对应空间
          if (createdAccount.groupId.isNotEmpty) {
            final groupIndex = groups.indexWhere(
              (group) => group.id == createdAccount.groupId,
            );
            if (groupIndex >= 0) {
              if (!groups[groupIndex].accountUids.contains(
                createdAccount.uid,
              )) {
                groups[groupIndex].accountUids.add(createdAccount.uid);
                LoggerUtil.i('已将账号添加到现有空间: ${groups[groupIndex].name}');
              }
            } else {
              // 如果空间不存在，添加到默认空间
              final defaultGroup = getOrCreateDefaultGroup();
              if (!defaultGroup.accountUids.contains(createdAccount.uid)) {
                defaultGroup.accountUids.add(createdAccount.uid);
                createdAccount.groupId = defaultGroup.id;
                LoggerUtil.i('空间不存在，已将账号添加到默认空间');
              }
            }
          } else {
            // 如果没有空间ID，添加到默认空间
            final defaultGroup = getOrCreateDefaultGroup();
            if (!defaultGroup.accountUids.contains(createdAccount.uid)) {
              defaultGroup.accountUids.add(createdAccount.uid);
              createdAccount.groupId = defaultGroup.id;
              LoggerUtil.i('账号无空间ID，已将账号添加到默认空间');
            }
          }
        } else {
          // API创建失败，仍然添加到本地
          LoggerUtil.w('API创建账号失败，回退到本地创建');
          accounts.add(account);

          // 添加到默认空间
          final defaultGroup = getOrCreateDefaultGroup();
          if (!defaultGroup.accountUids.contains(account.uid)) {
            defaultGroup.accountUids.add(account.uid);
            account.groupId = defaultGroup.id;
            LoggerUtil.i('已将账号添加到默认空间（本地）');
          }
        }
      } else {
        // 如果账号已存在，更新账号信息
        LoggerUtil.i('账号已存在，更新账号信息: ${account.account}, UID: ${account.uid}');

        // 如果已存在的账号有ID，而新账号没有，保留原ID
        if (accounts[index].id != null &&
            accounts[index].id!.isNotEmpty &&
            (account.id == null || account.id!.isEmpty)) {
          account.id = accounts[index].id;
          LoggerUtil.i('保留原账号ID: ${account.id}');
        }

        // 更新账号信息
        accounts[index] = account;

        // 尝试调用API更新账号
        if (account.id != null && account.id!.isNotEmpty) {
          LoggerUtil.i('调用API更新账号: ${account.id}');
          final success = await updateAccountInfo(account);
          if (success) {
            LoggerUtil.i('API更新账号成功');
          } else {
            LoggerUtil.w('API更新账号失败，已在本地更新');
          }
        }
      }

      // 保存账号和空间
      saveAccounts();
      saveGroups();

      LoggerUtil.i('账号添加/更新操作完成');
      return true;
    } catch (e) {
      LoggerUtil.e('添加或更新账号失败: $e');
      return false;
    }
  }

  // 从Cookie创建账号信息
  AccountUserInfoModle createAccountFromCookie(
    PlatTypeEnum platType,
    List<Cookie> cookies,
    String uid,
    String nickname,
    String avatar,
  ) {
    // 将Cookie列表转换为字符串
    final cookieString = cookieListToString(cookies);

    // 提取access_token和refresh_token
    String? accessToken;
    String? refreshToken;

    // 尝试从cookie中提取token
    for (var cookie in cookies) {
      if (cookie.name == 'access_token') {
        accessToken = cookie.value;
      } else if (cookie.name == 'refresh_token') {
        refreshToken = cookie.value;
      }
    }

    // 如果没有直接找到，尝试从cookie字符串中提取
    if (accessToken == null) {
      final accessTokenMatch = RegExp(
        r'access_token=([^;]+)',
      ).firstMatch(cookieString);
      if (accessTokenMatch != null && accessTokenMatch.groupCount >= 1) {
        accessToken = accessTokenMatch.group(1);
      }
    }

    if (refreshToken == null) {
      final refreshTokenMatch = RegExp(
        r'refresh_token=([^;]+)',
      ).firstMatch(cookieString);
      if (refreshTokenMatch != null && refreshTokenMatch.groupCount >= 1) {
        refreshToken = refreshTokenMatch.group(1);
      }
    }

    return AccountUserInfoModle(
      platType,
      cookieString,
      DateTime.now().toIso8601String(),
      uid,
      nickname,
      // 使用昵称作为账号
      avatar,
      nickname,
      0,
      // 粉丝数
      0,
      // 阅读数
      0,
      // 点赞数
      0,
      // 收藏数
      0,
      // 转发数
      0,
      // 评论数
      DateTime.now().millisecondsSinceEpoch,
      // 最后统计时间
      0,
      // 作品数
      0,
      // 收入
      online: true,
      // 新创建的账号默认为在线状态
      accessToken: cookieString,
      refreshToken: refreshToken,
    );
  }

  // 设置当前账号
  void setCurrentAccount(AccountUserInfoModle account) {
    LoggerUtil.i(
      '【AccountPersistentService】设置当前账号: ${account.nickname}, 平台: ${account.type.name}, UID: ${account.uid}',
    );

    // 确保账号在账号列表中
    final existingIndex = accounts.indexWhere(
      (a) => a.uid == account.uid && a.type == account.type,
    );
    if (existingIndex == -1) {
      LoggerUtil.w('【AccountPersistentService】设置的当前账号不在账号列表中，将添加到列表');
      accounts.add(account);
      saveAccounts();
    }

    currentAccount.value = account;
    _saveCurrentAccount();

    LoggerUtil.i('【AccountPersistentService】当前账号设置成功');
  }

  // 删除账号
  Future<bool> deleteAccount(AccountUserInfoModle account) async {
    try {
      // 如果账号有ID字段，调用API删除
      if (account.id != null && account.id!.isNotEmpty) {
        LoggerUtil.i('调用API删除账号: ${account.id}');

        // 调用API删除账号
        final response = await deleteAccountApi(account.id!);

        // 检查API响应
        if (response != null &&
            response.statusCode == 200 &&
            response.data != null) {
          final data = response.data!;

          if (data['code'] == 0) {
            LoggerUtil.i('API删除账号成功: ${account.id}');
          } else {
            LoggerUtil.e('API删除账号失败: ${data['message']}');
            return false;
          }
        } else {
          LoggerUtil.e('API删除账号请求失败');
          return false;
        }
      } else {
        LoggerUtil.i('账号无ID，仅从本地删除: ${account.uid}');
      }

      // 无论API是否成功，都从本地删除账号
      // 先从账号列表中删除
      accounts.removeWhere(
        (element) => element.type == account.type && element.uid == account.uid,
      );

      // 然后从所有空间中删除该账号的UID
      for (var group in groups) {
        group.accountUids.remove(account.uid);
      }

      // 保存更改
      saveAccounts();
      saveGroups();

      // 如果删除的是当前账号，则清空当前账号
      if (currentAccount.value != null &&
          currentAccount.value!.type == account.type &&
          currentAccount.value!.uid == account.uid) {
        currentAccount.value = null;
        _saveCurrentAccount();
      }

      LoggerUtil.i('已删除账号: ${account.account}, UID: ${account.uid}');
      return true;
    } catch (e) {
      LoggerUtil.e('删除账号失败: $e');
      return false;
    }
  }

  // 获取指定平台的所有账号
  List<AccountUserInfoModle> getAccountsByPlatType(PlatTypeEnum platType) {
    return accounts.where((account) => account.type == platType).toList();
  }

  // 清空所有账号
  void clearAllAccounts() {
    accounts.clear();
    currentAccount.value = null;
    saveAccounts();
    _saveCurrentAccount();
  }

  // 更新小红书账号信息
  Future<void> _updateXhsAccountInfo(AccountUserInfoModle account) async {
    try {
      // 从cookie中提取用户ID
      final userId = XhsService.extractUserIdFromCookie(account.cookie ?? '');
      // if (userId == null) {
      //   LoggerUtil.e('从小红书cookie中提取用户ID失败');
      //   account.online = false; // 无法提取用户ID，设置为离线
      //   return;
      // }

      // 调用小红书服务获取用户信息
      final userInfo = await XhsService.getUserInfo(account.refreshToken ?? '');

      if (userInfo != null) {
        // 更新账号信息
        account.nickname = userInfo['name'] ?? account.nickname;
        account.avatar = userInfo['avatar'] ?? account.avatar;
        account.fansCount = userInfo['fans_count'] ?? account.fansCount;
        account.uid = userId ?? ''; // 使用从cookie中提取的用户ID
        account.online = true; // 能获取到用户信息，设置为在线
        LoggerUtil.i('已更新小红书账号信息: ${account.nickname}');
      } else {
        account.online = false; // 无法获取用户信息，设置为离线
        LoggerUtil.w('无法获取小红书账号信息，账号可能已掉线: ${account.account}');
      }
    } catch (e) {
      account.online = false; // 发生异常，设置为离线
      LoggerUtil.e('获取小红书用户信息失败: $e');
    }
  }

  // 更新微信视频号账号信息
  Future<void> _updateWxSphAccountInfo(AccountUserInfoModle account) async {
    try {
      // 调用微信视频号服务获取用户信息
      final userInfo = await WxSphService.getUserInfo(
        account.accessToken ?? '',
      );

      if (userInfo != null) {
        // 更新账号信息
        account.nickname = userInfo.nickname;
        account.avatar = userInfo.avatar;
        account.fansCount = userInfo.fansCount;
        account.uid = userInfo.authorId; // 使用作者ID作为UID
        account.online = true; // 能获取到用户信息，设置为在线
        LoggerUtil.i('已更新微信视频号账号信息: ${account.nickname}');
      } else {
        account.online = false; // 无法获取用户信息，设置为离线
        LoggerUtil.w('无法获取微信视频号账号信息，账号可能已掉线: ${account.account}');
      }
    } catch (e) {
      account.online = false; // 发生异常，设置为离线
      LoggerUtil.e('获取微信视频号用户信息失败: $e');
    }
  }

  // 获取账号所在的空间ID
  String? getAccountGroupId(String uid) {
    for (var group in groups) {
      if (group.accountUids.contains(uid)) {
        return group.id;
      }
    }
    return null;
  }

  // 获取或创建默认空间
  LocalAccountGroupModel getOrCreateDefaultGroup() {
    // 查找默认空间
    final defaultGroup = groups.firstWhereOrNull((group) => group.isDefault);

    // 如果找到默认空间，直接返回
    if (defaultGroup != null) {
      return defaultGroup;
    }

    // 如果没有默认空间，创建一个
    final newDefaultGroup = LocalAccountGroupModel(
      const Uuid().v4(),
      '默认空间',
      [],
      isDefault: true,
    );

    // 添加到空间列表
    groups.add(newDefaultGroup);

    // 保存空间
    saveGroups();

    return newDefaultGroup;
  }

  // 获取指定空间下的所有账号
  List<AccountUserInfoModle> getAccountsByGroupId(String groupId) {
    final group = groups.firstWhereOrNull((group) => group.id == groupId);
    if (group == null) return [];

    return accounts
        .where((account) => group.accountUids.contains(account.uid))
        .toList();
  }

  // 获取所有空间信息
  List<Map<String, dynamic>> getAllGroupsInfo() {
    return groups.map((group) {
      final accountsInGroup = getAccountsByGroupId(group.id);
      return {
        'id': group.id,
        'name': group.name,
        'accountCount': accountsInGroup.length,
        'expanded': group.expanded,
      };
    }).toList();
  }

  // 根据UID获取账号信息
  AccountUserInfoModle? getAccountByUid(String uid) {
    return accounts.firstWhereOrNull((account) => account.uid == uid);
  }

  // 从API获取所有账号列表并替换本地数据
  Future<bool> fetchAccountsFromApi() async {
    try {
      LoggerUtil.i('开始从API获取账号列表');

      final response = await getAllAccountsApi();

      if (response != null &&
          response.statusCode == 200 &&
          response.data != null) {
        final data = response.data!;

        // 记录完整的API响应数据结构
        LoggerUtil.i(
          '获取账号列表API响应: code=${data['code']}, 是否包含data字段: ${data.containsKey('data')}',
        );

        if (data['code'] == 0 && data.containsKey('data')) {
          // 记录data字段的类型
          final dataType = data['data']?.runtimeType;
          LoggerUtil.i('API返回的data字段类型: $dataType');

          // 尝试获取账号列表数据
          List<dynamic> accountsList = [];

          // 处理各种可能的数据结构
          if (data['data'] == null) {
            LoggerUtil.w('API返回的data字段为null，没有账号数据');
            return false;
          } else if (data['data'] is List) {
            // 直接是账号列表
            accountsList = data['data'] as List<dynamic>;
            LoggerUtil.i('API返回的data是List类型，包含 ${accountsList.length} 个账号');
          } else if (data['data'] is Map) {
            final mapData = data['data'] as Map<String, dynamic>;
            LoggerUtil.i('API返回的data是Map类型，keys: ${mapData.keys.toList()}');

            // 尝试从map中获取账号列表
            if (mapData.containsKey('list') && mapData['list'] is List) {
              accountsList = mapData['list'] as List<dynamic>;
              LoggerUtil.i('从data.list中获取到 ${accountsList.length} 个账号');
            } else if (mapData.containsKey('accounts') &&
                mapData['accounts'] is List) {
              accountsList = mapData['accounts'] as List<dynamic>;
              LoggerUtil.i('从data.accounts中获取到 ${accountsList.length} 个账号');
            } else if (mapData.containsKey('items') &&
                mapData['items'] is List) {
              accountsList = mapData['items'] as List<dynamic>;
              LoggerUtil.i('从data.items中获取到 ${accountsList.length} 个账号');
            } else if (mapData.containsKey('data') && mapData['data'] is List) {
              accountsList = mapData['data'] as List<dynamic>;
              LoggerUtil.i('从data.data中获取到 ${accountsList.length} 个账号');
            } else {
              // 如果没有找到明确的列表字段，尝试将整个map作为单个账号处理
              if (mapData.containsKey('account') ||
                  mapData.containsKey('uid')) {
                accountsList = [mapData];
                LoggerUtil.i('将整个data作为单个账号处理');
              } else {
                LoggerUtil.e('无法从API响应中提取账号列表数据');
                return false;
              }
            }
          } else {
            LoggerUtil.e('API返回的账号数据格式不正确: $dataType');
            return false;
          }

          // 清空当前账号列表
          accounts.clear();

          // 将API返回的账号添加到本地列表
          for (var item in accountsList) {
            try {
              // 确保item是Map类型
              if (item is! Map<String, dynamic>) {
                LoggerUtil.e('账号数据不是Map类型: ${item.runtimeType}');
                continue;
              }

              final accountData = item;

              // 检查必要的字段
              if (!accountData.containsKey('uid') ||
                  accountData['uid'] == null) {
                LoggerUtil.e('账号数据缺少uid字段: $accountData');
                continue;
              }

              // 解析平台类型
              PlatTypeEnum platType;
              final typeValue =
                  accountData['type']?.toString().toLowerCase() ?? 'douyin';
              platType = PlatTypeEnumExt.fromString(typeValue);

              // 安全地获取字段值
              String uid = accountData['uid']?.toString() ?? '';
              String accountName = accountData['account']?.toString() ?? '';
              String avatar = accountData['avatar']?.toString() ?? '';
              String nickname = accountData['nickname']?.toString() ?? '';
              int fansCount = _safeParseInt(accountData['fansCount']);
              int readCount = _safeParseInt(accountData['readCount']);
              int likeCount = _safeParseInt(accountData['likeCount']);
              int collectCount = _safeParseInt(accountData['collectCount']);
              int forwardCount = _safeParseInt(accountData['forwardCount']);
              int commentCount = _safeParseInt(accountData['commentCount']);
              int workCount = _safeParseInt(accountData['workCount']);
              int income = _safeParseInt(accountData['income']);

              // 解析时间戳
              int lastStatsTime = 0;
              if (accountData['lastStatsTime'] != null) {
                try {
                  lastStatsTime =
                      DateTime.parse(
                        accountData['lastStatsTime'].toString(),
                      ).millisecondsSinceEpoch;
                } catch (e) {
                  LoggerUtil.e(
                    '解析lastStatsTime失败: ${accountData['lastStatsTime']}',
                  );
                }
              }

              // 创建账号对象
              final accountObj = AccountUserInfoModle(
                platType,
                accountData['cookie']?.toString() ?? '',
                accountData['loginTime'],
                uid,
                accountName,
                avatar,
                nickname,
                fansCount,
                readCount,
                likeCount,
                collectCount,
                forwardCount,
                commentCount,
                lastStatsTime,
                workCount,
                income,
                online: true,
                groupId: accountData['groupId']?.toString() ?? '',
                accessToken: accountData['access_token']?.toString(),
                refreshToken: accountData['refresh_token']?.toString(),
                id:
                    accountData['_id']?.toString() ??
                    accountData['id']?.toString(),
              );

              // 添加到账号列表
              accounts.add(accountObj);

              // 确保账号添加到对应空间
              if (accountObj.groupId.isNotEmpty) {
                final groupIndex = groups.indexWhere(
                  (group) => group.id == accountObj.groupId,
                );
                if (groupIndex >= 0) {
                  if (!groups[groupIndex].accountUids.contains(
                    accountObj.uid,
                  )) {
                    groups[groupIndex].accountUids.add(accountObj.uid);
                  }
                }
              }

              LoggerUtil.i(
                '已添加账号: ${accountObj.account}, UID: ${accountObj.uid}',
              );
            } catch (e) {
              LoggerUtil.e('解析账号数据失败: $e');
            }
          }

          // 保存更改
          // saveAccounts();
          // saveGroups();

          LoggerUtil.i('成功从API获取并更新了 ${accounts.length} 个账号');
          return true;
        } else {
          LoggerUtil.e('获取账号列表失败: ${data['message']}');
        }
      }

      return false;
    } catch (e) {
      LoggerUtil.e('从API获取账号列表失败: $e');
      return false;
    }
  }

  // 安全解析整数值
  int _safeParseInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is num) return value.toInt();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        return 0;
      }
    }
    return 0;
  }

  // 添加空间
  LocalAccountGroupModel addGroup(String name) {
    final newGroup = LocalAccountGroupModel(const Uuid().v4(), name, []);
    groups.add(newGroup);
    saveGroups();
    return newGroup;
  }

  // 编辑空间
  void editGroup(String groupId, String newName) {
    final index = groups.indexWhere((group) => group.id == groupId);
    if (index >= 0) {
      groups[index].name = newName;
      saveGroups();
    }
  }

  // 删除空间
  void deleteGroup(String groupId) {
    // 如果只有一个空间，不允许删除
    if (groups.length <= 1) {
      return;
    }

    // 找到要删除的空间
    final groupToDelete = groups.firstWhereOrNull(
      (group) => group.id == groupId,
    );
    if (groupToDelete == null) return;

    // 将该空间下的账号移动到默认空间
    final defaultGroup = groups.firstWhere(
      (group) => group.id != groupId,
      orElse: () => groups[0],
    );

    defaultGroup.accountUids.addAll(groupToDelete.accountUids);

    // 删除空间
    groups.removeWhere((group) => group.id == groupId);

    // 保存更改
    saveGroups();
  }

  // 将账号移动到指定空间
  Future<bool> moveAccountToGroup(String uid, String targetGroupId) async {
    try {
      LoggerUtil.i('开始将账号(UID: $uid)移动到空间: $targetGroupId');

      // 查找账号
      final account = accounts.firstWhereOrNull(
        (account) => account.uid == uid,
      );
      if (account == null) {
        LoggerUtil.e('找不到UID为 $uid 的账号');
        return false;
      }

      // 保存原始空间ID，以便API失败时恢复
      final originalGroupId = account.groupId;

      // 更新账号空间ID
      account.groupId = targetGroupId;

      bool apiSuccess = false;
      // 如果账号有ID，调用API更新
      if (account.id != null && account.id!.isNotEmpty) {
        LoggerUtil.i('调用API更新账号空间: ${account.id}, 新空间: $targetGroupId');

        try {
          // 调用更新账号API
          final response = await updateAccountApi(account);

          // 检查API响应
          if (response != null &&
              response.statusCode == 200 &&
              response.data != null) {
            final data = response.data!;
            apiSuccess = data['code'] == 0;

            if (apiSuccess) {
              LoggerUtil.i('API更新账号空间成功: ${account.id}');
            } else {
              LoggerUtil.e('API更新账号空间失败: ${data['message'] ?? "未知错误"}');
              // 恢复原始空间ID
              account.groupId = originalGroupId;
              return false;
            }
          } else {
            LoggerUtil.e('API更新账号空间失败: 无效响应');
            // 恢复原始空间ID
            account.groupId = originalGroupId;
            return false;
          }
        } catch (e) {
          LoggerUtil.e('API更新账号空间异常: $e');
          // 恢复原始空间ID
          account.groupId = originalGroupId;
          return false;
        }
      } else {
        LoggerUtil.i('账号无ID，仅在本地更新空间: ${account.uid}');
        apiSuccess = true; // 本地账号视为API操作成功
      }

      // 只有API成功时，才在本地执行移动操作以确保数据一致性
      if (apiSuccess) {
        // 首先从所有空间中移除该账号
        for (var group in groups) {
          group.accountUids.remove(uid);
        }

        // 将账号添加到目标空间
        final targetGroup = groups.firstWhereOrNull(
          (group) => group.id == targetGroupId,
        );
        if (targetGroup != null) {
          targetGroup.accountUids.add(uid);
        } else {
          LoggerUtil.w('找不到ID为 $targetGroupId 的目标空间');
          // 如果找不到目标空间，尝试创建一个
          final newGroup = LocalAccountGroupModel(targetGroupId, '新空间', [uid]);
          groups.add(newGroup);
          LoggerUtil.i('创建了新的空间: ID=$targetGroupId');
        }

        // 保存更改
        saveAccounts();
        saveGroups();

        LoggerUtil.i('已将账号(UID: $uid)移动到空间: ${targetGroup?.name ?? "新空间"}');
        return true;
      } else {
        LoggerUtil.e('API移动失败，不更新本地数据');
        return false;
      }
    } catch (e) {
      LoggerUtil.e('移动账号到空间失败: $e');
      return false;
    }
  }

  // 批量移动账号到指定空间
  Future<bool> moveAccountsToGroup(
    List<String> uids,
    String targetGroupId,
  ) async {
    try {
      LoggerUtil.i('开始批量移动账号，账号数: ${uids.length}, 目标空间ID: $targetGroupId');

      if (uids.isEmpty) {
        LoggerUtil.w('没有要移动的账号');
        return false;
      }

      // 检查目标空间是否存在
      LocalAccountGroupModel? targetGroup = groups.firstWhereOrNull(
        (group) => group.id == targetGroupId,
      );

      // 如果找不到完全匹配的ID，尝试查找部分匹配的ID
      if (targetGroup == null && targetGroupId.isNotEmpty) {
        for (var group in groups) {
          if (group.id.contains(targetGroupId) ||
              targetGroupId.contains(group.id)) {
            LoggerUtil.i('找到部分匹配的空间ID: ${group.id}');
            targetGroup = group;
            targetGroupId = group.id; // 更新为完整的空间ID
            break;
          }
        }
      }

      // 如果仍然找不到目标空间，创建一个新空间
      if (targetGroup == null && targetGroupId.isNotEmpty) {
        LoggerUtil.w('找不到目标空间，将创建新空间: $targetGroupId');
        targetGroup = LocalAccountGroupModel(targetGroupId, '新空间', []);
        groups.add(targetGroup);
      }

      // 记录成功和失败的账号
      int successCount = 0;
      int failCount = 0;
      List<String> failedUids = [];

      // 逐个移动账号
      for (var uid in uids) {
        final success = await moveAccountToGroup(uid, targetGroupId);
        if (success) {
          successCount++;
        } else {
          failCount++;
          failedUids.add(uid);
        }
      }

      // 记录结果
      LoggerUtil.i('批量移动账号完成，成功: $successCount, 失败: $failCount');
      if (failCount > 0) {
        LoggerUtil.w('移动失败的账号: $failedUids');
      }

      // 保存更改
      saveAccounts();
      saveGroups();

      // 如果至少有一个账号移动成功，返回true
      return successCount > 0;
    } catch (e) {
      LoggerUtil.e('批量移动账号失败: $e');
      return false;
    }
  }

  /// 更新账号信息
  Future<bool> updateAccountInfo(AccountUserInfoModle account) async {
    try {
      LoggerUtil.i('开始更新账号信息: ${account.id}');

      // 如果账号有ID，调用API更新
      if (account.id != null && account.id!.isNotEmpty) {
        LoggerUtil.i('调用API更新账号: ${account.id}');

        // 调用API更新账号
        final response = await updateAccountApi(account);

        // 检查API响应
        if (response != null &&
            response.statusCode == 200 &&
            response.data != null) {
          final data = response.data!;

          if (data['code'] == 0) {
            LoggerUtil.i('API更新账号成功: ${account.id}');

            // 更新本地账号
            final index = accounts.indexWhere(
              (a) => a.uid == account.uid && a.type == account.type,
            );
            if (index >= 0) {
              accounts[index] = account;
              saveAccounts();
            }

            return true;
          } else {
            LoggerUtil.e('API更新账号失败: ${data['message']}');
            return false;
          }
        } else {
          LoggerUtil.e('API更新账号请求失败');
          return false;
        }
      } else {
        LoggerUtil.i('账号无ID，仅在本地更新: ${account.uid}');

        // 更新本地账号
        final index = accounts.indexWhere(
          (a) => a.uid == account.uid && a.type == account.type,
        );
        if (index >= 0) {
          accounts[index] = account;
          saveAccounts();
          return true;
        } else {
          LoggerUtil.e('找不到要更新的账号: ${account.uid}');
          return false;
        }
      }
    } catch (e) {
      LoggerUtil.e('更新账号信息失败: $e');
      return false;
    }
  }

  // 获取平台账号信息的统一接口
  Future<AccountUserInfoModle?> getPlatformAccountInfo(
    PlatTypeEnum platType,
    String cookie,
    String uid,
  ) async {
    try {
      LoggerUtil.i('开始获取平台账号信息: 平台=${platType.name}, UID=$uid');

      // 创建一个临时的账号对象用于获取信息
      final tempAccount = AccountUserInfoModle(
        platType,
        cookie,
        DateTime.now().toIso8601String(),
        uid,
        '',
        // 账号名称，将由各平台API填充
        '',
        // 头像，将由各平台API填充
        '',
        // 昵称，将由各平台API填充
        0,
        // 粉丝数，将由各平台API填充
        0,
        // 阅读数
        0,
        // 点赞数
        0,
        // 收藏数
        0,
        // 转发数
        0,
        // 评论数
        DateTime.now().millisecondsSinceEpoch,
        0,
        // 作品数
        0,
        // 收入
        online: true, // 默认为离线，成功获取信息后设为在线
      );

      // 根据平台类型获取账号信息
      switch (platType) {
        case PlatTypeEnum.douyin:
          await _getDouyinAccountInfo(tempAccount);
          break;
        case PlatTypeEnum.kwai:
          await _getKwaiAccountInfo(tempAccount);
          break;
        case PlatTypeEnum.xhs:
          await _getXhsAccountInfo(tempAccount);
          break;
        case PlatTypeEnum.wxWph:
          await _getWxSphAccountInfo(tempAccount);
          break;
        default:
          LoggerUtil.w('不支持的平台类型: ${platType.name}');
          return null;
      }

      // 如果成功获取到账号信息（在线状态为true），则返回账号对象
      if (tempAccount.online) {
        LoggerUtil.i(
          '成功获取平台账号信息: ${tempAccount.nickname}, 粉丝数: ${tempAccount.fansCount}',
        );
        return tempAccount;
      } else {
        LoggerUtil.w('获取平台账号信息失败，账号可能已掉线');
        return tempAccount;
      }
    } catch (e) {
      LoggerUtil.e('获取平台账号信息异常: $e');
      return null;
    }
  }

  // 获取抖音账号信息
  Future<void> _getDouyinAccountInfo(AccountUserInfoModle account) async {
    try {
      // 调用抖音服务获取用户信息
      final userInfo = await _douyinService.getUserInfo(account.cookie ?? '');

      // 更新账号信息
      account.nickname = userInfo.nickname;
      account.account = userInfo.nickname; // 使用昵称作为账号名
      account.avatar = userInfo.avatar;
      account.fansCount = userInfo.fansCount;
      account.uid = userInfo.uid; // 确保UID是最新的
      account.online = true; // 能获取到用户信息，设置为在线
      LoggerUtil.i('已获取抖音账号信息: ${account.nickname}, 粉丝数: ${account.fansCount}');
    } catch (e) {
      account.online = false; // 发生异常，设置为离线
      LoggerUtil.e('获取抖音用户信息失败: $e');
    }
  }

  // 获取快手账号信息
  Future<void> _getKwaiAccountInfo(AccountUserInfoModle account) async {
    try {
      // 从cookie中提取api_ph参数
      final apiPh = _extractApiPhFromCookie(account.cookie ?? '');
      if (apiPh == null) {
        LoggerUtil.e('从快手cookie中提取api_ph失败');
        account.online = false; // 无法提取api_ph，设置为离线
        return;
      }

      // 调用快手服务获取用户信息
      final userInfoResponse = await KwaiService.getUserInfo(
        apiPh,
        account.cookie ?? '',
      );

      if (userInfoResponse != null && userInfoResponse['result'] == 1) {
        final userInfo = userInfoResponse['data'];
        if (userInfo != null) {
          account.nickname = userInfo['userName'] ?? '';
          account.account = userInfo['userName'] ?? ''; // 使用用户名作为账号名
          account.avatar = userInfo['headurl'] ?? '';
          account.fansCount = userInfo['fansCnt'] ?? 0;
          // 快手的UID可能需要特殊处理
          if (userInfo['userId'] != null) {
            account.uid = userInfo['userId'].toString();
          }
          account.online = true; // 能获取到用户信息，设置为在线
          LoggerUtil.i(
            '已获取快手账号信息: ${account.nickname}, 粉丝数: ${account.fansCount}',
          );
        } else {
          account.online = false; // 响应中没有用户信息，设置为离线
          LoggerUtil.w('快手API响应中没有用户信息');
        }
      } else {
        account.online = false; // 响应错误，设置为离线
        LoggerUtil.w('快手API返回错误，账号可能已掉线');
      }
    } catch (e) {
      account.online = false; // 发生异常，设置为离线
      LoggerUtil.e('获取快手用户信息失败: $e');
    }
  }

  // 获取小红书账号信息
  Future<void> _getXhsAccountInfo(AccountUserInfoModle account) async {
    try {
      // 从cookie中提取用户ID
      final userId = XhsService.extractUserIdFromCookie(account.cookie ?? '');
      if (userId == null) {
        LoggerUtil.e('从小红书cookie中提取用户ID失败');
        account.online = false; // 无法提取用户ID，设置为离线
        return;
      }

      // 调用小红书服务获取用户信息
      final userInfo = await XhsService.getUserInfo(account.cookie ?? '');

      if (userInfo != null) {
        // 更新账号信息
        account.nickname = userInfo['name'] ?? '';
        account.account = userInfo['name'] ?? ''; // 使用名称作为账号名
        account.avatar = userInfo['avatar'] ?? '';
        account.fansCount = userInfo['fans_count'] ?? 0;
        account.uid = userId; // 使用从cookie中提取的用户ID
        account.online = true; // 能获取到用户信息，设置为在线
        LoggerUtil.i(
          '已获取小红书账号信息: ${account.nickname}, 粉丝数: ${account.fansCount}',
        );
      } else {
        account.online = false; // 无法获取用户信息，设置为离线
        LoggerUtil.w('无法获取小红书账号信息，账号可能已掉线');
      }
    } catch (e) {
      account.online = false; // 发生异常，设置为离线
      LoggerUtil.e('获取小红书用户信息失败: $e');
    }
  }

  // 获取微信视频号账号信息
  Future<void> _getWxSphAccountInfo(AccountUserInfoModle account) async {
    try {
      // 调用微信视频号服务获取用户信息
      final userInfo = await WxSphService.getUserInfo(account.cookie ?? '');

      if (userInfo != null) {
        // 更新账号信息
        account.nickname = userInfo.nickname;
        account.account = userInfo.nickname; // 使用昵称作为账号名
        account.avatar = userInfo.avatar;
        account.fansCount = userInfo.fansCount;
        account.uid = userInfo.authorId; // 使用作者ID作为UID
        account.online = true; // 能获取到用户信息，设置为在线
        LoggerUtil.i(
          '已获取微信视频号账号信息: ${account.nickname}, 粉丝数: ${account.fansCount}',
        );
      } else {
        account.online = false; // 无法获取用户信息，设置为离线
        LoggerUtil.w('无法获取微信视频号账号信息，账号可能已掉线');
      }
    } catch (e) {
      account.online = false; // 发生异常，设置为离线
      LoggerUtil.e('获取微信视频号用户信息失败: $e');
    }
  }
}

/// 账号空间模型
class LocalAccountGroupModel {
  /// 空间ID
  String id;

  /// 空间名称
  String name;

  /// 空间下的账号UID列表
  List<String> accountUids;

  /// 是否展开显示
  bool expanded;

  /// 是否为默认空间
  bool isDefault;

  LocalAccountGroupModel(
    this.id,
    this.name,
    this.accountUids, {
    this.expanded = true,
    this.isDefault = false,
  });

  factory LocalAccountGroupModel.fromJson(Map<String, dynamic> json) {
    return LocalAccountGroupModel(
      json['id'] as String,
      json['name'] as String,
      List<String>.from(json['accountUids'] as List),
      expanded: json['expanded'] as bool? ?? true,
      isDefault: json['isDefault'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'accountUids': accountUids,
    'expanded': expanded,
    'isDefault': isDefault,
  };
}
