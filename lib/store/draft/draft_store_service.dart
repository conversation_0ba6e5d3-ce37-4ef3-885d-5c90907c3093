import 'dart:convert';
import 'dart:io';
import 'package:aitoearn_app/config/logger.dart';
import 'package:aitoearn_app/models/draft_models/draft_model.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

/// 草稿箱持久化服务
class DraftStoreService extends GetxService {
  // 存储键
  static const String _draftBoxesKey = 'draft_boxes';
  
  // 默认草稿箱名称
  static const String _defaultDraftBoxName = '默认草稿箱';
  
  // 草稿箱列表
  final RxList<DraftBox> draftBoxes = <DraftBox>[].obs;
  
  // GetStorage实例
  final GetStorage _storage = GetStorage();
  
  // UUID生成器
  final Uuid _uuid = const Uuid();
  
  @override
  void onInit() {
    super.onInit();
    _loadDraftBoxes();
  }
  
  /// 加载所有草稿箱
  Future<void> _loadDraftBoxes() async {
    try {
      final String? draftBoxesJson = _storage.read<String>(_draftBoxesKey);
      
      if (draftBoxesJson != null) {
        final List<dynamic> draftBoxesData = jsonDecode(draftBoxesJson);
        final List<DraftBox> boxes = draftBoxesData
            .map((data) => DraftBox.fromJson(data))
            .toList();
        
        draftBoxes.assignAll(boxes);
        LoggerUtil.i('已加载 ${boxes.length} 个草稿箱');
      } else {
        // 如果没有草稿箱数据，创建默认草稿箱
        _createDefaultDraftBox();
      }
    } catch (e) {
      LoggerUtil.e('加载草稿箱失败: $e');
      // 出错时创建默认草稿箱
      _createDefaultDraftBox();
    }
  }
  
  /// 创建默认草稿箱
  void _createDefaultDraftBox() {
    final defaultBox = DraftBox(
      id: _uuid.v4(),
      name: _defaultDraftBoxName,
      createTime: DateTime.now(),
      updateTime: DateTime.now(),
      description: '默认存储草稿的地方',
      drafts: [],
      isDefault: true,
    );
    
    draftBoxes.add(defaultBox);
    _saveDraftBoxes();
    LoggerUtil.i('已创建默认草稿箱');
  }
  
  /// 保存所有草稿箱
  Future<void> _saveDraftBoxes() async {
    try {
      final List<Map<String, dynamic>> draftBoxesData = 
          draftBoxes.map((box) => box.toJson()).toList();
      
      final String draftBoxesJson = jsonEncode(draftBoxesData);
      await _storage.write(_draftBoxesKey, draftBoxesJson);
      LoggerUtil.i('已保存 ${draftBoxes.length} 个草稿箱');
    } catch (e) {
      LoggerUtil.e('保存草稿箱失败: $e');
    }
  }
  
  /// 获取所有草稿箱
  List<DraftBox> getAllDraftBoxes() {
    return draftBoxes;
  }
  
  /// 获取默认草稿箱
  DraftBox? getDefaultDraftBox() {
    return draftBoxes.firstWhereOrNull((box) => box.isDefault);
  }
  
  /// 获取指定ID的草稿箱
  DraftBox? getDraftBoxById(String id) {
    return draftBoxes.firstWhereOrNull((box) => box.id == id);
  }
  
  /// 创建新草稿箱
  Future<DraftBox> createDraftBox({
    required String name,
    String? description,
    String? icon,
    String? color,
  }) async {
    final newBox = DraftBox(
      id: _uuid.v4(),
      name: name,
      createTime: DateTime.now(),
      updateTime: DateTime.now(),
      description: description,
      icon: icon,
      color: color,
      drafts: [],
      isDefault: false,
    );
    
    draftBoxes.add(newBox);
    await _saveDraftBoxes();
    return newBox;
  }
  
  /// 更新草稿箱
  Future<bool> updateDraftBox(DraftBox draftBox) async {
    final index = draftBoxes.indexWhere((box) => box.id == draftBox.id);
    
    if (index != -1) {
      // 更新时间
      final updatedBox = draftBox.copyWith(updateTime: DateTime.now());
      draftBoxes[index] = updatedBox;
      await _saveDraftBoxes();
      return true;
    }
    
    return false;
  }
  
  /// 删除草稿箱
  Future<bool> deleteDraftBox(String id) async {
    // 不允许删除默认草稿箱
    final box = getDraftBoxById(id);
    if (box == null) return false;
    
    if (box.isDefault) {
      LoggerUtil.w('不能删除默认草稿箱');
      return false;
    }
    
    // 删除草稿箱中的所有草稿文件
    for (final draft in box.drafts) {
      await _deleteDraftFiles(draft);
    }
    
    // 删除草稿箱
    draftBoxes.removeWhere((box) => box.id == id);
    await _saveDraftBoxes();
    return true;
  }
  
  /// 设置默认草稿箱
  Future<bool> setDefaultDraftBox(String id) async {
    final index = draftBoxes.indexWhere((box) => box.id == id);
    
    if (index != -1) {
      // 先将所有草稿箱设为非默认
      for (int i = 0; i < draftBoxes.length; i++) {
        if (draftBoxes[i].isDefault) {
          draftBoxes[i] = draftBoxes[i].copyWith(isDefault: false);
        }
      }
      
      // 将指定草稿箱设为默认
      draftBoxes[index] = draftBoxes[index].copyWith(isDefault: true);
      await _saveDraftBoxes();
      return true;
    }
    
    return false;
  }
  
  /// 获取指定草稿箱中的所有草稿
  List<DraftItem> getDraftsInBox(String boxId) {
    final box = getDraftBoxById(boxId);
    return box?.drafts ?? [];
  }
  
  /// 创建新草稿
  Future<bool> createDraft({
    required String boxId,
    required DraftItem draft,
  }) async {
    final boxIndex = draftBoxes.indexWhere((box) => box.id == boxId);
    
    if (boxIndex == -1) {
      LoggerUtil.e('草稿箱不存在: $boxId');
      return false;
    }
    
    // 保存文件
    final savedDraft = await _saveDraftFiles(draft);
    
    // 添加到草稿箱
    final box = draftBoxes[boxIndex];
    final updatedDrafts = [...box.drafts, savedDraft];
    draftBoxes[boxIndex] = box.copyWith(
      drafts: updatedDrafts,
      updateTime: DateTime.now(),
    );
    
    await _saveDraftBoxes();
    return true;
  }
  
  /// 更新草稿
  Future<bool> updateDraft({
    required String boxId,
    required String draftId,
    required DraftItem draft,
  }) async {
    final boxIndex = draftBoxes.indexWhere((box) => box.id == boxId);
    
    if (boxIndex == -1) {
      LoggerUtil.e('草稿箱不存在: $boxId');
      return false;
    }
    
    final box = draftBoxes[boxIndex];
    final draftIndex = box.drafts.indexWhere((d) => d.id == draftId);
    
    if (draftIndex == -1) {
      LoggerUtil.e('草稿不存在: $draftId');
      return false;
    }
    
    // 保存文件
    final savedDraft = await _saveDraftFiles(draft);
    
    // 更新草稿
    final updatedDrafts = [...box.drafts];
    updatedDrafts[draftIndex] = savedDraft;
    
    draftBoxes[boxIndex] = box.copyWith(
      drafts: updatedDrafts,
      updateTime: DateTime.now(),
    );
    
    await _saveDraftBoxes();
    return true;
  }
  
  /// 删除草稿
  Future<bool> deleteDraft({
    required String boxId,
    required String draftId,
  }) async {
    final boxIndex = draftBoxes.indexWhere((box) => box.id == boxId);
    
    if (boxIndex == -1) {
      LoggerUtil.e('草稿箱不存在: $boxId');
      return false;
    }
    
    final box = draftBoxes[boxIndex];
    final draftIndex = box.drafts.indexWhere((d) => d.id == draftId);
    
    if (draftIndex == -1) {
      LoggerUtil.e('草稿不存在: $draftId');
      return false;
    }
    
    // 删除草稿文件
    await _deleteDraftFiles(box.drafts[draftIndex]);
    
    // 删除草稿
    final updatedDrafts = [...box.drafts];
    updatedDrafts.removeAt(draftIndex);
    
    draftBoxes[boxIndex] = box.copyWith(
      drafts: updatedDrafts,
      updateTime: DateTime.now(),
    );
    
    await _saveDraftBoxes();
    return true;
  }
  
  /// 移动草稿到其他草稿箱
  Future<bool> moveDraft({
    required String fromBoxId,
    required String toBoxId,
    required String draftId,
  }) async {
    if (fromBoxId == toBoxId) return true;
    
    final fromBoxIndex = draftBoxes.indexWhere((box) => box.id == fromBoxId);
    final toBoxIndex = draftBoxes.indexWhere((box) => box.id == toBoxId);
    
    if (fromBoxIndex == -1 || toBoxIndex == -1) {
      LoggerUtil.e('草稿箱不存在');
      return false;
    }
    
    final fromBox = draftBoxes[fromBoxIndex];
    final toBox = draftBoxes[toBoxIndex];
    
    final draftIndex = fromBox.drafts.indexWhere((d) => d.id == draftId);
    
    if (draftIndex == -1) {
      LoggerUtil.e('草稿不存在: $draftId');
      return false;
    }
    
    // 获取要移动的草稿
    final draft = fromBox.drafts[draftIndex];
    
    // 从源草稿箱中移除
    final updatedFromDrafts = [...fromBox.drafts];
    updatedFromDrafts.removeAt(draftIndex);
    
    // 添加到目标草稿箱
    final updatedToDrafts = [...toBox.drafts, draft];
    
    // 更新两个草稿箱
    final now = DateTime.now();
    draftBoxes[fromBoxIndex] = fromBox.copyWith(
      drafts: updatedFromDrafts,
      updateTime: now,
    );
    
    draftBoxes[toBoxIndex] = toBox.copyWith(
      drafts: updatedToDrafts,
      updateTime: now,
    );
    
    await _saveDraftBoxes();
    return true;
  }
  
  /// 保存草稿相关文件（视频、图片等）到应用存储目录
  Future<DraftItem> _saveDraftFiles(DraftItem draft) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final draftDir = Directory('${appDir.path}/drafts/${draft.id}');
      
      // 确保目录存在
      if (!draftDir.existsSync()) {
        draftDir.createSync(recursive: true);
      }
      
      // 保存视频文件
      String? newVideoPath;
      if (draft.videoPath != null && draft.videoPath!.isNotEmpty) {
        final videoFile = File(draft.videoPath!);
        if (videoFile.existsSync()) {
          final fileName = videoFile.path.split('/').last;
          final newPath = '${draftDir.path}/$fileName';
          
          // 如果文件不在应用目录中，复制到应用目录
          if (!draft.videoPath!.startsWith(appDir.path)) {
            await videoFile.copy(newPath);
            newVideoPath = newPath;
          } else {
            newVideoPath = draft.videoPath;
          }
        }
      }
      
      // 保存图片文件
      List<String>? newImagePaths;
      if (draft.imagePaths != null && draft.imagePaths!.isNotEmpty) {
        newImagePaths = [];
        
        for (final imagePath in draft.imagePaths!) {
          final imageFile = File(imagePath);
          if (imageFile.existsSync()) {
            final fileName = imageFile.path.split('/').last;
            final newPath = '${draftDir.path}/$fileName';
            
            // 如果文件不在应用目录中，复制到应用目录
            if (!imagePath.startsWith(appDir.path)) {
              await imageFile.copy(newPath);
              newImagePaths.add(newPath);
            } else {
              newImagePaths.add(imagePath);
            }
          }
        }
      }
      
      // 保存缩略图文件
      String? newThumbnailPath;
      if (draft.thumbnailPath != null && draft.thumbnailPath!.isNotEmpty) {
        final thumbnailFile = File(draft.thumbnailPath!);
        if (thumbnailFile.existsSync()) {
          final fileName = thumbnailFile.path.split('/').last;
          final newPath = '${draftDir.path}/$fileName';
          
          // 如果文件不在应用目录中，复制到应用目录
          if (!draft.thumbnailPath!.startsWith(appDir.path)) {
            await thumbnailFile.copy(newPath);
            newThumbnailPath = newPath;
          } else {
            newThumbnailPath = draft.thumbnailPath;
          }
        }
      }
      
      // 返回更新后的草稿
      return draft.copyWith(
        videoPath: newVideoPath,
        imagePaths: newImagePaths,
        thumbnailPath: newThumbnailPath,
      );
    } catch (e) {
      LoggerUtil.e('保存草稿文件失败: $e');
      return draft;
    }
  }
  
  /// 删除草稿相关文件
  Future<void> _deleteDraftFiles(DraftItem draft) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final draftDir = Directory('${appDir.path}/drafts/${draft.id}');
      
      if (draftDir.existsSync()) {
        await draftDir.delete(recursive: true);
        LoggerUtil.i('已删除草稿文件: ${draft.id}');
      }
    } catch (e) {
      LoggerUtil.e('删除草稿文件失败: $e');
    }
  }
  
  /// 更新草稿（使用草稿对象）
  Future<bool> updateDraftWithObject({
    required String boxId,
    required DraftItem draft,
  }) async {
    final boxIndex = draftBoxes.indexWhere((box) => box.id == boxId);
    
    if (boxIndex == -1) {
      LoggerUtil.e('草稿箱不存在: $boxId');
      return false;
    }
    
    final box = draftBoxes[boxIndex];
    final draftIndex = box.drafts.indexWhere((d) => d.id == draft.id);
    
    if (draftIndex == -1) {
      LoggerUtil.e('草稿不存在: ${draft.id}');
      return false;
    }
    
    // 更新时间
    final updatedDraft = draft.copyWith(updateTime: DateTime.now());
    
    // 保存文件
    final savedDraft = await _saveDraftFiles(updatedDraft);
    
    // 更新草稿
    final updatedDrafts = [...box.drafts];
    updatedDrafts[draftIndex] = savedDraft;
    
    draftBoxes[boxIndex] = box.copyWith(
      drafts: updatedDrafts,
      updateTime: DateTime.now(),
    );
    
    await _saveDraftBoxes();
    return true;
  }
} 