{"build": {"afterSign": "scripts/notarize.cjs"}, "name": "aiToEarn", "version": "0.6.0-alpha.4", "main": "dist-electron/main/index.js", "description": "艺咖 win打包使用管理员的终端", "author": "艺咖", "license": "MIT", "private": true, "engines": {"node": "20.x.x"}, "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:7777/"}}, "type": "module", "scripts": {"dev": "chcp 65001 && vite", "dev:mac": "vite", "build": "tsc && vite build && electron-builder", "build:notsc": "vite build && electron-builder", "preview": "vite preview", "pretest": "vite build --mode=test", "test": "vitest run", "rebuild": "electron-rebuild -f -w better-sqlite3", "lint": "npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mocks,electron}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"{src,electron}/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/"}, "dependencies": {"@ant-design/colors": "^7.2.0", "@ant-design/icons": "^5.6.1", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@ffprobe-installer/ffprobe": "^2.1.2", "@types/form-data": "^2.5.2", "antd": "^5.23.1", "antd-img-crop": "^4.24.0", "axios": "^1.7.9", "better-sqlite3": "^11.8.1", "coordtransform": "^2.1.2", "crc32": "^0.2.2", "cropperjs": "^1.6.2", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "echarts": "^5.6.0", "electron-log": "^5.3.0", "electron-serve": "^2.1.1", "electron-store": "^10.0.0", "electron-updater": "^6.3.9", "events": "^3.3.0", "ffprobe-static": "^3.1.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.2", "fs": "^0.0.1-security", "image-size": "^1.2.0", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.30.1", "node-cache": "^5.1.2", "node-schedule": "^2.1.1", "os": "^0.1.2", "p-queue": "^8.1.0", "path": "^0.12.7", "qs": "^6.14.0", "react-intersection-observer": "^9.16.0", "react-masonry-css": "^1.0.16", "react-router-dom": "6", "react-sortablejs": "^6.1.4", "reflect-metadata": "^0.2.2", "sharp": "^0.33.5", "sortablejs": "^1.15.6", "typeorm": "^0.3.20", "uninstall": "^0.0.0", "uuid": "^11.0.5", "vite": "^6.2.3", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-svg-icons": "^2.0.1", "xml2js": "^0.6.2", "zustand": "^5.0.3"}, "devDependencies": {"@electron/rebuild": "^3.7.1", "@playwright/test": "^1.48.2", "@types/fluent-ffmpeg": "^2.1.27", "@types/form-data": "^2.2.1", "@types/lodash": "^4.17.15", "@types/mime-types": "^2.1.4", "@types/node-schedule": "^2.1.7", "@types/qs": "^6.9.18", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/sortablejs": "^1.15.8", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "electron": "^33.2.0", "electron-builder": "^26.0.12", "electron-notarize": "^1.2.2", "eslint": "^8.42.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8.4.49", "postcss-import": "^16.1.0", "prettier": "^3.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "sass": "^1.83.4", "sass-loader": "^16.0.4", "tailwindcss": "^3.4.15", "typescript": "^5.4.2", "vite": "^5.4.11", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgr": "^4.3.0", "vitest": "^2.1.5"}}