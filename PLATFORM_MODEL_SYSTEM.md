# 平台模型系统

## 设计理念

将每个平台的参数和账号信息提取到独立的model中，发布时直接分发对应的model，实现更清晰的架构和更好的可维护性。

## 架构设计

### 1. 基础平台发布模型 (`BasePlatformPublishModel`)

抽象基类，定义所有平台共有的属性和方法：

```dart
abstract class BasePlatformPublishModel {
  /// 账号信息
  final Map<String, dynamic> account;
  
  /// 平台类型
  final String platform;
  
  /// 基础内容
  final String? title;
  final String? description;
  final List<String>? topics;
  
  /// 媒体文件
  final File? video;
  final List<File>? images;
  final File? cover;
  
  /// 定时发布
  final bool isScheduled;
  final DateTime? scheduledTime;
  
  /// 位置信息
  final dynamic selectedLocation;

  /// 转换为发布参数Map
  Map<String, dynamic> toPublishParams();
  
  /// 验证参数
  String? validate();
  
  /// 获取平台特定配置
  Map<String, dynamic> getPlatformConfig();
}
```

### 2. 平台特定模型

#### 抖音发布模型 (`DouyinPublishModel`)

```dart
class DouyinPublishModel extends BasePlatformPublishModel {
  /// 抖音特定配置
  final String? selfDeclare;              // 自主声明
  final List<Map<String, String>>? mentionedUsers;  // @好友
  final String? activityId;               // 活动参与
  final String? hotspotId;                // 热点关联
  final String? mixId;                    // 合集选择
  final int visibilityType;               // 可见性类型 0:公开 1:私密 2:好友可见

  /// 从账号配置创建
  factory DouyinPublishModel.fromAccountConfig({
    required Map<String, dynamic> account,
    required Map<String, dynamic> config,
    File? video,
    List<File>? images,
    File? cover,
  });
}
```

#### 小红书发布模型 (`XiaohongshuPublishModel`)

```dart
class XiaohongshuPublishModel extends BasePlatformPublishModel {
  /// 小红书特定配置
  final bool isPrivate;                   // 是否私密
  final bool disableComment;              // 关闭评论

  /// 从账号配置创建
  factory XiaohongshuPublishModel.fromAccountConfig({
    required Map<String, dynamic> account,
    required Map<String, dynamic> config,
    File? video,
    List<File>? images,
    File? cover,
  });
}
```

#### 快手发布模型 (`KuaishouPublishModel`)

```dart
class KuaishouPublishModel extends BasePlatformPublishModel {
  /// 快手特定配置
  final int visibilityType;               // 可见性类型
  final bool allowComment;                // 允许评论
  final bool allowDownload;               // 允许下载

  /// 从账号配置创建
  factory KuaishouPublishModel.fromAccountConfig({
    required Map<String, dynamic> account,
    required Map<String, dynamic> config,
    File? video,
    List<File>? images,
    File? cover,
  });
}
```

### 3. 平台模型工厂 (`PlatformPublishModelFactory`)

统一的模型创建和管理工厂：

```dart
class PlatformPublishModelFactory {
  /// 从账号配置创建平台发布模型
  static BasePlatformPublishModel? createFromAccountConfig({
    required Map<String, dynamic> account,
    required Map<String, dynamic> config,
    File? video,
    List<File>? images,
    File? cover,
  });

  /// 批量创建平台发布模型
  static List<BasePlatformPublishModel> createBatchFromAccountConfigs({
    required List<Map<String, dynamic>> selectedAccounts,
    required Map<String, Map<String, dynamic>> accountConfigs,
    File? video,
    List<File>? images,
    File? cover,
  });

  /// 验证所有发布模型
  static String? validatePublishModels(List<BasePlatformPublishModel> models);
}
```

## 使用流程

### 1. 模型创建

```dart
// 批量创建平台发布模型
final publishModels = PlatformPublishModelFactory.createBatchFromAccountConfigs(
  selectedAccounts: state.selectedAccounts,
  accountConfigs: state.accountPlatformConfigs,
  video: video,
  images: images,
  cover: cover,
);
```

### 2. 模型验证

```dart
// 验证发布模型
final validationError = PlatformPublishModelFactory.validatePublishModels(publishModels);
if (validationError != null) {
  throw Exception(validationError);
}
```

### 3. 发布执行

```dart
// 使用模型发布
final tasks = await publishFactory.publishWithPlatformModels(
  publishModels,
  progressCallback: (progress, message) {
    // 进度回调
  },
);
```

## 核心优势

### 1. 类型安全
- **强类型**: 每个平台都有明确的类型定义
- **编译时检查**: 编译时就能发现类型错误
- **IDE支持**: 完整的代码提示和自动补全

### 2. 代码清晰
- **职责分离**: 每个模型只负责自己平台的逻辑
- **易于理解**: 平台特定的配置一目了然
- **维护简单**: 修改某个平台不影响其他平台

### 3. 扩展性强
- **新平台**: 只需创建新的模型类
- **新功能**: 在对应模型中添加新属性
- **向后兼容**: 不影响现有代码

### 4. 验证完善
- **平台特定验证**: 每个平台有自己的验证逻辑
- **统一验证接口**: 通过工厂统一验证
- **错误信息明确**: 精确定位问题所在

## 平台特定功能

### 1. 抖音平台

```dart
// 抖音特定配置
@override
Map<String, dynamic> getPlatformConfig() {
  final config = <String, dynamic>{};
  
  if (selfDeclare != null) config['self_declare'] = selfDeclare;
  if (mentionedUsers != null) config['mentioned_users'] = mentionedUsers;
  if (activityId != null) config['activity_id'] = activityId;
  if (hotspotId != null) config['hotspot_id'] = hotspotId;
  if (mixId != null) config['mix_id'] = mixId;
  config['visibility_type'] = visibilityType;
  
  return config;
}

// 抖音特定验证
@override
String? validate() {
  if (title != null && title!.length > 30) {
    return '抖音标题不能超过30个字符';
  }
  return null;
}
```

### 2. 小红书平台

```dart
// 小红书特定配置
@override
Map<String, dynamic> getPlatformConfig() {
  return {
    'is_private': isPrivate,
    'disable_comment': disableComment,
  };
}

// 小红书特定验证
@override
String? validate() {
  if (title != null && title!.length > 20) {
    return '小红书标题不能超过20个字符';
  }
  return null;
}
```

## 工厂方法功能

### 1. 平台信息管理

```dart
/// 获取平台显示名称
static String getPlatformDisplayName(String platform) {
  switch (platform) {
    case 'douyin': return '抖音';
    case 'xhs': return '小红书';
    case 'kwai': return '快手';
    default: return platform.toUpperCase();
  }
}

/// 获取平台主题色
static int getPlatformColor(String platform) {
  switch (platform) {
    case 'douyin': return 0xFF000000; // 黑色
    case 'xhs': return 0xFFFF2442; // 小红书红
    case 'kwai': return 0xFFFF6600; // 快手橙
    default: return 0xFF6366F1; // 默认紫色
  }
}
```

### 2. 配置管理

```dart
/// 获取平台的默认配置
static Map<String, dynamic> getDefaultConfig(String platform) {
  switch (platform) {
    case 'douyin':
      return {
        'visibility_type': 0,
        'self_declare': null,
        'mentioned_users': <Map<String, String>>[],
      };
    case 'xhs':
      return {
        'is_private': false,
        'disable_comment': false,
      };
    default:
      return {};
  }
}

/// 合并配置（用户配置覆盖默认配置）
static Map<String, dynamic> mergeConfig(
  String platform,
  Map<String, dynamic> userConfig,
) {
  final defaultConfig = getDefaultConfig(platform);
  final mergedConfig = Map<String, dynamic>.from(defaultConfig);
  
  userConfig.forEach((key, value) {
    if (value != null) {
      mergedConfig[key] = value;
    }
  });
  
  return mergedConfig;
}
```

### 3. 能力检查

```dart
/// 检查是否支持定时发布
static bool supportsScheduledPublish(String platform) {
  switch (platform) {
    case 'douyin':
    case 'xhs':
    case 'kwai':
      return true;
    default:
      return false;
  }
}

/// 获取平台支持的最大标题长度
static int getMaxTitleLength(String platform) {
  switch (platform) {
    case 'douyin': return 30;
    case 'xhs': return 20;
    case 'kwai': return 50;
    default: return 100;
  }
}
```

## 发布工厂集成

### 1. 模型转换

```dart
// 将平台模型转换为传统的发布参数模型
PublishParamsModel _convertModelToPublishParams(BasePlatformPublishModel publishModel) {
  final publishParams = PublishParamsModel(
    publishModel.video ?? File(''),
    title: publishModel.title,
    description: publishModel.description,
    topics: publishModel.topics,
    images: publishModel.images,
    cover: publishModel.cover,
    isScheduled: publishModel.isScheduled,
    scheduledTime: publishModel.scheduledTime,
  );
  
  // 设置平台特定配置
  publishParams.platformSpecificConfig = publishModel.getPlatformConfig();
  
  return publishParams;
}
```

### 2. 任务创建

```dart
// 为每个平台模型创建发布任务
for (final publishModel in publishModels) {
  // 获取平台实例
  final platInstance = PlatManager.instance.getPlatInstance(publishModel.platform);
  
  // 转换为传统的发布参数模型
  final publishParams = _convertModelToPublishParams(publishModel);
  
  // 创建发布任务
  final task = PublishTask(
    id: DateTime.now().millisecondsSinceEpoch.toString(),
    platform: publishModel.platform,
    platInstance: platInstance,
    account: publishModel.account,
    params: publishParams,
  );
  
  await executeTask(task);
}
```

## 扩展示例

### 添加新平台（B站）

```dart
/// B站发布模型
class BilibiliPublishModel extends BasePlatformPublishModel {
  /// B站特定配置
  final int partition;                    // 分区
  final List<String>? tags;              // 标签
  final bool allowReprint;               // 允许转载
  final String? source;                  // 转载来源

  BilibiliPublishModel({
    required super.account,
    super.title,
    super.description,
    super.topics,
    super.video,
    super.images,
    super.cover,
    super.isScheduled,
    super.scheduledTime,
    super.selectedLocation,
    this.partition = 1,
    this.tags,
    this.allowReprint = false,
    this.source,
  }) : super(platform: 'bilibili');

  @override
  Map<String, dynamic> getPlatformConfig() {
    return {
      'partition': partition,
      'tags': tags,
      'allow_reprint': allowReprint,
      'source': source,
    };
  }

  @override
  String? validate() {
    if (title != null && title!.length > 80) {
      return 'B站标题不能超过80个字符';
    }
    return null;
  }
}
```

## 总结

通过平台模型系统，我们实现了：

1. **清晰的架构**: 每个平台都有独立的模型类
2. **类型安全**: 强类型定义，编译时检查
3. **易于扩展**: 添加新平台只需创建新模型
4. **统一管理**: 通过工厂统一创建和验证
5. **平台特色**: 每个平台可以有独特的配置和验证逻辑

这种设计为多平台发布系统提供了坚实的基础，既保证了代码的清晰性，又具备了良好的扩展性。
