import 'dart:io';

import 'package:aitoearn_app/config/logger.dart';

void main() async {
  LoggerUtil.i('🚀 开始自动检测和重命名不符合Flutter命名规范的文件...');

  final projectRoot = Directory.current;
  final libDir = Directory('${projectRoot.path}/lib');

  if (!libDir.existsSync()) {
    LoggerUtil.i('❌ 错误：找不到lib目录');
    return;
  }

  final renamer = FlutterFileRenamer(libDir);
  await renamer.processAll();
}

class FlutterFileRenamer {
  final Directory libDir;
  final List<String> renamedFiles = [];
  final List<String> renamedDirs = [];
  final Map<String, String> importUpdates = {};

  FlutterFileRenamer(this.libDir);

  Future<void> processAll() async {
    LoggerUtil.i('📁 扫描项目结构...');

    // 1. 自动检测需要重命名的目录
    await _detectAndRenameDirectories();

    // 2. 自动检测需要重命名的文件
    await _detectAndRenameFiles();

    // 3. 更新所有import语句
    await _updateAllImports();

    // 4. 生成报告
    _generateReport();

    // 5. 运行检查
    await _runChecks();
  }

  Future<void> _detectAndRenameDirectories() async {
    LoggerUtil.i('\n📂 检测需要重命名的目录...');

    await for (final entity in libDir.list(recursive: true)) {
      if (entity is Directory) {
        final dirName = entity.path.split(Platform.pathSeparator).last;
        final newDirName = _convertToSnakeCase(dirName);

        if (dirName != newDirName && _shouldRename(dirName)) {
          final parentPath = entity.parent.path;
          final newPath = '$parentPath${Platform.pathSeparator}$newDirName';

          try {
            LoggerUtil.i('  📁 $dirName → $newDirName');
            await entity.rename(newPath);
            renamedDirs.add('$dirName → $newDirName');
            importUpdates['$dirName/'] = '$newDirName/';
          } catch (e) {
            LoggerUtil.e('  ❌ 重命名目录失败: $dirName → $newDirName ($e)');
          }
        }
      }
    }
  }

  Future<void> _detectAndRenameFiles() async {
    LoggerUtil.i('\n📄 检测需要重命名的文件...');

    await for (final entity in libDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        final fileName = entity.path.split(Platform.pathSeparator).last;
        final nameWithoutExt = fileName.replaceAll('.dart', '');
        final newNameWithoutExt = _convertToSnakeCase(nameWithoutExt);
        final newFileName = '$newNameWithoutExt.dart';

        if (fileName != newFileName && _shouldRename(nameWithoutExt)) {
          final parentPath = entity.parent.path;
          final newPath = '$parentPath${Platform.pathSeparator}$newFileName';

          try {
            LoggerUtil.i('  📄 $fileName → $newFileName');
            await entity.rename(newPath);
            renamedFiles.add('$fileName → $newFileName');
            importUpdates[fileName] = newFileName;
          } catch (e) {
            LoggerUtil.e('  ❌ 重命名文件失败: $fileName → $newFileName ($e)');
          }
        }
      }
    }
  }

  Future<void> _updateAllImports() async {
    if (importUpdates.isEmpty) {
      LoggerUtil.i('\n✅ 没有需要更新的import语句');
      return;
    }

    LoggerUtil.i('\n🔄 更新import语句...');
    var updatedCount = 0;

    await for (final entity in libDir.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        try {
          final content = await entity.readAsString();
          var updatedContent = content;
          var hasChanges = false;

          for (final update in importUpdates.entries) {
            if (updatedContent.contains(update.key)) {
              updatedContent = updatedContent.replaceAll(
                update.key,
                update.value,
              );
              hasChanges = true;
            }
          }

          if (hasChanges) {
            await entity.writeAsString(updatedContent);
            updatedCount++;
          }
        } catch (e) {
          LoggerUtil.e('  ❌ 更新import失败: ${entity.path} ($e)');
        }
      }
    }

    LoggerUtil.i('  ✅ 更新了 $updatedCount 个文件的import语句');
  }

  void _generateReport() {
    LoggerUtil.i('\n📊 重命名报告:');
    LoggerUtil.i('=' * 50);

    if (renamedDirs.isNotEmpty) {
      LoggerUtil.i('\n📁 重命名的目录 (${renamedDirs.length}):');
      for (final dir in renamedDirs) {
        LoggerUtil.i('  • $dir');
      }
    }

    if (renamedFiles.isNotEmpty) {
      LoggerUtil.i('\n📄 重命名的文件 (${renamedFiles.length}):');
      for (final file in renamedFiles) {
        LoggerUtil.i('  • $file');
      }
    }

    if (renamedDirs.isEmpty && renamedFiles.isEmpty) {
      LoggerUtil.i('\n✅ 所有文件和目录都已符合Flutter命名规范！');
    } else {
      LoggerUtil.i('\n✅ 总共处理了 ${renamedDirs.length + renamedFiles.length} 个项目');
    }
  }

  Future<void> _runChecks() async {
    LoggerUtil.i('\n🔍 运行检查...');

    // 检查是否有编译错误
    LoggerUtil.i('\n运行 flutter analyze...');
    final analyzeResult = await Process.run('flutter', [
      'analyze',
    ], workingDirectory: libDir.parent.path);

    if (analyzeResult.exitCode == 0) {
      LoggerUtil.i('✅ 代码分析通过，没有发现问题');
    } else {
      LoggerUtil.i('⚠️  发现一些问题，请查看详细信息:');
      LoggerUtil.i(analyzeResult.stdout);
      if (analyzeResult.stderr.isNotEmpty) {
        LoggerUtil.e('错误信息:');
        LoggerUtil.e(analyzeResult.stderr);
      }
    }

    LoggerUtil.i('\n💡 建议:');
    LoggerUtil.i('1. 运行 "flutter clean" 清理缓存');
    LoggerUtil.i('2. 运行 "flutter pub get" 重新获取依赖');
    LoggerUtil.i('3. 运行 "dart fix --apply" 自动修复一些问题');
    LoggerUtil.i('4. 在IDE中重新索引项目');
  }

  String _convertToSnakeCase(String input) {
    // 处理常见的缩写和特殊情况
    final specialCases = {
      'API': 'api',
      'APIs': 'apis',
      'URL': 'url',
      'HTTP': 'http',
      'JSON': 'json',
      'XML': 'xml',
      'UI': 'ui',
      'ID': 'id',
      'UUID': 'uuid',
    };

    var result = input;

    // 先处理特殊缩写
    for (final entry in specialCases.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }

    // 转换驼峰命名为下划线命名
    result =
        result
            .replaceAllMapped(
              RegExp(r'([a-z])([A-Z])'),
              (match) => '${match.group(1)}_${match.group(2)!.toLowerCase()}',
            )
            .replaceAllMapped(
              RegExp(r'([A-Z])([A-Z][a-z])'),
              (match) =>
                  '${match.group(1)!.toLowerCase()}_${match.group(2)!.toLowerCase()}',
            )
            .toLowerCase();

    // 清理多余的下划线
    result = result.replaceAll(RegExp(r'_+'), '_');
    result = result.replaceAll(RegExp(r'^_|_$'), '');

    return result;
  }

  bool _shouldRename(String name) {
    // 跳过已经符合规范的名称
    if (name == name.toLowerCase() && !name.contains(RegExp(r'[A-Z]'))) {
      return false;
    }

    // 跳过生成的文件
    if (name.endsWith('.g') || name.startsWith('messages_')) {
      return false;
    }

    // 跳过一些特殊文件
    final skipList = {'main', 'l10n', 'generated'};

    return !skipList.contains(name.toLowerCase());
  }
}
