# 平台配置功能实现说明

## 功能概述

为 `AccountSelector` 组件中的每个已选择账号添加了点击交互功能。当用户点击某个账号项时，会弹出一个平台配置对话框，显示该平台特定的发布参数配置选项。

## 主要功能

### 1. 账号点击交互
- 在 `AccountSelector` 组件的 `_buildAccountItem` 方法中添加了 `GestureDetector`
- 点击账号头像会打开对应平台的配置对话框
- 添加了配置指示器，显示账号是否有自定义配置

### 2. 平台配置对话框 (`PlatformConfigDialog`)
包含以下配置选项：

#### 通用配置
- **自定义标题**: 为该账号设置专门的标题，留空则使用全局标题
- **话题标签**: 设置平台特定的话题标签，支持平台限制（如抖音最多5个，小红书最多20个）
- **定时发布**: 独立的定时发布设置，支持平台特定的时间限制
- **地理位置**: 为每个平台单独选择位置信息
- **可见性设置**: 允许评论、允许分享等基础设置

#### 平台特定配置
- **抖音**:
  - 自主声明（内容自行拍摄/内容取材网络/内容由AI生成/可能引人不适/虚构演绎，仅供娱乐/危险行为，请勿模仿）
  - 作品可见性（公开/仅好友可见/仅自己可见）
  - @好友设置（最多100个好友）
  - 热点关联选择
  - 活动参与选择
  - 合集选择
  - 背景音乐设置（保留原声/选择背景音乐）
- **小红书**: 同步到朋友圈选项
- **快手**: 允许下载选项
- **微信视频号**: 仅好友可见选项

### 3. 位置选择功能
- 支持为每个平台单独选择地理位置
- 根据不同平台跳转到对应的位置获取页面：
  - 抖音: `DouyinLocationSelectPage`
  - 小红书: `XhsLocationSelectPage`
  - 快手: `KwaiLocationSelectPage`
  - 微信视频号: `WxSphLocationSelectPage`
- 使用账户的 `accessToken` 字段进行位置API调用

### 4. 数据存储和管理
- 在 `PublishState` 中添加了 `accountPlatformConfigs` 用于存储每个账号的配置
- 在 `PublishLogic` 中添加了配置管理方法：
  - `getAccountPlatformConfig()`: 获取账号配置
  - `setAccountPlatformConfig()`: 设置账号配置
  - `clearAccountPlatformConfig()`: 清除账号配置

### 5. 发布数据整合
- 修改了 `preparePublishData()` 方法，优先使用账号特定配置
- 配置优先级：账号特定配置 > 全局配置
- 支持以下配置的账号级别覆盖：
  - 标题
  - 话题标签
  - 位置信息
  - 定时发布设置
  - 平台特定参数

## 文件修改清单

### 新增文件
- `lib/pages/publish/widgets/platform_config_dialog.dart` - 平台配置对话框

### 修改文件
1. `lib/pages/publish/state.dart`
   - 添加 `accountPlatformConfigs` 响应式变量

2. `lib/pages/publish/logic.dart`
   - 添加账号配置管理方法
   - 修改 `preparePublishData()` 方法支持账号特定配置

3. `lib/pages/publish/widgets/account_selector.dart`
   - 添加账号点击交互
   - 添加配置指示器
   - 导入平台配置对话框

4. `lib/pages/publish/widgets/location_section.dart`
   - 更新位置选择逻辑，使用 `accessToken` 而不是 `cookie`

## 使用方式

1. 在发布页面选择多个账号
2. 点击任意账号头像
3. 在弹出的配置对话框中设置该账号的专属参数
4. 保存配置后，该账号会显示橙色配置指示器
5. 发布时会自动使用账号特定的配置

## 技术特点

- **响应式设计**: 使用 GetX 的响应式状态管理
- **平台适配**: 根据不同平台显示相应的配置选项和限制
- **数据持久化**: 配置数据存储在应用状态中，页面切换不丢失
- **优雅降级**: 未配置的账号使用全局配置作为默认值
- **用户体验**: 清晰的视觉指示器和直观的操作流程

## 扩展性

该实现具有良好的扩展性，可以轻松添加：
- 新的平台支持
- 更多的配置选项
- 配置模板功能
- 配置导入导出功能
