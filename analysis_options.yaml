# 项目代码分析配置
# 此文件配置了Dart代码静态分析器，用于检查错误、警告和代码规范
#
# 分析器识别的问题会在支持Dart的IDE中显示
# 也可以通过命令行运行 `flutter analyze` 来调用分析器

# 包含Flutter推荐的lint规则集
# 这些规则旨在鼓励良好的编码实践
include: package:flutter_lints/flutter.yaml

# 分析器配置
analyzer:
  # 排除分析的文件和目录
  exclude:
    - "**/*.g.dart" # 生成的代码文件
    - "**/*.freezed.dart" # Freezed生成的文件
    - "**/*.mocks.dart" # Mock生成的文件
    - "build/**" # 构建输出目录
    - "lib/generated/**" # 生成的代码目录
    - "test/.test_coverage.dart" # 测试覆盖率文件
    - "integration_test/**" # 集成测试文件

  # 强错误配置
  errors:
    # 将某些警告提升为错误
    # invalid_annotation_target: error
    missing_required_param: error
    # missing_return: error
    dead_code: warning
    avoid_print: ignore
    
    
    # 导入相关
    # unused_import: warning
    # unnecessary_import: warning

    # 类型相关
    # always_declare_return_types: warning
    type_annotate_public_apis: warning

  # 语言配置
  language:
    # strict-casts: true
    # strict-inference: true
    # strict-raw-types: true

# Lint规则配置
linter:
  rules:
    # === 样式规则 ===

    # 命名约定
    camel_case_types: true # 类型使用驼峰命名
    camel_case_extensions: true # 扩展使用驼峰命名
    file_names: true # 文件名使用下划线
    non_constant_identifier_names: true # 非常量标识符使用驼峰命名
    constant_identifier_names: true # 常量使用驼峰命名

    # 导入和库
    always_use_package_imports: true # 始终使用package导入
    directives_ordering: true # 导入指令排序

    # 代码格式
    always_put_required_named_parameters_first: true # required参数放在前面
    always_put_control_body_on_new_line: true # 控制体另起一行
    prefer_const_constructors_in_immutables: true # 不可变类使用const构造函数
    prefer_const_declarations: true # 优先使用const声明
    prefer_const_literals_to_create_immutables: true # 使用const字面量创建不可变对象

    # === 错误预防规则 ===

    # 空安全
    avoid_null_checks_in_equality_operators: true # 避免在相等操作符中进行空检查
    no_logic_in_create_state: true # createState中不要有逻辑

    # 类型安全
    # always_declare_return_types: true # 始终声明返回类型
    # type_annotate_public_apis: true # 公共API添加类型注解
    # avoid_types_on_closure_parameters: true # 闭包参数避免类型注解

    # 异常处理
    only_throw_errors: true # 只抛出Error对象

    # === 性能规则 ===

    # Widget性能
    avoid_function_literals_in_foreach_calls: true # forEach中避免函数字面量
    prefer_for_elements_to_map_fromIterable: true # 优先使用for元素而不是map
    prefer_spread_collections: true # 优先使用展开集合

    # 字符串性能
    prefer_interpolation_to_compose_strings: true # 优先使用字符串插值
    unnecessary_string_interpolations: true # 避免不必要的字符串插值

    # === Flutter特定规则 ===

    # Widget规则
    use_key_in_widget_constructors: true # Widget构造函数使用key
    prefer_const_constructors: true # 优先使用const构造函数
    sized_box_for_whitespace: true # 使用SizedBox而不是Container创建空白
    avoid_unnecessary_containers: true # 避免不必要的Container

    # 状态管理
    avoid_print: true # 避免使用print（生产环境）

    # === 可读性规则 ===

    # 注释和文档
    slash_for_doc_comments: true # 文档注释使用///
    # package_api_docs: true # 包API需要文档
    # public_member_api_docs: false # 公共成员API文档（可选）

    # 代码清晰度
    prefer_single_quotes: true # 优先使用单引号
    unnecessary_this: true # 避免不必要的this
    prefer_is_empty: true # 优先使用isEmpty
    prefer_is_not_empty: true # 优先使用isNotEmpty

    # 条件表达式
    prefer_conditional_assignment: true # 优先使用条件赋值
    prefer_if_null_operators: true # 优先使用??操作符

    # 多平台支持
    depend_on_referenced_packages: true # 依赖引用的包