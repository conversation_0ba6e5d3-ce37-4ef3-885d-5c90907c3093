# MediaKit 相关规则
-keep class com.alexmercerind.** { *; }
-keep class wseemann.media.** { *; }
-keep class app.jawn.** { *; }
-keep class org.mpris.** { *; }

# Flutter 相关规则
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# 保持原生库
-keep class org.videolan.libvlc.** { *; }
-keep class org.videolan.** { *; }
-keep class com.sun.jna.** { *; }

# 保持 Kotlin 相关类
-keep class kotlin.** { *; }
-keep class kotlinx.** { *; }

# 保持注解
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes Exceptions
-keepattributes InnerClasses 

# Google Play Core 相关规则
-keep class com.google.android.play.** { *; }
-keep class com.google.android.gms.common.** { *; }

# 新的Play Feature Delivery和Play In-App Update规则
-keep class com.google.android.play.core.splitinstall.** { *; }
-keep class com.google.android.play.core.tasks.** { *; }
-keep class com.google.android.play.core.common.** { *; }
-keep class com.google.android.play.core.appupdate.** { *; }
-keep class com.google.android.play.core.install.** { *; }
-keep class com.google.android.play.core.splitcompat.** { *; }
-keep class com.google.android.play.feature.** { *; }

# 从missing_rules.txt自动生成的规则
-dontwarn com.google.android.play.core.tasks.OnFailureListener
-dontwarn com.google.android.play.core.tasks.OnSuccessListener
-dontwarn com.google.android.play.core.tasks.Task

# 特别保留错误信息中提到的类
-keep class com.google.android.gms.common.annotation.NoNullnessRewrite { *; }
-keep class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }

# 防止Flutter Play Store相关类被删除
-keep class io.flutter.embedding.android.FlutterPlayStoreSplitApplication { *; }
-keep class io.flutter.embedding.engine.deferredcomponents.PlayStoreDeferredComponentManager { *; }
-keep class io.flutter.embedding.engine.deferredcomponents.PlayStoreDeferredComponentManager$* { *; }

# XML Stream 相关规则
-keep class javax.xml.stream.** { *; }
-keep class org.apache.tika.** { *; }

# 防止混淆PlayStoreDeferredComponentManager相关类
-keep class io.flutter.embedding.engine.deferredcomponents.** { *; }
-keep class io.flutter.embedding.android.** { *; } 