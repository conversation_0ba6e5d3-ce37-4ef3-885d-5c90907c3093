.errorBoundary {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  code {
    font-size: var(--fs-md);
    margin-bottom: 50px;
  }
  &:after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 64px;
    user-select: none;
    -webkit-app-region: drag;
  }

  :global {
    h1 {
      font-size: var(--fs-xl);
      margin-bottom: 20px;
    }
  }
}