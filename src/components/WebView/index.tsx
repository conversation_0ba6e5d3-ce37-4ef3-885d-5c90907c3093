import {
  ForwardedRef,
  forwardRef,
  memo,
  useEffect,
  useRef,
  useState,
} from 'react';
import { generateUUID } from '../../utils';
import styles from './webView.module.scss';
import { Spin } from 'antd';
import { ICookieParams } from '../../../electron/main/account/BrowserWindow/browserWindow';
import { AccountInfo } from '../../views/account/comment';
import { PlatType } from '../../../commont/AccountEnum';

export interface IWebViewRef {}

export interface IWebViewProps {
  url: string;
  cookieParams?: ICookieParams;
  // 是否开启沙盒化模式
  partition?: boolean | string;
  account?: AccountInfo;
  allowpopups?: boolean;
}

const WebView = memo(
  forwardRef(
    (
      { url, cookieParams, partition, account, allowpopups }: IWebViewProps,
      ref: ForwardedRef<IWebViewRef>,
    ) => {
      const webviewRef = useRef<HTMLWebViewElement>(null);
      // webView id
      const [webViewId, setWebViewId] = useState(-1);
      const [loading, setLoading] = useState(true);
      // 隔离ID
      const partitionId = useRef(generateUUID());

      useEffect(() => {
        console.log(cookieParams);
        console.log(JSON.stringify(cookieParams));
        setLoading(true);

        webviewRef.current?.addEventListener('dom-ready', async (e) => {
          // 每个平台localStorage添加
          if (account) {
            let jsCode;
            if (account?.type === PlatType.Douyin) {
              jsCode = `
                localStorage.setItem('douyin_web_hide_guide', '1');
                localStorage.setItem('user_info', '{"uid":"${account.account}","nickname":"${account.nickname}","avatarUrl":"${account.avatar}"}');
                localStorage.setItem('useShortcut2', '{"Wed Mar 12 2025":false}');
              `;
            }
            // @ts-ignore
            webviewRef.current!.executeJavaScript(jsCode);
          }

          // @ts-ignore
          if (webviewRef.current?.getURL() === 'about:blank') {
            // @ts-ignore
            const id = webviewRef.current!.getWebContentsId();
            setWebViewId(id);

            // @ts-ignore
            await window.ipcRenderer.invoke('ICP_ACCOUNT_CREATE_BROWSER_VIEW', {
              webViewId: id,
              cookieParams,
            });
          }
          setLoading(false);
        });

        return () => {
          window.ipcRenderer.invoke(
            'ICP_ACCOUNT_DESTROY_BROWSER_VIEW',
            webViewId,
          );
        };
      }, []);

      return (
        url && (
          <div className={styles.webview}>
            <Spin spinning={loading} tip="加载中...">
              <webview
                // @ts-ignore
                disablewebsecurity={'true'}
                ref={webviewRef}
                // @ts-ignore
                allowpopups={allowpopups ? 'true' : undefined}
                webpreferences="sandbox"
                src={loading ? 'about:blank' : url}
                style={{ width: '100%', height: '100%' }}
                partition={
                  typeof partition === 'boolean'
                    ? partition
                      ? partitionId.current
                      : undefined
                    : partition
                }
                useragent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
              ></webview>
            </Spin>
          </div>
        )
      );
    },
  ),
);
WebView.displayName = 'WebView';

export default WebView;
