/*  全局css变量 */
:root {
  /* 中性面板色组---------------------------------------------------- */
  --whiteColor1: #fff;
  --grayColor0: rgb(247,250,250);
  --grayColor1: #F3F3F3;
  --grayColor2: #EEEEEE;
  --grayColor3: #E7E7E7;
  --grayColor4: #DCDCDC;
  --grayColor5: #C5C5C5;
  --grayColor6: #A6A6A6;
  --grayColor7: #8B8B8B;
  --grayColor8: #777777;
  --grayColor9: #5E5E5E;
  --grayColor10: #4B4B4B;
  --grayColor11: #383838;
  --grayColor12: #2C2C2C;
  --grayColor13: #242424;
  --grayColor14: #181818;

  /* 超大号字 */
  --fs-xl: 20px;
  /* 大号字 */
  --fs-lg: 18px;
  /* 中号字 */
  --fs-md: 16px;
  /* 小号字 */
  --fs-sm: 14px;
  /* 超小号字 */
  --fs-xs: 12px;

  /*
    主题颜色逻辑在：main.tsx
   */

  /* 渐变背景色 */
  --gradientBackColor: linear-gradient(to right, #c565ef, #55D9ED);
  /*--gradientBackColor: linear-gradient(to right, #7881fd, #f2c3cf);*/

  /* 警告色 */
  --warningColor: #d48806;
  /* 错误色 */
  --errerColor: red;
  /* 成功色 */
  --successColor: rgb(47, 178, 122);
}
