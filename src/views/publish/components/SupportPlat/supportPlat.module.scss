.supportPlat {
  :global {
    .supportPlat-tip {
      margin-bottom: 10px;
      font-size: var(--fs-xs);
      display: flex;
      align-items: center;
      &--line {
        width: 100%;
        height: 1px;
        background: rgb(243, 243, 243);
      }
      &-text {
        white-space: nowrap;
        padding: 0 15px;
        color: rgb(136, 145, 145);
      }
    }
    .supportPlat-con {
      //display: grid;
      //grid-template-columns: repeat(8, 1fr);
      gap: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 340px;
      img {
        width: 25px;
      }
    }
    .supportPlatBg-title {
      display: none;
    }
  }
}

.supportPlatBg {
  padding: 15px;
  display: flex;
  border-radius: 10px;
  background: rgb(247, 250, 250);
  :global {
    .supportPlat-tip {
      display: none;
    }
    .supportPlat-con {
      display: grid;
      gap: 10px;
      grid-template-columns: repeat(7, 1fr);
    }
    .supportPlatBg-title {
      display: block;
      font-weight: 900;
      font-size: var(--fs-xs);
      color: rgb(157, 164, 164);
      margin: 3px 10px 0 0;
      white-space: nowrap;
    }
  }
}