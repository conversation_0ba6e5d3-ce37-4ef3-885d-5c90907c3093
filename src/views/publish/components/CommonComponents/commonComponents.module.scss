.chooseChunk {
  display: flex;
  cursor: pointer;
  flex-direction: column;
  background: rgb(247, 250, 250);
  width: 160px;
  height: 160px;
  box-sizing: border-box;
  padding: 20px;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  border: 1px solid transparent;
  transition: 0.3s;
  img {
    width: 50px;
    height: 45px;
    object-fit: contain;
  }
  button {
    margin-top: 20px;
    width: 110px;
    &:hover {
      opacity: 0.8;
    }
  }
}

.chooseAccountChunk {
  height: 180px;
  background: var(--colorPrimary1);
  border: 1px dashed transparent;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &:hover {
    border-color: var(--colorPrimary6);
  }
  :global {
    .chooseAccountChunk-user {
      font-size: 60px;
      color: var(--colorPrimary4);
      margin-bottom: 10px;
    }
  }
}

.usersSelect {
  display: flex;
  padding: 3px 0;
  align-items: center;
  :global {
    .usersSelect-con {
      margin-left: 10px;
      &-name {
        font-size: var(--fs-md);
      }
      &-footer {
        margin-top: 5px;
        font-size: var(--fs-xs);
        display: flex;
        &-follower {
          color: var(--grayColor7);
        }
        &-unique {
          margin-left: 20px;
        }
      }
    }
  }
}

.locationSelect {
  :global {
    .location-name {
      margin-bottom: 2px;
    }
    .location-simpleAddress {
      font-size: var(--fs-xs);
      color: var(--grayColor7);
    }
  }
}

.hotspotSelect {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 10px;
  :global {
    .hotspotSelect-left {
      display: flex;
      align-items: center;
      img {
        width: 35px;
        height: 35px;
        border-radius: 4px;
        object-fit: cover;
        margin-right: 10px;
      }
    }

    .hotspotSelect-right {
    }
  }
}

.activitySelect {
  border-bottom: 1px solid var(--grayColor3);
  height: 50px;
  cursor: pointer;
  padding: 10px 0;
  &:last-of-type {
    border-bottom: none;
  }
  :global {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .activitySelect-left {
      display: flex;
      align-items: center;
      height: 100%;
      &-img {
        overflow: hidden;
        border-radius: 6px;
        margin-left: 10px;
      }
      ul {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
        li {
          &:nth-child(1) {
            font-weight: 900;
          }
        }
      }
      img {
        display: block;
        width: 36px;
        height: 42px;
        border-radius: 8px;
        margin-right: 8px;
      }
    }
    .activitySelect-right {
      display: flex;
      flex-direction: column;
      align-items: end;
      justify-content: space-between;
      height: 100%;
      &-top {
        padding: 1px 4px;
        border-radius: 6px;
        font-size: var(--fs-xs);
        background: rgb(250, 250, 250);
        border: 1px solid #bbbbbb;
      }
      &-bottom {
        button {
          margin: 0 0 0 10px;
          padding: 0;
          height: auto;
        }
      }
    }
  }
}

.activityDetails {
  :global {
    .activityDetails-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f1f1f1;
      padding: 30px 0;
      margin-bottom: 20px;
      &-left {
        display: flex;
        align-items: center;
        &-img {
          overflow: hidden;
          img {
            width: 50px;
            object-fit: contain;
            margin-right: 10px;
            border-radius: 6px;
          }
        }
        ul {
          display: flex;
          flex-direction: column;
        }
        &-title {
          font-weight: 900;
          font-size: var(--fs-md);
          margin-bottom: 3px;
        }
        &-hot {
          font-size: var(--fs-xs);
          color: var(--grayColor7);
        }
      }

      &-tag {
        padding: 1px 4px;
        border-radius: 6px;
        font-size: var(--fs-xs);
        margin-bottom: 6px;
        background: rgb(225, 225, 225);
      }
    }

    .activityDetails-bottom {
      h2 {
        font-size: var(--fs-sm);
      }
      &-text {
        color: var(--grayColor7);
        margin-bottom: 10px;
      }
      ul {
        li {
          margin-right: 5px;
        }
      }
    }
  }
}

.tips {
  color: rgb(178, 185, 185);
  text-align: left;
  font-size: var(--fs-sm);
}

.accountRestartLogin {
  display: flex;
  align-items: center;
  font-size: var(--fs-sm);
  button {
    margin: 0;
    padding: 0;
    height: auto;
  }
}

.mixSelectItem {
  justify-content: space-between;
  align-items: center;
  display: flex;
  :global {
    .mixSelectItem-left {
      display: flex;
      align-items: center;
      img {
        height: 100%;
        width: 30px;
        object-fit: contain;
        margin-right: 8px;
      }
    }
  }
}
