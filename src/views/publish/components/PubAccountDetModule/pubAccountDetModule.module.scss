.pubAccountDetModule {
  min-height: 300px;
  :global {
    .pubAccountDetModule-tips {
      margin-bottom: 20px;
      & > .anticon {
        margin-left: 10px;
        color: var(--colorPrimary6);
      }
    }

    .pubAccountDetModule-accounts {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      max-height: 60vh;
      overflow: auto;
      gap: 10px;
      &-account {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        overflow: hidden;
      }
      &-name {
        white-space: nowrap;
        margin-top: 5px;
        text-overflow: ellipsis;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        &-wrapper {
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .anticon {
          margin-right: 3px;
          display: none;
        }
      }
      &-proxy {
        color: var(--grayColor7);
        font-size: var(--fs-xs);
        white-space: nowrap;
        width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      &-disable {
        color: var(--errerColor);
        .pubAccountDetModule-accounts-disable-icon {
          display: inline-block;
        }
        .pubAccountDetModule-accounts-proxy {
          color: var(--errerColor);
        }
      }
      &-ol {
        color: var(--successColor);
        .pubAccountDetModule-accounts-ol-icon {
          display: inline-block;
        }
        .pubAccountDetModule-accounts-proxy {
          color: var(--successColor);
        }
      }
    }
  }
}

.loginStatusDisable {
  display: flex;
  justify-content: space-between;
}
