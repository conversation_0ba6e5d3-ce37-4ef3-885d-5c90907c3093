.videoPubSetModal {
  :global {
    textarea {
      resize: none;
      height: 150px !important;
    }

    .ant-tabs-tab {
      padding: 0;
      margin: 0 10px 0 0 !important;
      background: #fff;
      transition: 0.3s;
    }
    .ant-tabs-ink-bar {
      height: 3px !important;
    }
    .ant-tabs-tab-active {
      background: var(--colorPrimary2);
    }
    .videoPubSetModal_con {
      display: flex;
      justify-content: space-between;
      &--noMore {
        justify-content: center;
        .videoPubSetModal_con-left {
          display: none !important;
        }
        .videoPubSetModal_con-right {
          width: 250px;
        }
      }
      &-red {
        color: var(--errerColor);
        margin-right: 5px;
      }
      &-tips {
        color: rgb(178, 185, 185);
      }
      &_top {
        margin-bottom: 10px;
        button {
          width: 100%;
        }
      }
      &-left {
        width: calc(70% - 50px);
        height: 55vh;
        overflow: auto;
        padding: 0 20px 20px 0;

        h1 {
          font-weight: 400;
          font-size: var(--fs-sm);
          display: flex;
          align-items: center;
          margin: 20px 0 8px 0;
        }
      }
      &-right {
        width: 30%;
        video {
          width: 100%;
          border-radius: 20px;
        }
      }
    }
    .ant-tabs-nav-wrap {
      border: 1px solid #e3e3e3;
      border-right: none;
      border-left: none;
    }
    .videoPubSetModal-tabLabel {
      height: 70px;
      width: 100px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      .ant-alert {
        position: absolute;
        z-index: 10;
        top: 0;
        width: 100%;
        height: 20px;
        padding: 0;
        border-radius: 0 0 10px 10px;
        display: flex;
        border-top: none;
        justify-content: center;
        .ant-alert-content {
          flex: none;
        }
        .anticon {
          font-size: var(--fs-xs);
          margin-right: 4px;
        }
        .ant-alert-message {
          font-size: var(--fs-xs);
        }
      }
      & > span {
        display: flex;
        overflow: hidden;
        & > img {
          width: 15px;
        }
      }
      .ant-avatar {
        flex-shrink: 0 !important;
      }
      &-name {
        display: block;
        margin: 0 3px;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .videoPubSetModalVideo {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-height: 500px;
      .ant-segmented {
        margin-bottom: 15px;
      }

      &-video {
        background: black;
        border-radius: 30px;
        overflow: hidden;
        box-sizing: border-box;
        position: relative;
        width: 100%;
        height: 510px;
        &-wrapper {
          margin: 5px;
          box-sizing: border-box;
          height: 100%;
          border-radius: 30px;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }
        &-top {
          position: absolute;
          width: 50%;
          height: 20px;
          z-index: 10;
          border-radius: 0 0 15px 15px;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          background: black;
        }
        video {
          width: 100%;
          max-height: 100%;
          object-fit: cover;
          border-radius: 0;
        }
      }

      &-coverPreview {
        display: flex;
        justify-content: space-between;
        &-con {
          width: calc(50% - 5px);
        }
        &-box {
          width: 100%;
          margin-bottom: 5px;
          border-radius: 10px;
          overflow: hidden;
          &-img {
            background: rgb(196, 196, 196);
            width: 100%;
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          &-bottom {
            background: rgb(188, 188, 188);
            color: #fff;
            padding: 8px;
            box-sizing: border-box;
            p {
              font-size: 10px;
            }
            ul {
              display: flex;
              justify-content: space-between;
              li {
                white-space: nowrap;
                font-size: 7px;
                display: flex;
                align-items: center;
                box-sizing: border-box;
                &:nth-child(1) {
                  max-width: 80%;
                }
                span {
                  text-overflow: ellipsis;
                  overflow: hidden;
                }
                .anticon {
                  margin-right: 2px;
                }
                .ant-avatar {
                  width: 16px;
                  height: 16px;
                  margin-right: 3px;
                  flex-shrink: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}

.videoPubSetModal_titleWrap {
  padding-bottom: 10px;
  display: flex;
  align-items: center;

  :global {
    .videoPubSetModal-title {
    }
    .videoPubSetModal-more {
      margin-left: 30px;
      font-size: var(--fs-sm);
      &-core {
        display: flex;
        align-items: center;
        label {
          margin-right: 5px;
        }
      }
      &-tips {
        font-weight: 400;
        font-size: var(--fs-xs);
        color: var(--grayColor7);
      }
    }
  }
}