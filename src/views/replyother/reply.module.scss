.reply {
  height: 100%;
  :global {
  }

  .postList {
    padding: 0 20px;
  }
  
  .webviewModal {
    :global {
      .ant-modal-content {
        padding: 0;
        overflow: hidden;
        height: 80vh;
        
        .ant-modal-body {
          height: 100%;
          padding: 0;
        }
        
        .ant-modal-close {
          top: 10px;
          right: 10px;
          z-index: 1000;
        }
      }
    }
  }
  
  .webviewContainer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .customWebviewModal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .modalOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .modalContent {
    position: relative;
    width: 90%;
    height: 90%;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    z-index: 1001;
  }
  
  .modalHeader {
    display: flex;
    justify-content: flex-end;
    padding: 8px;
    background-color: #f0f0f0;
  }
  
  .closeButton {
    color: #333;
    
    &:hover {
      color: #1890ff;
    }
  }
  
  .modalBody {
    flex: 1;
    position: relative;
    overflow: hidden;
  }
  
  .loadingContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1;
  }
  
  // 瀑布流布局样式
  .myMasonryGrid {
    display: flex;
    width: auto;
    margin-left: -16px; // 列间距
  }
  
  .myMasonryGridColumn {
    padding-left: 16px; // 列间距
    background-clip: padding-box;
  }
  
  .masonryItem {
    margin-bottom: 16px;
    
    :global {
      .ant-card {
        width: 320px;
      }
    }
  }

  // 任务面板样式
  .taskPanel {
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 8px;
    
    :global {
      .ant-collapse {
        border-radius: 8px;
        overflow: hidden;
      }
    }
  }
  
  .customCommentsSection {
    background-color: #f9f9f9;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
  }
  
  .commentsList {
    max-height: 150px;
    overflow-y: auto;
    margin-bottom: 12px;
  }
  
  .commentItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  // 添加自动加载区域样式
  .loadMoreArea {
    width: 100%;
    padding: 16px 0;
    min-height: 60px;
  }
  
  .loadingMore {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: 14px;
  }
  
  .noMoreData {
    color: #999;
    font-size: 14px;
    text-align: center;
    
    :global {
      .ant-divider {
        color: #999;
        font-size: 14px;
      }
    }
  }
}

.account {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;

  .account-noSelect {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 8px;
    padding: 20px;
    color: #999;
    font-size: 16px;

    .anticon {
      font-size: 24px;
      margin-bottom: 8px;
    }
  }
}