.publish {
  width: 100%;
  height: 100%;
  display: flex;

  :global {
    .ant-segmented {
      padding: 30px 10px;

      .ant-segmented-item {
        margin-bottom: 5px;
        padding: 3px 40px 3px 0;
      }
    }
  }
}

.taskContainer {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.tabsContainer {
  background-color: #fff;
  padding: 0 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.taskList {
  overflow-y: auto;
  padding: 16px;
  background: #f5f5f5;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  :global {
    .ant-list {
      min-height: 100%;
    }

    .ant-list-items {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      justify-content: flex-start;
    }

    .ant-list-item {
      flex: 0 0 calc(33.333% - 16px);
      max-width: 350px;
      margin: 0 !important;
    }

    .ant-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      transition: all 0.3s;
      max-width: 350px;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .ant-card-cover {
      height: 180px;
      overflow: hidden;
      background: #fafafa;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .ant-card-body {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 16px;
    }

    .ant-card-meta {
      flex: 1;
    }

    .ant-card-actions {
      background: #fafafa;
      padding: 12px 0;
    }
  }
}

.taskCard {
gap: 0px 28px;

  .taskImage {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .taskTitle {
    margin-bottom: 12px;

    h5 {
      margin: 0 0 8px 0;
      font-size: 16px;
      line-height: 1.4;
    }
  }

  .taskInfo {
    .taskProgress {
      margin-bottom: 8px;
    }

    .ant-typography {
      margin-bottom: 8px;
      line-height: 1.6;
    }
  }

  .taskDeadline {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
  }
}

.taskContent {
  display: flex;
  flex: 1;
  margin-bottom: 12px;
}

.imageContainer {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  margin-right: 12px;
  overflow: hidden;
  border-radius: 4px;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.productInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.productTitle {
  text-align: left;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: normal;
  color: #333;
}

.priceInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.price {
  font-size: 16px;
  font-weight: bold;
  color: #ff4d4f;
}

.likes {
  font-size: 12px;
  color: #999;
}

.discountTag {
  margin: 0;
  display: flex;
  gap: 4px;
  flex-direction: row;
  justify-content: flex-start;
}

.viewsInfo {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.level {
  color: #666;
}

.taskFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
}

.taskTips {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}

.infoIcon {
  margin-left: 4px;
  color: #999;
  cursor: pointer;
  
  &:hover {
    color: #1890ff;
  }
}

.applyButton {
  background-color: #a66ae4;
  border-color: #a66ae4;
  
  &:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }
}

.taskPageContainer {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.taskHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.taskHeaderLeft {
  display: flex;
  gap: 12px;
}

.taskButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  color: #666;
  
  &:hover {
    background-color: #e6f7ff;
    color: #1890ff;
  }
  
  span {
    font-size: 14px;
  }
}

.activeTaskButton {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
}

.taskHeaderRight {
  display: flex;
  gap: 12px;
}

.withdrawText {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1890ff;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    color: #40a9ff;
  }
}

.taskContent {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.taskListContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 16px 0;
}

.productGridContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.productGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 16px;
  
  @media (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.productCard {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s;
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.productContent {
  display: flex;
  margin-bottom: 12px;
  flex: 1;
}

.imageContainer {
  width: 128px;
  height: 128px;
  flex-shrink: 0;
  margin-right: 12px;
  overflow: hidden;
  border-radius: 4px;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.productInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.priceRow {
  display: flex;
  flex-direction: column;
}

.price {
  font-size: 16px;
  font-weight: bold;
  color: #ff4d4f;
}

.discountTag {
  margin: 0;
  display: flex;
  gap: 4px;
  flex-direction: row;
  justify-content: flex-start;
}

.infoRow {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: auto;
}

.infoItem {
  display: flex;
  font-size: 12px;
  color: #666;
}

.infoLabel {
  color: #999;
  margin-right: 4px;
}

.infoValue {
  color: #666;
}

.sales {
  font-size: 12px;
  color: #999;
}

.viewsInfo {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
  margin-top: auto;
}

.level {
  color: #666;
}

.taskFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: auto;
}

.taskTips {
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}

.infoIcon {
  margin-left: 4px;
  color: #999;
  cursor: pointer;
  
  &:hover {
    color: #1890ff;
  }
}

.applyButton {
  background-color: #a66ae4;
  border-color: #a66ae4;
  
  &:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }
}

.loadMoreContainer {
  text-align: center;
  padding: 16px 0;
  
  button {
    color: #a66ae4;
    font-size: 14px;
    
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  margin-bottom: 16px;
}

.platformIconWrapper {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.1);
  }
}

.platformIcon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

// 响应式布局
@media (max-width: 1200px) {
  .taskList {
    :global {
      .ant-list-item {
        flex: 0 0 calc(50% - 16px);
        max-width: 350px;
      }
    }
  }
}

@media (max-width: 768px) {
  .taskList {
    padding: 12px;

    :global {
      .ant-list-item {
        flex: 0 0 100%;
        max-width: 350px;
      }
    }
  }
}

.loadMoreTrigger {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
}

.taskDetail {
  padding: 16px;
  
  .taskDetailHeader {
    margin-bottom: 16px;
    display: flex;
    flex-direction: row;
    gap: 12px;
  }
  
  .taskDescription {
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
  }
  
  .bannerContainer {
    margin: 16px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    
    .taskBanner {
      :global {
        .ant-carousel .slick-dots {
          bottom: 16px;
          
          li button {
            background: rgba(255, 255, 255, 0.7);
            
            &:hover {
              background: #fff;
            }
          }
          
          li.slick-active button {
            background: #fff;
          }
        }
        
        .ant-carousel .slick-prev,
        .ant-carousel .slick-next {
          z-index: 10;
          width: 40px;
          height: 40px;
          background: rgba(0, 0, 0, 0.3);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &:hover {
            background: rgba(0, 0, 0, 0.5);
          }
          
          &:before {
            display: none;
          }
        }
      }
    }
    
    .bannerItem {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f0f0f0;
    }
    
    .bannerImage {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}