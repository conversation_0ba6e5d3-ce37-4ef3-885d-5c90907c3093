.taskDetailModal {
  :global {
    .ant-modal-content {
      padding: 0;
      overflow: hidden;
      border-radius: 8px;
    }
    
    .ant-modal-body {
      padding: 0;
    }
  }
}

.taskDetailContainer {
  display: flex;
  flex-direction: column;
}

.taskTitle {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  padding: 16px 24px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

.taskContent {
  display: flex;
  padding: 24px;
}

.taskImageContainer {
  width: 300px;
  flex-shrink: 0;
  margin-right: 24px;
  position: relative;
}

.taskImage {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.taskImageTags {
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  padding: 12px;
}

.imageTag {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
}

.richTextTag {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  border-radius: 4px;
  padding: 8px 12px;
  max-width: 280px;
  
  // 富文本内容样式
  :global {
    p {
      margin: 0 0 8px;
      line-height: 1.5;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    strong, b {
      font-weight: 600;
    }
    
    em, i {
      font-style: italic;
    }
    
    ul, ol {
      margin: 8px 0;
      padding-left: 20px;
    }
    
    li {
      margin-bottom: 4px;
    }
    
    a {
      color: #1890ff;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    img {
      max-width: 100%;
      height: auto;
      margin: 8px 0;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 8px 0;
      
      th, td {
        border: 1px solid #e8e8e8;
        padding: 8px;
        text-align: left;
      }
      
      th {
        background-color: #f5f5f5;
        font-weight: 500;
      }
    }
    
    blockquote {
      margin: 8px 0;
      padding: 8px 16px;
      border-left: 4px solid #f0f0f0;
      background-color: #fafafa;
      color: #666;
    }
    
    code {
      background-color: #f5f5f5;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: monospace;
    }
    
    pre {
      background-color: #f5f5f5;
      padding: 12px;
      border-radius: 4px;
      overflow-x: auto;
      margin: 8px 0;
      
      code {
        background-color: transparent;
        padding: 0;
      }
    }
  }
}

.taskInfoContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.infoItem {
  display: flex;
  align-items: center;
}

.infoLabel {
  width: 100px;
  color: #666;
  font-size: 14px;
}

.infoValue {
  color: #333;
  font-size: 14px;
}

.infoPrice {
  color: #ff4d4f;
  font-size: 16px;
  font-weight: 500;
}

.infoReward {
  color: #ff4d4f;
  font-size: 16px;
  font-weight: 500;
}

.taskFooter {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px 24px 24px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

.applyButton {
  background-color: #a66ae4;
  border-color: #a66ae4;
  padding: 0 32px;
  height: 40px;
  
  &:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }
}

.cancelButton {
  padding: 0 32px;
  height: 40px;
}

.noDataContainer {
  padding: 40px;
  text-align: center;
  color: #999;
} 