.taskInfoModal {
  :global {
    .ant-modal-content {
      padding: 0;
      overflow: hidden;
      border-radius: 8px;
    }
    
    .ant-modal-body {
      padding: 0;
    }
    
    .ant-modal-close {
      color: #999;
      
      &:hover {
        color: #333;
      }
    }
  }
}

.taskInfoContainer {
  display: flex;
  flex-direction: column;
}

.taskInfoHeader {
  padding: 20px 24px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.taskTitle {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.taskId {
  font-size: 14px;
  color: #999;
  font-weight: normal;
  margin-left: 12px;
  display: flex;
  align-items: center;
}

.copyIcon {
  margin-left: 4px;
  cursor: pointer;
  color: #1890ff;
  
  &:hover {
    color: #40a9ff;
  }
}

.taskTag {
  font-size: 12px;
}

.taskInfoContent {
  padding: 24px;
  flex: 1;
}

.videoContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.taskImageContainer {
  width: 100%;
  height: 240px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.taskImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.videoOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.playIcon {
  font-size: 48px;
  color: #fff;
  opacity: 0.8;
  transition: opacity 0.2s, transform 0.2s;
  
  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
}

.videoCaption {
  margin-top: 8px;
  color: #666;
  font-size: 14px;
}

.taskDetails {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.taskDetail {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.detailIcon {
  margin-right: 8px;
  color: #999;
  font-size: 16px;
}

.detailLabel {
  color: #666;
  margin-right: 8px;
  min-width: 70px;
}

.detailValue {
  color: #333;
  font-weight: 500;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.taskDescription {
  margin-bottom: 24px;
}

.descriptionContent {
  color: #666;
  line-height: 1.6;
  
  p {
    margin-bottom: 12px;
  }
  
  img {
    max-width: 100%;
    border-radius: 4px;
    margin: 8px 0;
  }
}

.videoDetails {
  margin-bottom: 24px;
}

.videoInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.videoInfoItem {
  display: flex;
  align-items: flex-start;
}

.videoInfoLabel {
  color: #666;
  margin-right: 8px;
  min-width: 70px;
}

.videoInfoValue {
  color: #333;
  flex: 1;
}

.taskInfoFooter {
  padding: 16px 24px;
  background-color: #f9f9f9;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.taskStatus {
  display: flex;
  align-items: center;
}

.statusLabel {
  color: #666;
  margin-right: 8px;
}

.taskActions {
  display: flex;
  gap: 12px;
}

.cancelButton {
  min-width: 80px;
}

.applyButton {
  min-width: 100px;
  
  &:hover {
    background-color: #9559d1 !important;
    border-color: #9559d1 !important;
  }
}

.noDataContainer {
  padding: 40px;
  text-align: center;
  color: #999;
  font-size: 16px;
} 