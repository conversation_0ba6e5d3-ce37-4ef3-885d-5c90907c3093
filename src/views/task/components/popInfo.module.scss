.taskInfoModal {
  :global {
    .ant-modal-content {
      padding: 0;
      overflow: hidden;
      border-radius: 8px;
    }
    
    .ant-modal-body {
      padding: 0;
    }
    
    .ant-modal-close {
      color: #999;
      
      &:hover {
        color: #333;
      }
    }
  }
}

.taskInfoContainer {
  display: flex;
  flex-direction: column;
}

.taskInfoHeader {
  padding: 20px 24px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.taskTitle {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.taskId {
  font-size: 14px;
  color: #999;
  font-weight: normal;
  margin-left: 12px;
  display: flex;
  align-items: center;
}

.copyIcon {
  margin-left: 4px;
  cursor: pointer;
  color: #1890ff;
  
  &:hover {
    color: #40a9ff;
  }
}

.taskTag {
  font-size: 12px;
}

.taskInfoContent {
  padding: 24px;
  flex: 1;
}

.taskImageContainer {
  width: 100%;
  height: 240px;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.taskImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.taskDetails {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.taskDetail {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.detailIcon {
  margin-right: 8px;
  color: #999;
  font-size: 16px;
}

.detailLabel {
  color: #666;
  margin-right: 8px;
  min-width: 70px;
}

.detailValue {
  color: #333;
  font-weight: 500;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.taskDescription {
  margin-bottom: 24px;
}

.descriptionContent {
  color: #666;
  line-height: 1.6;
  
  p {
    margin-bottom: 12px;
  }
  
  img {
    max-width: 100%;
    border-radius: 4px;
    margin: 8px 0;
  }
}

.appDetails {
  margin-bottom: 24px;
}

.appInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.appInfoItem {
  display: flex;
  align-items: flex-start;
}

.appInfoLabel {
  color: #666;
  margin-right: 8px;
  min-width: 70px;
}

.appInfoValue {
  color: #333;
  flex: 1;
}

.taskInfoFooter {
  padding: 16px 24px;
  background-color: #f9f9f9;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.taskStatus {
  display: flex;
  align-items: center;
}

.statusLabel {
  color: #666;
  margin-right: 8px;
}

.taskActions {
  display: flex;
  gap: 12px;
}

.cancelButton {
  min-width: 80px;
}

.applyButton {
  min-width: 100px;
  background-color: #a66ae4;
  border-color: #a66ae4;
  
  &:hover {
    background-color: #73d13d;
    border-color: #73d13d;
  }
}

.noDataContainer {
  padding: 40px;
  text-align: center;
  color: #999;
  font-size: 16px;
} 