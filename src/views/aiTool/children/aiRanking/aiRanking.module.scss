@use "@/styles/mixin";

.aiRanking {
  padding: 15px;
  width: 100%;
  min-width: 0;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 0;
  font-size: 14px;
  :global {
    .aiRanking-rank {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 30px;
      text-align: center;
      line-height: 30px;
      font-size: var(--fs-md);
      .ant-tag {
        margin-top: 6px;
      }
      &-up, &-down {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        height: 20px;
        margin: 0;
        min-width: 40px;
        .anticon {
          margin: 0 2px 0 0 !important;
        }
      }
      &-up {
        background: var(red);
      }
      &-down {
      }
    }
    .ant-table-tbody {
      .ant-table-row {
        &:nth-child(odd) {
          background: #f2f5f6;
        }
        td {
          .anticon {
            margin-left: 5px;
            cursor: pointer;
          }
        }
      }
    }
    .aiRanking-topthree {
      width: 30px;
      color: #fff;
      background: url("../../images/bg-rank-index.png") no-repeat center / cover;
    }
    .ant-table-thead {
      th {
        white-space: nowrap;
        color: #9a9a9a !important;
        padding-top: 10px !important;
        padding-bottom: 10px !important;
        font-weight: 400 !important;
        .anticon {
          margin-right: 3px;
        }
      }
    }
    .aiRanking-title {
      margin-bottom: 20px;
      font-weight: 900;
      font-size: 24px;
    }
    .aiRanking-productName {
      display: flex;
      align-items: center;
      overflow: hidden;
      label {
        font-size: var(--fs-md);
        margin-bottom: 6px;
      }
      .ant-avatar {
        white-space: nowrap;
        flex-shrink: 0;
      }
      &-con {
        overflow: hidden;
        margin-left: 10px;
        white-space: nowrap;
        text-overflow: ellipsis;
        &-des {
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
          color: var(--grayColor8);
          font-size: var(--fs-xs);
        }
      }
    }
    .aiRanking-head {
      width: 100%;
      margin-bottom: 10px;
      background: var(--grayColor0);
      border-radius: 6px;
      padding: 15px;
      box-sizing: border-box;
      .aiRanking-head-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        &:last-of-type {
          margin-bottom: 0;
        }
        .aiRanking-head-title {
          margin-right: 20px;
          color: var(--grayColor7);
        }
      }
    }

    .aiRanking-content {
      height: 100%;
      min-width: 0;
      display: flex;
      min-height: 0;
      @include mixin.table-flex-container-with-selectors;
    }
  }
}
