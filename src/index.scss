@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  background: #fff;
  color: #222;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>,
  'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
  'Noto Color Emoji';
}

.ant-tabs-dropdown {
  z-index: 10000000;
}

/* 确保根元素和 body 占满整个视口高度 */
html, body, #root {
  @apply h-full w-full m-0 p-0;
}

/* 添加一些全局的布局类 */
@layer components {
  .page-container {
    @apply w-full h-full p-4 overflow-auto;
  }
  
  .content-wrapper {
    @apply max-w-7xl mx-auto w-full h-full;
  }
}

.ant-drawer-header {
  -webkit-app-region: no-drag;
}

.ant-image-preview-close {
  top: 60px;
}

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

code {
  background-color: #1a1a1a;
  padding: 2px 4px;
  margin: 0 4px;
  border-radius: 4px;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: var(--whiteColor1);
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
  code {
    background-color: #f9f9f9;
  }
}

/* 去掉列表项的点 */
* {
  margin: 0;
  padding: 0;
}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* 全局自定义滚动条样式 */
.ant-table-body {
  scrollbar-width: auto;
  scrollbar-color: auto;
}
*::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

*::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 6px;
}

*::-webkit-scrollbar-thumb {
  background: #cccccc;
  border-radius: 6px;
  border: 2px solid #f0f0f0;
}

*::-webkit-scrollbar-thumb:hover {
  background: #a2a2a2;
}
