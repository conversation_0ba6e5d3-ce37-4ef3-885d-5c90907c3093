# AI生成和话题选择功能实现

## 功能概述

根据桌面端TSX版本的实现，在Flutter中添加了AI生成标题/描述功能和话题选择功能，完全复刻了桌面端的交互逻辑和API调用。

## 1. AI生成功能

### 核心组件
- **`AiCreateButton`** - AI生成按钮组件
- **`AiCreateType`** - AI生成类型枚举

### 功能特点
- **智能生成**: 基于视频内容生成标题和描述
- **文件上传**: 使用 `/api/file/upload` 接口上传视频文件
- **缓存机制**: 防止重复上传相同视频文件
- **加载状态**: 显示生成进度和加载动画
- **错误处理**: 完善的错误提示和异常处理

### API接口
```dart
// AI生成接口
Future<String?> generateVideoAiTitleApi({
  required String url,      // 视频URL
  required int type,        // 1=标题, 2=描述
  required int max,         // 最大字数
})

// 文件上传接口
POST /api/file/upload
```

### 使用场景
1. **视频发布页面**: 标题和描述输入框旁的AI生成按钮
2. **平台配置对话框**: 抖音平台的标题和描述AI生成

### 技术实现
- **防抖处理**: 避免重复点击
- **文件缓存**: 基于文件大小和名称生成缓存key
- **响应式UI**: 根据生成状态动态更新按钮样式
- **错误恢复**: 网络错误时的重试机制

## 2. 话题选择功能

### 核心组件
- **`TopicSelector`** - 话题选择器组件
- **`TopicItem`** - 话题数据模型
- **`TopicResponse`** - 话题响应模型

### 功能特点
- **实时搜索**: 输入关键词实时搜索话题
- **防抖搜索**: 500ms防抖，减少API调用
- **多选支持**: 支持选择多个话题标签
- **数量限制**: 根据平台限制话题数量
- **视觉反馈**: 清晰的选中状态和交互反馈

### API接口
```dart
// 话题搜索接口
Future<TopicResponse?> getTopicsApi({
  required String platform,     // 平台类型
  required String keyword,       // 搜索关键词
  required String accessToken,   // 访问令牌
})

// 其他相关接口
getUsersApi()        // 获取用户列表(@好友)
getActivitiesApi()   // 获取活动列表
getHotspotsApi()     // 获取热点列表
getMixListApi()      // 获取合集列表
```

### 平台支持
- **抖音**: 话题搜索、浏览量显示
- **小红书**: 话题搜索、浏览量显示
- **快手**: 话题搜索、浏览量显示
- **微信视频号**: 基础话题支持

### 交互设计
- **搜索框**: 顶部搜索输入框，带搜索图标
- **结果列表**: 下拉显示搜索结果，显示话题名称和浏览量
- **已选标签**: 蓝色标签显示已选话题，可单独删除
- **数量提示**: 显示可添加的话题数量限制

## 3. 集成实现

### 视频发布页面集成
```dart
// 标题输入框
_buildInputField(
  controller: _titleController,
  focusNode: _titleFocusNode,
  hint: 'video_publish.add_video_title'.tr,
  aiType: AiCreateType.title,
  maxLength: 30,
)

// 描述输入框
_buildInputField(
  controller: _descriptionController,
  focusNode: _descriptionFocusNode,
  hint: 'video_publish.add_video_description'.tr,
  aiType: AiCreateType.description,
  maxLength: 500,
)
```

### 平台配置对话框集成
```dart
// 话题选择器
TopicSelector(
  platform: platform,
  accessToken: accessToken,
  maxCount: topicMax,
  selectedTopics: selectedTopics,
  onChanged: (topics) {
    setState(() {
      _config['topics'] = topics;
    });
  },
)

// AI生成按钮
AiCreateButton(
  type: AiCreateType.title,
  videoFilePath: videoPath,
  maxLength: titleMax,
  onAiCreateFinish: (text) {
    _titleController.text = text;
    _config['custom_title'] = text;
  },
)
```

## 4. 数据流程

### AI生成流程
1. 用户点击AI生成按钮
2. 检查视频文件是否存在
3. 生成文件缓存key，检查是否已上传
4. 如未上传，调用 `/api/file/upload` 上传视频
5. 调用 `/tools/ai/video/title` 生成内容
6. 将生成结果填入对应输入框

### 话题选择流程
1. 用户输入搜索关键词
2. 500ms防抖后发起搜索请求
3. 调用 `/publish/topics` 接口搜索话题
4. 显示搜索结果列表
5. 用户点击选择话题
6. 更新已选话题列表

## 5. 错误处理

### AI生成错误处理
- **文件不存在**: 提示用户上传视频
- **上传失败**: 显示具体错误信息
- **生成失败**: 提示网络错误或重试
- **令牌无效**: 提示重新登录

### 话题搜索错误处理
- **网络错误**: 显示网络错误提示
- **令牌无效**: 提示账号访问令牌无效
- **搜索无结果**: 清空结果列表
- **选择限制**: 提示最大选择数量

## 6. 性能优化

### 缓存策略
- **视频文件缓存**: 避免重复上传相同视频
- **搜索防抖**: 减少不必要的API调用
- **结果缓存**: 相同关键词复用搜索结果

### UI优化
- **加载状态**: 清晰的加载动画和进度提示
- **响应式设计**: 适配不同屏幕尺寸
- **流畅交互**: 平滑的动画和过渡效果

## 7. 扩展性

### 新平台支持
- 在 `TopicSelector` 中添加新平台的话题搜索逻辑
- 在 `AiCreateButton` 中支持新平台的AI生成参数

### 新功能扩展
- 支持更多AI生成类型（如封面、标签等）
- 添加话题推荐功能
- 支持话题收藏和历史记录

## 8. 技术特点

- **完全复刻**: 与桌面端功能和交互完全一致
- **类型安全**: 使用强类型定义，减少运行时错误
- **响应式**: 基于GetX的响应式状态管理
- **模块化**: 组件化设计，易于维护和扩展
- **用户友好**: 清晰的提示信息和错误处理
