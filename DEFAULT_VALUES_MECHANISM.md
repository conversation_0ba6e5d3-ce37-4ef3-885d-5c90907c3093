# 发布配置默认值机制

## 功能概述

实现了发布配置的默认值机制：如果某个账号的发布配置中没有填写标题、描述或话题，则自动使用发布页面的通用标题、描述和话题作为默认值。

## 设计原理

### 1. 数据来源层级

```
1. 账号自定义配置 (最高优先级)
   ├── custom_title (账号自定义标题)
   ├── description (账号自定义描述)
   └── topics (账号自定义话题)

2. 通用页面配置 (默认值)
   ├── state.videoTitle.value (页面通用标题)
   ├── state.videoDescription.value (页面通用描述)
   └── state.videoTags (页面通用话题)

3. 空值 (最低优先级)
```

### 2. 优先级逻辑

```dart
// 标题优先级
title: config['custom_title']?.isNotEmpty == true 
    ? config['custom_title']                    // 1. 账号自定义标题
    : (config['title']?.isNotEmpty == true 
        ? config['title']                       // 2. 账号配置标题
        : defaultTitle)                         // 3. 页面通用标题

// 描述优先级
description: config['description']?.isNotEmpty == true 
    ? config['description']                     // 1. 账号自定义描述
    : defaultDescription                        // 2. 页面通用描述

// 话题优先级
topics: (config['topics'] as List<String>?)?.isNotEmpty == true 
    ? config['topics'] as List<String>?        // 1. 账号自定义话题
    : defaultTopics                            // 2. 页面通用话题
```

## 实现细节

### 1. 平台模型更新

每个平台模型的工厂方法都添加了默认值参数：

#### 抖音模型 (`DouyinPublishModel`)

```dart
factory DouyinPublishModel.fromAccountConfig({
  required Map<String, dynamic> account,
  required Map<String, dynamic> config,
  File? video,
  List<File>? images,
  File? cover,
  String? defaultTitle,        // 新增：默认标题
  String? defaultDescription,  // 新增：默认描述
  List<String>? defaultTopics, // 新增：默认话题
}) {
  return DouyinPublishModel(
    account: account,
    title: config['custom_title']?.isNotEmpty == true 
        ? config['custom_title'] 
        : (config['title']?.isNotEmpty == true ? config['title'] : defaultTitle),
    description: config['description']?.isNotEmpty == true 
        ? config['description'] 
        : defaultDescription,
    topics: (config['topics'] as List<String>?)?.isNotEmpty == true 
        ? config['topics'] as List<String>? 
        : defaultTopics,
    // ... 其他配置
  );
}
```

#### 小红书模型 (`XiaohongshuPublishModel`)

```dart
factory XiaohongshuPublishModel.fromAccountConfig({
  // 相同的参数结构和逻辑
  String? defaultTitle,
  String? defaultDescription,
  List<String>? defaultTopics,
}) {
  // 相同的优先级逻辑
}
```

#### 快手模型 (`KuaishouPublishModel`)

```dart
factory KuaishouPublishModel.fromAccountConfig({
  // 相同的参数结构和逻辑
  String? defaultTitle,
  String? defaultDescription,
  List<String>? defaultTopics,
}) {
  // 相同的优先级逻辑
}
```

### 2. 工厂方法更新

#### 单个模型创建

```dart
static BasePlatformPublishModel? createFromAccountConfig({
  required Map<String, dynamic> account,
  required Map<String, dynamic> config,
  File? video,
  List<File>? images,
  File? cover,
  String? defaultTitle,        // 新增参数
  String? defaultDescription,  // 新增参数
  List<String>? defaultTopics, // 新增参数
}) {
  // 根据平台类型调用对应的工厂方法，传递默认值
}
```

#### 批量模型创建

```dart
static List<BasePlatformPublishModel> createBatchFromAccountConfigs({
  required List<Map<String, dynamic>> selectedAccounts,
  required Map<String, Map<String, dynamic>> accountConfigs,
  File? video,
  List<File>? images,
  File? cover,
  String? defaultTitle,        // 新增参数
  String? defaultDescription,  // 新增参数
  List<String>? defaultTopics, // 新增参数
}) {
  // 为每个账号创建模型时传递默认值
}
```

### 3. 发布逻辑更新

在 `PublishLogic` 中调用模型创建时传递页面的通用值：

```dart
// 创建平台发布模型列表，传递通用的标题和描述作为默认值
final publishModels = PlatformPublishModelFactory.createBatchFromAccountConfigs(
  selectedAccounts: state.selectedAccounts,
  accountConfigs: state.accountPlatformConfigs,
  video: video,
  images: images,
  cover: cover,
  defaultTitle: state.videoTitle.value.isNotEmpty ? state.videoTitle.value : null,
  defaultDescription: state.videoDescription.value.isNotEmpty ? state.videoDescription.value : null,
  defaultTopics: state.videoTags.isNotEmpty ? state.videoTags : null,
);
```

## 使用场景

### 1. 完全使用默认值

**场景**: 用户在发布页面填写了通用标题和描述，但没有为任何账号单独配置

```
页面配置:
- 标题: "我的精彩视频"
- 描述: "这是一个很棒的视频"
- 话题: ["生活", "分享"]

账号配置: (空)

结果: 所有账号都使用页面的通用配置
```

### 2. 部分账号自定义

**场景**: 用户为某些账号设置了特殊的标题和描述

```
页面配置:
- 标题: "我的精彩视频"
- 描述: "这是一个很棒的视频"

抖音账号配置:
- 标题: "抖音专属标题"
- 描述: (空)

小红书账号配置:
- 标题: (空)
- 描述: "小红书专属描述"

结果:
- 抖音: 标题="抖音专属标题", 描述="这是一个很棒的视频"
- 小红书: 标题="我的精彩视频", 描述="小红书专属描述"
```

### 3. 混合配置

**场景**: 不同账号有不同程度的自定义配置

```
页面配置:
- 标题: "通用标题"
- 描述: "通用描述"
- 话题: ["通用话题"]

账号A: 完全自定义
- 标题: "A账号标题"
- 描述: "A账号描述"
- 话题: ["A话题"]

账号B: 部分自定义
- 标题: "B账号标题"
- 描述: (空) -> 使用"通用描述"
- 话题: (空) -> 使用["通用话题"]

账号C: 完全使用默认
- 标题: (空) -> 使用"通用标题"
- 描述: (空) -> 使用"通用描述"
- 话题: (空) -> 使用["通用话题"]
```

## 验证逻辑

### 1. 空值检查

```dart
// 检查字符串是否为空
config['custom_title']?.isNotEmpty == true

// 检查列表是否为空
(config['topics'] as List<String>?)?.isNotEmpty == true
```

### 2. 类型安全

```dart
// 安全的类型转换
config['topics'] as List<String>?

// 空值合并
config['title'] ?? defaultTitle
```

## 优势总结

### 1. 用户体验
- **简化配置**: 用户不需要为每个账号重复填写相同内容
- **灵活定制**: 可以为特定账号设置个性化内容
- **智能默认**: 自动使用页面配置作为默认值

### 2. 开发维护
- **统一逻辑**: 所有平台使用相同的默认值机制
- **类型安全**: 完整的空值检查和类型转换
- **易于扩展**: 新平台可以轻松集成相同机制

### 3. 数据一致性
- **优先级明确**: 清晰的配置优先级规则
- **回退机制**: 多层级的配置回退
- **验证完整**: 完善的空值和类型验证

## 扩展计划

### 1. 更多默认值类型
- 默认封面图片
- 默认发布时间
- 默认可见性设置

### 2. 配置模板
- 保存常用配置为模板
- 快速应用模板到账号
- 模板的导入导出

### 3. 智能推荐
- 基于历史配置推荐默认值
- 根据平台特性推荐配置
- AI生成个性化配置

## 总结

通过实现默认值机制，我们显著提升了用户体验：

1. **减少重复工作**: 用户不需要为每个账号重复填写相同内容
2. **保持灵活性**: 仍然支持为特定账号设置个性化配置
3. **智能回退**: 自动使用页面通用配置作为默认值
4. **类型安全**: 完整的空值检查和类型验证

这种设计既简化了用户操作，又保持了系统的灵活性和可扩展性。
