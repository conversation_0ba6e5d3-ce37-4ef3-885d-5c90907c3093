# 哎呦赚 Flutter 应用

## 项目介绍
哎呦赚是一款基于Flutter开发的多平台内容创作工具，集成主流社交平台API，提供热点内容获取、素材管理、一键发布等功能。

## 技术架构
- **核心框架**: Flutter 3.x
- **状态管理**: GetX
- **网络请求**: Dio (封装于 `lib/network/http.dart`)
- **路由管理**: GetX Router `lib/routers/router.dart`
- **UI组件库**: tdesign_flutter
- **本地存储**: GetX Persistent
- **国际化**: GetX Localization

## 项目结构
```
lib/
├── api/                 # API接口层
│   ├── account/         # 账户相关接口
│   ├── hot/             # 热点内容接口
│   ├── media_material/  # 媒体素材接口
│   ├── platform_apis/   # 各平台专用接口
│   └── models/          # API数据模型
├── config/              # 应用配置
│   ├── app_config.dart  # 应用基础配置
│   ├── api_keys.dart    # 第三方API密钥
│   └── plat_config/     # 平台配置参数
├── i18n/                # 国际化支持
│   ├── app_translations.dart
│   └── languages/
├── main.dart            # 应用入口
├── models/              # 业务数据模型
│   ├── draft_models/    # 草稿箱模型
│   ├── hot_events_models/
│   └── platform_models/
├── network/             # 网络层
│   ├── dio_client.dart  # Dio实例配置
│   ├── dio_interceptors.dart
│   └── http.dart        # 请求工具类
├── pages/               # 应用页面
│   ├── home_page/       # 首页
│   ├── draft/           # 草稿箱
│   ├── mine/            # 个人中心
│   ├── publish/         # 发布页面
│   └── [page]/          # 其他页面
│       ├── view.dart    # 视图
│       ├── logic.dart   # 逻辑
│       ├── state.dart   # 状态
│       └── widgets/     # 页面组件
├── plat_core/           # 平台核心模块
│   ├── plats/           # 各平台实现
│   ├── services/        # 平台服务
│   └── utils/           # 平台工具
├── res/                 # 资源文件
│   ├── app_colors.dart  # 颜色定义
│   ├── constant.dart    # 常量
│   └── dimens.dart      # 尺寸定义
├── routers/             # 路由配置
├── store/               # 数据存储
├── utils/               # 工具类
│   ├── dialog/          # 对话框工具
│   ├── permission/      # 权限处理
│   ├── toast_and_loading.dart
│   └── bubble_menu/     # 气泡菜单工具
├── viewModels/          # 视图模型
└── widgets/             # 通用组件
```

## 开发规范

### 页面开发
遵循GetX架构，每个页面拆分为2个核心文件：
- **xx_view.dart**: GetView的UI布局，仅包含Widget构建
- **xx_controller.dart**: 业务逻辑，继承GetxController
- **widgets/**: 复杂页面拆分子组件

### 代码规范
- 使用`dart format`格式化代码
- Git提交前通过husky执行自动格式化
- 遵循Effective Dart编码规范

## 快速开始
### 环境要求
- Flutter 3.29.3
- Dart 3.7.2
- Xcode 14+ (iOS)
- Android Studio Hedgehog+ (Android)

### 安装依赖
```bash
flutter pub get
```

### 运行应用
```bash
flutter run
```

### 构建发布
```bash
# Android
flutter build appbundle

# iOS
flutter build ipa
```

## 国际化
项目使用GetX自带的国际化方案，配置文件位于 `lib/i18n/` 目录下。

### 文件位置
- 翻译配置：`lib/i18n/app_translations.dart`
- 语言服务管理：`lib/i18n/language_service.dart`
- 入口文件：`lib/main.dart`

### 使用方法
1. **定义翻译**：在 `app_translations.dart` 中配置语言映射
```dart:/Users/<USER>/AndroidStudioProjects/aitoearn_app/lib/i18n/app_translations.dart
class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'zh_CN': {
      'home': '首页',
      'draft': '草稿箱',
      // ...其他翻译
    },
    'en_US': {
      'home': 'Home',
      'draft': 'Draft',
      // ...其他翻译
    },
  };
}
```

2. **初始化配置**：在 `main.dart` 中初始化国际化
```dart:/Users/<USER>/AndroidStudioProjects/aitoearn_app/lib/main.dart
void main() {
  runApp(
    GetMaterialApp(
      translations: AppTranslations(),
      locale: Locale('zh', 'CN'), // 默认语言
      fallbackLocale: Locale('zh', 'CN'), // 备用语言
      // ...其他配置
    ),
  );
}
```

3. **获取翻译**：在代码中使用 `tr()` 方法
```dart
Text('home'.tr)
// 带参数的翻译
Text('greeting'.trParams({'name': '用户'}))
```

4. **切换语言**：使用 `Get.updateLocale()` 方法
```dart
// 切换到英文
Get.updateLocale(Locale('en', 'US'));
// 切换到中文
Get.updateLocale(Locale('zh', 'CN'));
```

## JSON序列化
执行shell脚本自动生成序列化代码：
```bash
./shell/start_json_build_watch.bat
```
推荐使用在线工具：https://caijinglong.github.io/json2dart/index_ch.html

## 工具类用法

### Toast
- **文件位置**: `lib/utils/toast_and_loading.dart`
- **常用方法**:
  ```dart
  // 普通提示
  showToast('操作成功');
  
  // 成功提示
  showSuccess('提交成功');
  
  // 错误提示
  showError('提交失败，请重试');
  ```

### 全局加载弹窗
- **文件位置**: `lib/utils/toast_and_loading.dart`
- **常用方法**:
  ```dart
  // 显示加载弹窗
  final cancelFunc = showProgress(text: '加载中...');
  
  // 关闭加载弹窗
  cancelFunc();
  // 或
  dismissAllProgress();
  ```

### Dialog
- **文件位置**: `lib/utils/dialog/dialog_helper.dart`
- **常用方法**:
  ```dart
  // 普通对话框
  DialogHelper.showAlertDialog(
    title: '提示',
    content: '确定要删除吗？',
    onConfirm: () {},
  );
  
  // 简单确认对话框
  final result = await DialogHelper.showSimpleDialog(
    content: '确定要退出吗？',
  );
  
  // 底部选项对话框
  DialogHelper.showBottomSheetDialog(
    items: [
      MenuItem('选项1', onTap: () {}),
      MenuItem('选项2', onTap: () {}),
    ],
  );
  ```

### 表单对话框
- **文件位置**: `lib/utils/dialog/form_dialog_helper.dart`
- **常用方法**:
  ```dart
  final result = await FormDialogHelper.showFormDialog(
    title: '新建项目',
    formItems: [
      InputFormItem(
        name: '项目名称',
        hint: '请输入项目名称',
        required: true,
      ),
      DropdownFormItem(
        name: '项目类型',
        options: ['公开', '私有'],
      ),
    ],
  );
  ```

### 气泡菜单
- **文件位置**: `lib/utils/bubble_menu/menu_pop_helper.dart` (假设路径)
- **常用方法**:
  ```dart
  MenuPopHelper.show(
    context: context,
    items: [
      MenuOption('编辑', onTap: () {}),
      MenuOption('删除', onTap: () {}),
    ],
    position: RelativeRect.fromRect(
      Rect.fromLTWH(100, 100, 0, 0),
      Rect.fromLTWH(0, 0, MediaQuery.of(context).size.width, MediaQuery.of(context).size.height),
    ),
  );
  ```

### 统一页面封装
- **文件位置**: `lib/pages/base/base_page.dart`
- **使用示例**:
  ```dart
  class HomePage extends StatelessWidget {
    @override
    Widget build(BuildContext context) {
      return BasePage(
        title: '首页',
        body: Center(child: Text('首页内容')),
        action: IconButton(
          icon: Icon(Icons.settings),
          onPressed: () {},
        ),
      );
    }
  }
  ```

### 网络库
- **文件位置**: `lib/network/http.dart`
- **常用方法**:
  ```dart
  // GET请求
  final data = await Http.get('/api/user', query: {'id': '123'});
  
  // POST请求
  final result = await Http.post('/api/login', data: {'username': 'test', 'password': '123'});
  
  // 带token请求
  final userInfo = await Http.get('/api/user/info', withToken: true);
  ```

### 网络图加载
- **文件位置**: `lib/widgets/network_image_widget.dart`
- **使用示例**:
  ```dart
  NetworkImageWidget(
    'https://example.com/image.jpg',
    width: 100,
    height: 100,
    borderRadius: BorderRadius.circular(8),
    placeholder: CircularProgressIndicator(),
  );
  ```

### 大图展示（支持多图）
- **文件位置**: `lib/utils/image_preview_viewer.dart`
- **常用方法**:
  ```dart
  ImagePreviewViewer.show(
    context: context,
    imageUrls: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    heroTags: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg']
    initialIndex: 0,
  );
  ```
### 视频展示
- **文件位置**: `lib/utils/video_preview_viewer.dart`
- **常用方法**:
  ```dart
  VideoPreviewViewer.show(
    context: context,
    videoUrl: 'https://example.com/video.mp4',
    heroTag: 'https://example.com/video.mp4',
  );
  ```
  
### 底部弹出框
- **文件位置**: `lib/utils/universal_bottom_sheet.dart`
- **常用方法**:
  ```dart
  UniversalBottomSheet.show(context: context, child: Text("内容"))
  ```

### 通用下拉菜单 - 简单
- **文件位置**: `lib/widgets/dropdown/common_dropdown.dart`
- **常用方法**:
  ```dart
  CommonDropdown(
    items: state.topicTypeList.value,
    value: state.currentTopicType.value,
    onChanged: (String? value) {
      logic.changeTopicType(value!);
    },
    isLoading: state.topicTypeList.value == null,
  )
  ```
  
### 统一WebView浏览页面
- **文件位置**: `lib/pages/webview/web_page.dart`
- **常用方法**:
  ```dart
  Get.toNamed(AppRouter.webPath, arguments: {
    'url': 'https://www.baidu.com',
    'title': '百度',
    'fullScreen': true,
  });
  ```