// Gologin provides a cloud browser that can be used to run puppeteer aytomation.
// It will handle the browser start and close management - you just need to control the browser with pupputter
import { GologinApi } from 'gologin';
import axios from "axios";

const token =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RpZCI6IjY4ODIwNjJkMWU3NmQzMzAyOTE2MTQ1ZSIsInR5cGUiOiJ1c2VyIiwic3ViIjoiNjg4MjA2MmQxZTc2ZDMzMDI5MTYxNDU5In0.F_kI0rnS1f6pojy76bOUB2xciyS0RbGqh9u1esY0S-U';
const profileId = "6882062e1e76d330291614da"
const gologin = GologinApi({
  token,
  //profile_id: profileId,
});

async function main() {

  const { browser } = await gologin.launch({
    cloud: true,
    profileId
    // pass profileId parameter if you want to run particular profile
    // profileId: profileId,
  });

  const page = await browser.newPage();
  await page.goto('https://iphey.com/', { waitUntil: 'networkidle2' });

  let remoteOrbitaUrl = '';
  while (true) {
    const res = await axios(
      'https://api.gologin.com/browser/run-sync?isPinging=true',
      {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        data: {
          "profiles": [
            {
              "id": profileId,
              "isRunning": true
            },
          ],
          "type": "desktop",
          "googleAnalyticsId": ""
        }
      },
    );
    console.log("----------------------------")
    console.log(res.data['profilesRunningInWeb']);
    remoteOrbitaUrl = res.data['profilesRunningInWeb'][0].remoteOrbitaUrl;
    if (remoteOrbitaUrl) {
      break;
    }
    // 等待 n 秒
    await new Promise((resolve) => setTimeout(resolve, 5000));
  }
  console.log(remoteOrbitaUrl);

  await new Promise((resolve) => setTimeout(resolve, 1115000));
  const status = await page.$eval('.trustworthy:not(.hide)',
    (elt) => elt?.innerText?.trim(),
  );

  console.log('status', status);

  return status;
}

main().catch(console.error)
  .finally(gologin.exit);