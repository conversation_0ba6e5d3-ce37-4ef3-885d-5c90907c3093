import { GologinApi } from 'gologin';
//const { GologinApi } = require('gologin');


// Token can be passed here in code or from env
const token = process.env.GL_API_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2ODgwY2QzYzcwMTQxN2EzNjY2ZDcxOTAiLCJ0eXBlIjoiZGV2Iiwiand0aWQiOiI2ODgwY2RjM2M4Y2JhMmU2YjQxZjNiOTQifQ.3O95S4TIhzJsZTWdZSr2UISAH2T3_034mNNYul-wdvQ';
const gologin = GologinApi({
  token,
  profile_id: '6881eff39f571b939c8a2d30',
});

async function main() {
  const { browser } = await gologin.launch();

  // Opens new page in browser
  const page = await browser.newPage();

  // Goes to website and waits untill all parts of the website is loaded
  await page.goto('https://iphey.com/', { waitUntil: 'networkidle2' });

  // Reads profile check result in website
  const status = await page.$eval('.trustworthy:not(.hide)',
    (elt) => elt?.innerText?.trim(),
  );

  await new Promise((resolve) => setTimeout(resolve, 10000));
  console.log('status', status);

  return status;
}

main().catch(console.error)
  .finally(gologin.exit);