import { chromium }  from 'playwright';
import md5  from 'md5';
import axios  from 'axios';

const HEADERS =  {
  "Content-Type": "application/json",
  "Accept": "application/json",
};
const acc_info = {
  // Insert your account information in both variables below
  "email": "<EMAIL>",
  "password": md5("Yika888666!")
};

async function get_token() {
  const signIn_URL = "https://api.multilogin.com/user/signin";
  try {
    const response = await axios.post(signIn_URL, acc_info, { headers: HEADERS });
    return response.data.data.token;
  } catch (error) {
    console.log(error.message);
    console.log("Response data:", error.response.data);
    return false;
  }
};
// Insert the Folder ID and the Profile ID below
const folder_id = "aab5e49e-0ec2-4254-b33f-44868dd373a2";
const profile_id = "bfc9cc37-5d6f-4211-a9d7-184a2945c3f1";

async function start_browserProfile(){
  const token = await get_token();
  if (!token) return;
  // Update HEADERS with bearer token retrived from the get_token function
  HEADERS.Authorization = 'Bearer ' + token;
  // Launch a profile defining "Playwright" as automation type
  const profileLaunch_URL = `https://launcher.mlx.yt:45001/api/v2/profile/f/${folder_id}/p/${profile_id}/start?automation_type=playwright&headless_mode=false`;
  try {
    const response = await axios.get(profileLaunch_URL, { headers: HEADERS });
    const browserURL = `http://127.0.0.1:${response.data.data.port}`;
    // if you prefer to connect with browserWSEndpoint, try to get the webSocketDebuggerUrl by following request
    // const {data : {webSocketDebuggerUrl}} = await axios.get(`${browserURL}/json/version`)
    const browser = await chromium.connectOverCDP(browserURL,{timeout:10000});
    const context = browser.contexts()[0];
    const page = await context.newPage();
    await page.goto("https://multilogin.com/");
    await page.screenshot({path: "example.png"});
    await page.close();
  } catch (error) {
    console.log("Error:", error.message);
    if (error.response) {
      console.log("Response data:", error.response.data);
    }
  }
};

start_browserProfile();