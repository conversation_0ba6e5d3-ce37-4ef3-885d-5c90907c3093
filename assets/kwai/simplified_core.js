// 简化版的快手签名核心JS文件

// 模拟快手签名的计算函数
(function() {
  // 用于签名计算的内部函数
  function generateSignature(md5String) {
    // 将MD5转为字符数组
    var chars = md5String.split('');
    
    // 基本字符表
    var baseTable = "0123456789abcdef";
    var encodeTable = "abcdefghijklmnopqrstuvwxyz0123456789";
    
    // 简单混淆算法
    var result = "";
    for (var i = 0; i < md5String.length; i++) {
      var c = md5String.charAt(i);
      var index = baseTable.indexOf(c);
      if (index >= 0) {
        result += encodeTable.charAt((index + i) % encodeTable.length);
      } else {
        result += c;
      }
    }
    
    // 添加时间戳作为签名的一部分，确保每次都不同
    var timestamp = Date.now().toString().substring(7, 13);
    result += timestamp;
    
    // 生成固定长度的签名
    while (result.length < 46) {
      result += encodeTable.charAt(Math.floor(Math.random() * encodeTable.length));
    }
    
    return result;
  }
  
  // 创建一个全局对象用于存储方法和状态
  var kwaiSignModule = {};
  
  // 初始化方法
  kwaiSignModule.init = function() {
    console.log("快手签名模块初始化成功");
    return true;
  };
  
  // 签名方法
  kwaiSignModule.sign = function(md5String, callback) {
    try {
      // 生成签名
      var signature = generateSignature(md5String);
      
      // 使用回调返回结果
      if (callback && typeof callback === 'object') {
        if (callback.suc && typeof callback.suc === 'function') {
          callback.suc(signature);
        }
      }
      
      return signature;
    } catch (e) {
      console.error("签名生成失败: ", e);
      
      if (callback && typeof callback === 'object') {
        if (callback.err && typeof callback.err === 'function') {
          callback.err(e.toString());
        }
      }
      
      return null;
    }
  };
  
  // 创建全局导出对象
  var exports = {
    realm: {
      global: {
        "$encode": kwaiSignModule.sign
      }
    }
  };
  
  // 导出到全局对象
  var default_1 = {
    75407: function(obj) {
      if (obj && typeof obj === 'object') {
        obj.exports = exports;
      }
      return exports;
    }
  };
  
  // 暴露到全局作用域
  globalThis.default_1 = default_1;
  
  // 直接导出签名方法用于测试
  globalThis.kwaiSign = function(md5Str, callback) {
    return kwaiSignModule.sign(md5Str, callback);
  };
})();
