// 测试签名JS文件
// 提供用于测试JS引擎和快手签名的辅助函数

(function() {
  // 测试全局环境变量
  var _testGlobalEnvironment = function() {
    var globals = [];
    try {
      // 尝试访问常见的全局对象
      if (typeof window !== 'undefined') globals.push('window');
      if (typeof document !== 'undefined') globals.push('document');
      if (typeof navigator !== 'undefined') globals.push('navigator');
      if (typeof console !== 'undefined') globals.push('console');
      if (typeof JSON !== 'undefined') globals.push('JSON');
      if (typeof Date !== 'undefined') globals.push('Date');
      if (typeof Math !== 'undefined') globals.push('Math');
      if (typeof Object !== 'undefined') globals.push('Object');
      if (typeof Array !== 'undefined') globals.push('Array');
      if (typeof String !== 'undefined') globals.push('String');
      if (typeof Number !== 'undefined') globals.push('Number');
      if (typeof Boolean !== 'undefined') globals.push('Boolean');
      if (typeof Error !== 'undefined') globals.push('Error');
      if (typeof Function !== 'undefined') globals.push('Function');
      if (typeof RegExp !== 'undefined') globals.push('RegExp');
      if (typeof Promise !== 'undefined') globals.push('Promise');
    } catch(e) {
      return { error: e.toString(), availableGlobals: [] };
    }
    
    return { 
      availableGlobals: globals,
      hasBasicGlobals: globals.includes('JSON') && globals.includes('Date') && globals.includes('Math')
    };
  };

  // 模拟签名函数
  var _generateTestSignature = function(md5String) {
    // 简单地将MD5字符串反转并添加一些固定字符
    var reversed = md5String.split('').reverse().join('');
    return 'test_' + reversed + '_' + Date.now().toString().substr(-6);
  };

  // 暴露给全局的测试函数
  globalThis.testSign = function(md5String, callback) {
    try {
      var signature = _generateTestSignature(md5String);
      if (callback && typeof callback === 'object') {
        if (callback.suc && typeof callback.suc === 'function') {
          callback.suc(signature);
        }
      } else if (callback && typeof callback === 'function') {
        callback({success: true, signature: signature});
      }
      return signature;
    } catch (e) {
      if (callback && typeof callback === 'object') {
        if (callback.err && typeof callback.err === 'function') {
          callback.err(e.toString());
        }
      } else if (callback && typeof callback === 'function') {
        callback({success: false, error: e.toString()});
      }
      return null;
    }
  };

  // 测试JS环境是否正常
  globalThis.testJsEnvironment = function() {
    return {
      status: 'ok',
      timestamp: Date.now(),
      environment: _testGlobalEnvironment(),
      features: {
        json: typeof JSON !== 'undefined',
        date: typeof Date !== 'undefined',
        math: typeof Math !== 'undefined',
        function: typeof Function !== 'undefined',
        object: typeof Object !== 'undefined',
        array: typeof Array !== 'undefined'
      }
    };
  };
  
  // 测试kuaiShoSignCore.js的加载情况
  globalThis.testKwaiSignCore = function() {
    try {
      // 测试default_1是否存在
      var hasDefault1 = typeof default_1 !== 'undefined';
      
      // 测试default_1[75407]是否是函数
      var hasMainFunc = hasDefault1 && typeof default_1[75407] === 'function';
      
      return {
        success: hasMainFunc,
        hasDefault1: hasDefault1,
        hasMainFunc: hasMainFunc
      };
    } catch(e) {
      return {
        success: false,
        error: e.toString()
      };
    }
  };
  
  // 测试创建一个对象并执行签名初始化
  globalThis.testKwaiSignInit = function() {
    try {
      var obj = {
        exports: {},
        id: 75407,
        loaded: true
      };
      
      // 尝试执行初始化函数
      if (typeof default_1 !== 'undefined' && typeof default_1[75407] === 'function') {
        default_1[75407](obj);
        
        // 测试obj.exports是否包含realm
        var hasRealm = typeof obj.exports === 'object' && obj.exports !== null && typeof obj.exports.realm === 'object';
        
        // 测试realm.global是否包含$encode
        var hasEncode = hasRealm && 
                        typeof obj.exports.realm.global === 'object' && 
                        typeof obj.exports.realm.global['$encode'] === 'function';
        
        return {
          success: hasEncode,
          hasExports: typeof obj.exports === 'object' && obj.exports !== null,
          hasRealm: hasRealm,
          hasEncode: hasEncode
        };
      }
      
      return {
        success: false,
        error: 'default_1[75407] is not a function'
      };
    } catch(e) {
      return {
        success: false,
        error: e.toString()
      };
    }
  };
})(); 