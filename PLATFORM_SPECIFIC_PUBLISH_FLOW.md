# 平台特定发布流程

## 问题背景

在原有的发布系统中，所有平台共用相同的发布参数，但现在每个平台账号都有独立的配置参数：

- **抖音**: 自主声明、@好友、活动参与、热点关联、合集选择等
- **小红书**: 评论设置、私密笔记等
- **其他平台**: 各自特有的配置项

这需要重新设计发布流程的参数分发机制。

## 解决方案

### 1. 平台特定参数模型 (`PlatformPublishParams`)

创建了新的参数模型来处理每个平台的独立配置：

```dart
class PlatformPublishParams {
  /// 账号信息
  final Map<String, dynamic> account;
  
  /// 平台类型
  final String platform;
  
  /// 基础内容
  final String? title;
  final String? description;
  final List<String>? topics;
  
  /// 媒体文件
  final File? video;
  final List<File>? images;
  final File? cover;
  
  /// 平台特定配置
  final Map<String, dynamic> platformConfig;
  
  /// 通用配置
  final bool isScheduled;
  final DateTime? scheduledTime;
  final VisibleTypeEnum visibleType;
  final LocationModel? location;
}
```

### 2. 多平台发布管理器 (`MultiPlatformPublishManager`)

提供统一的参数创建和验证机制：

```dart
class MultiPlatformPublishManager {
  /// 从选中账号和配置创建平台发布参数列表
  static List<PlatformPublishParams> createPlatformParams({
    required List<Map<String, dynamic>> selectedAccounts,
    required Map<String, Map<String, dynamic>> accountConfigs,
    required File? video,
    required List<File>? images,
    required File? cover,
    Function(double progress, String message)? progressCallback,
  });

  /// 验证发布参数
  static String? validatePublishParams(List<PlatformPublishParams> paramsList);
}
```

## 新发布流程

### 1. 参数创建阶段

```dart
// 创建平台特定的发布参数列表
final platformParamsList = MultiPlatformPublishManager.createPlatformParams(
  selectedAccounts: state.selectedAccounts,
  accountConfigs: state.accountPlatformConfigs,
  video: video,
  images: images,
  cover: cover,
  progressCallback: (progress, message) {
    // 进度回调
  },
);
```

### 2. 参数验证阶段

```dart
// 验证发布参数
final validationError = MultiPlatformPublishManager.validatePublishParams(platformParamsList);
if (validationError != null) {
  throw Exception(validationError);
}
```

### 3. 发布执行阶段

```dart
// 使用新的发布方法
final tasks = await publishFactory.publishWithPlatformParams(platformParamsList);
```

## 平台特定配置处理

### 1. 抖音平台配置

```dart
case 'douyin':
  // 抖音特定配置
  if (accountConfig['self_declare'] != null) {
    config['self_declare'] = accountConfig['self_declare'];
  }
  if (accountConfig['mentioned_users'] != null) {
    config['mentioned_users'] = accountConfig['mentioned_users'];
  }
  if (accountConfig['activity_id'] != null) {
    config['activity_id'] = accountConfig['activity_id'];
  }
  // ... 其他抖音特定配置
  break;
```

### 2. 小红书平台配置

```dart
case 'xhs':
  // 小红书特定配置
  if (accountConfig['disable_comment'] != null) {
    config['disable_comment'] = accountConfig['disable_comment'];
  }
  break;
```

### 3. 可见性配置转换

```dart
// 抖音可见性转换
if (platform == 'douyin') {
  final visibilityType = accountConfig['visibility_type'] as int? ?? 0;
  switch (visibilityType) {
    case 0: visibleType = VisibleTypeEnum.public; break;
    case 1: visibleType = VisibleTypeEnum.private; break;
    case 2: visibleType = VisibleTypeEnum.friends; break;
  }
}

// 小红书可见性转换
else if (platform == 'xhs') {
  final isPrivate = accountConfig['is_private'] as bool? ?? false;
  visibleType = isPrivate ? VisibleTypeEnum.private : VisibleTypeEnum.public;
}
```

### 4. 位置信息处理

```dart
// 抖音位置处理
if (platform == 'douyin') {
  location = LocationModel(
    name: selectedLocation.address ?? '',
    latitude: selectedLocation.latitude?.toDouble(),
    longitude: selectedLocation.longitude?.toDouble(),
    platformSpecific: {
      'poi_id': selectedLocation.poiId,
      'city_code': selectedLocation.cityCode,
    },
  );
}

// 小红书位置处理
else if (platform == 'xhs') {
  location = LocationModel(
    name: selectedLocation.poiName ?? '',
    latitude: selectedLocation.latitude?.toDouble(),
    longitude: selectedLocation.longitude?.toDouble(),
    platformSpecific: {
      'poi_id': selectedLocation.poiId,
      'poi_type': selectedLocation.poiType,
    },
  );
}
```

## 发布工厂更新

### 1. 新增发布方法

```dart
// 使用平台特定参数发布
Future<List<PublishTask>> publishWithPlatformParams(
  List<PlatformPublishParams> platformParamsList, {
  Function(double progress, String message)? progressCallback,
}) async {
  // 为每个平台参数创建发布任务
  for (final platformParams in platformParamsList) {
    // 获取平台实例
    final platInstance = PlatManager.instance.getPlatInstance(platformParams.platform);
    
    // 转换为传统的发布参数模型
    final publishParams = _convertToPublishParamsModel(platformParams);
    
    // 创建发布任务
    final task = PublishTask(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      platform: platformParams.platform,
      platInstance: platInstance,
      account: platformParams.account,
      params: publishParams,
    );
    
    // 执行任务
    await executeTask(task);
  }
}
```

### 2. 参数转换方法

```dart
// 将平台特定参数转换为传统的发布参数模型
PublishParamsModel _convertToPublishParamsModel(PlatformPublishParams platformParams) {
  final publishParams = PublishParamsModel(
    platformParams.video ?? File(''),
    title: platformParams.title,
    description: platformParams.description,
    topics: platformParams.topics,
    images: platformParams.images,
    cover: platformParams.cover,
    visibleType: platformParams.visibleType,
    location: platformParams.location,
    isScheduled: platformParams.isScheduled,
    scheduledTime: platformParams.scheduledTime,
  );
  
  // 设置平台特定配置
  publishParams.platformSpecificConfig = platformParams.platformConfig;
  
  return publishParams;
}
```

## 验证机制

### 1. 基础验证

```dart
static String? validatePublishParams(List<PlatformPublishParams> paramsList) {
  if (paramsList.isEmpty) {
    return '没有选择发布账号';
  }
  
  for (final params in paramsList) {
    // 检查媒体文件
    if (params.video == null && (params.images == null || params.images!.isEmpty)) {
      return '账号 ${params.account['nickname']} 缺少媒体文件';
    }
    
    // 检查标题
    if (params.title == null || params.title!.isEmpty) {
      return '账号 ${params.account['nickname']} 缺少标题';
    }
  }
}
```

### 2. 平台特定验证

```dart
static String? _validatePlatformSpecific(PlatformPublishParams params) {
  switch (params.platform) {
    case 'douyin':
      if (params.title != null && params.title!.length > 30) {
        return '抖音标题不能超过30个字符';
      }
      break;
      
    case 'xhs':
      if (params.title != null && params.title!.length > 20) {
        return '小红书标题不能超过20个字符';
      }
      break;
  }
}
```

## 使用方式

### 1. 在PublishLogic中调用

```dart
// 使用新的发布方法
final success = await publishVideoWithPlatformParams(backgroundMode: false);
```

### 2. 兼容性保持

```dart
// 保留原有方法以兼容旧代码
Future<bool> publishVideo({bool backgroundMode = false}) async {
  // 原有实现保持不变
}
```

## 优势总结

### 1. 灵活性
- **平台定制**: 每个平台可以有独特的配置参数
- **独立配置**: 每个账号的配置互不影响
- **扩展性**: 易于添加新平台和新配置项

### 2. 可维护性
- **模块化**: 平台特定逻辑分离
- **类型安全**: 强类型的参数模型
- **验证机制**: 完善的参数验证

### 3. 用户体验
- **个性化**: 每个平台账号可以有不同的发布配置
- **一致性**: 统一的发布流程和进度反馈
- **可靠性**: 完善的错误处理和验证机制

## 扩展计划

### 1. 新平台支持
- 快手平台特定配置
- 微信视频号特定配置
- B站平台特定配置

### 2. 高级功能
- 配置模板功能
- 批量配置管理
- 配置导入导出

### 3. 性能优化
- 并行发布优化
- 参数缓存机制
- 内存使用优化

## 总结

通过实现平台特定的发布流程，我们解决了每个平台账号独立配置的需求，同时保持了系统的灵活性和可扩展性。这种设计为未来的功能扩展和新平台支持奠定了坚实的基础。
