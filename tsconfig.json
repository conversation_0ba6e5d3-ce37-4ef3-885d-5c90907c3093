{
  "compilerOptions": {
    "types": ["vite-plugin-svgr/client", "node"],
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@@/*": [
        "commont/*"
      ],
    },
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
  },
  "include": [
    "src",
    "electron",
    "commont"
, "electron/main/api/.ts"  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ],
}