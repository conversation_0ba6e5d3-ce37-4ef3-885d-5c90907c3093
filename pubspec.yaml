name: a<PERSON>earn_app
description: "哎呦赚"
version: 1.0.1+1
publish_to: none

environment:
  sdk: ">=3.7.2 <4.0.0"
  flutter: ">=3.29.0 <=3.29.3"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8
  # webview
  webview_flutter: ^4.11.0
  # cookie管理
  webview_cookie_manager:
    git:
      url: https://github.com/fryette/webview_cookie_manager
      ref: 8572dd54c437c919aedc8dca6c5bffd25fe74b3c
  # http请求库
  dio: ^5.6.0
  # 组件库
  tdesign_flutter: 0.2.0
  # 状态管理库 getx
  get: ^4.7.2
  # 本地储存
  get_storage: ^2.1.1
  # json序列号
  json_annotation: ^4.9.0
  # 日志
  logger: ^2.5.0
  # 键盘事件扩展
  keyboard_actions: ^4.2.0
  # 获取设备信息
  device_info_plus: ^11.3.3
  # 验证码输入框
  pinput: ^5.0.1
  # svg库
  flutter_svg: ^2.0.17
  # 页面动画库
  animations: ^2.0.11
  # 不需要 context 的全局提示
  bot_toast: ^4.1.3
  # 上传图片、视频组件
  wechat_assets_picker: ^9.5.0
  xml2json: ^6.2.7
  xml: ^6.5.0
  mime: ^2.0.0
  ducafe_ui_core: ^1.0.6
  crypto: ^3.0.6
  uuid: ^4.2.2
  path_provider: ^2.1.5
  crclib: ^3.0.0
  http_parser: ^4.1.2
  permission_handler: ^12.0.0+1
  # 视频缩略图
  video_thumbnail: ^0.5.6
  # 日历
  table_calendar: ^3.1.3
  args: ^2.7.0
  path: ^1.9.1
  http: ^1.4.0
  image_picker: ^1.0.8
  # 版本检测
  pub_semver: ^2.2.0
  # 打开网页
  url_launcher: ^6.3.1
  geolocator: ^14.0.1
  google_sign_in: ^6.3.0
  google_sign_in_all_platforms: ^1.2.1
  ffmpeg_kit_flutter_minimal: ^6.0.8
  window_manager: ^0.5.0
  # 媒体播放
  media_kit: ^1.1.10
  media_kit_video: ^1.1.10
  media_kit_libs_windows_video: ^1.0.8
  media_kit_libs_android_video: 1.3.5
  media_kit_libs_macos_video: ^1.1.4
  collection: ^1.19.1
  # 图片库统一
  extended_image: ^10.0.1
  file_picker: ^10.2.0
  intl: any
  skeletonizer: ^1.4.3
  package_info_plus: ^8.3.0
  flutter_lazy_indexed_stack: ^0.0.6
  fl_chart: ^1.0.0
  # 下拉菜单
  dropdown_button2: ^2.3.9
  flutter_json_view: ^1.1.5
  flutter_inappwebview: ^6.1.5
  ffmpeg_kit_flutter_new: ^3.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  custom_lint: ^0.7.3
  json_serializable: ^6.8.0
  husky: ^0.1.7
  lint_staged: ^0.5.1

flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/theme.json
    - assets/images/
    - assets/images/ai/
    - assets/images/login/
    - assets/images/account/
    - assets/images/account/plat_icon/
    - assets/images/publish/
    - assets/images/publish/hotspot/
    - assets/svg/
    - assets/fingerprint/utils.js

lint_staged:
  "lib/**.dart": dart format && dart fix --apply

targets:
  $default:
    builders:
      json_serializable:
        options:
          checked: false
          any_map: true
          explicit_to_json: true
