{"$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json", "appId": "cn.aitoearn.pc", "productName": "哎哟赚AiToEarn", "asar": true, "asarUnpack": ["**/node_modules/sharp/**/*", "**/node_modules/@img/**/*"], "directories": {"output": "release/${version}"}, "files": ["dist-electron", "dist"], "mac": {"gatekeeperAssess": false, "hardenedRuntime": true, "entitlements": "scripts/entitlements.mac.plist", "entitlementsInherit": "scripts/entitlements.mac.plist", "identity": null, "artifactName": "${productName}-${version}-${arch}.${ext}", "target": [{"target": "default", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "artifactName": "${productName}-${version}.${ext}"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false}, "publish": {"provider": "generic", "channel": "latest", "url": "https://ylzsfile.yikart.cn/att/"}, "extraResources": [{"from": "public/assets", "to": "assets", "filter": ["**/*"]}]}